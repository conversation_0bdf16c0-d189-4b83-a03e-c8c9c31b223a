@use '@angular/material' as mat
@use '@angular/material/core/theming/palettes' as mat-palettes
@use 'sass:map'
@use 'sass:meta'

// Include the common styles for Angular Material
@include mat.core()

// Create our custom palettes
// TODO: Verify and use the correct palette names for your desired theme.
// Using $blue-palette for primary and $rose-palette for accent as placeholders.
// Explicitly setting default hue to 40 for M3 compatibility.
$app-primary: mat.m2-define-palette(mat-palettes.$blue-palette, 40)
$app-accent: mat.m2-define-palette(mat-palettes.$rose-palette, 40)
$app-warn: mat.m2-define-palette(mat-palettes.$red-palette, 40)

// Create the theme object
$app-theme: mat.define-light-theme((
  color: (
    primary: $app-primary,
    accent: $app-accent,
    warn: $app-warn
  )
))

// Apply the theme to all Angular Material components
@include mat.all-component-themes($app-theme)

// Custom styles for Material components
.mat-toolbar
  &.app-toolbar
    @include mat.elevation(4)
    background: mat.get-color-from-palette($app-primary, 700)
    color: white

.mat-mdc-card
  @include mat.elevation(2)
  border-radius: 8px
  margin-bottom: 16px
  .mat-mdc-card-header
    padding: 16px 16px 0
  .mat-mdc-card-content
    padding: 16px
  .mat-mdc-card-actions
    padding: 8px
    display: flex
    justify-content: flex-end

.mat-form-field
  width: 100%
  margin-bottom: 16px

.mat-table
  width: 100%
  background: white
  @include mat.elevation(1)
  border-radius: 4px
  overflow: hidden

.mat-button,
.mat-raised-button,
.mat-stroked-button
  text-transform: uppercase
  font-weight: 500

// Utility classes
.elevation-z1
  @include mat.elevation(1)
.elevation-z2
  @include mat.elevation(2)
.elevation-z3
  @include mat.elevation(3)
.elevation-z4
  @include mat.elevation(4)

// Material Button styles
.mat-mdc-button, .mat-mdc-raised-button
  &.rounded
    border-radius: 24px
  &.full-width
    width: 100%

// Material Table styles
.mat-mdc-table
  width: 100%
  background: transparent
  .mat-mdc-header-cell
    font-weight: 600
    color: mat.get-color-from-palette($app-primary, 900)
  .mat-mdc-row
    &:hover
      background-color: mat.get-color-from-palette($app-primary, 50)

// Material Form Field styles
.mat-mdc-form-field
  width: 100%
  margin-bottom: 16px

// Material List styles
.mat-mdc-list
  .mat-mdc-list-item
    &:hover
      background-color: mat.get-color-from-palette($app-primary, 50)

// Material Dialog styles
.mat-mdc-dialog-container
  .mat-mdc-dialog-title
    color: mat.get-color-from-palette($app-primary, 900)
  .mat-mdc-dialog-content
    padding: 16px
  .mat-mdc-dialog-actions
    padding: 8px
    display: flex
    justify-content: flex-end
    gap: 8px

// Material Chip styles
.mat-mdc-chip
  &.status-chip
    &.active
      background-color: mat.get-color-from-palette(mat.$green-palette, 100)
      color: mat.get-color-from-palette(mat.$green-palette, 900)
    &.inactive
      background-color: mat.get-color-from-palette(mat.$grey-palette, 100)
      color: mat.get-color-from-palette(mat.$grey-palette, 900)
    &.warning
      background-color: mat.get-color-from-palette(mat.$orange-palette, 100)
      color: mat.get-color-from-palette(mat.$orange-palette, 900)
    &.error
      background-color: mat.get-color-from-palette($app-warn, 100)
      color: mat.get-color-from-palette($app-warn, 900)
