import './polyfills.server.mjs';
import {
  __spreadValues
} from "./chunk-5WKMABBB.mjs";

// src/app/modules/auth/auth.routes.ts
var routes = [
  __spreadValues({
    path: "login",
    loadComponent: () => import("./chunk-J7M3OMFD.mjs").then((m) => m.LoginComponent)
  }, true ? { \u0275entryName: "src/app/modules/auth/components/login/login.component.ts" } : {})
];
export {
  routes
};
//# sourceMappingURL=chunk-5SFP2AKX.mjs.map
