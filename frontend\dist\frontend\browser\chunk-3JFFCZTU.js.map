{"version": 3, "sources": ["src/app/shared/components/modals/shared-dependency-modal/shared-dependency-modal.component.ts", "src/app/modules/dependencies/dependencies.component.ts"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, OnChanges } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';\nimport { SlideModalComponent } from '../../slide-modal/slide-modal.component';\nimport { FormInputComponent } from '../../form-input/form-input.component';\nimport { ApplicationSelectionService, ApplicationOption } from '../../../services/application-selection.service';\nimport { Observable } from 'rxjs';\n\nexport interface SharedDependencyFormData {\n  applicationId?: number;\n  name: string;\n  type: string;\n  version: string;\n  description: string;\n  criticality: string;\n  status: string;\n  maintainer: string;\n  licenseType: string;\n  repository?: string;\n  documentation?: string;\n}\n\n@Component({\n  selector: 'app-shared-dependency-modal',\n  standalone: true,\n  imports: [CommonModule, ReactiveFormsModule, SlideModalComponent, FormInputComponent],\n  template: `\n    <app-slide-modal\n      [isOpen]=\"isOpen\"\n      [title]=\"editMode ? 'Edit Dependency' : 'Add Dependency'\"\n      [subtitle]=\"showApplicationSelection ? 'Configure dependency details and select application' : 'Configure dependency details'\"\n      [loading]=\"loading\"\n      [canConfirm]=\"dependencyForm.valid\"\n      confirmText=\"Save Dependency\"\n      (closed)=\"onClose()\"\n      (confirmed)=\"onSave()\"\n      (cancelled)=\"onClose()\"\n    >\n      <form [formGroup]=\"dependencyForm\" class=\"dependency-form\">\n        <!-- Application Selection (only when not in applications module) -->\n        <div class=\"form-group\" *ngIf=\"showApplicationSelection\">\n          <label class=\"form-label\">Application *</label>\n          <select formControlName=\"applicationId\" class=\"form-select\">\n            <option value=\"\">Select an application</option>\n            <option *ngFor=\"let app of applicationOptions$ | async\" [value]=\"app.id\">\n              {{ app.name }} - {{ app.department }}\n            </option>\n          </select>\n          <div class=\"field-error\" *ngIf=\"getFieldError('applicationId')\">\n            {{ getFieldError('applicationId') }}\n          </div>\n        </div>\n\n        <div class=\"form-grid\">\n          <app-form-input\n            label=\"Dependency Name\"\n            placeholder=\"e.g., Angular, Express.js\"\n            formControlName=\"name\"\n            [required]=\"true\"\n            [errorMessage]=\"getFieldError('name')\"\n          ></app-form-input>\n\n          <div class=\"form-group\">\n            <label class=\"form-label\">Type</label>\n            <select formControlName=\"type\" class=\"form-select\">\n              <option value=\"library\">Library</option>\n              <option value=\"framework\">Framework</option>\n              <option value=\"service\">Service</option>\n              <option value=\"database\">Database</option>\n              <option value=\"api\">API</option>\n              <option value=\"tool\">Tool</option>\n              <option value=\"infrastructure\">Infrastructure</option>\n            </select>\n          </div>\n\n          <app-form-input\n            label=\"Version\"\n            placeholder=\"e.g., 16.2.0, ^4.18.2\"\n            formControlName=\"version\"\n            [required]=\"true\"\n            [errorMessage]=\"getFieldError('version')\"\n          ></app-form-input>\n\n          <div class=\"form-group\">\n            <label class=\"form-label\">Criticality</label>\n            <select formControlName=\"criticality\" class=\"form-select\">\n              <option value=\"low\">Low</option>\n              <option value=\"medium\">Medium</option>\n              <option value=\"high\">High</option>\n              <option value=\"critical\">Critical</option>\n            </select>\n          </div>\n\n          <div class=\"form-group\">\n            <label class=\"form-label\">Status</label>\n            <select formControlName=\"status\" class=\"form-select\">\n              <option value=\"active\">Active</option>\n              <option value=\"deprecated\">Deprecated</option>\n              <option value=\"outdated\">Outdated</option>\n              <option value=\"vulnerable\">Vulnerable</option>\n            </select>\n          </div>\n\n          <app-form-input\n            label=\"Maintainer\"\n            placeholder=\"e.g., Google, Microsoft\"\n            formControlName=\"maintainer\"\n            [required]=\"true\"\n            [errorMessage]=\"getFieldError('maintainer')\"\n          ></app-form-input>\n\n          <div class=\"form-group\">\n            <label class=\"form-label\">License Type</label>\n            <select formControlName=\"licenseType\" class=\"form-select\">\n              <option value=\"MIT\">MIT</option>\n              <option value=\"Apache-2.0\">Apache 2.0</option>\n              <option value=\"GPL-3.0\">GPL 3.0</option>\n              <option value=\"BSD-3-Clause\">BSD 3-Clause</option>\n              <option value=\"ISC\">ISC</option>\n              <option value=\"MPL-2.0\">Mozilla Public License 2.0</option>\n              <option value=\"proprietary\">Proprietary</option>\n              <option value=\"other\">Other</option>\n            </select>\n          </div>\n\n          <app-form-input\n            label=\"Repository URL\"\n            placeholder=\"https://github.com/...\"\n            formControlName=\"repository\"\n            [errorMessage]=\"getFieldError('repository')\"\n          ></app-form-input>\n\n          <app-form-input\n            label=\"Documentation URL\"\n            placeholder=\"https://docs.example.com/...\"\n            formControlName=\"documentation\"\n            [errorMessage]=\"getFieldError('documentation')\"\n          ></app-form-input>\n\n          <div class=\"form-group full-width\">\n            <label class=\"form-label\">Description</label>\n            <textarea \n              formControlName=\"description\"\n              class=\"form-textarea\"\n              placeholder=\"Describe the purpose and usage of this dependency...\"\n              rows=\"3\"\n            ></textarea>\n          </div>\n        </div>\n      </form>\n    </app-slide-modal>\n  `,\n  styles: [`\n    .dependency-form {\n      display: flex;\n      flex-direction: column;\n      gap: var(--spacing-lg);\n    }\n\n    .form-grid {\n      display: grid;\n      grid-template-columns: 1fr 1fr;\n      gap: var(--spacing-lg);\n    }\n\n    .form-group {\n      display: flex;\n      flex-direction: column;\n      gap: var(--spacing-sm);\n    }\n\n    .form-group.full-width {\n      grid-column: 1 / -1;\n    }\n\n    .form-label {\n      font-size: var(--text-sm);\n      font-weight: 500;\n      color: var(--secondary-700);\n    }\n\n    .form-select,\n    .form-textarea {\n      height: 40px;\n      padding: 0 var(--spacing-md);\n      border: 1px solid var(--secondary-300);\n      border-radius: var(--radius-md);\n      background: white;\n      font-size: var(--text-sm);\n      color: var(--secondary-800);\n      transition: all 0.2s ease;\n\n      &:focus {\n        outline: none;\n        border-color: var(--primary-500);\n        box-shadow: 0 0 0 3px var(--primary-100);\n      }\n    }\n\n    .form-textarea {\n      height: auto;\n      padding: var(--spacing-sm) var(--spacing-md);\n      resize: vertical;\n      min-height: 80px;\n    }\n\n    .field-error {\n      font-size: var(--text-sm);\n      color: var(--error-600);\n    }\n\n    @media (max-width: 768px) {\n      .form-grid {\n        grid-template-columns: 1fr;\n        gap: var(--spacing-md);\n      }\n    }\n  `]\n})\nexport class SharedDependencyModalComponent implements OnInit, OnChanges {\n  @Input() isOpen = false;\n  @Input() editMode = false;\n  @Input() initialData: SharedDependencyFormData | null = null;\n  @Input() loading = false;\n  @Input() showApplicationSelection = true; // Hide when used within applications module\n\n  @Output() closed = new EventEmitter<void>();\n  @Output() saved = new EventEmitter<SharedDependencyFormData>();\n\n  dependencyForm!: FormGroup;\n  applicationOptions$: Observable<ApplicationOption[]>;\n\n  constructor(\n    private fb: FormBuilder,\n    private applicationSelectionService: ApplicationSelectionService\n  ) {\n    this.applicationOptions$ = this.applicationSelectionService.getApplicationOptions();\n  }\n\n  ngOnInit(): void {\n    this.initializeForm();\n  }\n\n  ngOnChanges(): void {\n    if (this.dependencyForm && this.initialData) {\n      this.dependencyForm.patchValue(this.initialData);\n    }\n  }\n\n  private initializeForm(): void {\n    const formConfig: any = {\n      name: ['', [Validators.required, Validators.minLength(2)]],\n      type: ['library', [Validators.required]],\n      version: ['', [Validators.required]],\n      description: [''],\n      criticality: ['medium', [Validators.required]],\n      status: ['active', [Validators.required]],\n      maintainer: ['', [Validators.required]],\n      licenseType: ['MIT', [Validators.required]],\n      repository: [''],\n      documentation: ['']\n    };\n\n    if (this.showApplicationSelection) {\n      formConfig.applicationId = ['', [Validators.required]];\n    }\n\n    this.dependencyForm = this.fb.group(formConfig);\n\n    if (this.initialData) {\n      this.dependencyForm.patchValue(this.initialData);\n    }\n  }\n\n  getFieldError(fieldName: string): string {\n    const field = this.dependencyForm.get(fieldName);\n    if (field && field.invalid && field.touched) {\n      if (field.errors?.['required']) {\n        return `${fieldName} is required`;\n      }\n      if (field.errors?.['minlength']) {\n        return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;\n      }\n      if (field.errors?.['url']) {\n        return `${fieldName} must be a valid URL`;\n      }\n    }\n    return '';\n  }\n\n  onSave(): void {\n    if (this.dependencyForm.valid) {\n      const formData: SharedDependencyFormData = this.dependencyForm.value;\n      this.saved.emit(formData);\n    }\n  }\n\n  onClose(): void {\n    this.closed.emit();\n  }\n}\n", "import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';\nimport { CardComponent } from '../../shared/components/card/card.component';\nimport { ButtonComponent } from '../../shared/components/button/button.component';\nimport { TableComponent, TableColumn, TableAction } from '../../shared/components/table/table.component';\n\nimport { ChartComponent } from '../../shared/components/chart/chart.component';\nimport { SharedDependencyModalComponent, SharedDependencyFormData } from '../../shared/components/modals/shared-dependency-modal/shared-dependency-modal.component';\n\ninterface Dependency {\n  id: number;\n  name: string;\n  type: string;\n  version: string;\n  criticality: string;\n  status: string;\n  isInternal: boolean;\n  lastUpdated: Date;\n  applications: number;\n  vulnerabilities: number;\n  description?: string;\n}\n\n@Component({\n  selector: 'app-dependencies',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    ReactiveFormsModule,\n    CardComponent,\n    ButtonComponent,\n    TableComponent,\n    ChartComponent,\n    SharedDependencyModalComponent\n  ],\n  template: `\n    <div class=\"dependencies-page\">\n      <div class=\"page-content\">\n        <div class=\"page-header\">\n          <div class=\"header-content\">\n            <h1>Dependencies</h1>\n            <p class=\"page-subtitle\">Manage and monitor application dependencies</p>\n          </div>\n          <div class=\"header-actions\">\n            <app-button\n              variant=\"primary\"\n              leftIcon=\"M12 4v16m8-8H4\"\n              (clicked)=\"openAddModal()\"\n            >\n              Add Dependency\n            </app-button>\n          </div>\n        </div>\n\n      <!-- Statistics Cards -->\n      <div class=\"stats-grid\">\n        <app-card title=\"Total Dependencies\">\n          <div class=\"stat-content\">\n            <div class=\"stat-number\">{{ stats.total }}</div>\n            <div class=\"stat-change positive\">+{{ stats.newThisMonth }} this month</div>\n          </div>\n        </app-card>\n\n        <app-card title=\"Critical Dependencies\">\n          <div class=\"stat-content\">\n            <div class=\"stat-number critical\">{{ stats.critical }}</div>\n            <div class=\"stat-change\">{{ stats.criticalPercentage }}% of total</div>\n          </div>\n        </app-card>\n\n        <app-card title=\"Outdated\">\n          <div class=\"stat-content\">\n            <div class=\"stat-number warning\">{{ stats.outdated }}</div>\n            <div class=\"stat-change\">Need updates</div>\n          </div>\n        </app-card>\n\n        <app-card title=\"Vulnerabilities\">\n          <div class=\"stat-content\">\n            <div class=\"stat-number error\">{{ stats.vulnerabilities }}</div>\n            <div class=\"stat-change\">Security issues</div>\n          </div>\n        </app-card>\n      </div>\n\n      <!-- Charts Section -->\n      <div class=\"charts-section\">\n        <app-card title=\"Dependencies by Type\" subtitle=\"Distribution of dependency types\">\n          <app-chart\n            type=\"doughnut\"\n            [data]=\"typeChartData\"\n            [options]=\"chartOptions\"\n            height=\"300px\"\n          ></app-chart>\n        </app-card>\n\n        <app-card title=\"Criticality Distribution\" subtitle=\"Dependencies by criticality level\">\n          <app-chart\n            type=\"bar\"\n            [data]=\"criticalityChartData\"\n            [options]=\"chartOptions\"\n            height=\"300px\"\n          ></app-chart>\n        </app-card>\n      </div>\n\n      <!-- Dependencies Table -->\n      <app-card title=\"Dependencies\" subtitle=\"All application dependencies\">\n        <div class=\"table-controls\">\n          <div class=\"search-controls\">\n            <input\n              type=\"text\"\n              placeholder=\"Search dependencies...\"\n              class=\"search-input\"\n              (input)=\"onSearchInput($event)\"\n            >\n            <select class=\"filter-select\" (change)=\"onFilterChanged('type', $event)\">\n              <option value=\"\">All Types</option>\n              <option value=\"library\">Library</option>\n              <option value=\"framework\">Framework</option>\n              <option value=\"service\">Service</option>\n              <option value=\"database\">Database</option>\n              <option value=\"api\">API</option>\n              <option value=\"tool\">Tool</option>\n              <option value=\"infrastructure\">Infrastructure</option>\n            </select>\n            <select class=\"filter-select\" (change)=\"onFilterChanged('criticality', $event)\">\n              <option value=\"\">All Criticality</option>\n              <option value=\"low\">Low</option>\n              <option value=\"medium\">Medium</option>\n              <option value=\"high\">High</option>\n              <option value=\"critical\">Critical</option>\n            </select>\n          </div>\n        </div>\n\n        <app-table\n          [columns]=\"tableColumns\"\n          [data]=\"filteredDependencies\"\n          [actions]=\"tableActions\"\n          [loading]=\"loading\"\n          [sortable]=\"true\"\n          (sortChanged)=\"onSortChanged($event)\"\n        ></app-table>\n      </app-card>\n\n      <!-- Add/Edit Modal -->\n      <app-shared-dependency-modal\n        [isOpen]=\"showModal\"\n        [editMode]=\"editMode\"\n        [initialData]=\"editData\"\n        [loading]=\"modalLoading\"\n        [showApplicationSelection]=\"true\"\n        (closed)=\"closeModal()\"\n        (saved)=\"onDependencySaved($event)\"\n      ></app-shared-dependency-modal>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .dependencies-page {\n      min-height: 100%;\n    }\n\n    .page-content {\n      padding: var(--spacing-xl);\n    }\n\n    .page-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: flex-start;\n      margin-bottom: var(--spacing-xl);\n    }\n\n    .header-content h1 {\n      margin: 0 0 var(--spacing-sm) 0;\n      font-size: var(--text-3xl);\n      font-weight: 700;\n      color: var(--secondary-900);\n    }\n\n    .page-subtitle {\n      margin: 0;\n      font-size: var(--text-lg);\n      color: var(--secondary-600);\n    }\n\n    .stats-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n      gap: var(--spacing-lg);\n      margin-bottom: var(--spacing-xl);\n    }\n\n    .stat-content {\n      display: flex;\n      flex-direction: column;\n      gap: var(--spacing-sm);\n      padding: var(--spacing-lg);\n    }\n\n    .stat-number {\n      font-size: var(--text-3xl);\n      font-weight: 700;\n      color: var(--secondary-900);\n\n      &.critical {\n        color: var(--error-600);\n      }\n\n      &.warning {\n        color: var(--warning-600);\n      }\n\n      &.error {\n        color: var(--error-600);\n      }\n    }\n\n    .stat-change {\n      font-size: var(--text-sm);\n      color: var(--secondary-600);\n\n      &.positive {\n        color: var(--success-600);\n      }\n    }\n\n    .charts-section {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n      gap: var(--spacing-lg);\n      margin-bottom: var(--spacing-xl);\n    }\n\n    .table-controls {\n      margin-bottom: var(--spacing-lg);\n    }\n\n    .search-controls {\n      display: flex;\n      gap: var(--spacing-md);\n      align-items: center;\n    }\n\n    .search-input,\n    .filter-select {\n      height: 40px;\n      padding: 0 var(--spacing-md);\n      border: 1px solid var(--secondary-300);\n      border-radius: var(--radius-md);\n      background: white;\n      font-size: var(--text-sm);\n      color: var(--secondary-800);\n      transition: all 0.2s ease;\n\n      &:focus {\n        outline: none;\n        border-color: var(--primary-500);\n        box-shadow: 0 0 0 3px var(--primary-100);\n      }\n    }\n\n    .search-input {\n      flex: 1;\n      min-width: 200px;\n    }\n\n    .filter-select {\n      min-width: 150px;\n      cursor: pointer;\n      background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e\");\n      background-position: right var(--spacing-sm) center;\n      background-repeat: no-repeat;\n      background-size: 16px;\n      padding-right: 40px;\n      appearance: none;\n    }\n\n    @media (max-width: 768px) {\n      .page-header {\n        flex-direction: column;\n        gap: var(--spacing-md);\n        align-items: stretch;\n      }\n\n      .stats-grid {\n        grid-template-columns: 1fr;\n        gap: var(--spacing-md);\n      }\n\n      .charts-section {\n        grid-template-columns: 1fr;\n        gap: var(--spacing-md);\n      }\n\n      .search-controls {\n        flex-direction: column;\n        align-items: stretch;\n      }\n\n      .search-input,\n      .filter-select {\n        min-width: auto;\n      }\n    }\n  `]\n})\nexport class DependenciesComponent implements OnInit {\n  dependencies: Dependency[] = [];\n  filteredDependencies: Dependency[] = [];\n  loading = false;\n\n  // Modal state\n  showModal = false;\n  editMode = false;\n  editData: SharedDependencyFormData | null = null;\n  modalLoading = false;\n\n  // Statistics\n  stats = {\n    total: 0,\n    critical: 0,\n    outdated: 0,\n    vulnerabilities: 0,\n    newThisMonth: 0,\n    criticalPercentage: 0\n  };\n\n  // Chart data\n  typeChartData: any = null;\n  criticalityChartData: any = null;\n  chartOptions = {\n    responsive: true,\n    maintainAspectRatio: false\n  };\n\n  // Table configuration\n  tableColumns: TableColumn[] = [\n    { key: 'name', label: 'Name', sortable: true },\n    { key: 'type', label: 'Type', type: 'badge', sortable: true },\n    { key: 'version', label: 'Version', sortable: true },\n    { key: 'criticality', label: 'Criticality', type: 'badge', sortable: true },\n    { key: 'status', label: 'Status', type: 'badge', sortable: true },\n    { key: 'applications', label: 'Apps', type: 'number', sortable: true },\n    { key: 'vulnerabilities', label: 'Vulnerabilities', type: 'number', sortable: true },\n    { key: 'lastUpdated', label: 'Last Updated', type: 'date', sortable: true }\n  ];\n\n  tableActions: TableAction[] = [\n    {\n      label: 'Edit',\n      icon: 'M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7',\n      variant: 'ghost',\n      action: (item) => this.editDependency(item)\n    },\n    {\n      label: 'Delete',\n      icon: 'M3 6h18m-2 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2',\n      variant: 'error',\n      action: (item) => this.deleteDependency(item)\n    }\n  ];\n\n\n\n  constructor(private fb: FormBuilder) {}\n\n  ngOnInit(): void {\n    this.loadDependencies();\n  }\n\n  private loadDependencies(): void {\n    this.loading = true;\n\n    // Mock data - replace with actual API call\n    setTimeout(() => {\n      this.dependencies = this.generateMockDependencies();\n      this.filteredDependencies = [...this.dependencies];\n      this.updateStats();\n      this.updateChartData();\n      this.loading = false;\n    }, 1000);\n  }\n\n  private generateMockDependencies(): Dependency[] {\n    const types = ['library', 'framework', 'service', 'database', 'api', 'tool', 'infrastructure'];\n    const criticalities = ['low', 'medium', 'high', 'critical'];\n    const statuses = ['active', 'deprecated', 'end_of_life', 'security_risk', 'update_required'];\n\n    return Array.from({ length: 50 }, (_, i) => ({\n      id: i + 1,\n      name: `Dependency ${i + 1}`,\n      type: types[Math.floor(Math.random() * types.length)],\n      version: `${Math.floor(Math.random() * 5) + 1}.${Math.floor(Math.random() * 10)}.${Math.floor(Math.random() * 10)}`,\n      criticality: criticalities[Math.floor(Math.random() * criticalities.length)],\n      status: statuses[Math.floor(Math.random() * statuses.length)],\n      isInternal: Math.random() > 0.7,\n      lastUpdated: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000),\n      applications: Math.floor(Math.random() * 20) + 1,\n      vulnerabilities: Math.floor(Math.random() * 5),\n      description: `Description for dependency ${i + 1}`\n    }));\n  }\n\n  private updateStats(): void {\n    this.stats.total = this.dependencies.length;\n    this.stats.critical = this.dependencies.filter(d => d.criticality === 'critical').length;\n    this.stats.outdated = this.dependencies.filter(d => d.status === 'update_required').length;\n    this.stats.vulnerabilities = this.dependencies.reduce((sum, d) => sum + d.vulnerabilities, 0);\n    this.stats.newThisMonth = this.dependencies.filter(d =>\n      new Date(d.lastUpdated).getMonth() === new Date().getMonth()\n    ).length;\n    this.stats.criticalPercentage = Math.round((this.stats.critical / this.stats.total) * 100);\n  }\n\n  private updateChartData(): void {\n    // Type distribution chart\n    const typeCount = this.dependencies.reduce((acc, dep) => {\n      acc[dep.type] = (acc[dep.type] || 0) + 1;\n      return acc;\n    }, {} as any);\n\n    this.typeChartData = {\n      labels: Object.keys(typeCount),\n      datasets: [{\n        data: Object.values(typeCount),\n        backgroundColor: [\n          '#0ea5e9', '#22c55e', '#f59e0b', '#ef4444', '#8b5cf6',\n          '#06b6d4', '#84cc16'\n        ]\n      }]\n    };\n\n    // Criticality distribution chart\n    const criticalityCount = this.dependencies.reduce((acc, dep) => {\n      acc[dep.criticality] = (acc[dep.criticality] || 0) + 1;\n      return acc;\n    }, {} as any);\n\n    this.criticalityChartData = {\n      labels: Object.keys(criticalityCount),\n      datasets: [{\n        label: 'Dependencies',\n        data: Object.values(criticalityCount),\n        backgroundColor: ['#22c55e', '#f59e0b', '#f97316', '#ef4444']\n      }]\n    };\n  }\n\n  openAddModal(): void {\n    this.editMode = false;\n    this.editData = null;\n    this.showModal = true;\n  }\n\n  editDependency(dependency: Dependency): void {\n    this.editMode = true;\n    this.editData = {\n      name: dependency.name,\n      type: dependency.type,\n      version: dependency.version,\n      description: dependency.description || '',\n      criticality: dependency.criticality,\n      status: dependency.status,\n      maintainer: 'Unknown', // Default value since not in original interface\n      licenseType: 'MIT' // Default value since not in original interface\n    };\n    this.showModal = true;\n  }\n\n  deleteDependency(dependency: Dependency): void {\n    if (confirm(`Are you sure you want to delete ${dependency.name}?`)) {\n      this.dependencies = this.dependencies.filter(d => d.id !== dependency.id);\n      this.filteredDependencies = this.filteredDependencies.filter(d => d.id !== dependency.id);\n      this.updateStats();\n      this.updateChartData();\n    }\n  }\n\n  closeModal(): void {\n    this.showModal = false;\n    this.editMode = false;\n    this.editData = null;\n  }\n\n  onDependencySaved(dependencyData: SharedDependencyFormData): void {\n    this.modalLoading = true;\n\n    setTimeout(() => {\n      console.log('Dependency saved:', dependencyData);\n\n      // In a real app, you would make an API call here and refresh the data\n      this.modalLoading = false;\n      this.closeModal();\n      this.loadDependencies(); // Refresh the list\n    }, 1000);\n  }\n\n  onSearchInput(event: Event): void {\n    const target = event.target as HTMLInputElement;\n    this.onSearchChanged(target.value);\n  }\n\n  onSearchChanged(searchTerm: string): void {\n    this.applyFilters();\n  }\n\n  onFilterChanged(filterType: string, event: Event): void {\n    const target = event.target as HTMLSelectElement;\n    // Apply filter logic here\n    this.applyFilters();\n  }\n\n  onFiltersChanged(filters: any): void {\n    this.applyFilters();\n  }\n\n  onSortChanged(sort: any): void {\n    // Implement sorting logic\n    console.log('Sort changed:', sort);\n  }\n\n  private applyFilters(): void {\n    // Implement filtering logic\n    this.filteredDependencies = [...this.dependencies];\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CY,IAAA,yBAAA,GAAA,UAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AAFwD,IAAA,qBAAA,SAAA,OAAA,EAAA;AACtD,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,MAAA,OAAA,OAAA,YAAA,GAAA;;;;;AAGJ,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,cAAA,eAAA,GAAA,GAAA;;;;;AATJ,IAAA,yBAAA,GAAA,OAAA,CAAA,EAAyD,GAAA,SAAA,CAAA;AAC7B,IAAA,iBAAA,GAAA,eAAA;AAAa,IAAA,uBAAA;AACvC,IAAA,yBAAA,GAAA,UAAA,EAAA,EAA4D,GAAA,UAAA,EAAA;AACzC,IAAA,iBAAA,GAAA,uBAAA;AAAqB,IAAA,uBAAA;AACtC,IAAA,qBAAA,GAAA,wDAAA,GAAA,GAAA,UAAA,EAAA;;AAGF,IAAA,uBAAA;AACA,IAAA,qBAAA,GAAA,qDAAA,GAAA,GAAA,OAAA,EAAA;AAGF,IAAA,uBAAA;;;;AAP4B,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,sBAAA,GAAA,GAAA,OAAA,mBAAA,CAAA;AAIA,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,cAAA,eAAA,CAAA;;;AA2K9B,IAAO,iCAAP,MAAO,gCAA8B;EAc/B;EACA;EAdD,SAAS;EACT,WAAW;EACX,cAA+C;EAC/C,UAAU;EACV,2BAA2B;;EAE1B,SAAS,IAAI,aAAY;EACzB,QAAQ,IAAI,aAAY;EAElC;EACA;EAEA,YACU,IACA,6BAAwD;AADxD,SAAA,KAAA;AACA,SAAA,8BAAA;AAER,SAAK,sBAAsB,KAAK,4BAA4B,sBAAqB;EACnF;EAEA,WAAQ;AACN,SAAK,eAAc;EACrB;EAEA,cAAW;AACT,QAAI,KAAK,kBAAkB,KAAK,aAAa;AAC3C,WAAK,eAAe,WAAW,KAAK,WAAW;IACjD;EACF;EAEQ,iBAAc;AACpB,UAAM,aAAkB;MACtB,MAAM,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,UAAU,CAAC,CAAC,CAAC;MACzD,MAAM,CAAC,WAAW,CAAC,WAAW,QAAQ,CAAC;MACvC,SAAS,CAAC,IAAI,CAAC,WAAW,QAAQ,CAAC;MACnC,aAAa,CAAC,EAAE;MAChB,aAAa,CAAC,UAAU,CAAC,WAAW,QAAQ,CAAC;MAC7C,QAAQ,CAAC,UAAU,CAAC,WAAW,QAAQ,CAAC;MACxC,YAAY,CAAC,IAAI,CAAC,WAAW,QAAQ,CAAC;MACtC,aAAa,CAAC,OAAO,CAAC,WAAW,QAAQ,CAAC;MAC1C,YAAY,CAAC,EAAE;MACf,eAAe,CAAC,EAAE;;AAGpB,QAAI,KAAK,0BAA0B;AACjC,iBAAW,gBAAgB,CAAC,IAAI,CAAC,WAAW,QAAQ,CAAC;IACvD;AAEA,SAAK,iBAAiB,KAAK,GAAG,MAAM,UAAU;AAE9C,QAAI,KAAK,aAAa;AACpB,WAAK,eAAe,WAAW,KAAK,WAAW;IACjD;EACF;EAEA,cAAc,WAAiB;AAC7B,UAAM,QAAQ,KAAK,eAAe,IAAI,SAAS;AAC/C,QAAI,SAAS,MAAM,WAAW,MAAM,SAAS;AAC3C,UAAI,MAAM,SAAS,UAAU,GAAG;AAC9B,eAAO,GAAG,SAAS;MACrB;AACA,UAAI,MAAM,SAAS,WAAW,GAAG;AAC/B,eAAO,GAAG,SAAS,qBAAqB,MAAM,OAAO,WAAW,EAAE,cAAc;MAClF;AACA,UAAI,MAAM,SAAS,KAAK,GAAG;AACzB,eAAO,GAAG,SAAS;MACrB;IACF;AACA,WAAO;EACT;EAEA,SAAM;AACJ,QAAI,KAAK,eAAe,OAAO;AAC7B,YAAM,WAAqC,KAAK,eAAe;AAC/D,WAAK,MAAM,KAAK,QAAQ;IAC1B;EACF;EAEA,UAAO;AACL,SAAK,OAAO,KAAI;EAClB;;qCAhFW,iCAA8B,4BAAA,WAAA,GAAA,4BAAA,2BAAA,CAAA;EAAA;yEAA9B,iCAA8B,WAAA,CAAA,CAAA,6BAAA,CAAA,GAAA,QAAA,EAAA,QAAA,UAAA,UAAA,YAAA,aAAA,eAAA,SAAA,WAAA,0BAAA,2BAAA,GAAA,SAAA,EAAA,QAAA,UAAA,OAAA,QAAA,GAAA,UAAA,CAAA,8BAAA,GAAA,OAAA,IAAA,MAAA,IAAA,QAAA,CAAA,CAAA,eAAA,mBAAA,GAAA,UAAA,aAAA,aAAA,UAAA,SAAA,YAAA,WAAA,YAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,WAAA,GAAA,CAAA,SAAA,cAAA,GAAA,MAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,SAAA,mBAAA,eAAA,6BAAA,mBAAA,QAAA,GAAA,YAAA,cAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,mBAAA,QAAA,GAAA,aAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,SAAA,WAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,SAAA,UAAA,GAAA,CAAA,SAAA,KAAA,GAAA,CAAA,SAAA,MAAA,GAAA,CAAA,SAAA,gBAAA,GAAA,CAAA,SAAA,WAAA,eAAA,yBAAA,mBAAA,WAAA,GAAA,YAAA,cAAA,GAAA,CAAA,mBAAA,eAAA,GAAA,aAAA,GAAA,CAAA,SAAA,KAAA,GAAA,CAAA,SAAA,QAAA,GAAA,CAAA,SAAA,MAAA,GAAA,CAAA,SAAA,UAAA,GAAA,CAAA,mBAAA,UAAA,GAAA,aAAA,GAAA,CAAA,SAAA,QAAA,GAAA,CAAA,SAAA,YAAA,GAAA,CAAA,SAAA,UAAA,GAAA,CAAA,SAAA,YAAA,GAAA,CAAA,SAAA,cAAA,eAAA,2BAAA,mBAAA,cAAA,GAAA,YAAA,cAAA,GAAA,CAAA,mBAAA,eAAA,GAAA,aAAA,GAAA,CAAA,SAAA,KAAA,GAAA,CAAA,SAAA,YAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,SAAA,cAAA,GAAA,CAAA,SAAA,KAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,SAAA,aAAA,GAAA,CAAA,SAAA,OAAA,GAAA,CAAA,SAAA,kBAAA,eAAA,0BAAA,mBAAA,cAAA,GAAA,cAAA,GAAA,CAAA,SAAA,qBAAA,eAAA,gCAAA,mBAAA,iBAAA,GAAA,cAAA,GAAA,CAAA,GAAA,cAAA,YAAA,GAAA,CAAA,mBAAA,eAAA,eAAA,wDAAA,QAAA,KAAA,GAAA,eAAA,GAAA,CAAA,mBAAA,iBAAA,GAAA,aAAA,GAAA,CAAA,SAAA,EAAA,GAAA,CAAA,GAAA,SAAA,GAAA,SAAA,SAAA,GAAA,CAAA,SAAA,eAAA,GAAA,MAAA,GAAA,CAAA,GAAA,OAAA,GAAA,CAAA,GAAA,aAAA,CAAA,GAAA,UAAA,SAAA,wCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAhMvC,MAAA,yBAAA,GAAA,mBAAA,CAAA;AAOE,MAAA,qBAAA,UAAA,SAAA,4EAAA;AAAA,eAAU,IAAA,QAAA;MAAS,CAAA,EAAC,aAAA,SAAA,+EAAA;AAAA,eACP,IAAA,OAAA;MAAQ,CAAA,EAAC,aAAA,SAAA,+EAAA;AAAA,eACT,IAAA,QAAA;MAAS,CAAA;AAEtB,MAAA,yBAAA,GAAA,QAAA,CAAA;AAEE,MAAA,qBAAA,GAAA,+CAAA,GAAA,GAAA,OAAA,CAAA;AAaA,MAAA,yBAAA,GAAA,OAAA,CAAA;AACE,MAAA,oBAAA,GAAA,kBAAA,CAAA;AAQA,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAwB,GAAA,SAAA,CAAA;AACI,MAAA,iBAAA,GAAA,MAAA;AAAI,MAAA,uBAAA;AAC9B,MAAA,yBAAA,GAAA,UAAA,CAAA,EAAmD,GAAA,UAAA,CAAA;AACzB,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA;AAC/B,MAAA,yBAAA,IAAA,UAAA,CAAA;AAA0B,MAAA,iBAAA,IAAA,WAAA;AAAS,MAAA,uBAAA;AACnC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAwB,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA;AAC/B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAyB,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA;AACjC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAoB,MAAA,iBAAA,IAAA,KAAA;AAAG,MAAA,uBAAA;AACvB,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAqB,MAAA,iBAAA,IAAA,MAAA;AAAI,MAAA,uBAAA;AACzB,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA+B,MAAA,iBAAA,IAAA,gBAAA;AAAc,MAAA,uBAAA,EAAS,EAC/C;AAGX,MAAA,oBAAA,IAAA,kBAAA,EAAA;AAQA,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,SAAA,CAAA;AACI,MAAA,iBAAA,IAAA,aAAA;AAAW,MAAA,uBAAA;AACrC,MAAA,yBAAA,IAAA,UAAA,EAAA,EAA0D,IAAA,UAAA,EAAA;AACpC,MAAA,iBAAA,IAAA,KAAA;AAAG,MAAA,uBAAA;AACvB,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAuB,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA;AAC7B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAqB,MAAA,iBAAA,IAAA,MAAA;AAAI,MAAA,uBAAA;AACzB,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAyB,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA,EAAS,EACnC;AAGX,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,SAAA,CAAA;AACI,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA;AAChC,MAAA,yBAAA,IAAA,UAAA,EAAA,EAAqD,IAAA,UAAA,EAAA;AAC5B,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA;AAC7B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA2B,MAAA,iBAAA,IAAA,YAAA;AAAU,MAAA,uBAAA;AACrC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAyB,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA;AACjC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA2B,MAAA,iBAAA,IAAA,YAAA;AAAU,MAAA,uBAAA,EAAS,EACvC;AAGX,MAAA,oBAAA,IAAA,kBAAA,EAAA;AAQA,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,SAAA,CAAA;AACI,MAAA,iBAAA,IAAA,cAAA;AAAY,MAAA,uBAAA;AACtC,MAAA,yBAAA,IAAA,UAAA,EAAA,EAA0D,IAAA,UAAA,EAAA;AACpC,MAAA,iBAAA,IAAA,KAAA;AAAG,MAAA,uBAAA;AACvB,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA2B,MAAA,iBAAA,IAAA,YAAA;AAAU,MAAA,uBAAA;AACrC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAwB,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA;AAC/B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA6B,MAAA,iBAAA,IAAA,cAAA;AAAY,MAAA,uBAAA;AACzC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAoB,MAAA,iBAAA,IAAA,KAAA;AAAG,MAAA,uBAAA;AACvB,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAwB,MAAA,iBAAA,IAAA,4BAAA;AAA0B,MAAA,uBAAA;AAClD,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA4B,MAAA,iBAAA,IAAA,aAAA;AAAW,MAAA,uBAAA;AACvC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAsB,MAAA,iBAAA,IAAA,OAAA;AAAK,MAAA,uBAAA,EAAS,EAC7B;AAGX,MAAA,oBAAA,IAAA,kBAAA,EAAA,EAKkB,IAAA,kBAAA,EAAA;AASlB,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAmC,IAAA,SAAA,CAAA;AACP,MAAA,iBAAA,IAAA,aAAA;AAAW,MAAA,uBAAA;AACrC,MAAA,oBAAA,IAAA,YAAA,EAAA;AAMF,MAAA,uBAAA,EAAM,EACF,EACD;;;AAzHP,MAAA,qBAAA,UAAA,IAAA,MAAA,EAAiB,SAAA,IAAA,WAAA,oBAAA,gBAAA,EACwC,YAAA,IAAA,2BAAA,wDAAA,8BAAA,EACqE,WAAA,IAAA,OAAA,EAC3G,cAAA,IAAA,eAAA,KAAA;AAOb,MAAA,oBAAA;AAAA,MAAA,qBAAA,aAAA,IAAA,cAAA;AAEqB,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,wBAAA;AAkBrB,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,YAAA,IAAA,EAAiB,gBAAA,IAAA,cAAA,MAAA,CAAA;AAqBjB,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,YAAA,IAAA,EAAiB,gBAAA,IAAA,cAAA,SAAA,CAAA;AA4BjB,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,YAAA,IAAA,EAAiB,gBAAA,IAAA,cAAA,YAAA,CAAA;AAsBjB,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,gBAAA,IAAA,cAAA,YAAA,CAAA;AAOA,MAAA,oBAAA;AAAA,MAAA,qBAAA,gBAAA,IAAA,cAAA,eAAA,CAAA;;oBA/GA,cAAY,SAAA,MAAA,WAAE,qBAAmB,oBAAA,gBAAA,8BAAA,sBAAA,4BAAA,iBAAA,sBAAA,mBAAA,oBAAA,iBAAE,qBAAqB,kBAAkB,GAAA,QAAA,CAAA,28CAAA,EAAA,CAAA;;;sEAkMzE,gCAA8B,CAAA;UArM1C;uBACW,+BAA6B,YAC3B,MAAI,SACP,CAAC,cAAc,qBAAqB,qBAAqB,kBAAkB,GAAC,UAC3E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA6HT,QAAA,CAAA,4+CAAA,EAAA,CAAA;8EAqEQ,QAAM,CAAA;UAAd;MACQ,UAAQ,CAAA;UAAhB;MACQ,aAAW,CAAA;UAAnB;MACQ,SAAO,CAAA;UAAf;MACQ,0BAAwB,CAAA;UAAhC;MAES,QAAM,CAAA;UAAf;MACS,OAAK,CAAA;UAAd;;;;6EARU,gCAA8B,EAAA,WAAA,kCAAA,UAAA,iGAAA,YAAA,IAAA,CAAA;AAAA,GAAA;;;AC6FrC,IAAO,wBAAP,MAAO,uBAAqB;EA0DZ;EAzDpB,eAA6B,CAAA;EAC7B,uBAAqC,CAAA;EACrC,UAAU;;EAGV,YAAY;EACZ,WAAW;EACX,WAA4C;EAC5C,eAAe;;EAGf,QAAQ;IACN,OAAO;IACP,UAAU;IACV,UAAU;IACV,iBAAiB;IACjB,cAAc;IACd,oBAAoB;;;EAItB,gBAAqB;EACrB,uBAA4B;EAC5B,eAAe;IACb,YAAY;IACZ,qBAAqB;;;EAIvB,eAA8B;IAC5B,EAAE,KAAK,QAAQ,OAAO,QAAQ,UAAU,KAAI;IAC5C,EAAE,KAAK,QAAQ,OAAO,QAAQ,MAAM,SAAS,UAAU,KAAI;IAC3D,EAAE,KAAK,WAAW,OAAO,WAAW,UAAU,KAAI;IAClD,EAAE,KAAK,eAAe,OAAO,eAAe,MAAM,SAAS,UAAU,KAAI;IACzE,EAAE,KAAK,UAAU,OAAO,UAAU,MAAM,SAAS,UAAU,KAAI;IAC/D,EAAE,KAAK,gBAAgB,OAAO,QAAQ,MAAM,UAAU,UAAU,KAAI;IACpE,EAAE,KAAK,mBAAmB,OAAO,mBAAmB,MAAM,UAAU,UAAU,KAAI;IAClF,EAAE,KAAK,eAAe,OAAO,gBAAgB,MAAM,QAAQ,UAAU,KAAI;;EAG3E,eAA8B;IAC5B;MACE,OAAO;MACP,MAAM;MACN,SAAS;MACT,QAAQ,CAAC,SAAS,KAAK,eAAe,IAAI;;IAE5C;MACE,OAAO;MACP,MAAM;MACN,SAAS;MACT,QAAQ,CAAC,SAAS,KAAK,iBAAiB,IAAI;;;EAMhD,YAAoB,IAAe;AAAf,SAAA,KAAA;EAAkB;EAEtC,WAAQ;AACN,SAAK,iBAAgB;EACvB;EAEQ,mBAAgB;AACtB,SAAK,UAAU;AAGf,eAAW,MAAK;AACd,WAAK,eAAe,KAAK,yBAAwB;AACjD,WAAK,uBAAuB,CAAC,GAAG,KAAK,YAAY;AACjD,WAAK,YAAW;AAChB,WAAK,gBAAe;AACpB,WAAK,UAAU;IACjB,GAAG,GAAI;EACT;EAEQ,2BAAwB;AAC9B,UAAM,QAAQ,CAAC,WAAW,aAAa,WAAW,YAAY,OAAO,QAAQ,gBAAgB;AAC7F,UAAM,gBAAgB,CAAC,OAAO,UAAU,QAAQ,UAAU;AAC1D,UAAM,WAAW,CAAC,UAAU,cAAc,eAAe,iBAAiB,iBAAiB;AAE3F,WAAO,MAAM,KAAK,EAAE,QAAQ,GAAE,GAAI,CAAC,GAAG,OAAO;MAC3C,IAAI,IAAI;MACR,MAAM,cAAc,IAAI,CAAC;MACzB,MAAM,MAAM,KAAK,MAAM,KAAK,OAAM,IAAK,MAAM,MAAM,CAAC;MACpD,SAAS,GAAG,KAAK,MAAM,KAAK,OAAM,IAAK,CAAC,IAAI,CAAC,IAAI,KAAK,MAAM,KAAK,OAAM,IAAK,EAAE,CAAC,IAAI,KAAK,MAAM,KAAK,OAAM,IAAK,EAAE,CAAC;MACjH,aAAa,cAAc,KAAK,MAAM,KAAK,OAAM,IAAK,cAAc,MAAM,CAAC;MAC3E,QAAQ,SAAS,KAAK,MAAM,KAAK,OAAM,IAAK,SAAS,MAAM,CAAC;MAC5D,YAAY,KAAK,OAAM,IAAK;MAC5B,aAAa,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,OAAM,IAAK,KAAK,KAAK,KAAK,KAAK,GAAI;MAC3E,cAAc,KAAK,MAAM,KAAK,OAAM,IAAK,EAAE,IAAI;MAC/C,iBAAiB,KAAK,MAAM,KAAK,OAAM,IAAK,CAAC;MAC7C,aAAa,8BAA8B,IAAI,CAAC;MAChD;EACJ;EAEQ,cAAW;AACjB,SAAK,MAAM,QAAQ,KAAK,aAAa;AACrC,SAAK,MAAM,WAAW,KAAK,aAAa,OAAO,OAAK,EAAE,gBAAgB,UAAU,EAAE;AAClF,SAAK,MAAM,WAAW,KAAK,aAAa,OAAO,OAAK,EAAE,WAAW,iBAAiB,EAAE;AACpF,SAAK,MAAM,kBAAkB,KAAK,aAAa,OAAO,CAAC,KAAK,MAAM,MAAM,EAAE,iBAAiB,CAAC;AAC5F,SAAK,MAAM,eAAe,KAAK,aAAa,OAAO,OACjD,IAAI,KAAK,EAAE,WAAW,EAAE,SAAQ,OAAO,oBAAI,KAAI,GAAG,SAAQ,CAAE,EAC5D;AACF,SAAK,MAAM,qBAAqB,KAAK,MAAO,KAAK,MAAM,WAAW,KAAK,MAAM,QAAS,GAAG;EAC3F;EAEQ,kBAAe;AAErB,UAAM,YAAY,KAAK,aAAa,OAAO,CAAC,KAAK,QAAO;AACtD,UAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK;AACvC,aAAO;IACT,GAAG,CAAA,CAAS;AAEZ,SAAK,gBAAgB;MACnB,QAAQ,OAAO,KAAK,SAAS;MAC7B,UAAU,CAAC;QACT,MAAM,OAAO,OAAO,SAAS;QAC7B,iBAAiB;UACf;UAAW;UAAW;UAAW;UAAW;UAC5C;UAAW;;OAEd;;AAIH,UAAM,mBAAmB,KAAK,aAAa,OAAO,CAAC,KAAK,QAAO;AAC7D,UAAI,IAAI,WAAW,KAAK,IAAI,IAAI,WAAW,KAAK,KAAK;AACrD,aAAO;IACT,GAAG,CAAA,CAAS;AAEZ,SAAK,uBAAuB;MAC1B,QAAQ,OAAO,KAAK,gBAAgB;MACpC,UAAU,CAAC;QACT,OAAO;QACP,MAAM,OAAO,OAAO,gBAAgB;QACpC,iBAAiB,CAAC,WAAW,WAAW,WAAW,SAAS;OAC7D;;EAEL;EAEA,eAAY;AACV,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,YAAY;EACnB;EAEA,eAAe,YAAsB;AACnC,SAAK,WAAW;AAChB,SAAK,WAAW;MACd,MAAM,WAAW;MACjB,MAAM,WAAW;MACjB,SAAS,WAAW;MACpB,aAAa,WAAW,eAAe;MACvC,aAAa,WAAW;MACxB,QAAQ,WAAW;MACnB,YAAY;;MACZ,aAAa;;;AAEf,SAAK,YAAY;EACnB;EAEA,iBAAiB,YAAsB;AACrC,QAAI,QAAQ,mCAAmC,WAAW,IAAI,GAAG,GAAG;AAClE,WAAK,eAAe,KAAK,aAAa,OAAO,OAAK,EAAE,OAAO,WAAW,EAAE;AACxE,WAAK,uBAAuB,KAAK,qBAAqB,OAAO,OAAK,EAAE,OAAO,WAAW,EAAE;AACxF,WAAK,YAAW;AAChB,WAAK,gBAAe;IACtB;EACF;EAEA,aAAU;AACR,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,WAAW;EAClB;EAEA,kBAAkB,gBAAwC;AACxD,SAAK,eAAe;AAEpB,eAAW,MAAK;AACd,cAAQ,IAAI,qBAAqB,cAAc;AAG/C,WAAK,eAAe;AACpB,WAAK,WAAU;AACf,WAAK,iBAAgB;IACvB,GAAG,GAAI;EACT;EAEA,cAAc,OAAY;AACxB,UAAM,SAAS,MAAM;AACrB,SAAK,gBAAgB,OAAO,KAAK;EACnC;EAEA,gBAAgB,YAAkB;AAChC,SAAK,aAAY;EACnB;EAEA,gBAAgB,YAAoB,OAAY;AAC9C,UAAM,SAAS,MAAM;AAErB,SAAK,aAAY;EACnB;EAEA,iBAAiB,SAAY;AAC3B,SAAK,aAAY;EACnB;EAEA,cAAc,MAAS;AAErB,YAAQ,IAAI,iBAAiB,IAAI;EACnC;EAEQ,eAAY;AAElB,SAAK,uBAAuB,CAAC,GAAG,KAAK,YAAY;EACnD;;qCA1NW,wBAAqB,4BAAA,WAAA,CAAA;EAAA;yEAArB,wBAAqB,WAAA,CAAA,CAAA,kBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,IAAA,QAAA,CAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,WAAA,WAAA,YAAA,kBAAA,GAAA,SAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,SAAA,oBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,eAAA,UAAA,GAAA,CAAA,SAAA,uBAAA,GAAA,CAAA,GAAA,eAAA,UAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,SAAA,UAAA,GAAA,CAAA,GAAA,eAAA,SAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,CAAA,GAAA,eAAA,OAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,SAAA,wBAAA,YAAA,kCAAA,GAAA,CAAA,QAAA,YAAA,UAAA,SAAA,GAAA,QAAA,SAAA,GAAA,CAAA,SAAA,4BAAA,YAAA,mCAAA,GAAA,CAAA,QAAA,OAAA,UAAA,SAAA,GAAA,QAAA,SAAA,GAAA,CAAA,SAAA,gBAAA,YAAA,8BAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,QAAA,QAAA,eAAA,0BAAA,GAAA,gBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,QAAA,GAAA,CAAA,SAAA,EAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,SAAA,WAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,SAAA,UAAA,GAAA,CAAA,SAAA,KAAA,GAAA,CAAA,SAAA,MAAA,GAAA,CAAA,SAAA,gBAAA,GAAA,CAAA,SAAA,KAAA,GAAA,CAAA,SAAA,QAAA,GAAA,CAAA,SAAA,MAAA,GAAA,CAAA,SAAA,UAAA,GAAA,CAAA,GAAA,eAAA,WAAA,QAAA,WAAA,WAAA,UAAA,GAAA,CAAA,GAAA,UAAA,SAAA,UAAA,YAAA,eAAA,WAAA,0BAAA,CAAA,GAAA,UAAA,SAAA,+BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAjR9B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA+B,GAAA,OAAA,CAAA,EACH,GAAA,OAAA,CAAA,EACC,GAAA,OAAA,CAAA,EACK,GAAA,IAAA;AACtB,MAAA,iBAAA,GAAA,cAAA;AAAY,MAAA,uBAAA;AAChB,MAAA,yBAAA,GAAA,KAAA,CAAA;AAAyB,MAAA,iBAAA,GAAA,6CAAA;AAA2C,MAAA,uBAAA,EAAI;AAE1E,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA4B,GAAA,cAAA,CAAA;AAIxB,MAAA,qBAAA,WAAA,SAAA,+DAAA;AAAA,eAAW,IAAA,aAAA;MAAc,CAAA;AAEzB,MAAA,iBAAA,IAAA,kBAAA;AACF,MAAA,uBAAA,EAAa,EACT;AAIV,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,YAAA,CAAA,EACe,IAAA,OAAA,CAAA,EACT,IAAA,OAAA,EAAA;AACC,MAAA,iBAAA,EAAA;AAAiB,MAAA,uBAAA;AAC1C,MAAA,yBAAA,IAAA,OAAA,EAAA;AAAkC,MAAA,iBAAA,EAAA;AAAoC,MAAA,uBAAA,EAAM,EACxE;AAGR,MAAA,yBAAA,IAAA,YAAA,EAAA,EAAwC,IAAA,OAAA,CAAA,EACZ,IAAA,OAAA,EAAA;AACU,MAAA,iBAAA,EAAA;AAAoB,MAAA,uBAAA;AACtD,MAAA,yBAAA,IAAA,OAAA,EAAA;AAAyB,MAAA,iBAAA,EAAA;AAAwC,MAAA,uBAAA,EAAM,EACnE;AAGR,MAAA,yBAAA,IAAA,YAAA,EAAA,EAA2B,IAAA,OAAA,CAAA,EACC,IAAA,OAAA,EAAA;AACS,MAAA,iBAAA,EAAA;AAAoB,MAAA,uBAAA;AACrD,MAAA,yBAAA,IAAA,OAAA,EAAA;AAAyB,MAAA,iBAAA,IAAA,cAAA;AAAY,MAAA,uBAAA,EAAM,EACvC;AAGR,MAAA,yBAAA,IAAA,YAAA,EAAA,EAAkC,IAAA,OAAA,CAAA,EACN,IAAA,OAAA,EAAA;AACO,MAAA,iBAAA,EAAA;AAA2B,MAAA,uBAAA;AAC1D,MAAA,yBAAA,IAAA,OAAA,EAAA;AAAyB,MAAA,iBAAA,IAAA,iBAAA;AAAe,MAAA,uBAAA,EAAM,EAC1C,EACG;AAIb,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA4B,IAAA,YAAA,EAAA;AAExB,MAAA,oBAAA,IAAA,aAAA,EAAA;AAMF,MAAA,uBAAA;AAEA,MAAA,yBAAA,IAAA,YAAA,EAAA;AACE,MAAA,oBAAA,IAAA,aAAA,EAAA;AAMF,MAAA,uBAAA,EAAW;AAIb,MAAA,yBAAA,IAAA,YAAA,EAAA,EAAuE,IAAA,OAAA,EAAA,EACzC,IAAA,OAAA,EAAA,EACG,IAAA,SAAA,EAAA;AAKzB,MAAA,qBAAA,SAAA,SAAA,uDAAA,QAAA;AAAA,eAAS,IAAA,cAAA,MAAA;MAAqB,CAAA;AAJhC,MAAA,uBAAA;AAMA,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA8B,MAAA,qBAAA,UAAA,SAAA,yDAAA,QAAA;AAAA,eAAU,IAAA,gBAAgB,QAAM,MAAA;MAAS,CAAA;AACrE,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAiB,MAAA,iBAAA,IAAA,WAAA;AAAS,MAAA,uBAAA;AAC1B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAwB,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA;AAC/B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA0B,MAAA,iBAAA,IAAA,WAAA;AAAS,MAAA,uBAAA;AACnC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAwB,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA;AAC/B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAyB,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA;AACjC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAoB,MAAA,iBAAA,IAAA,KAAA;AAAG,MAAA,uBAAA;AACvB,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAqB,MAAA,iBAAA,IAAA,MAAA;AAAI,MAAA,uBAAA;AACzB,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA+B,MAAA,iBAAA,IAAA,gBAAA;AAAc,MAAA,uBAAA,EAAS;AAExD,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA8B,MAAA,qBAAA,UAAA,SAAA,yDAAA,QAAA;AAAA,eAAU,IAAA,gBAAgB,eAAa,MAAA;MAAS,CAAA;AAC5E,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAiB,MAAA,iBAAA,IAAA,iBAAA;AAAe,MAAA,uBAAA;AAChC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAoB,MAAA,iBAAA,IAAA,KAAA;AAAG,MAAA,uBAAA;AACvB,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAuB,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA;AAC7B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAqB,MAAA,iBAAA,IAAA,MAAA;AAAI,MAAA,uBAAA;AACzB,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAyB,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA,EAAS,EACnC,EACL;AAGR,MAAA,yBAAA,IAAA,aAAA,EAAA;AAME,MAAA,qBAAA,eAAA,SAAA,iEAAA,QAAA;AAAA,eAAe,IAAA,cAAA,MAAA;MAAqB,CAAA;AACrC,MAAA,uBAAA,EAAY;AAIf,MAAA,yBAAA,IAAA,+BAAA,EAAA;AAME,MAAA,qBAAA,UAAA,SAAA,gFAAA;AAAA,eAAU,IAAA,WAAA;MAAY,CAAA,EAAC,SAAA,SAAA,6EAAA,QAAA;AAAA,eACd,IAAA,kBAAA,MAAA;MAAyB,CAAA;AACnC,MAAA,uBAAA,EAA8B,EACzB;;;AAlGyB,MAAA,oBAAA,EAAA;AAAA,MAAA,4BAAA,IAAA,MAAA,KAAA;AACS,MAAA,oBAAA,CAAA;AAAA,MAAA,6BAAA,KAAA,IAAA,MAAA,cAAA,aAAA;AAMA,MAAA,oBAAA,CAAA;AAAA,MAAA,4BAAA,IAAA,MAAA,QAAA;AACT,MAAA,oBAAA,CAAA;AAAA,MAAA,6BAAA,IAAA,IAAA,MAAA,oBAAA,YAAA;AAMQ,MAAA,oBAAA,CAAA;AAAA,MAAA,4BAAA,IAAA,MAAA,QAAA;AAOF,MAAA,oBAAA,CAAA;AAAA,MAAA,4BAAA,IAAA,MAAA,eAAA;AAW/B,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,aAAA,EAAsB,WAAA,IAAA,YAAA;AAStB,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,oBAAA,EAA6B,WAAA,IAAA,YAAA;AAsC/B,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,WAAA,IAAA,YAAA,EAAwB,QAAA,IAAA,oBAAA,EACK,WAAA,IAAA,YAAA,EACL,WAAA,IAAA,OAAA,EACL,YAAA,IAAA;AAQrB,MAAA,oBAAA;AAAA,MAAA,qBAAA,UAAA,IAAA,SAAA,EAAoB,YAAA,IAAA,QAAA,EACC,eAAA,IAAA,QAAA,EACG,WAAA,IAAA,YAAA,EACA,4BAAA,IAAA;;;IA7H5B;IACA;IACA;IAAmB;IAAA;IACnB;IACA;IACA;IACA;IACA;EAA8B,GAAA,QAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uDAAA,EAAA,CAAA;;;sEAoRrB,uBAAqB,CAAA;UA/RjC;uBACW,oBAAkB,YAChB,MAAI,SACP;MACP;MACA;MACA;MACA;MACA;MACA;MACA;MACA;OACD,UACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA2HT,QAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAAA,EAAA,CAAA;;;;6EAuJU,uBAAqB,EAAA,WAAA,yBAAA,UAAA,0DAAA,YAAA,IAAA,CAAA;AAAA,GAAA;", "names": []}