{"version": 3, "sources": ["src/app/modules/auth/auth.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\r\n\r\nexport const routes: Routes = [\r\n  {\r\n    path: 'login',\r\n    loadComponent: () => import('./components/login/login.component').then(m => m.LoginComponent)\r\n  }\r\n];\r\n"], "mappings": ";;;;;;AAEO,IAAM,SAAiB;EAC5B;IACE,MAAM;IACN,eAAe,MAAM,OAAO,sBAAoC,EAAE,KAAK,OAAK,EAAE,cAAc;;;", "names": []}