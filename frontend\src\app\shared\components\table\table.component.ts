import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ButtonComponent } from '../button/button.component';

export interface TableColumn {
  key: string;
  label: string;
  sortable?: boolean;
  width?: string;
  type?: 'text' | 'number' | 'date' | 'badge' | 'boolean' | 'actions';
  format?: (value: any) => string;
  badgeConfig?: {
    [key: string]: { color: string; label?: string };
  };
}

export interface TableAction {
  label: string;
  icon?: string;
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'ghost' | 'outline';
  action: (item: any) => void;
  visible?: (item: any) => boolean;
}

export interface SortConfig {
  column: string;
  direction: 'asc' | 'desc';
}

@Component({
  selector: 'app-table',
  standalone: true,
  imports: [CommonModule, FormsModule, ButtonComponent],
  template: `
    <div class="table-container">
      <!-- Table Header -->
      <div class="table-header" *ngIf="showHeader">
        <div class="table-title-section">
          <h3 class="table-title" *ngIf="title">{{ title }}</h3>
          <p class="table-subtitle" *ngIf="subtitle">{{ subtitle }}</p>
        </div>

        <div class="table-actions">
          <!-- Search -->
          <div class="search-container" *ngIf="searchable">
            <svg class="search-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="11" cy="11" r="8"></circle>
              <path d="m21 21-4.35-4.35"></path>
            </svg>
            <input
              type="text"
              class="search-input"
              placeholder="Search..."
              [(ngModel)]="searchTerm"
              (input)="onSearch()"
            >
          </div>

          <!-- Custom Actions -->
          <ng-content select="[slot=actions]"></ng-content>
        </div>
      </div>

      <!-- Table -->
      <div class="table-wrapper">
        <table class="table" [class.table-striped]="striped" [class.table-hover]="hoverable">
          <!-- Table Head -->
          <thead class="table-head">
            <tr>
              <th
                *ngFor="let column of columns"
                class="table-header-cell"
                [class.sortable]="column.sortable"
                [style.width]="column.width"
                (click)="onSort(column)"
              >
                <div class="header-content">
                  <span>{{ column.label }}</span>
                  <div class="sort-indicator" *ngIf="column.sortable">
                    <svg
                      class="sort-icon"
                      [class.sort-active]="sortConfig?.column === column.key"
                      [class.sort-desc]="sortConfig?.column === column.key && sortConfig?.direction === 'desc'"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                    >
                      <polyline points="6,9 12,15 18,9"></polyline>
                    </svg>
                  </div>
                </div>
              </th>
            </tr>
          </thead>

          <!-- Table Body -->
          <tbody class="table-body">
            <!-- Loading State -->
            <tr *ngIf="loading" class="loading-row">
              <td [attr.colspan]="columns.length" class="loading-cell">
                <div class="loading-content">
                  <div class="spinner"></div>
                  <span>Loading...</span>
                </div>
              </td>
            </tr>

            <!-- Empty State -->
            <tr *ngIf="!loading && (!data || data.length === 0)" class="empty-row">
              <td [attr.colspan]="columns.length" class="empty-cell">
                <div class="empty-content">
                  <svg class="empty-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <circle cx="11" cy="11" r="8"></circle>
                    <path d="m21 21-4.35-4.35"></path>
                  </svg>
                  <h4>{{ emptyMessage || 'No data available' }}</h4>
                  <p>{{ emptyDescription || 'There are no items to display.' }}</p>
                </div>
              </td>
            </tr>

            <!-- Data Rows -->
            <tr
              *ngFor="let item of paginatedData; let i = index; trackBy: trackByFn"
              class="table-row"
              [class.selected]="isSelected(item)"
              (click)="onRowClick(item)"
            >
              <td
                *ngFor="let column of columns"
                class="table-cell"
                [class]="'cell-' + column.type"
              >
                <!-- Text Content -->
                <span *ngIf="column.type === 'text' || !column.type" class="cell-text">
                  {{ column.format ? column.format(getColumnValue(item, column.key)) : getColumnValue(item, column.key) }}
                </span>

                <!-- Number Content -->
                <span *ngIf="column.type === 'number'" class="cell-number">
                  {{ column.format ? column.format(getColumnValue(item, column.key)) : (getColumnValue(item, column.key) | number) }}
                </span>

                <!-- Date Content -->
                <span *ngIf="column.type === 'date'" class="cell-date">
                  {{ column.format ? column.format(getColumnValue(item, column.key)) : (getColumnValue(item, column.key) | date:'medium') }}
                </span>

                <!-- Badge Content -->
                <span
                  *ngIf="column.type === 'badge'"
                  class="cell-badge"
                  [class]="'badge-' + getBadgeClass(getColumnValue(item, column.key), column.badgeConfig)"
                >
                  {{ getBadgeLabel(getColumnValue(item, column.key), column.badgeConfig) }}
                </span>

                <!-- Boolean Content -->
                <span *ngIf="column.type === 'boolean'" class="cell-boolean">
                  <span
                    class="boolean-indicator"
                    [class.boolean-true]="getColumnValue(item, column.key)"
                    [class.boolean-false]="!getColumnValue(item, column.key)"
                  >
                    {{ getColumnValue(item, column.key) ? 'Active' : 'Inactive' }}
                  </span>
                </span>

                <!-- Actions Content -->
                <div *ngIf="column.type === 'actions'" class="cell-actions">
                  <ng-container *ngFor="let action of getVisibleActions(item)">
                    <app-button
                      [variant]="action.variant || 'ghost'"
                      size="sm"
                      [leftIcon]="action.icon || ''"
                      (clicked)="action.action(item)"
                    >
                      {{ action.label }}
                    </app-button>
                  </ng-container>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Table Footer -->
      <div class="table-footer" *ngIf="showFooter && !loading">
        <div class="footer-info">
          <span class="item-count">
            Showing {{ startIndex + 1 }}-{{ endIndex }} of {{ filteredData.length }} items
          </span>
        </div>

        <!-- Pagination -->
        <div class="pagination" *ngIf="paginated && totalPages > 1">
          <app-button
            variant="outline"
            size="sm"
            leftIcon="M15 19l-7-7 7-7"
            [disabled]="currentPage === 1"
            (clicked)="goToPage(currentPage - 1)"
          >
            Previous
          </app-button>

          <div class="page-numbers">
            <button
              *ngFor="let page of getPageNumbers()"
              class="page-button"
              [class.active]="page === currentPage"
              [disabled]="page === '...'"
              (click)="goToPage(page)"
            >
              {{ page }}
            </button>
          </div>

          <app-button
            variant="outline"
            size="sm"
            rightIcon="M9 5l7 7-7 7"
            [disabled]="currentPage === totalPages"
            (clicked)="goToPage(currentPage + 1)"
          >
            Next
          </app-button>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['./table.component.scss']
})
export class TableComponent implements OnInit {
  @Input() data: any[] = [];
  @Input() columns: TableColumn[] = [];
  @Input() actions: TableAction[] = [];
  @Input() title = '';
  @Input() subtitle = '';
  @Input() loading = false;
  @Input() searchable = true;
  @Input() sortable = true;
  @Input() paginated = true;
  @Input() pageSize = 10;
  @Input() striped = true;
  @Input() hoverable = true;
  @Input() selectable = false;
  @Input() showHeader = true;
  @Input() showFooter = true;
  @Input() emptyMessage = '';
  @Input() emptyDescription = '';

  @Output() rowClick = new EventEmitter<any>();
  @Output() sortChange = new EventEmitter<SortConfig>();
  @Output() searchChange = new EventEmitter<string>();
  @Output() pageChange = new EventEmitter<number>();

  searchTerm = '';
  sortConfig: SortConfig | null = null;
  currentPage = 1;
  selectedItems: any[] = [];

  filteredData: any[] = [];
  paginatedData: any[] = [];

  ngOnInit(): void {
    this.updateData();
  }

  ngOnChanges(): void {
    this.updateData();
  }

  private updateData(): void {
    this.filterData();
    this.sortData();
    this.paginateData();
  }

  private filterData(): void {
    if (!this.searchTerm.trim()) {
      this.filteredData = [...this.data];
      return;
    }

    const term = this.searchTerm.toLowerCase();
    this.filteredData = this.data.filter(item => {
      return this.columns.some(column => {
        const value = this.getColumnValue(item, column.key);
        return value && value.toString().toLowerCase().includes(term);
      });
    });

    this.currentPage = 1; // Reset to first page when filtering
  }

  private sortData(): void {
    if (!this.sortConfig) return;

    this.filteredData.sort((a, b) => {
      const aValue = this.getColumnValue(a, this.sortConfig!.column);
      const bValue = this.getColumnValue(b, this.sortConfig!.column);

      let comparison = 0;
      if (aValue < bValue) comparison = -1;
      if (aValue > bValue) comparison = 1;

      return this.sortConfig!.direction === 'desc' ? -comparison : comparison;
    });
  }

  private paginateData(): void {
    if (!this.paginated) {
      this.paginatedData = this.filteredData;
      return;
    }

    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.paginatedData = this.filteredData.slice(startIndex, endIndex);
  }

  onSearch(): void {
    this.updateData();
    this.searchChange.emit(this.searchTerm);
  }

  onSort(column: TableColumn): void {
    if (!column.sortable) return;

    if (this.sortConfig?.column === column.key) {
      this.sortConfig.direction = this.sortConfig.direction === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortConfig = { column: column.key, direction: 'asc' };
    }

    this.updateData();
    this.sortChange.emit(this.sortConfig);
  }

  onRowClick(item: any): void {
    if (this.selectable) {
      this.toggleSelection(item);
    }
    this.rowClick.emit(item);
  }

  goToPage(page: number | string): void {
    if (typeof page === 'string') return;

    this.currentPage = Math.max(1, Math.min(page, this.totalPages));
    this.paginateData();
    this.pageChange.emit(this.currentPage);
  }

  getColumnValue(item: any, key: string): any {
    return key.split('.').reduce((obj, prop) => obj?.[prop], item);
  }

  getBadgeClass(value: any, config?: any): string {
    return config?.[value]?.color || 'default';
  }

  getBadgeLabel(value: any, config?: any): string {
    return config?.[value]?.label || value;
  }

  getVisibleActions(item: any): TableAction[] {
    return this.actions.filter(action => !action.visible || action.visible(item));
  }

  isSelected(item: any): boolean {
    return this.selectedItems.includes(item);
  }

  toggleSelection(item: any): void {
    const index = this.selectedItems.indexOf(item);
    if (index > -1) {
      this.selectedItems.splice(index, 1);
    } else {
      this.selectedItems.push(item);
    }
  }

  getPageNumbers(): (number | string)[] {
    const pages: (number | string)[] = [];
    const total = this.totalPages;
    const current = this.currentPage;

    if (total <= 7) {
      for (let i = 1; i <= total; i++) {
        pages.push(i);
      }
    } else {
      pages.push(1);

      if (current > 4) {
        pages.push('...');
      }

      const start = Math.max(2, current - 1);
      const end = Math.min(total - 1, current + 1);

      for (let i = start; i <= end; i++) {
        pages.push(i);
      }

      if (current < total - 3) {
        pages.push('...');
      }

      pages.push(total);
    }

    return pages;
  }

  trackByFn(index: number, item: any): any {
    return item.id || index;
  }

  get totalPages(): number {
    return Math.ceil(this.filteredData.length / this.pageSize);
  }

  get startIndex(): number {
    return (this.currentPage - 1) * this.pageSize;
  }

  get endIndex(): number {
    return Math.min(this.startIndex + this.pageSize, this.filteredData.length);
  }
}
