{"version": 3, "sources": ["src/app/modules/reports/reports.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { CardComponent } from '../../shared/components/card/card.component';\n\n@Component({\n  selector: 'app-reports',\n  standalone: true,\n  imports: [CommonModule, CardComponent],\n  template: `\n    <div class=\"reports-page\">\n      <h1>Reports</h1>\n      <app-card title=\"Reports & Analytics\" subtitle=\"Generate and view system reports\">\n        <div class=\"placeholder\">\n          <p>Reports module coming soon...</p>\n        </div>\n      </app-card>\n    </div>\n  `,\n  styles: [`\n    .reports-page {\n      min-height: 100%;\n    }\n    .placeholder {\n      padding: var(--spacing-xl);\n      text-align: center;\n      color: var(--secondary-600);\n    }\n  `]\n})\nexport class ReportsComponent {}\n"], "mappings": ";;;;;;;;;;;;;;;;AA6BM,IAAO,mBAAP,MAAO,kBAAgB;;qCAAhB,mBAAgB;EAAA;yEAAhB,mBAAgB,WAAA,CAAA,CAAA,aAAA,CAAA,GAAA,OAAA,GAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,cAAA,GAAA,CAAA,SAAA,uBAAA,YAAA,kCAAA,GAAA,CAAA,GAAA,aAAA,CAAA,GAAA,UAAA,SAAA,0BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AApBzB,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA0B,GAAA,IAAA;AACpB,MAAA,iBAAA,GAAA,SAAA;AAAO,MAAA,uBAAA;AACX,MAAA,yBAAA,GAAA,YAAA,CAAA,EAAkF,GAAA,OAAA,CAAA,EACvD,GAAA,GAAA;AACpB,MAAA,iBAAA,GAAA,+BAAA;AAA6B,MAAA,uBAAA,EAAI,EAChC,EACG;;oBARL,cAAc,aAAa,GAAA,QAAA,CAAA,+OAAA,EAAA,CAAA;;;sEAsB1B,kBAAgB,CAAA;UAzB5B;uBACW,eAAa,YACX,MAAI,SACP,CAAC,cAAc,aAAa,GAAC,UAC5B;;;;;;;;;KAST,QAAA,CAAA,+ZAAA,EAAA,CAAA;;;;6EAYU,kBAAgB,EAAA,WAAA,oBAAA,UAAA,gDAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}