import './polyfills.server.mjs';
import {
  StakeholderModalComponent
} from "./chunk-JCRQO2VH.mjs";
import {
  FormInputComponent
} from "./chunk-ZMVY5VP4.mjs";
import {
  SlideModalComponent
} from "./chunk-V4LU676I.mjs";
import {
  CheckboxControlValueAccessor,
  DefaultValueAccessor,
  FormBuilder,
  FormControlName,
  FormGroupDirective,
  FormsModule,
  MaxLengthValidator,
  NgControlStatus,
  NgControlStatusGroup,
  NgSelectOption,
  ReactiveFormsModule,
  RequiredValidator,
  SelectControlValueAccessor,
  Validators,
  ɵNgNoValidate,
  ɵNgSelectMultipleOption
} from "./chunk-22WSPHJT.mjs";
import {
  ButtonComponent
} from "./chunk-NVGHB2VF.mjs";
import {
  ActivatedRoute,
  RouterLink,
  RouterModule
} from "./chunk-MUYKQ5QZ.mjs";
import {
  CommonModule,
  Component,
  ContentChildren,
  EventEmitter,
  Input,
  NgForOf,
  NgIf,
  Output,
  ViewChild,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵNgOnChangesFeature,
  ɵɵadvance,
  ɵɵattribute,
  ɵɵclassMap,
  ɵɵclassMapInterpolate1,
  ɵɵclassProp,
  ɵɵcontentQuery,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵloadQuery,
  ɵɵnamespaceHTML,
  ɵɵnamespaceSVG,
  ɵɵnextContext,
  ɵɵprojection,
  ɵɵprojectionDef,
  ɵɵproperty,
  ɵɵqueryRefresh,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵsanitizeUrl,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵviewQuery
} from "./chunk-IPMSWJNG.mjs";
import {
  __spreadProps,
  __spreadValues
} from "./chunk-5WKMABBB.mjs";

// src/app/shared/components/tabs/tabs.component.ts
var _c0 = ["*"];
function TabsComponent_button_2__svg_svg_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(0, "svg", 9);
    \u0275\u0275element(1, "path", 10);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const tab_r2 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275attribute("d", tab_r2.icon);
  }
}
function TabsComponent_button_2_span_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 11);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const tab_r2 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275classProp("error-badge", tab_r2.hasError);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", tab_r2.badge, " ");
  }
}
function TabsComponent_button_2__svg_svg_5_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(0, "svg", 12);
    \u0275\u0275element(1, "circle", 13)(2, "line", 14)(3, "line", 15);
    \u0275\u0275elementEnd();
  }
}
function TabsComponent_button_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 4);
    \u0275\u0275listener("click", function TabsComponent_button_2_Template_button_click_0_listener() {
      const tab_r2 = \u0275\u0275restoreView(_r1).$implicit;
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.selectTab(tab_r2.id));
    });
    \u0275\u0275template(1, TabsComponent_button_2__svg_svg_1_Template, 2, 1, "svg", 5);
    \u0275\u0275elementStart(2, "span", 6);
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275template(4, TabsComponent_button_2_span_4_Template, 2, 3, "span", 7)(5, TabsComponent_button_2__svg_svg_5_Template, 4, 0, "svg", 8);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const tab_r2 = ctx.$implicit;
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275classProp("active", tab_r2.id === ctx_r2.activeTabId)("disabled", tab_r2.disabled)("has-error", tab_r2.hasError);
    \u0275\u0275property("disabled", tab_r2.disabled);
    \u0275\u0275attribute("aria-selected", tab_r2.id === ctx_r2.activeTabId)("aria-controls", "tab-panel-" + tab_r2.id);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", tab_r2.icon);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(tab_r2.label);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", tab_r2.badge);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", tab_r2.hasError && !tab_r2.badge);
  }
}
var TabComponent = class _TabComponent {
  id = "";
  label = "";
  icon = "";
  disabled = false;
  badge = "";
  hasError = false;
  active = false;
  static \u0275fac = function TabComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _TabComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _TabComponent, selectors: [["app-tab"]], inputs: { id: "id", label: "label", icon: "icon", disabled: "disabled", badge: "badge", hasError: "hasError", active: "active" }, ngContentSelectors: _c0, decls: 2, vars: 2, consts: [[1, "tab-content"]], template: function TabComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275projectionDef();
      \u0275\u0275elementStart(0, "div", 0);
      \u0275\u0275projection(1);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275classProp("active", ctx.active);
    }
  }, dependencies: [CommonModule], styles: ["\n\n.tab-content[_ngcontent-%COMP%] {\n  display: none;\n}\n.tab-content.active[_ngcontent-%COMP%] {\n  display: block;\n}\n/*# sourceMappingURL=tabs.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TabComponent, [{
    type: Component,
    args: [{ selector: "app-tab", standalone: true, imports: [CommonModule], template: `
    <div class="tab-content" [class.active]="active">
      <ng-content></ng-content>
    </div>
  `, styles: ["/* angular:styles/component:scss;becd9c23e661a7d07891aaf268d2045960e8f6c2127c8f5e8c40589a42b05d36;C:/Users/<USER>/Documents/VibeCoding/AppCatalog/app-catalog/frontend/src/app/shared/components/tabs/tabs.component.ts */\n.tab-content {\n  display: none;\n}\n.tab-content.active {\n  display: block;\n}\n/*# sourceMappingURL=tabs.component.css.map */\n"] }]
  }], null, { id: [{
    type: Input
  }], label: [{
    type: Input
  }], icon: [{
    type: Input
  }], disabled: [{
    type: Input
  }], badge: [{
    type: Input
  }], hasError: [{
    type: Input
  }], active: [{
    type: Input
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(TabComponent, { className: "TabComponent", filePath: "src/app/shared/components/tabs/tabs.component.ts", lineNumber: 32 });
})();
var TabsComponent = class _TabsComponent {
  orientation = "horizontal";
  size = "md";
  variant = "default";
  activeTabId = "";
  tabs = [];
  tabChange = new EventEmitter();
  tabComponents;
  ngAfterContentInit() {
    if (!this.activeTabId && this.tabs.length > 0) {
      const firstEnabledTab = this.tabs.find((tab) => !tab.disabled);
      if (firstEnabledTab) {
        this.activeTabId = firstEnabledTab.id;
      }
    }
    this.updateTabComponents();
  }
  ngOnChanges() {
    if (this.tabComponents) {
      this.updateTabComponents();
    }
  }
  get containerClasses() {
    const classes = [
      "tabs",
      `tabs-${this.size}`,
      `tabs-${this.variant}`,
      `tabs-${this.orientation}`
    ];
    return classes.join(" ");
  }
  selectTab(tabId) {
    const tab = this.tabs.find((t) => t.id === tabId);
    if (!tab || tab.disabled)
      return;
    this.activeTabId = tabId;
    this.updateTabComponents();
    this.tabChange.emit(tabId);
  }
  updateTabComponents() {
    if (this.tabComponents) {
      this.tabComponents.forEach((tabComponent) => {
        tabComponent.active = tabComponent.id === this.activeTabId;
      });
    }
  }
  trackByTab(index, tab) {
    return tab.id;
  }
  // Public methods for external control
  setActiveTab(tabId) {
    this.selectTab(tabId);
  }
  getActiveTab() {
    return this.activeTabId;
  }
  setTabError(tabId, hasError) {
    const tab = this.tabs.find((t) => t.id === tabId);
    if (tab) {
      tab.hasError = hasError;
    }
  }
  setTabBadge(tabId, badge) {
    const tab = this.tabs.find((t) => t.id === tabId);
    if (tab) {
      tab.badge = badge;
    }
  }
  disableTab(tabId, disabled = true) {
    const tab = this.tabs.find((t) => t.id === tabId);
    if (tab) {
      tab.disabled = disabled;
      if (disabled && this.activeTabId === tabId) {
        const nextTab = this.tabs.find((t) => t.id !== tabId && !t.disabled);
        if (nextTab) {
          this.selectTab(nextTab.id);
        }
      }
    }
  }
  static \u0275fac = function TabsComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _TabsComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _TabsComponent, selectors: [["app-tabs"]], contentQueries: function TabsComponent_ContentQueries(rf, ctx, dirIndex) {
    if (rf & 1) {
      \u0275\u0275contentQuery(dirIndex, TabComponent, 4);
    }
    if (rf & 2) {
      let _t;
      \u0275\u0275queryRefresh(_t = \u0275\u0275loadQuery()) && (ctx.tabComponents = _t);
    }
  }, inputs: { orientation: "orientation", size: "size", variant: "variant", activeTabId: "activeTabId", tabs: "tabs" }, outputs: { tabChange: "tabChange" }, features: [\u0275\u0275NgOnChangesFeature], ngContentSelectors: _c0, decls: 5, vars: 6, consts: [[1, "tabs-container"], [1, "tabs-nav"], ["class", "tab-button", "role", "tab", 3, "active", "disabled", "has-error", "click", 4, "ngFor", "ngForOf", "ngForTrackBy"], [1, "tabs-content"], ["role", "tab", 1, "tab-button", 3, "click", "disabled"], ["class", "tab-icon", "viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", 4, "ngIf"], [1, "tab-label"], ["class", "tab-badge", 3, "error-badge", 4, "ngIf"], ["class", "tab-error-icon", "viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", 4, "ngIf"], ["viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", 1, "tab-icon"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2"], [1, "tab-badge"], ["viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", 1, "tab-error-icon"], ["cx", "12", "cy", "12", "r", "10"], ["x1", "15", "y1", "9", "x2", "9", "y2", "15"], ["x1", "9", "y1", "9", "x2", "15", "y2", "15"]], template: function TabsComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275projectionDef();
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1);
      \u0275\u0275template(2, TabsComponent_button_2_Template, 6, 13, "button", 2);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(3, "div", 3);
      \u0275\u0275projection(4);
      \u0275\u0275elementEnd()();
    }
    if (rf & 2) {
      \u0275\u0275classMap(ctx.containerClasses);
      \u0275\u0275advance();
      \u0275\u0275classProp("tabs-nav-vertical", ctx.orientation === "vertical");
      \u0275\u0275advance();
      \u0275\u0275property("ngForOf", ctx.tabs)("ngForTrackBy", ctx.trackByTab);
    }
  }, dependencies: [CommonModule, NgForOf, NgIf], styles: ['\n\n.tabs-container[_ngcontent-%COMP%] {\n  display: flex;\n  background: white;\n  border-radius: var(--radius-lg);\n  border: 1px solid var(--secondary-200);\n  overflow: hidden;\n}\n.tabs-horizontal[_ngcontent-%COMP%] {\n  flex-direction: column;\n}\n.tabs-vertical[_ngcontent-%COMP%] {\n  flex-direction: row;\n  min-height: 400px;\n}\n.tabs-nav[_ngcontent-%COMP%] {\n  display: flex;\n  background: var(--neutral-50);\n  border-bottom: 1px solid var(--secondary-200);\n}\n.tabs-nav-vertical[_ngcontent-%COMP%] {\n  flex-direction: column;\n  border-bottom: none;\n  border-right: 1px solid var(--secondary-200);\n  min-width: 200px;\n}\n.tab-button[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n  padding: var(--spacing-md) var(--spacing-lg);\n  border: none;\n  background: none;\n  color: var(--secondary-600);\n  font-size: var(--text-sm);\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  position: relative;\n  white-space: nowrap;\n}\n.tab-button[_ngcontent-%COMP%]:hover:not(.disabled) {\n  background: var(--secondary-100);\n  color: var(--secondary-800);\n}\n.tab-button.active[_ngcontent-%COMP%] {\n  background: white;\n  color: var(--primary-600);\n  font-weight: 600;\n}\n.tab-button.disabled[_ngcontent-%COMP%] {\n  opacity: 0.5;\n  cursor: not-allowed;\n  pointer-events: none;\n}\n.tab-button.has-error[_ngcontent-%COMP%] {\n  color: var(--error-600);\n}\n.tab-button[_ngcontent-%COMP%]:focus-visible {\n  outline: 2px solid var(--primary-500);\n  outline-offset: -2px;\n}\n.tabs-sm[_ngcontent-%COMP%]   .tab-button[_ngcontent-%COMP%] {\n  padding: var(--spacing-sm) var(--spacing-md);\n  font-size: var(--text-xs);\n}\n.tabs-lg[_ngcontent-%COMP%]   .tab-button[_ngcontent-%COMP%] {\n  padding: var(--spacing-lg) var(--spacing-xl);\n  font-size: var(--text-base);\n}\n.tabs-pills[_ngcontent-%COMP%]   .tabs-nav[_ngcontent-%COMP%] {\n  background: transparent;\n  border: none;\n  padding: var(--spacing-sm);\n  gap: var(--spacing-xs);\n}\n.tabs-pills[_ngcontent-%COMP%]   .tab-button[_ngcontent-%COMP%] {\n  border-radius: var(--radius-md);\n}\n.tabs-pills[_ngcontent-%COMP%]   .tab-button.active[_ngcontent-%COMP%] {\n  background: var(--primary-500);\n  color: white;\n}\n.tabs-underline[_ngcontent-%COMP%]   .tabs-nav[_ngcontent-%COMP%] {\n  background: transparent;\n  border-bottom: 1px solid var(--secondary-200);\n}\n.tabs-underline[_ngcontent-%COMP%]   .tab-button[_ngcontent-%COMP%] {\n  border-bottom: 2px solid transparent;\n  border-radius: 0;\n}\n.tabs-underline[_ngcontent-%COMP%]   .tab-button.active[_ngcontent-%COMP%] {\n  background: transparent;\n  border-bottom-color: var(--primary-500);\n  color: var(--primary-600);\n}\n.tabs-underline[_ngcontent-%COMP%]   .tab-button[_ngcontent-%COMP%]:hover:not(.disabled):not(.active) {\n  background: var(--primary-50);\n}\n.tab-icon[_ngcontent-%COMP%] {\n  width: 16px;\n  height: 16px;\n  flex-shrink: 0;\n}\n.tabs-sm[_ngcontent-%COMP%]   .tab-icon[_ngcontent-%COMP%] {\n  width: 14px;\n  height: 14px;\n}\n.tabs-lg[_ngcontent-%COMP%]   .tab-icon[_ngcontent-%COMP%] {\n  width: 18px;\n  height: 18px;\n}\n.tab-label[_ngcontent-%COMP%] {\n  flex: 1;\n  text-align: left;\n}\n.tabs-vertical[_ngcontent-%COMP%]   .tab-label[_ngcontent-%COMP%] {\n  text-align: left;\n}\n.tab-badge[_ngcontent-%COMP%] {\n  min-width: 18px;\n  height: 18px;\n  padding: 0 var(--spacing-xs);\n  background: var(--primary-100);\n  color: var(--primary-700);\n  font-size: 10px;\n  font-weight: 600;\n  border-radius: 9px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  line-height: 1;\n}\n.tab-badge.error-badge[_ngcontent-%COMP%] {\n  background: var(--error-100);\n  color: var(--error-700);\n}\n.tabs-sm[_ngcontent-%COMP%]   .tab-badge[_ngcontent-%COMP%] {\n  min-width: 16px;\n  height: 16px;\n  font-size: 9px;\n}\n.tabs-lg[_ngcontent-%COMP%]   .tab-badge[_ngcontent-%COMP%] {\n  min-width: 20px;\n  height: 20px;\n  font-size: 11px;\n}\n.tab-error-icon[_ngcontent-%COMP%] {\n  width: 14px;\n  height: 14px;\n  color: var(--error-500);\n  flex-shrink: 0;\n}\n.tabs-content[_ngcontent-%COMP%] {\n  flex: 1;\n  padding: var(--spacing-lg);\n  background: white;\n}\n.tabs-sm[_ngcontent-%COMP%]   .tabs-content[_ngcontent-%COMP%] {\n  padding: var(--spacing-md);\n}\n.tabs-lg[_ngcontent-%COMP%]   .tabs-content[_ngcontent-%COMP%] {\n  padding: var(--spacing-xl);\n}\n.tabs-vertical[_ngcontent-%COMP%]   .tabs-content[_ngcontent-%COMP%] {\n  overflow-y: auto;\n}\n.tabs-default[_ngcontent-%COMP%]   .tab-button.active[_ngcontent-%COMP%]::before {\n  content: "";\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 2px;\n  background: var(--primary-500);\n}\n.tabs-vertical.tabs-default[_ngcontent-%COMP%]   .tab-button.active[_ngcontent-%COMP%]::before {\n  top: 0;\n  bottom: 0;\n  left: auto;\n  right: 0;\n  width: 2px;\n  height: auto;\n}\n@media (max-width: 768px) {\n  .tabs-vertical[_ngcontent-%COMP%] {\n    flex-direction: column;\n    min-height: auto;\n  }\n  .tabs-nav-vertical[_ngcontent-%COMP%] {\n    flex-direction: row;\n    border-right: none;\n    border-bottom: 1px solid var(--secondary-200);\n    min-width: auto;\n    overflow-x: auto;\n  }\n  .tabs-vertical[_ngcontent-%COMP%]   .tabs-content[_ngcontent-%COMP%] {\n    overflow-y: visible;\n  }\n  .tab-button[_ngcontent-%COMP%] {\n    flex-shrink: 0;\n  }\n}\n@media (max-width: 480px) {\n  .tab-button[_ngcontent-%COMP%] {\n    padding: var(--spacing-sm) var(--spacing-md);\n    font-size: var(--text-xs);\n  }\n  .tab-label[_ngcontent-%COMP%] {\n    display: none;\n  }\n  .tab-icon[_ngcontent-%COMP%] {\n    width: 16px;\n    height: 16px;\n  }\n  .tabs-content[_ngcontent-%COMP%] {\n    padding: var(--spacing-md);\n  }\n}\n@media print {\n  .tabs-nav[_ngcontent-%COMP%] {\n    display: none;\n  }\n  .tabs-content[_ngcontent-%COMP%] {\n    padding: 0;\n    border: none;\n  }\n  .tab-content[_ngcontent-%COMP%] {\n    display: block !important;\n    page-break-inside: avoid;\n  }\n}\n/*# sourceMappingURL=tabs.component.css.map */'] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TabsComponent, [{
    type: Component,
    args: [{ selector: "app-tabs", standalone: true, imports: [CommonModule], template: `
    <div class="tabs-container" [class]="containerClasses">
      <!-- Tab Navigation -->
      <div class="tabs-nav" [class.tabs-nav-vertical]="orientation === 'vertical'">
        <button
          *ngFor="let tab of tabs; trackBy: trackByTab"
          class="tab-button"
          [class.active]="tab.id === activeTabId"
          [class.disabled]="tab.disabled"
          [class.has-error]="tab.hasError"
          [disabled]="tab.disabled"
          (click)="selectTab(tab.id)"
          [attr.aria-selected]="tab.id === activeTabId"
          [attr.aria-controls]="'tab-panel-' + tab.id"
          role="tab"
        >
          <!-- Icon -->
          <svg *ngIf="tab.icon" class="tab-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" [attr.d]="tab.icon" />
          </svg>

          <!-- Label -->
          <span class="tab-label">{{ tab.label }}</span>

          <!-- Badge -->
          <span *ngIf="tab.badge" class="tab-badge" [class.error-badge]="tab.hasError">
            {{ tab.badge }}
          </span>

          <!-- Error Indicator -->
          <svg *ngIf="tab.hasError && !tab.badge" class="tab-error-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="15" y1="9" x2="9" y2="15"></line>
            <line x1="9" y1="9" x2="15" y2="15"></line>
          </svg>
        </button>
      </div>

      <!-- Tab Content -->
      <div class="tabs-content">
        <ng-content></ng-content>
      </div>
    </div>
  `, styles: ['/* src/app/shared/components/tabs/tabs.component.scss */\n.tabs-container {\n  display: flex;\n  background: white;\n  border-radius: var(--radius-lg);\n  border: 1px solid var(--secondary-200);\n  overflow: hidden;\n}\n.tabs-horizontal {\n  flex-direction: column;\n}\n.tabs-vertical {\n  flex-direction: row;\n  min-height: 400px;\n}\n.tabs-nav {\n  display: flex;\n  background: var(--neutral-50);\n  border-bottom: 1px solid var(--secondary-200);\n}\n.tabs-nav-vertical {\n  flex-direction: column;\n  border-bottom: none;\n  border-right: 1px solid var(--secondary-200);\n  min-width: 200px;\n}\n.tab-button {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n  padding: var(--spacing-md) var(--spacing-lg);\n  border: none;\n  background: none;\n  color: var(--secondary-600);\n  font-size: var(--text-sm);\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  position: relative;\n  white-space: nowrap;\n}\n.tab-button:hover:not(.disabled) {\n  background: var(--secondary-100);\n  color: var(--secondary-800);\n}\n.tab-button.active {\n  background: white;\n  color: var(--primary-600);\n  font-weight: 600;\n}\n.tab-button.disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n  pointer-events: none;\n}\n.tab-button.has-error {\n  color: var(--error-600);\n}\n.tab-button:focus-visible {\n  outline: 2px solid var(--primary-500);\n  outline-offset: -2px;\n}\n.tabs-sm .tab-button {\n  padding: var(--spacing-sm) var(--spacing-md);\n  font-size: var(--text-xs);\n}\n.tabs-lg .tab-button {\n  padding: var(--spacing-lg) var(--spacing-xl);\n  font-size: var(--text-base);\n}\n.tabs-pills .tabs-nav {\n  background: transparent;\n  border: none;\n  padding: var(--spacing-sm);\n  gap: var(--spacing-xs);\n}\n.tabs-pills .tab-button {\n  border-radius: var(--radius-md);\n}\n.tabs-pills .tab-button.active {\n  background: var(--primary-500);\n  color: white;\n}\n.tabs-underline .tabs-nav {\n  background: transparent;\n  border-bottom: 1px solid var(--secondary-200);\n}\n.tabs-underline .tab-button {\n  border-bottom: 2px solid transparent;\n  border-radius: 0;\n}\n.tabs-underline .tab-button.active {\n  background: transparent;\n  border-bottom-color: var(--primary-500);\n  color: var(--primary-600);\n}\n.tabs-underline .tab-button:hover:not(.disabled):not(.active) {\n  background: var(--primary-50);\n}\n.tab-icon {\n  width: 16px;\n  height: 16px;\n  flex-shrink: 0;\n}\n.tabs-sm .tab-icon {\n  width: 14px;\n  height: 14px;\n}\n.tabs-lg .tab-icon {\n  width: 18px;\n  height: 18px;\n}\n.tab-label {\n  flex: 1;\n  text-align: left;\n}\n.tabs-vertical .tab-label {\n  text-align: left;\n}\n.tab-badge {\n  min-width: 18px;\n  height: 18px;\n  padding: 0 var(--spacing-xs);\n  background: var(--primary-100);\n  color: var(--primary-700);\n  font-size: 10px;\n  font-weight: 600;\n  border-radius: 9px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  line-height: 1;\n}\n.tab-badge.error-badge {\n  background: var(--error-100);\n  color: var(--error-700);\n}\n.tabs-sm .tab-badge {\n  min-width: 16px;\n  height: 16px;\n  font-size: 9px;\n}\n.tabs-lg .tab-badge {\n  min-width: 20px;\n  height: 20px;\n  font-size: 11px;\n}\n.tab-error-icon {\n  width: 14px;\n  height: 14px;\n  color: var(--error-500);\n  flex-shrink: 0;\n}\n.tabs-content {\n  flex: 1;\n  padding: var(--spacing-lg);\n  background: white;\n}\n.tabs-sm .tabs-content {\n  padding: var(--spacing-md);\n}\n.tabs-lg .tabs-content {\n  padding: var(--spacing-xl);\n}\n.tabs-vertical .tabs-content {\n  overflow-y: auto;\n}\n.tabs-default .tab-button.active::before {\n  content: "";\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  height: 2px;\n  background: var(--primary-500);\n}\n.tabs-vertical.tabs-default .tab-button.active::before {\n  top: 0;\n  bottom: 0;\n  left: auto;\n  right: 0;\n  width: 2px;\n  height: auto;\n}\n@media (max-width: 768px) {\n  .tabs-vertical {\n    flex-direction: column;\n    min-height: auto;\n  }\n  .tabs-nav-vertical {\n    flex-direction: row;\n    border-right: none;\n    border-bottom: 1px solid var(--secondary-200);\n    min-width: auto;\n    overflow-x: auto;\n  }\n  .tabs-vertical .tabs-content {\n    overflow-y: visible;\n  }\n  .tab-button {\n    flex-shrink: 0;\n  }\n}\n@media (max-width: 480px) {\n  .tab-button {\n    padding: var(--spacing-sm) var(--spacing-md);\n    font-size: var(--text-xs);\n  }\n  .tab-label {\n    display: none;\n  }\n  .tab-icon {\n    width: 16px;\n    height: 16px;\n  }\n  .tabs-content {\n    padding: var(--spacing-md);\n  }\n}\n@media print {\n  .tabs-nav {\n    display: none;\n  }\n  .tabs-content {\n    padding: 0;\n    border: none;\n  }\n  .tab-content {\n    display: block !important;\n    page-break-inside: avoid;\n  }\n}\n/*# sourceMappingURL=tabs.component.css.map */\n'] }]
  }], null, { orientation: [{
    type: Input
  }], size: [{
    type: Input
  }], variant: [{
    type: Input
  }], activeTabId: [{
    type: Input
  }], tabs: [{
    type: Input
  }], tabChange: [{
    type: Output
  }], tabComponents: [{
    type: ContentChildren,
    args: [TabComponent]
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(TabsComponent, { className: "TabsComponent", filePath: "src/app/shared/components/tabs/tabs.component.ts", lineNumber: 92 });
})();

// src/app/modules/applications/components/dependency-modal/dependency-modal.component.ts
var DependencyModalComponent = class _DependencyModalComponent {
  fb;
  isOpen = false;
  editMode = false;
  initialData = null;
  loading = false;
  closed = new EventEmitter();
  saved = new EventEmitter();
  dependencyForm;
  constructor(fb) {
    this.fb = fb;
  }
  ngOnInit() {
    this.initializeForm();
  }
  ngOnChanges() {
    if (this.dependencyForm && this.initialData) {
      this.dependencyForm.patchValue(this.initialData);
    }
  }
  initializeForm() {
    this.dependencyForm = this.fb.group({
      name: ["", [Validators.required, Validators.minLength(2)]],
      type: ["library", [Validators.required]],
      version: ["", [Validators.required]],
      criticality: ["medium", [Validators.required]],
      isInternal: [false],
      description: [""]
    });
    if (this.initialData) {
      this.dependencyForm.patchValue(this.initialData);
    }
  }
  getFieldError(fieldName) {
    const field = this.dependencyForm.get(fieldName);
    if (field && field.invalid && field.touched) {
      if (field.errors?.["required"]) {
        return `${fieldName} is required`;
      }
      if (field.errors?.["minlength"]) {
        return `${fieldName} must be at least ${field.errors["minlength"].requiredLength} characters`;
      }
    }
    return "";
  }
  onClose() {
    this.dependencyForm.reset();
    this.closed.emit();
  }
  onSave() {
    if (this.dependencyForm.valid) {
      this.saved.emit(this.dependencyForm.value);
    }
  }
  static \u0275fac = function DependencyModalComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _DependencyModalComponent)(\u0275\u0275directiveInject(FormBuilder));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _DependencyModalComponent, selectors: [["app-dependency-modal"]], inputs: { isOpen: "isOpen", editMode: "editMode", initialData: "initialData", loading: "loading" }, outputs: { closed: "closed", saved: "saved" }, features: [\u0275\u0275NgOnChangesFeature], decls: 46, vars: 9, consts: [["subtitle", "Configure application dependency details", "confirmText", "Save Dependency", 3, "closed", "confirmed", "cancelled", "isOpen", "title", "loading", "canConfirm"], [1, "dependency-form", 3, "formGroup"], [1, "form-grid"], ["label", "Dependency Name", "placeholder", "e.g., Angular, Express.js", "formControlName", "name", 3, "required", "errorMessage"], [1, "form-group"], [1, "form-label"], ["formControlName", "type", 1, "form-select"], ["value", "library"], ["value", "framework"], ["value", "service"], ["value", "database"], ["value", "api"], ["value", "tool"], ["value", "infrastructure"], ["label", "Version", "placeholder", "e.g., 16.2.0, ^4.18.2", "formControlName", "version", 3, "required", "errorMessage"], ["formControlName", "criticality", 1, "form-select"], ["value", "low"], ["value", "medium"], ["value", "high"], ["value", "critical"], [1, "form-group", "full-width"], [1, "checkbox-label"], ["type", "checkbox", "formControlName", "isInternal", 1, "checkbox-input"], [1, "checkbox-text"], [1, "checkbox-description"], ["formControlName", "description", "placeholder", "Describe the purpose and usage of this dependency...", "rows", "3", 1, "form-textarea"]], template: function DependencyModalComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "app-slide-modal", 0);
      \u0275\u0275listener("closed", function DependencyModalComponent_Template_app_slide_modal_closed_0_listener() {
        return ctx.onClose();
      })("confirmed", function DependencyModalComponent_Template_app_slide_modal_confirmed_0_listener() {
        return ctx.onSave();
      })("cancelled", function DependencyModalComponent_Template_app_slide_modal_cancelled_0_listener() {
        return ctx.onClose();
      });
      \u0275\u0275elementStart(1, "form", 1)(2, "div", 2);
      \u0275\u0275element(3, "app-form-input", 3);
      \u0275\u0275elementStart(4, "div", 4)(5, "label", 5);
      \u0275\u0275text(6, "Type");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(7, "select", 6)(8, "option", 7);
      \u0275\u0275text(9, "Library");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(10, "option", 8);
      \u0275\u0275text(11, "Framework");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(12, "option", 9);
      \u0275\u0275text(13, "Service");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(14, "option", 10);
      \u0275\u0275text(15, "Database");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(16, "option", 11);
      \u0275\u0275text(17, "API");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(18, "option", 12);
      \u0275\u0275text(19, "Tool");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(20, "option", 13);
      \u0275\u0275text(21, "Infrastructure");
      \u0275\u0275elementEnd()()();
      \u0275\u0275element(22, "app-form-input", 14);
      \u0275\u0275elementStart(23, "div", 4)(24, "label", 5);
      \u0275\u0275text(25, "Criticality");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(26, "select", 15)(27, "option", 16);
      \u0275\u0275text(28, "Low");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(29, "option", 17);
      \u0275\u0275text(30, "Medium");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(31, "option", 18);
      \u0275\u0275text(32, "High");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(33, "option", 19);
      \u0275\u0275text(34, "Critical");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(35, "div", 20)(36, "label", 21);
      \u0275\u0275element(37, "input", 22);
      \u0275\u0275elementStart(38, "span", 23);
      \u0275\u0275text(39, "Internal Dependency");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(40, "span", 24);
      \u0275\u0275text(41, "This is an internal company service or library");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(42, "div", 20)(43, "label", 5);
      \u0275\u0275text(44, "Description");
      \u0275\u0275elementEnd();
      \u0275\u0275element(45, "textarea", 25);
      \u0275\u0275elementEnd()()()();
    }
    if (rf & 2) {
      \u0275\u0275property("isOpen", ctx.isOpen)("title", ctx.editMode ? "Edit Dependency" : "Add Dependency")("loading", ctx.loading)("canConfirm", ctx.dependencyForm.valid);
      \u0275\u0275advance();
      \u0275\u0275property("formGroup", ctx.dependencyForm);
      \u0275\u0275advance(2);
      \u0275\u0275property("required", true)("errorMessage", ctx.getFieldError("name"));
      \u0275\u0275advance(19);
      \u0275\u0275property("required", true)("errorMessage", ctx.getFieldError("version"));
    }
  }, dependencies: [CommonModule, ReactiveFormsModule, \u0275NgNoValidate, NgSelectOption, \u0275NgSelectMultipleOption, DefaultValueAccessor, CheckboxControlValueAccessor, SelectControlValueAccessor, NgControlStatus, NgControlStatusGroup, RequiredValidator, FormGroupDirective, FormControlName, SlideModalComponent, FormInputComponent], styles: [`

.dependency-form[_ngcontent-%COMP%] {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}
.form-grid[_ngcontent-%COMP%] {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
}
.form-group[_ngcontent-%COMP%] {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}
.form-group.full-width[_ngcontent-%COMP%] {
  grid-column: 1/-1;
}
.form-label[_ngcontent-%COMP%] {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--secondary-700);
}
.form-select[_ngcontent-%COMP%], 
.form-textarea[_ngcontent-%COMP%] {
  border: 1px solid var(--secondary-300);
  border-radius: var(--radius-md);
  background: white;
  font-size: var(--text-sm);
  color: var(--secondary-800);
  transition: all 0.2s ease;
}
.form-select[_ngcontent-%COMP%]:focus, 
.form-textarea[_ngcontent-%COMP%]:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}
.form-select[_ngcontent-%COMP%] {
  height: 40px;
  padding: 0 var(--spacing-md);
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-sm) center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
  appearance: none;
}
.form-textarea[_ngcontent-%COMP%] {
  padding: var(--spacing-md);
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}
.checkbox-label[_ngcontent-%COMP%] {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  cursor: pointer;
  padding: var(--spacing-md);
  border: 1px solid var(--secondary-200);
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
}
.checkbox-label[_ngcontent-%COMP%]:hover {
  border-color: var(--primary-300);
  background: var(--primary-25);
}
.checkbox-input[_ngcontent-%COMP%] {
  margin: 0;
  margin-top: 2px;
}
.checkbox-text[_ngcontent-%COMP%] {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--secondary-900);
  margin-bottom: var(--spacing-xs);
}
.checkbox-description[_ngcontent-%COMP%] {
  font-size: var(--text-xs);
  color: var(--secondary-600);
  display: block;
}
@media (max-width: 768px) {
  .form-grid[_ngcontent-%COMP%] {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
}
/*# sourceMappingURL=dependency-modal.component.css.map */`] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(DependencyModalComponent, [{
    type: Component,
    args: [{ selector: "app-dependency-modal", standalone: true, imports: [CommonModule, ReactiveFormsModule, SlideModalComponent, FormInputComponent], template: `
    <app-slide-modal
      [isOpen]="isOpen"
      [title]="editMode ? 'Edit Dependency' : 'Add Dependency'"
      subtitle="Configure application dependency details"
      [loading]="loading"
      [canConfirm]="dependencyForm.valid"
      confirmText="Save Dependency"
      (closed)="onClose()"
      (confirmed)="onSave()"
      (cancelled)="onClose()"
    >
      <form [formGroup]="dependencyForm" class="dependency-form">
        <div class="form-grid">
          <app-form-input
            label="Dependency Name"
            placeholder="e.g., Angular, Express.js"
            formControlName="name"
            [required]="true"
            [errorMessage]="getFieldError('name')"
          ></app-form-input>

          <div class="form-group">
            <label class="form-label">Type</label>
            <select formControlName="type" class="form-select">
              <option value="library">Library</option>
              <option value="framework">Framework</option>
              <option value="service">Service</option>
              <option value="database">Database</option>
              <option value="api">API</option>
              <option value="tool">Tool</option>
              <option value="infrastructure">Infrastructure</option>
            </select>
          </div>

          <app-form-input
            label="Version"
            placeholder="e.g., 16.2.0, ^4.18.2"
            formControlName="version"
            [required]="true"
            [errorMessage]="getFieldError('version')"
          ></app-form-input>

          <div class="form-group">
            <label class="form-label">Criticality</label>
            <select formControlName="criticality" class="form-select">
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="critical">Critical</option>
            </select>
          </div>

          <div class="form-group full-width">
            <label class="checkbox-label">
              <input
                type="checkbox"
                formControlName="isInternal"
                class="checkbox-input"
              >
              <span class="checkbox-text">Internal Dependency</span>
              <span class="checkbox-description">This is an internal company service or library</span>
            </label>
          </div>

          <div class="form-group full-width">
            <label class="form-label">Description</label>
            <textarea
              formControlName="description"
              class="form-textarea"
              placeholder="Describe the purpose and usage of this dependency..."
              rows="3"
            ></textarea>
          </div>
        </div>
      </form>
    </app-slide-modal>
  `, styles: [`/* angular:styles/component:scss;4d5060112449270b6dfca7879f3b0037af11853a47421dfa7edb349ba29883a5;C:/Users/<USER>/Documents/VibeCoding/AppCatalog/app-catalog/frontend/src/app/modules/applications/components/dependency-modal/dependency-modal.component.ts */
.dependency-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}
.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
}
.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}
.form-group.full-width {
  grid-column: 1/-1;
}
.form-label {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--secondary-700);
}
.form-select,
.form-textarea {
  border: 1px solid var(--secondary-300);
  border-radius: var(--radius-md);
  background: white;
  font-size: var(--text-sm);
  color: var(--secondary-800);
  transition: all 0.2s ease;
}
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}
.form-select {
  height: 40px;
  padding: 0 var(--spacing-md);
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-sm) center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
  appearance: none;
}
.form-textarea {
  padding: var(--spacing-md);
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}
.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  cursor: pointer;
  padding: var(--spacing-md);
  border: 1px solid var(--secondary-200);
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
}
.checkbox-label:hover {
  border-color: var(--primary-300);
  background: var(--primary-25);
}
.checkbox-input {
  margin: 0;
  margin-top: 2px;
}
.checkbox-text {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--secondary-900);
  margin-bottom: var(--spacing-xs);
}
.checkbox-description {
  font-size: var(--text-xs);
  color: var(--secondary-600);
  display: block;
}
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
}
/*# sourceMappingURL=dependency-modal.component.css.map */
`] }]
  }], () => [{ type: FormBuilder }], { isOpen: [{
    type: Input
  }], editMode: [{
    type: Input
  }], initialData: [{
    type: Input
  }], loading: [{
    type: Input
  }], closed: [{
    type: Output
  }], saved: [{
    type: Output
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(DependencyModalComponent, { className: "DependencyModalComponent", filePath: "src/app/modules/applications/components/dependency-modal/dependency-modal.component.ts", lineNumber: 204 });
})();

// src/app/modules/applications/components/tech-stack-modal/tech-stack-modal.component.ts
var TechStackModalComponent = class _TechStackModalComponent {
  fb;
  isOpen = false;
  editMode = false;
  initialData = null;
  loading = false;
  closed = new EventEmitter();
  saved = new EventEmitter();
  techStackForm;
  constructor(fb) {
    this.fb = fb;
  }
  ngOnInit() {
    this.initializeForm();
  }
  ngOnChanges() {
    if (this.techStackForm && this.initialData) {
      this.techStackForm.patchValue(this.initialData);
    }
  }
  initializeForm() {
    this.techStackForm = this.fb.group({
      category: ["frontend", [Validators.required]],
      technology: ["", [Validators.required, Validators.minLength(2)]],
      version: ["", [Validators.required]],
      purpose: ["", [Validators.required]],
      isCore: [false]
    });
    if (this.initialData) {
      this.techStackForm.patchValue(this.initialData);
    }
  }
  getFieldError(fieldName) {
    const field = this.techStackForm.get(fieldName);
    if (field && field.invalid && field.touched) {
      if (field.errors?.["required"]) {
        return `${fieldName} is required`;
      }
      if (field.errors?.["minlength"]) {
        return `${fieldName} must be at least ${field.errors["minlength"].requiredLength} characters`;
      }
    }
    return "";
  }
  onClose() {
    this.techStackForm.reset();
    this.closed.emit();
  }
  onSave() {
    if (this.techStackForm.valid) {
      this.saved.emit(this.techStackForm.value);
    }
  }
  static \u0275fac = function TechStackModalComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _TechStackModalComponent)(\u0275\u0275directiveInject(FormBuilder));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _TechStackModalComponent, selectors: [["app-tech-stack-modal"]], inputs: { isOpen: "isOpen", editMode: "editMode", initialData: "initialData", loading: "loading" }, outputs: { closed: "closed", saved: "saved" }, features: [\u0275\u0275NgOnChangesFeature], decls: 38, vars: 9, consts: [["subtitle", "Configure technology stack details", "confirmText", "Save Technology", 3, "closed", "confirmed", "cancelled", "isOpen", "title", "loading", "canConfirm"], [1, "tech-stack-form", 3, "formGroup"], [1, "form-grid"], [1, "form-group"], [1, "form-label"], ["formControlName", "category", 1, "form-select"], ["value", "frontend"], ["value", "backend"], ["value", "database"], ["value", "infrastructure"], ["value", "monitoring"], ["value", "security"], ["value", "testing"], ["value", "deployment"], ["value", "communication"], ["label", "Technology", "placeholder", "e.g., React, Node.js, PostgreSQL", "formControlName", "technology", 3, "required", "errorMessage"], ["label", "Version", "placeholder", "e.g., 18.2.0, 16.x", "formControlName", "version", 3, "required", "errorMessage"], [1, "checkbox-label"], ["type", "checkbox", "formControlName", "isCore", 1, "checkbox-input"], [1, "checkbox-text"], [1, "checkbox-description"], [1, "form-group", "full-width"], ["formControlName", "purpose", "placeholder", "Describe how this technology is used in the application...", "rows", "3", 1, "form-textarea"]], template: function TechStackModalComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "app-slide-modal", 0);
      \u0275\u0275listener("closed", function TechStackModalComponent_Template_app_slide_modal_closed_0_listener() {
        return ctx.onClose();
      })("confirmed", function TechStackModalComponent_Template_app_slide_modal_confirmed_0_listener() {
        return ctx.onSave();
      })("cancelled", function TechStackModalComponent_Template_app_slide_modal_cancelled_0_listener() {
        return ctx.onClose();
      });
      \u0275\u0275elementStart(1, "form", 1)(2, "div", 2)(3, "div", 3)(4, "label", 4);
      \u0275\u0275text(5, "Category");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(6, "select", 5)(7, "option", 6);
      \u0275\u0275text(8, "Frontend");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(9, "option", 7);
      \u0275\u0275text(10, "Backend");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(11, "option", 8);
      \u0275\u0275text(12, "Database");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(13, "option", 9);
      \u0275\u0275text(14, "Infrastructure");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(15, "option", 10);
      \u0275\u0275text(16, "Monitoring");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(17, "option", 11);
      \u0275\u0275text(18, "Security");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(19, "option", 12);
      \u0275\u0275text(20, "Testing");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(21, "option", 13);
      \u0275\u0275text(22, "Deployment");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(23, "option", 14);
      \u0275\u0275text(24, "Communication");
      \u0275\u0275elementEnd()()();
      \u0275\u0275element(25, "app-form-input", 15)(26, "app-form-input", 16);
      \u0275\u0275elementStart(27, "div", 3)(28, "label", 17);
      \u0275\u0275element(29, "input", 18);
      \u0275\u0275elementStart(30, "span", 19);
      \u0275\u0275text(31, "Core Technology");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(32, "span", 20);
      \u0275\u0275text(33, "This is a core/critical technology for the application");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(34, "div", 21)(35, "label", 4);
      \u0275\u0275text(36, "Purpose");
      \u0275\u0275elementEnd();
      \u0275\u0275element(37, "textarea", 22);
      \u0275\u0275elementEnd()()()();
    }
    if (rf & 2) {
      \u0275\u0275property("isOpen", ctx.isOpen)("title", ctx.editMode ? "Edit Technology" : "Add Technology")("loading", ctx.loading)("canConfirm", ctx.techStackForm.valid);
      \u0275\u0275advance();
      \u0275\u0275property("formGroup", ctx.techStackForm);
      \u0275\u0275advance(24);
      \u0275\u0275property("required", true)("errorMessage", ctx.getFieldError("technology"));
      \u0275\u0275advance();
      \u0275\u0275property("required", true)("errorMessage", ctx.getFieldError("version"));
    }
  }, dependencies: [CommonModule, ReactiveFormsModule, \u0275NgNoValidate, NgSelectOption, \u0275NgSelectMultipleOption, DefaultValueAccessor, CheckboxControlValueAccessor, SelectControlValueAccessor, NgControlStatus, NgControlStatusGroup, RequiredValidator, FormGroupDirective, FormControlName, SlideModalComponent, FormInputComponent], styles: [`

.tech-stack-form[_ngcontent-%COMP%] {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}
.form-grid[_ngcontent-%COMP%] {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
}
.form-group[_ngcontent-%COMP%] {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}
.form-group.full-width[_ngcontent-%COMP%] {
  grid-column: 1/-1;
}
.form-label[_ngcontent-%COMP%] {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--secondary-700);
}
.form-select[_ngcontent-%COMP%], 
.form-textarea[_ngcontent-%COMP%] {
  border: 1px solid var(--secondary-300);
  border-radius: var(--radius-md);
  background: white;
  font-size: var(--text-sm);
  color: var(--secondary-800);
  transition: all 0.2s ease;
}
.form-select[_ngcontent-%COMP%]:focus, 
.form-textarea[_ngcontent-%COMP%]:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}
.form-select[_ngcontent-%COMP%] {
  height: 40px;
  padding: 0 var(--spacing-md);
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-sm) center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
  appearance: none;
}
.form-textarea[_ngcontent-%COMP%] {
  padding: var(--spacing-md);
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}
.checkbox-label[_ngcontent-%COMP%] {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  cursor: pointer;
  padding: var(--spacing-md);
  border: 1px solid var(--secondary-200);
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
}
.checkbox-label[_ngcontent-%COMP%]:hover {
  border-color: var(--primary-300);
  background: var(--primary-25);
}
.checkbox-input[_ngcontent-%COMP%] {
  margin: 0;
  margin-top: 2px;
}
.checkbox-text[_ngcontent-%COMP%] {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--secondary-900);
  margin-bottom: var(--spacing-xs);
}
.checkbox-description[_ngcontent-%COMP%] {
  font-size: var(--text-xs);
  color: var(--secondary-600);
  display: block;
}
@media (max-width: 768px) {
  .form-grid[_ngcontent-%COMP%] {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
}
/*# sourceMappingURL=tech-stack-modal.component.css.map */`] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TechStackModalComponent, [{
    type: Component,
    args: [{ selector: "app-tech-stack-modal", standalone: true, imports: [CommonModule, ReactiveFormsModule, SlideModalComponent, FormInputComponent], template: `
    <app-slide-modal
      [isOpen]="isOpen"
      [title]="editMode ? 'Edit Technology' : 'Add Technology'"
      subtitle="Configure technology stack details"
      [loading]="loading"
      [canConfirm]="techStackForm.valid"
      confirmText="Save Technology"
      (closed)="onClose()"
      (confirmed)="onSave()"
      (cancelled)="onClose()"
    >
      <form [formGroup]="techStackForm" class="tech-stack-form">
        <div class="form-grid">
          <div class="form-group">
            <label class="form-label">Category</label>
            <select formControlName="category" class="form-select">
              <option value="frontend">Frontend</option>
              <option value="backend">Backend</option>
              <option value="database">Database</option>
              <option value="infrastructure">Infrastructure</option>
              <option value="monitoring">Monitoring</option>
              <option value="security">Security</option>
              <option value="testing">Testing</option>
              <option value="deployment">Deployment</option>
              <option value="communication">Communication</option>
            </select>
          </div>

          <app-form-input
            label="Technology"
            placeholder="e.g., React, Node.js, PostgreSQL"
            formControlName="technology"
            [required]="true"
            [errorMessage]="getFieldError('technology')"
          ></app-form-input>

          <app-form-input
            label="Version"
            placeholder="e.g., 18.2.0, 16.x"
            formControlName="version"
            [required]="true"
            [errorMessage]="getFieldError('version')"
          ></app-form-input>

          <div class="form-group">
            <label class="checkbox-label">
              <input
                type="checkbox"
                formControlName="isCore"
                class="checkbox-input"
              >
              <span class="checkbox-text">Core Technology</span>
              <span class="checkbox-description">This is a core/critical technology for the application</span>
            </label>
          </div>

          <div class="form-group full-width">
            <label class="form-label">Purpose</label>
            <textarea
              formControlName="purpose"
              class="form-textarea"
              placeholder="Describe how this technology is used in the application..."
              rows="3"
            ></textarea>
          </div>
        </div>
      </form>
    </app-slide-modal>
  `, styles: [`/* angular:styles/component:scss;b8cc3f6e975ddf3802f2502d92c01ec56ad4af27e57cabf8324661fe0e07479c;C:/Users/<USER>/Documents/VibeCoding/AppCatalog/app-catalog/frontend/src/app/modules/applications/components/tech-stack-modal/tech-stack-modal.component.ts */
.tech-stack-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}
.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
}
.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}
.form-group.full-width {
  grid-column: 1/-1;
}
.form-label {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--secondary-700);
}
.form-select,
.form-textarea {
  border: 1px solid var(--secondary-300);
  border-radius: var(--radius-md);
  background: white;
  font-size: var(--text-sm);
  color: var(--secondary-800);
  transition: all 0.2s ease;
}
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}
.form-select {
  height: 40px;
  padding: 0 var(--spacing-md);
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-sm) center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
  appearance: none;
}
.form-textarea {
  padding: var(--spacing-md);
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}
.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  cursor: pointer;
  padding: var(--spacing-md);
  border: 1px solid var(--secondary-200);
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
}
.checkbox-label:hover {
  border-color: var(--primary-300);
  background: var(--primary-25);
}
.checkbox-input {
  margin: 0;
  margin-top: 2px;
}
.checkbox-text {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--secondary-900);
  margin-bottom: var(--spacing-xs);
}
.checkbox-description {
  font-size: var(--text-xs);
  color: var(--secondary-600);
  display: block;
}
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
}
/*# sourceMappingURL=tech-stack-modal.component.css.map */
`] }]
  }], () => [{ type: FormBuilder }], { isOpen: [{
    type: Input
  }], editMode: [{
    type: Input
  }], initialData: [{
    type: Input
  }], loading: [{
    type: Input
  }], closed: [{
    type: Output
  }], saved: [{
    type: Output
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(TechStackModalComponent, { className: "TechStackModalComponent", filePath: "src/app/modules/applications/components/tech-stack-modal/tech-stack-modal.component.ts", lineNumber: 195 });
})();

// src/app/modules/applications/components/documentation-modal/documentation-modal.component.ts
var DocumentationModalComponent = class _DocumentationModalComponent {
  fb;
  isOpen = false;
  editMode = false;
  initialData = null;
  loading = false;
  closed = new EventEmitter();
  saved = new EventEmitter();
  documentationForm;
  constructor(fb) {
    this.fb = fb;
  }
  ngOnInit() {
    this.initializeForm();
  }
  ngOnChanges() {
    if (this.documentationForm && this.initialData) {
      this.documentationForm.patchValue(this.initialData);
    }
  }
  initializeForm() {
    this.documentationForm = this.fb.group({
      title: ["", [Validators.required, Validators.minLength(2)]],
      type: ["technical_spec", [Validators.required]],
      version: ["1.0", [Validators.required]],
      url: ["", [Validators.required]],
      isPublic: [false],
      description: [""]
    });
    if (this.initialData) {
      this.documentationForm.patchValue(this.initialData);
    }
  }
  getFieldError(fieldName) {
    const field = this.documentationForm.get(fieldName);
    if (field && field.invalid && field.touched) {
      if (field.errors?.["required"]) {
        return `${fieldName} is required`;
      }
      if (field.errors?.["minlength"]) {
        return `${fieldName} must be at least ${field.errors["minlength"].requiredLength} characters`;
      }
    }
    return "";
  }
  onClose() {
    this.documentationForm.reset();
    this.closed.emit();
  }
  onSave() {
    if (this.documentationForm.valid) {
      this.saved.emit(this.documentationForm.value);
    }
  }
  static \u0275fac = function DocumentationModalComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _DocumentationModalComponent)(\u0275\u0275directiveInject(FormBuilder));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _DocumentationModalComponent, selectors: [["app-documentation-modal"]], inputs: { isOpen: "isOpen", editMode: "editMode", initialData: "initialData", loading: "loading" }, outputs: { closed: "closed", saved: "saved" }, features: [\u0275\u0275NgOnChangesFeature], decls: 41, vars: 11, consts: [["subtitle", "Configure documentation details", "confirmText", "Save Documentation", 3, "closed", "confirmed", "cancelled", "isOpen", "title", "loading", "canConfirm"], [1, "documentation-form", 3, "formGroup"], [1, "form-grid"], ["label", "Document Title", "placeholder", "e.g., API Documentation, User Guide", "formControlName", "title", 3, "required", "errorMessage"], [1, "form-group"], [1, "form-label"], ["formControlName", "type", 1, "form-select"], ["value", "technical_spec"], ["value", "api_documentation"], ["value", "user_manual"], ["value", "installation_guide"], ["value", "troubleshooting"], ["value", "architecture_diagram"], ["value", "deployment_guide"], ["value", "security_policy"], ["value", "runbook"], ["value", "other"], ["label", "Version", "placeholder", "e.g., 1.0, 2.1.0", "formControlName", "version", 3, "required", "errorMessage"], ["label", "URL or Location", "placeholder", "e.g., https://docs.company.com/api", "formControlName", "url", 3, "required", "errorMessage"], [1, "form-group", "full-width"], [1, "checkbox-label"], ["type", "checkbox", "formControlName", "isPublic", 1, "checkbox-input"], [1, "checkbox-text"], [1, "checkbox-description"], ["formControlName", "description", "placeholder", "Describe the content and purpose of this documentation...", "rows", "3", 1, "form-textarea"]], template: function DocumentationModalComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "app-slide-modal", 0);
      \u0275\u0275listener("closed", function DocumentationModalComponent_Template_app_slide_modal_closed_0_listener() {
        return ctx.onClose();
      })("confirmed", function DocumentationModalComponent_Template_app_slide_modal_confirmed_0_listener() {
        return ctx.onSave();
      })("cancelled", function DocumentationModalComponent_Template_app_slide_modal_cancelled_0_listener() {
        return ctx.onClose();
      });
      \u0275\u0275elementStart(1, "form", 1)(2, "div", 2);
      \u0275\u0275element(3, "app-form-input", 3);
      \u0275\u0275elementStart(4, "div", 4)(5, "label", 5);
      \u0275\u0275text(6, "Document Type");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(7, "select", 6)(8, "option", 7);
      \u0275\u0275text(9, "Technical Specification");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(10, "option", 8);
      \u0275\u0275text(11, "API Documentation");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(12, "option", 9);
      \u0275\u0275text(13, "User Manual");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(14, "option", 10);
      \u0275\u0275text(15, "Installation Guide");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(16, "option", 11);
      \u0275\u0275text(17, "Troubleshooting Guide");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(18, "option", 12);
      \u0275\u0275text(19, "Architecture Diagram");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(20, "option", 13);
      \u0275\u0275text(21, "Deployment Guide");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(22, "option", 14);
      \u0275\u0275text(23, "Security Policy");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(24, "option", 15);
      \u0275\u0275text(25, "Runbook");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(26, "option", 16);
      \u0275\u0275text(27, "Other");
      \u0275\u0275elementEnd()()();
      \u0275\u0275element(28, "app-form-input", 17)(29, "app-form-input", 18);
      \u0275\u0275elementStart(30, "div", 19)(31, "label", 20);
      \u0275\u0275element(32, "input", 21);
      \u0275\u0275elementStart(33, "span", 22);
      \u0275\u0275text(34, "Public Documentation");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(35, "span", 23);
      \u0275\u0275text(36, "This documentation is publicly accessible");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(37, "div", 19)(38, "label", 5);
      \u0275\u0275text(39, "Description");
      \u0275\u0275elementEnd();
      \u0275\u0275element(40, "textarea", 24);
      \u0275\u0275elementEnd()()()();
    }
    if (rf & 2) {
      \u0275\u0275property("isOpen", ctx.isOpen)("title", ctx.editMode ? "Edit Documentation" : "Add Documentation")("loading", ctx.loading)("canConfirm", ctx.documentationForm.valid);
      \u0275\u0275advance();
      \u0275\u0275property("formGroup", ctx.documentationForm);
      \u0275\u0275advance(2);
      \u0275\u0275property("required", true)("errorMessage", ctx.getFieldError("title"));
      \u0275\u0275advance(25);
      \u0275\u0275property("required", true)("errorMessage", ctx.getFieldError("version"));
      \u0275\u0275advance();
      \u0275\u0275property("required", true)("errorMessage", ctx.getFieldError("url"));
    }
  }, dependencies: [CommonModule, ReactiveFormsModule, \u0275NgNoValidate, NgSelectOption, \u0275NgSelectMultipleOption, DefaultValueAccessor, CheckboxControlValueAccessor, SelectControlValueAccessor, NgControlStatus, NgControlStatusGroup, RequiredValidator, FormGroupDirective, FormControlName, SlideModalComponent, FormInputComponent], styles: ["\n\n.documentation-form[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-lg);\n}\n.form-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: var(--spacing-lg);\n}\n.form-group[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-sm);\n}\n.form-group.full-width[_ngcontent-%COMP%] {\n  grid-column: 1/-1;\n}\n.form-label[_ngcontent-%COMP%] {\n  font-size: var(--text-sm);\n  font-weight: 500;\n  color: var(--secondary-700);\n}\n.form-select[_ngcontent-%COMP%], \n.form-textarea[_ngcontent-%COMP%] {\n  padding: var(--spacing-md);\n  border: 1px solid var(--secondary-300);\n  border-radius: var(--radius-md);\n  background: white;\n  font-size: var(--text-sm);\n  color: var(--secondary-800);\n  transition: all 0.2s ease;\n}\n.form-select[_ngcontent-%COMP%]:focus, \n.form-textarea[_ngcontent-%COMP%]:focus {\n  outline: none;\n  border-color: var(--primary-500);\n  box-shadow: 0 0 0 3px var(--primary-100);\n}\n.form-textarea[_ngcontent-%COMP%] {\n  resize: vertical;\n  min-height: 80px;\n  font-family: inherit;\n}\n.checkbox-label[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: flex-start;\n  gap: var(--spacing-sm);\n  cursor: pointer;\n  padding: var(--spacing-md);\n  border: 1px solid var(--secondary-200);\n  border-radius: var(--radius-md);\n  transition: all 0.2s ease;\n}\n.checkbox-label[_ngcontent-%COMP%]:hover {\n  border-color: var(--primary-300);\n  background: var(--primary-25);\n}\n.checkbox-input[_ngcontent-%COMP%] {\n  margin: 0;\n  margin-top: 2px;\n}\n.checkbox-text[_ngcontent-%COMP%] {\n  font-size: var(--text-sm);\n  font-weight: 500;\n  color: var(--secondary-900);\n  margin-bottom: var(--spacing-xs);\n}\n.checkbox-description[_ngcontent-%COMP%] {\n  font-size: var(--text-xs);\n  color: var(--secondary-600);\n  display: block;\n}\n@media (max-width: 768px) {\n  .form-grid[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-md);\n  }\n}\n/*# sourceMappingURL=documentation-modal.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(DocumentationModalComponent, [{
    type: Component,
    args: [{ selector: "app-documentation-modal", standalone: true, imports: [CommonModule, ReactiveFormsModule, SlideModalComponent, FormInputComponent], template: `
    <app-slide-modal
      [isOpen]="isOpen"
      [title]="editMode ? 'Edit Documentation' : 'Add Documentation'"
      subtitle="Configure documentation details"
      [loading]="loading"
      [canConfirm]="documentationForm.valid"
      confirmText="Save Documentation"
      (closed)="onClose()"
      (confirmed)="onSave()"
      (cancelled)="onClose()"
    >
      <form [formGroup]="documentationForm" class="documentation-form">
        <div class="form-grid">
          <app-form-input
            label="Document Title"
            placeholder="e.g., API Documentation, User Guide"
            formControlName="title"
            [required]="true"
            [errorMessage]="getFieldError('title')"
          ></app-form-input>

          <div class="form-group">
            <label class="form-label">Document Type</label>
            <select formControlName="type" class="form-select">
              <option value="technical_spec">Technical Specification</option>
              <option value="api_documentation">API Documentation</option>
              <option value="user_manual">User Manual</option>
              <option value="installation_guide">Installation Guide</option>
              <option value="troubleshooting">Troubleshooting Guide</option>
              <option value="architecture_diagram">Architecture Diagram</option>
              <option value="deployment_guide">Deployment Guide</option>
              <option value="security_policy">Security Policy</option>
              <option value="runbook">Runbook</option>
              <option value="other">Other</option>
            </select>
          </div>

          <app-form-input
            label="Version"
            placeholder="e.g., 1.0, 2.1.0"
            formControlName="version"
            [required]="true"
            [errorMessage]="getFieldError('version')"
          ></app-form-input>

          <app-form-input
            label="URL or Location"
            placeholder="e.g., https://docs.company.com/api"
            formControlName="url"
            [required]="true"
            [errorMessage]="getFieldError('url')"
          ></app-form-input>

          <div class="form-group full-width">
            <label class="checkbox-label">
              <input 
                type="checkbox" 
                formControlName="isPublic"
                class="checkbox-input"
              >
              <span class="checkbox-text">Public Documentation</span>
              <span class="checkbox-description">This documentation is publicly accessible</span>
            </label>
          </div>

          <div class="form-group full-width">
            <label class="form-label">Description</label>
            <textarea 
              formControlName="description"
              class="form-textarea"
              placeholder="Describe the content and purpose of this documentation..."
              rows="3"
            ></textarea>
          </div>
        </div>
      </form>
    </app-slide-modal>
  `, styles: ["/* angular:styles/component:scss;c7ec6985eb21242eea4f6fd45b944b14dda291ffb8c73e3217e8db64444aa63d;C:/Users/<USER>/Documents/VibeCoding/AppCatalog/app-catalog/frontend/src/app/modules/applications/components/documentation-modal/documentation-modal.component.ts */\n.documentation-form {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-lg);\n}\n.form-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: var(--spacing-lg);\n}\n.form-group {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-sm);\n}\n.form-group.full-width {\n  grid-column: 1/-1;\n}\n.form-label {\n  font-size: var(--text-sm);\n  font-weight: 500;\n  color: var(--secondary-700);\n}\n.form-select,\n.form-textarea {\n  padding: var(--spacing-md);\n  border: 1px solid var(--secondary-300);\n  border-radius: var(--radius-md);\n  background: white;\n  font-size: var(--text-sm);\n  color: var(--secondary-800);\n  transition: all 0.2s ease;\n}\n.form-select:focus,\n.form-textarea:focus {\n  outline: none;\n  border-color: var(--primary-500);\n  box-shadow: 0 0 0 3px var(--primary-100);\n}\n.form-textarea {\n  resize: vertical;\n  min-height: 80px;\n  font-family: inherit;\n}\n.checkbox-label {\n  display: flex;\n  align-items: flex-start;\n  gap: var(--spacing-sm);\n  cursor: pointer;\n  padding: var(--spacing-md);\n  border: 1px solid var(--secondary-200);\n  border-radius: var(--radius-md);\n  transition: all 0.2s ease;\n}\n.checkbox-label:hover {\n  border-color: var(--primary-300);\n  background: var(--primary-25);\n}\n.checkbox-input {\n  margin: 0;\n  margin-top: 2px;\n}\n.checkbox-text {\n  font-size: var(--text-sm);\n  font-weight: 500;\n  color: var(--secondary-900);\n  margin-bottom: var(--spacing-xs);\n}\n.checkbox-description {\n  font-size: var(--text-xs);\n  color: var(--secondary-600);\n  display: block;\n}\n@media (max-width: 768px) {\n  .form-grid {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-md);\n  }\n}\n/*# sourceMappingURL=documentation-modal.component.css.map */\n"] }]
  }], () => [{ type: FormBuilder }], { isOpen: [{
    type: Input
  }], editMode: [{
    type: Input
  }], initialData: [{
    type: Input
  }], loading: [{
    type: Input
  }], closed: [{
    type: Output
  }], saved: [{
    type: Output
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(DocumentationModalComponent, { className: "DocumentationModalComponent", filePath: "src/app/modules/applications/components/documentation-modal/documentation-modal.component.ts", lineNumber: 193 });
})();

// src/app/modules/applications/components/vulnerability-modal/vulnerability-modal.component.ts
var VulnerabilityModalComponent = class _VulnerabilityModalComponent {
  fb;
  isOpen = false;
  editMode = false;
  initialData = null;
  loading = false;
  closed = new EventEmitter();
  saved = new EventEmitter();
  vulnerabilityForm;
  constructor(fb) {
    this.fb = fb;
  }
  ngOnInit() {
    this.initializeForm();
  }
  ngOnChanges() {
    if (this.vulnerabilityForm && this.initialData) {
      this.vulnerabilityForm.patchValue(this.initialData);
    }
  }
  initializeForm() {
    this.vulnerabilityForm = this.fb.group({
      title: ["", [Validators.required, Validators.minLength(5)]],
      severity: ["medium", [Validators.required]],
      cveId: [""],
      cvssScore: [0, [Validators.min(0), Validators.max(10)]],
      status: ["open", [Validators.required]],
      description: ["", [Validators.required, Validators.minLength(10)]]
    });
    if (this.initialData) {
      this.vulnerabilityForm.patchValue(this.initialData);
    }
  }
  getFieldError(fieldName) {
    const field = this.vulnerabilityForm.get(fieldName);
    if (field && field.invalid && field.touched) {
      if (field.errors?.["required"]) {
        return `${fieldName} is required`;
      }
      if (field.errors?.["minlength"]) {
        return `${fieldName} must be at least ${field.errors["minlength"].requiredLength} characters`;
      }
      if (field.errors?.["min"]) {
        return `${fieldName} must be at least ${field.errors["min"].min}`;
      }
      if (field.errors?.["max"]) {
        return `${fieldName} must be at most ${field.errors["max"].max}`;
      }
    }
    return "";
  }
  onClose() {
    this.vulnerabilityForm.reset();
    this.closed.emit();
  }
  onSave() {
    if (this.vulnerabilityForm.valid) {
      this.saved.emit(this.vulnerabilityForm.value);
    }
  }
  static \u0275fac = function VulnerabilityModalComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _VulnerabilityModalComponent)(\u0275\u0275directiveInject(FormBuilder));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _VulnerabilityModalComponent, selectors: [["app-vulnerability-modal"]], inputs: { isOpen: "isOpen", editMode: "editMode", initialData: "initialData", loading: "loading" }, outputs: { closed: "closed", saved: "saved" }, features: [\u0275\u0275NgOnChangesFeature], decls: 36, vars: 9, consts: [["subtitle", "Configure security vulnerability details", "confirmText", "Save Vulnerability", 3, "closed", "confirmed", "cancelled", "isOpen", "title", "loading", "canConfirm"], [1, "vulnerability-form", 3, "formGroup"], [1, "form-grid"], ["label", "Vulnerability Title", "placeholder", "e.g., SQL Injection in User Search", "formControlName", "title", 3, "required", "errorMessage"], [1, "form-group"], [1, "form-label"], ["formControlName", "severity", 1, "form-select"], ["value", "low"], ["value", "medium"], ["value", "high"], ["value", "critical"], ["label", "CVE ID (Optional)", "placeholder", "e.g., CVE-2024-0001", "formControlName", "cveId", 3, "errorMessage"], ["label", "CVSS Score", "placeholder", "e.g., 7.5", "formControlName", "cvssScore", "type", "number", 3, "errorMessage"], ["formControlName", "status", 1, "form-select"], ["value", "open"], ["value", "in_progress"], ["value", "resolved"], ["value", "false_positive"], ["value", "accepted_risk"], [1, "form-group", "full-width"], ["formControlName", "description", "placeholder", "Describe the vulnerability, its impact, and potential remediation steps...", "rows", "4", 1, "form-textarea"]], template: function VulnerabilityModalComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "app-slide-modal", 0);
      \u0275\u0275listener("closed", function VulnerabilityModalComponent_Template_app_slide_modal_closed_0_listener() {
        return ctx.onClose();
      })("confirmed", function VulnerabilityModalComponent_Template_app_slide_modal_confirmed_0_listener() {
        return ctx.onSave();
      })("cancelled", function VulnerabilityModalComponent_Template_app_slide_modal_cancelled_0_listener() {
        return ctx.onClose();
      });
      \u0275\u0275elementStart(1, "form", 1)(2, "div", 2);
      \u0275\u0275element(3, "app-form-input", 3);
      \u0275\u0275elementStart(4, "div", 4)(5, "label", 5);
      \u0275\u0275text(6, "Severity");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(7, "select", 6)(8, "option", 7);
      \u0275\u0275text(9, "Low");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(10, "option", 8);
      \u0275\u0275text(11, "Medium");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(12, "option", 9);
      \u0275\u0275text(13, "High");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(14, "option", 10);
      \u0275\u0275text(15, "Critical");
      \u0275\u0275elementEnd()()();
      \u0275\u0275element(16, "app-form-input", 11)(17, "app-form-input", 12);
      \u0275\u0275elementStart(18, "div", 4)(19, "label", 5);
      \u0275\u0275text(20, "Status");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(21, "select", 13)(22, "option", 14);
      \u0275\u0275text(23, "Open");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(24, "option", 15);
      \u0275\u0275text(25, "In Progress");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(26, "option", 16);
      \u0275\u0275text(27, "Resolved");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(28, "option", 17);
      \u0275\u0275text(29, "False Positive");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(30, "option", 18);
      \u0275\u0275text(31, "Accepted Risk");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(32, "div", 19)(33, "label", 5);
      \u0275\u0275text(34, "Description");
      \u0275\u0275elementEnd();
      \u0275\u0275element(35, "textarea", 20);
      \u0275\u0275elementEnd()()()();
    }
    if (rf & 2) {
      \u0275\u0275property("isOpen", ctx.isOpen)("title", ctx.editMode ? "Edit Vulnerability" : "Add Vulnerability")("loading", ctx.loading)("canConfirm", ctx.vulnerabilityForm.valid);
      \u0275\u0275advance();
      \u0275\u0275property("formGroup", ctx.vulnerabilityForm);
      \u0275\u0275advance(2);
      \u0275\u0275property("required", true)("errorMessage", ctx.getFieldError("title"));
      \u0275\u0275advance(13);
      \u0275\u0275property("errorMessage", ctx.getFieldError("cveId"));
      \u0275\u0275advance();
      \u0275\u0275property("errorMessage", ctx.getFieldError("cvssScore"));
    }
  }, dependencies: [CommonModule, ReactiveFormsModule, \u0275NgNoValidate, NgSelectOption, \u0275NgSelectMultipleOption, DefaultValueAccessor, SelectControlValueAccessor, NgControlStatus, NgControlStatusGroup, RequiredValidator, FormGroupDirective, FormControlName, SlideModalComponent, FormInputComponent], styles: ["\n\n.vulnerability-form[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-lg);\n}\n.form-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: var(--spacing-lg);\n}\n.form-group[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-sm);\n}\n.form-group.full-width[_ngcontent-%COMP%] {\n  grid-column: 1/-1;\n}\n.form-label[_ngcontent-%COMP%] {\n  font-size: var(--text-sm);\n  font-weight: 500;\n  color: var(--secondary-700);\n}\n.form-select[_ngcontent-%COMP%], \n.form-textarea[_ngcontent-%COMP%] {\n  padding: var(--spacing-md);\n  border: 1px solid var(--secondary-300);\n  border-radius: var(--radius-md);\n  background: white;\n  font-size: var(--text-sm);\n  color: var(--secondary-800);\n  transition: all 0.2s ease;\n}\n.form-select[_ngcontent-%COMP%]:focus, \n.form-textarea[_ngcontent-%COMP%]:focus {\n  outline: none;\n  border-color: var(--primary-500);\n  box-shadow: 0 0 0 3px var(--primary-100);\n}\n.form-textarea[_ngcontent-%COMP%] {\n  resize: vertical;\n  min-height: 100px;\n  font-family: inherit;\n}\n@media (max-width: 768px) {\n  .form-grid[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-md);\n  }\n}\n/*# sourceMappingURL=vulnerability-modal.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(VulnerabilityModalComponent, [{
    type: Component,
    args: [{ selector: "app-vulnerability-modal", standalone: true, imports: [CommonModule, ReactiveFormsModule, SlideModalComponent, FormInputComponent], template: `
    <app-slide-modal
      [isOpen]="isOpen"
      [title]="editMode ? 'Edit Vulnerability' : 'Add Vulnerability'"
      subtitle="Configure security vulnerability details"
      [loading]="loading"
      [canConfirm]="vulnerabilityForm.valid"
      confirmText="Save Vulnerability"
      (closed)="onClose()"
      (confirmed)="onSave()"
      (cancelled)="onClose()"
    >
      <form [formGroup]="vulnerabilityForm" class="vulnerability-form">
        <div class="form-grid">
          <app-form-input
            label="Vulnerability Title"
            placeholder="e.g., SQL Injection in User Search"
            formControlName="title"
            [required]="true"
            [errorMessage]="getFieldError('title')"
          ></app-form-input>

          <div class="form-group">
            <label class="form-label">Severity</label>
            <select formControlName="severity" class="form-select">
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="critical">Critical</option>
            </select>
          </div>

          <app-form-input
            label="CVE ID (Optional)"
            placeholder="e.g., CVE-2024-0001"
            formControlName="cveId"
            [errorMessage]="getFieldError('cveId')"
          ></app-form-input>

          <app-form-input
            label="CVSS Score"
            placeholder="e.g., 7.5"
            formControlName="cvssScore"
            type="number"
            [errorMessage]="getFieldError('cvssScore')"
          ></app-form-input>

          <div class="form-group">
            <label class="form-label">Status</label>
            <select formControlName="status" class="form-select">
              <option value="open">Open</option>
              <option value="in_progress">In Progress</option>
              <option value="resolved">Resolved</option>
              <option value="false_positive">False Positive</option>
              <option value="accepted_risk">Accepted Risk</option>
            </select>
          </div>

          <div class="form-group full-width">
            <label class="form-label">Description</label>
            <textarea 
              formControlName="description"
              class="form-textarea"
              placeholder="Describe the vulnerability, its impact, and potential remediation steps..."
              rows="4"
            ></textarea>
          </div>
        </div>
      </form>
    </app-slide-modal>
  `, styles: ["/* angular:styles/component:scss;1924c2363133b99f799eb8e5933add16b3f83089be7bf52afbf253648df2b569;C:/Users/<USER>/Documents/VibeCoding/AppCatalog/app-catalog/frontend/src/app/modules/applications/components/vulnerability-modal/vulnerability-modal.component.ts */\n.vulnerability-form {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-lg);\n}\n.form-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: var(--spacing-lg);\n}\n.form-group {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-sm);\n}\n.form-group.full-width {\n  grid-column: 1/-1;\n}\n.form-label {\n  font-size: var(--text-sm);\n  font-weight: 500;\n  color: var(--secondary-700);\n}\n.form-select,\n.form-textarea {\n  padding: var(--spacing-md);\n  border: 1px solid var(--secondary-300);\n  border-radius: var(--radius-md);\n  background: white;\n  font-size: var(--text-sm);\n  color: var(--secondary-800);\n  transition: all 0.2s ease;\n}\n.form-select:focus,\n.form-textarea:focus {\n  outline: none;\n  border-color: var(--primary-500);\n  box-shadow: 0 0 0 3px var(--primary-100);\n}\n.form-textarea {\n  resize: vertical;\n  min-height: 100px;\n  font-family: inherit;\n}\n@media (max-width: 768px) {\n  .form-grid {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-md);\n  }\n}\n/*# sourceMappingURL=vulnerability-modal.component.css.map */\n"] }]
  }], () => [{ type: FormBuilder }], { isOpen: [{
    type: Input
  }], editMode: [{
    type: Input
  }], initialData: [{
    type: Input
  }], loading: [{
    type: Input
  }], closed: [{
    type: Output
  }], saved: [{
    type: Output
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(VulnerabilityModalComponent, { className: "VulnerabilityModalComponent", filePath: "src/app/modules/applications/components/vulnerability-modal/vulnerability-modal.component.ts", lineNumber: 151 });
})();

// src/app/modules/applications/application-form/application-form.component.ts
var _c02 = ["tabsComponent"];
function ApplicationFormComponent_div_35_div_1_span_15_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 47);
    \u0275\u0275text(1, "Internal");
    \u0275\u0275elementEnd();
  }
}
function ApplicationFormComponent_div_35_div_1_p_16_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p", 48);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const dep_r5 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(dep_r5.description);
  }
}
function ApplicationFormComponent_div_35_div_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 36)(1, "div", 37)(2, "h4", 38);
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "button", 39);
    \u0275\u0275listener("click", function ApplicationFormComponent_div_35_div_1_Template_button_click_4_listener() {
      const i_r3 = \u0275\u0275restoreView(_r2).index;
      const ctx_r3 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r3.removeDependency(i_r3));
    });
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(5, "svg", 40);
    \u0275\u0275element(6, "polyline", 41)(7, "path", 42);
    \u0275\u0275elementEnd()()();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(8, "div", 43)(9, "span");
    \u0275\u0275text(10);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(11, "span");
    \u0275\u0275text(12);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(13, "span", 44);
    \u0275\u0275text(14);
    \u0275\u0275elementEnd();
    \u0275\u0275template(15, ApplicationFormComponent_div_35_div_1_span_15_Template, 2, 0, "span", 45);
    \u0275\u0275elementEnd();
    \u0275\u0275template(16, ApplicationFormComponent_div_35_div_1_p_16_Template, 2, 1, "p", 46);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const dep_r5 = ctx.$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(dep_r5.name);
    \u0275\u0275advance(6);
    \u0275\u0275classMapInterpolate1("entity-badge type-", dep_r5.type, "");
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(dep_r5.type);
    \u0275\u0275advance();
    \u0275\u0275classMapInterpolate1("entity-badge criticality-", dep_r5.criticality, "");
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(dep_r5.criticality);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1("v", dep_r5.version, "");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", dep_r5.isInternal);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", dep_r5.description);
  }
}
function ApplicationFormComponent_div_35_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 34);
    \u0275\u0275template(1, ApplicationFormComponent_div_35_div_1_Template, 17, 12, "div", 35);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r3 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngForOf", ctx_r3.dependencies);
  }
}
function ApplicationFormComponent_div_36_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 49)(1, "p");
    \u0275\u0275text(2, 'No dependencies added yet. Click "Add Dependency" to get started.');
    \u0275\u0275elementEnd()();
  }
}
function ApplicationFormComponent_div_44_div_1_span_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 47);
    \u0275\u0275text(1, "Core");
    \u0275\u0275elementEnd();
  }
}
function ApplicationFormComponent_div_44_div_1_p_14_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p", 48);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const tech_r8 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(tech_r8.purpose);
  }
}
function ApplicationFormComponent_div_44_div_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r6 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 36)(1, "div", 37)(2, "h4", 38);
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "button", 39);
    \u0275\u0275listener("click", function ApplicationFormComponent_div_44_div_1_Template_button_click_4_listener() {
      const i_r7 = \u0275\u0275restoreView(_r6).index;
      const ctx_r3 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r3.removeTechStack(i_r7));
    });
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(5, "svg", 40);
    \u0275\u0275element(6, "polyline", 41)(7, "path", 42);
    \u0275\u0275elementEnd()()();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(8, "div", 43)(9, "span", 47);
    \u0275\u0275text(10);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(11, "span", 44);
    \u0275\u0275text(12);
    \u0275\u0275elementEnd();
    \u0275\u0275template(13, ApplicationFormComponent_div_44_div_1_span_13_Template, 2, 0, "span", 45);
    \u0275\u0275elementEnd();
    \u0275\u0275template(14, ApplicationFormComponent_div_44_div_1_p_14_Template, 2, 1, "p", 46);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const tech_r8 = ctx.$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(tech_r8.technology);
    \u0275\u0275advance(7);
    \u0275\u0275textInterpolate(tech_r8.category);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1("v", tech_r8.version, "");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", tech_r8.isCore);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", tech_r8.purpose);
  }
}
function ApplicationFormComponent_div_44_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 34);
    \u0275\u0275template(1, ApplicationFormComponent_div_44_div_1_Template, 15, 5, "div", 35);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r3 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngForOf", ctx_r3.techStack);
  }
}
function ApplicationFormComponent_div_45_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 49)(1, "p");
    \u0275\u0275text(2, 'No technologies added yet. Click "Add Technology" to get started.');
    \u0275\u0275elementEnd()();
  }
}
function ApplicationFormComponent_div_53_div_1_span_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 47);
    \u0275\u0275text(1, "Primary Contact");
    \u0275\u0275elementEnd();
  }
}
function ApplicationFormComponent_div_53_div_1_p_14_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p", 48);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const stakeholder_r11 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(stakeholder_r11.email);
  }
}
function ApplicationFormComponent_div_53_div_1_p_15_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p", 48);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const stakeholder_r11 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(stakeholder_r11.responsibility);
  }
}
function ApplicationFormComponent_div_53_div_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r9 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 36)(1, "div", 37)(2, "h4", 38);
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "button", 39);
    \u0275\u0275listener("click", function ApplicationFormComponent_div_53_div_1_Template_button_click_4_listener() {
      const i_r10 = \u0275\u0275restoreView(_r9).index;
      const ctx_r3 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r3.removeStakeholder(i_r10));
    });
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(5, "svg", 40);
    \u0275\u0275element(6, "polyline", 41)(7, "path", 42);
    \u0275\u0275elementEnd()()();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(8, "div", 43)(9, "span");
    \u0275\u0275text(10);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(11, "span", 47);
    \u0275\u0275text(12);
    \u0275\u0275elementEnd();
    \u0275\u0275template(13, ApplicationFormComponent_div_53_div_1_span_13_Template, 2, 0, "span", 45);
    \u0275\u0275elementEnd();
    \u0275\u0275template(14, ApplicationFormComponent_div_53_div_1_p_14_Template, 2, 1, "p", 46)(15, ApplicationFormComponent_div_53_div_1_p_15_Template, 2, 1, "p", 46);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const stakeholder_r11 = ctx.$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(stakeholder_r11.name);
    \u0275\u0275advance(6);
    \u0275\u0275classMapInterpolate1("entity-badge role-", stakeholder_r11.role, "");
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(stakeholder_r11.role.replace("_", " "));
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(stakeholder_r11.department);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", stakeholder_r11.isPrimary);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", stakeholder_r11.email);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", stakeholder_r11.responsibility);
  }
}
function ApplicationFormComponent_div_53_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 34);
    \u0275\u0275template(1, ApplicationFormComponent_div_53_div_1_Template, 16, 9, "div", 35);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r3 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngForOf", ctx_r3.stakeholders);
  }
}
function ApplicationFormComponent_div_54_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 49)(1, "p");
    \u0275\u0275text(2, 'No stakeholders added yet. Click "Add Stakeholder" to get started.');
    \u0275\u0275elementEnd()();
  }
}
function ApplicationFormComponent_div_62_div_1_span_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 47);
    \u0275\u0275text(1, "Public");
    \u0275\u0275elementEnd();
  }
}
function ApplicationFormComponent_div_62_div_1_a_14_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "a", 51);
    \u0275\u0275text(1, "View Document");
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const doc_r14 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275property("href", doc_r14.url, \u0275\u0275sanitizeUrl);
  }
}
function ApplicationFormComponent_div_62_div_1_p_15_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p", 48);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const doc_r14 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(doc_r14.description);
  }
}
function ApplicationFormComponent_div_62_div_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r12 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 36)(1, "div", 37)(2, "h4", 38);
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "button", 39);
    \u0275\u0275listener("click", function ApplicationFormComponent_div_62_div_1_Template_button_click_4_listener() {
      const i_r13 = \u0275\u0275restoreView(_r12).index;
      const ctx_r3 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r3.removeDocumentation(i_r13));
    });
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(5, "svg", 40);
    \u0275\u0275element(6, "polyline", 41)(7, "path", 42);
    \u0275\u0275elementEnd()()();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(8, "div", 43)(9, "span", 47);
    \u0275\u0275text(10);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(11, "span", 44);
    \u0275\u0275text(12);
    \u0275\u0275elementEnd();
    \u0275\u0275template(13, ApplicationFormComponent_div_62_div_1_span_13_Template, 2, 0, "span", 45)(14, ApplicationFormComponent_div_62_div_1_a_14_Template, 2, 1, "a", 50);
    \u0275\u0275elementEnd();
    \u0275\u0275template(15, ApplicationFormComponent_div_62_div_1_p_15_Template, 2, 1, "p", 46);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const doc_r14 = ctx.$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(doc_r14.title);
    \u0275\u0275advance(7);
    \u0275\u0275textInterpolate(doc_r14.type.replace("_", " "));
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1("v", doc_r14.version, "");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", doc_r14.isPublic);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", doc_r14.url);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", doc_r14.description);
  }
}
function ApplicationFormComponent_div_62_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 34);
    \u0275\u0275template(1, ApplicationFormComponent_div_62_div_1_Template, 16, 6, "div", 35);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r3 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngForOf", ctx_r3.documentation);
  }
}
function ApplicationFormComponent_div_63_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 49)(1, "p");
    \u0275\u0275text(2, 'No documentation added yet. Click "Add Document" to get started.');
    \u0275\u0275elementEnd()();
  }
}
function ApplicationFormComponent_div_71_div_1_span_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 44);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const vuln_r17 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1("CVSS: ", vuln_r17.cvssScore, "");
  }
}
function ApplicationFormComponent_div_71_div_1_span_14_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 47);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const vuln_r17 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(vuln_r17.cveId);
  }
}
function ApplicationFormComponent_div_71_div_1_p_15_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p", 48);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const vuln_r17 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(vuln_r17.description);
  }
}
function ApplicationFormComponent_div_71_div_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r15 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 36)(1, "div", 37)(2, "h4", 38);
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "button", 39);
    \u0275\u0275listener("click", function ApplicationFormComponent_div_71_div_1_Template_button_click_4_listener() {
      const i_r16 = \u0275\u0275restoreView(_r15).index;
      const ctx_r3 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r3.removeVulnerability(i_r16));
    });
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(5, "svg", 40);
    \u0275\u0275element(6, "polyline", 41)(7, "path", 42);
    \u0275\u0275elementEnd()()();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(8, "div", 43)(9, "span");
    \u0275\u0275text(10);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(11, "span", 47);
    \u0275\u0275text(12);
    \u0275\u0275elementEnd();
    \u0275\u0275template(13, ApplicationFormComponent_div_71_div_1_span_13_Template, 2, 1, "span", 52)(14, ApplicationFormComponent_div_71_div_1_span_14_Template, 2, 1, "span", 45);
    \u0275\u0275elementEnd();
    \u0275\u0275template(15, ApplicationFormComponent_div_71_div_1_p_15_Template, 2, 1, "p", 46);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const vuln_r17 = ctx.$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(vuln_r17.title);
    \u0275\u0275advance(6);
    \u0275\u0275classMapInterpolate1("entity-badge criticality-", vuln_r17.severity, "");
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(vuln_r17.severity);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(vuln_r17.status.replace("_", " "));
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", vuln_r17.cvssScore > 0);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", vuln_r17.cveId);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", vuln_r17.description);
  }
}
function ApplicationFormComponent_div_71_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 34);
    \u0275\u0275template(1, ApplicationFormComponent_div_71_div_1_Template, 16, 9, "div", 35);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r3 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngForOf", ctx_r3.vulnerabilities);
  }
}
function ApplicationFormComponent_div_72_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 49)(1, "p");
    \u0275\u0275text(2, 'No vulnerabilities added yet. Click "Add Vulnerability" to get started.');
    \u0275\u0275elementEnd()();
  }
}
var ApplicationFormComponent = class _ApplicationFormComponent {
  fb;
  route;
  tabsComponent;
  isEditMode = false;
  isSaving = false;
  applicationId = null;
  // Form and data
  applicationForm;
  activeTabId = "basic";
  // Tab configuration
  tabItems = [
    { id: "basic", label: "Basic Information", icon: "M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" },
    { id: "dependencies", label: "Dependencies", icon: "M8 16l2.879-2.879m0 0a3 3 0 104.243-4.242 3 3 0 00-4.243 4.242zM21 12a9 9 0 11-18 0 9 9 0 0118 0z" },
    { id: "techstack", label: "Tech Stack", icon: "M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" },
    { id: "stakeholders", label: "Stakeholders", icon: "M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a4 4 0 11-8 0 4 4 0 018 0z" },
    { id: "documentation", label: "Documentation", icon: "M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" },
    { id: "security", label: "Security", icon: "M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" }
  ];
  // Dynamic arrays for related entities
  dependencies = [];
  techStack = [];
  stakeholders = [];
  documentation = [];
  vulnerabilities = [];
  // Modal state
  showDependencyModal = false;
  dependencyEditMode = false;
  dependencyEditData = null;
  dependencyEditIndex = -1;
  showTechStackModal = false;
  techStackEditMode = false;
  techStackEditData = null;
  techStackEditIndex = -1;
  showStakeholderModal = false;
  stakeholderEditMode = false;
  stakeholderEditData = null;
  stakeholderEditIndex = -1;
  showDocumentationModal = false;
  documentationEditMode = false;
  documentationEditData = null;
  documentationEditIndex = -1;
  showVulnerabilityModal = false;
  vulnerabilityEditMode = false;
  vulnerabilityEditData = null;
  vulnerabilityEditIndex = -1;
  modalLoading = false;
  // Dropdown options
  lifecycleStatusOptions = [
    { value: "planning", label: "Planning" },
    { value: "development", label: "Development" },
    { value: "testing", label: "Testing" },
    { value: "staging", label: "Staging" },
    { value: "production", label: "Production" },
    { value: "maintenance", label: "Maintenance" },
    { value: "deprecated", label: "Deprecated" },
    { value: "retired", label: "Retired" }
  ];
  businessCriticalityOptions = [
    { value: "low", label: "Low" },
    { value: "medium", label: "Medium" },
    { value: "high", label: "High" },
    { value: "critical", label: "Critical" }
  ];
  dependencyTypeOptions = [
    { value: "library", label: "Library" },
    { value: "framework", label: "Framework" },
    { value: "service", label: "Service" },
    { value: "database", label: "Database" },
    { value: "api", label: "API" },
    { value: "tool", label: "Tool" },
    { value: "infrastructure", label: "Infrastructure" }
  ];
  dependencyCriticalityOptions = [
    { value: "low", label: "Low" },
    { value: "medium", label: "Medium" },
    { value: "high", label: "High" },
    { value: "critical", label: "Critical" }
  ];
  techStackCategoryOptions = [
    { value: "frontend", label: "Frontend" },
    { value: "backend", label: "Backend" },
    { value: "database", label: "Database" },
    { value: "infrastructure", label: "Infrastructure" },
    { value: "monitoring", label: "Monitoring" },
    { value: "security", label: "Security" },
    { value: "testing", label: "Testing" },
    { value: "deployment", label: "Deployment" },
    { value: "communication", label: "Communication" }
  ];
  stakeholderRoleOptions = [
    { value: "product_owner", label: "Product Owner" },
    { value: "tech_lead", label: "Tech Lead" },
    { value: "developer", label: "Developer" },
    { value: "devops_engineer", label: "DevOps Engineer" },
    { value: "security_officer", label: "Security Officer" },
    { value: "business_analyst", label: "Business Analyst" },
    { value: "project_manager", label: "Project Manager" },
    { value: "architect", label: "Architect" },
    { value: "qa_engineer", label: "QA Engineer" },
    { value: "support_engineer", label: "Support Engineer" }
  ];
  documentationTypeOptions = [
    { value: "technical_spec", label: "Technical Specification" },
    { value: "api_documentation", label: "API Documentation" },
    { value: "user_manual", label: "User Manual" },
    { value: "deployment_guide", label: "Deployment Guide" },
    { value: "architecture_diagram", label: "Architecture Diagram" },
    { value: "security_policy", label: "Security Policy" },
    { value: "compliance_report", label: "Compliance Report" },
    { value: "runbook", label: "Runbook" },
    { value: "troubleshooting", label: "Troubleshooting" },
    { value: "changelog", label: "Changelog" }
  ];
  vulnerabilitySeverityOptions = [
    { value: "critical", label: "Critical" },
    { value: "high", label: "High" },
    { value: "medium", label: "Medium" },
    { value: "low", label: "Low" },
    { value: "info", label: "Info" }
  ];
  vulnerabilityStatusOptions = [
    { value: "open", label: "Open" },
    { value: "in_progress", label: "In Progress" },
    { value: "resolved", label: "Resolved" },
    { value: "accepted_risk", label: "Accepted Risk" },
    { value: "false_positive", label: "False Positive" }
  ];
  constructor(fb, route) {
    this.fb = fb;
    this.route = route;
  }
  ngOnInit() {
    this.initializeForm();
    this.checkEditMode();
  }
  initializeForm() {
    this.applicationForm = this.fb.group({
      name: ["", [Validators.required, Validators.minLength(2)]],
      description: ["", [Validators.required, Validators.minLength(10)]],
      owner: ["", [Validators.required]],
      lifecycleStatus: ["", [Validators.required]],
      businessCriticality: ["", [Validators.required]],
      version: ["", [Validators.required]],
      repository: [""],
      deploymentUrl: [""]
    });
  }
  checkEditMode() {
    this.route.params.subscribe((params) => {
      if (params["id"]) {
        this.isEditMode = true;
        this.applicationId = +params["id"];
        this.loadApplication(this.applicationId);
      }
    });
  }
  loadApplication(id) {
    this.applicationForm.patchValue({
      name: "E-commerce Platform",
      description: "Main customer-facing e-commerce application with shopping cart, payment processing, and order management.",
      owner: "John Doe",
      lifecycleStatus: "production",
      businessCriticality: "critical",
      version: "2.1.0",
      repository: "https://github.com/company/ecommerce",
      deploymentUrl: "https://shop.company.com"
    });
    this.dependencies = [
      { name: "Angular", type: "framework", version: "16.2.0", criticality: "high", isInternal: false, description: "Frontend framework" }
    ];
  }
  onTabChange(tabId) {
    this.activeTabId = tabId;
  }
  getFieldError(fieldName) {
    const field = this.applicationForm.get(fieldName);
    if (field && field.invalid && field.touched) {
      if (field.errors?.["required"]) {
        return `${fieldName} is required`;
      }
      if (field.errors?.["minlength"]) {
        return `${fieldName} must be at least ${field.errors["minlength"].requiredLength} characters`;
      }
      if (field.errors?.["email"]) {
        return "Please enter a valid email address";
      }
      if (field.errors?.["url"]) {
        return "Please enter a valid URL";
      }
    }
    return "";
  }
  // Modal methods for dependencies
  openDependencyModal() {
    this.dependencyEditMode = false;
    this.dependencyEditData = null;
    this.dependencyEditIndex = -1;
    this.showDependencyModal = true;
  }
  closeDependencyModal() {
    this.showDependencyModal = false;
    this.dependencyEditMode = false;
    this.dependencyEditData = null;
    this.dependencyEditIndex = -1;
  }
  saveDependency(data) {
    if (this.dependencyEditMode && this.dependencyEditIndex >= 0) {
      this.dependencies[this.dependencyEditIndex] = data;
    } else {
      this.dependencies.push(data);
    }
    this.closeDependencyModal();
  }
  removeDependency(index) {
    this.dependencies.splice(index, 1);
  }
  // Modal methods for tech stack
  openTechStackModal() {
    this.techStackEditMode = false;
    this.techStackEditData = null;
    this.techStackEditIndex = -1;
    this.showTechStackModal = true;
  }
  closeTechStackModal() {
    this.showTechStackModal = false;
    this.techStackEditMode = false;
    this.techStackEditData = null;
    this.techStackEditIndex = -1;
  }
  saveTechStack(data) {
    if (this.techStackEditMode && this.techStackEditIndex >= 0) {
      this.techStack[this.techStackEditIndex] = data;
    } else {
      this.techStack.push(data);
    }
    this.closeTechStackModal();
  }
  removeTechStack(index) {
    this.techStack.splice(index, 1);
  }
  // Modal methods for stakeholders
  openStakeholderModal() {
    this.stakeholderEditMode = false;
    this.stakeholderEditData = null;
    this.stakeholderEditIndex = -1;
    this.showStakeholderModal = true;
  }
  closeStakeholderModal() {
    this.showStakeholderModal = false;
    this.stakeholderEditMode = false;
    this.stakeholderEditData = null;
    this.stakeholderEditIndex = -1;
  }
  saveStakeholder(data) {
    if (this.stakeholderEditMode && this.stakeholderEditIndex >= 0) {
      this.stakeholders[this.stakeholderEditIndex] = data;
    } else {
      this.stakeholders.push(data);
    }
    this.closeStakeholderModal();
  }
  removeStakeholder(index) {
    this.stakeholders.splice(index, 1);
  }
  // Modal methods for documentation
  openDocumentationModal() {
    this.documentationEditMode = false;
    this.documentationEditData = null;
    this.documentationEditIndex = -1;
    this.showDocumentationModal = true;
  }
  closeDocumentationModal() {
    this.showDocumentationModal = false;
    this.documentationEditMode = false;
    this.documentationEditData = null;
    this.documentationEditIndex = -1;
  }
  saveDocumentation(data) {
    if (this.documentationEditMode && this.documentationEditIndex >= 0) {
      this.documentation[this.documentationEditIndex] = data;
    } else {
      this.documentation.push(data);
    }
    this.closeDocumentationModal();
  }
  removeDocumentation(index) {
    this.documentation.splice(index, 1);
  }
  // Modal methods for vulnerabilities
  openVulnerabilityModal() {
    this.vulnerabilityEditMode = false;
    this.vulnerabilityEditData = null;
    this.vulnerabilityEditIndex = -1;
    this.showVulnerabilityModal = true;
  }
  closeVulnerabilityModal() {
    this.showVulnerabilityModal = false;
    this.vulnerabilityEditMode = false;
    this.vulnerabilityEditData = null;
    this.vulnerabilityEditIndex = -1;
  }
  saveVulnerability(data) {
    if (this.vulnerabilityEditMode && this.vulnerabilityEditIndex >= 0) {
      this.vulnerabilities[this.vulnerabilityEditIndex] = data;
    } else {
      this.vulnerabilities.push(data);
    }
    this.closeVulnerabilityModal();
  }
  removeVulnerability(index) {
    this.vulnerabilities.splice(index, 1);
  }
  saveApplication() {
    if (this.applicationForm.valid) {
      this.isSaving = true;
      const applicationData = __spreadProps(__spreadValues({}, this.applicationForm.value), {
        dependencies: this.dependencies,
        techStack: this.techStack,
        stakeholders: this.stakeholders,
        documentation: this.documentation,
        vulnerabilities: this.vulnerabilities
      });
      console.log("Saving application:", applicationData);
      setTimeout(() => {
        this.isSaving = false;
        console.log("Application saved successfully!");
      }, 2e3);
    } else {
      this.markFormGroupTouched(this.applicationForm);
      this.switchToFirstErrorTab();
    }
  }
  markFormGroupTouched(formGroup) {
    Object.keys(formGroup.controls).forEach((key) => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }
  switchToFirstErrorTab() {
    if (this.applicationForm.invalid) {
      this.activeTabId = "basic";
      this.tabsComponent?.setActiveTab("basic");
      return;
    }
  }
  static \u0275fac = function ApplicationFormComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ApplicationFormComponent)(\u0275\u0275directiveInject(FormBuilder), \u0275\u0275directiveInject(ActivatedRoute));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _ApplicationFormComponent, selectors: [["app-application-form"]], viewQuery: function ApplicationFormComponent_Query(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275viewQuery(_c02, 5);
    }
    if (rf & 2) {
      let _t;
      \u0275\u0275queryRefresh(_t = \u0275\u0275loadQuery()) && (ctx.tabsComponent = _t.first);
    }
  }, decls: 78, vars: 56, consts: [["tabsComponent", ""], [1, "application-form-page"], [1, "page-header"], [1, "header-content"], [1, "header-info"], [1, "page-title"], [1, "page-subtitle"], [1, "header-actions"], ["variant", "outline", "routerLink", "/applications"], ["variant", "primary", 3, "clicked", "loading"], [1, "form-content"], [3, "ngSubmit", "formGroup"], [3, "tabChange", "tabs", "activeTabId"], ["id", "basic", "label", "Basic Information"], [1, "form-section"], [1, "form-grid"], ["label", "Application Name", "placeholder", "Enter application name", "formControlName", "name", 3, "required", "errorMessage"], ["label", "Owner", "placeholder", "Enter owner name", "formControlName", "owner", 3, "required", "errorMessage"], ["type", "select", "label", "Lifecycle Status", "placeholder", "Select status", "formControlName", "lifecycleStatus", 3, "options", "required", "errorMessage"], ["type", "select", "label", "Business Criticality", "placeholder", "Select criticality", "formControlName", "businessCriticality", 3, "options", "required", "errorMessage"], ["label", "Version", "placeholder", "e.g., 1.0.0", "formControlName", "version", 3, "required", "errorMessage"], ["type", "url", "label", "Repository URL", "placeholder", "https://github.com/...", "formControlName", "repository", 3, "errorMessage"], ["type", "url", "label", "Deployment URL", "placeholder", "https://app.company.com", "formControlName", "deploymentUrl", 3, "errorMessage"], ["type", "textarea", "label", "Description", "placeholder", "Describe the application's purpose and functionality", "formControlName", "description", 3, "required", "rows", "maxlength", "showCharacterCount", "errorMessage"], ["id", "dependencies", "label", "Dependencies"], [1, "section-header"], ["variant", "outline", "size", "sm", 3, "clicked"], ["class", "entity-list", 4, "ngIf"], ["class", "empty-state", 4, "ngIf"], ["id", "techstack", "label", "Tech Stack"], ["id", "stakeholders", "label", "Stakeholders"], ["id", "documentation", "label", "Documentation"], ["id", "security", "label", "Security"], [3, "closed", "saved", "isOpen", "editMode", "initialData", "loading"], [1, "entity-list"], ["class", "entity-item", 4, "ngFor", "ngForOf"], [1, "entity-item"], [1, "entity-header"], [1, "entity-title"], ["type", "button", 1, "remove-button", 3, "click"], ["viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor"], ["points", "3,6 5,6 21,6"], ["d", "m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"], [1, "entity-meta"], [1, "entity-version"], ["class", "entity-badge", 4, "ngIf"], ["class", "entity-description", 4, "ngIf"], [1, "entity-badge"], [1, "entity-description"], [1, "empty-state"], ["target", "_blank", "class", "entity-link", 3, "href", 4, "ngIf"], ["target", "_blank", 1, "entity-link", 3, "href"], ["class", "entity-version", 4, "ngIf"]], template: function ApplicationFormComponent_Template(rf, ctx) {
    if (rf & 1) {
      const _r1 = \u0275\u0275getCurrentView();
      \u0275\u0275elementStart(0, "div", 1)(1, "div", 2)(2, "div", 3)(3, "div", 4)(4, "h1", 5);
      \u0275\u0275text(5);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(6, "p", 6);
      \u0275\u0275text(7);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(8, "div", 7)(9, "app-button", 8);
      \u0275\u0275text(10, " Cancel ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(11, "app-button", 9);
      \u0275\u0275listener("clicked", function ApplicationFormComponent_Template_app_button_clicked_11_listener() {
        \u0275\u0275restoreView(_r1);
        return \u0275\u0275resetView(ctx.saveApplication());
      });
      \u0275\u0275text(12);
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(13, "div", 10)(14, "form", 11);
      \u0275\u0275listener("ngSubmit", function ApplicationFormComponent_Template_form_ngSubmit_14_listener() {
        \u0275\u0275restoreView(_r1);
        return \u0275\u0275resetView(ctx.saveApplication());
      });
      \u0275\u0275elementStart(15, "app-tabs", 12, 0);
      \u0275\u0275listener("tabChange", function ApplicationFormComponent_Template_app_tabs_tabChange_15_listener($event) {
        \u0275\u0275restoreView(_r1);
        return \u0275\u0275resetView(ctx.onTabChange($event));
      });
      \u0275\u0275elementStart(17, "app-tab", 13)(18, "div", 14)(19, "div", 15);
      \u0275\u0275element(20, "app-form-input", 16)(21, "app-form-input", 17)(22, "app-form-input", 18)(23, "app-form-input", 19)(24, "app-form-input", 20)(25, "app-form-input", 21)(26, "app-form-input", 22);
      \u0275\u0275elementEnd();
      \u0275\u0275element(27, "app-form-input", 23);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(28, "app-tab", 24)(29, "div", 14)(30, "div", 25)(31, "h3");
      \u0275\u0275text(32, "Application Dependencies");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(33, "app-button", 26);
      \u0275\u0275listener("clicked", function ApplicationFormComponent_Template_app_button_clicked_33_listener() {
        \u0275\u0275restoreView(_r1);
        return \u0275\u0275resetView(ctx.openDependencyModal());
      });
      \u0275\u0275text(34, " Add Dependency ");
      \u0275\u0275elementEnd()();
      \u0275\u0275template(35, ApplicationFormComponent_div_35_Template, 2, 1, "div", 27)(36, ApplicationFormComponent_div_36_Template, 3, 0, "div", 28);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(37, "app-tab", 29)(38, "div", 14)(39, "div", 25)(40, "h3");
      \u0275\u0275text(41, "Technology Stack");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(42, "app-button", 26);
      \u0275\u0275listener("clicked", function ApplicationFormComponent_Template_app_button_clicked_42_listener() {
        \u0275\u0275restoreView(_r1);
        return \u0275\u0275resetView(ctx.openTechStackModal());
      });
      \u0275\u0275text(43, " Add Technology ");
      \u0275\u0275elementEnd()();
      \u0275\u0275template(44, ApplicationFormComponent_div_44_Template, 2, 1, "div", 27)(45, ApplicationFormComponent_div_45_Template, 3, 0, "div", 28);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(46, "app-tab", 30)(47, "div", 14)(48, "div", 25)(49, "h3");
      \u0275\u0275text(50, "Stakeholders & Teams");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(51, "app-button", 26);
      \u0275\u0275listener("clicked", function ApplicationFormComponent_Template_app_button_clicked_51_listener() {
        \u0275\u0275restoreView(_r1);
        return \u0275\u0275resetView(ctx.openStakeholderModal());
      });
      \u0275\u0275text(52, " Add Stakeholder ");
      \u0275\u0275elementEnd()();
      \u0275\u0275template(53, ApplicationFormComponent_div_53_Template, 2, 1, "div", 27)(54, ApplicationFormComponent_div_54_Template, 3, 0, "div", 28);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(55, "app-tab", 31)(56, "div", 14)(57, "div", 25)(58, "h3");
      \u0275\u0275text(59, "Documentation & Resources");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(60, "app-button", 26);
      \u0275\u0275listener("clicked", function ApplicationFormComponent_Template_app_button_clicked_60_listener() {
        \u0275\u0275restoreView(_r1);
        return \u0275\u0275resetView(ctx.openDocumentationModal());
      });
      \u0275\u0275text(61, " Add Document ");
      \u0275\u0275elementEnd()();
      \u0275\u0275template(62, ApplicationFormComponent_div_62_Template, 2, 1, "div", 27)(63, ApplicationFormComponent_div_63_Template, 3, 0, "div", 28);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(64, "app-tab", 32)(65, "div", 14)(66, "div", 25)(67, "h3");
      \u0275\u0275text(68, "Security & Vulnerabilities");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(69, "app-button", 26);
      \u0275\u0275listener("clicked", function ApplicationFormComponent_Template_app_button_clicked_69_listener() {
        \u0275\u0275restoreView(_r1);
        return \u0275\u0275resetView(ctx.openVulnerabilityModal());
      });
      \u0275\u0275text(70, " Add Vulnerability ");
      \u0275\u0275elementEnd()();
      \u0275\u0275template(71, ApplicationFormComponent_div_71_Template, 2, 1, "div", 27)(72, ApplicationFormComponent_div_72_Template, 3, 0, "div", 28);
      \u0275\u0275elementEnd()()()()();
      \u0275\u0275elementStart(73, "app-dependency-modal", 33);
      \u0275\u0275listener("closed", function ApplicationFormComponent_Template_app_dependency_modal_closed_73_listener() {
        \u0275\u0275restoreView(_r1);
        return \u0275\u0275resetView(ctx.closeDependencyModal());
      })("saved", function ApplicationFormComponent_Template_app_dependency_modal_saved_73_listener($event) {
        \u0275\u0275restoreView(_r1);
        return \u0275\u0275resetView(ctx.saveDependency($event));
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(74, "app-tech-stack-modal", 33);
      \u0275\u0275listener("closed", function ApplicationFormComponent_Template_app_tech_stack_modal_closed_74_listener() {
        \u0275\u0275restoreView(_r1);
        return \u0275\u0275resetView(ctx.closeTechStackModal());
      })("saved", function ApplicationFormComponent_Template_app_tech_stack_modal_saved_74_listener($event) {
        \u0275\u0275restoreView(_r1);
        return \u0275\u0275resetView(ctx.saveTechStack($event));
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(75, "app-stakeholder-modal", 33);
      \u0275\u0275listener("closed", function ApplicationFormComponent_Template_app_stakeholder_modal_closed_75_listener() {
        \u0275\u0275restoreView(_r1);
        return \u0275\u0275resetView(ctx.closeStakeholderModal());
      })("saved", function ApplicationFormComponent_Template_app_stakeholder_modal_saved_75_listener($event) {
        \u0275\u0275restoreView(_r1);
        return \u0275\u0275resetView(ctx.saveStakeholder($event));
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(76, "app-documentation-modal", 33);
      \u0275\u0275listener("closed", function ApplicationFormComponent_Template_app_documentation_modal_closed_76_listener() {
        \u0275\u0275restoreView(_r1);
        return \u0275\u0275resetView(ctx.closeDocumentationModal());
      })("saved", function ApplicationFormComponent_Template_app_documentation_modal_saved_76_listener($event) {
        \u0275\u0275restoreView(_r1);
        return \u0275\u0275resetView(ctx.saveDocumentation($event));
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(77, "app-vulnerability-modal", 33);
      \u0275\u0275listener("closed", function ApplicationFormComponent_Template_app_vulnerability_modal_closed_77_listener() {
        \u0275\u0275restoreView(_r1);
        return \u0275\u0275resetView(ctx.closeVulnerabilityModal());
      })("saved", function ApplicationFormComponent_Template_app_vulnerability_modal_saved_77_listener($event) {
        \u0275\u0275restoreView(_r1);
        return \u0275\u0275resetView(ctx.saveVulnerability($event));
      });
      \u0275\u0275elementEnd()();
    }
    if (rf & 2) {
      \u0275\u0275advance(5);
      \u0275\u0275textInterpolate(ctx.isEditMode ? "Edit Application" : "New Application");
      \u0275\u0275advance(2);
      \u0275\u0275textInterpolate1(" ", ctx.isEditMode ? "Update application details and configuration." : "Register a new application in the catalog.", " ");
      \u0275\u0275advance(4);
      \u0275\u0275property("loading", ctx.isSaving);
      \u0275\u0275advance();
      \u0275\u0275textInterpolate1(" ", ctx.isEditMode ? "Update" : "Create", " Application ");
      \u0275\u0275advance(2);
      \u0275\u0275property("formGroup", ctx.applicationForm);
      \u0275\u0275advance();
      \u0275\u0275property("tabs", ctx.tabItems)("activeTabId", ctx.activeTabId);
      \u0275\u0275advance(5);
      \u0275\u0275property("required", true)("errorMessage", ctx.getFieldError("name"));
      \u0275\u0275advance();
      \u0275\u0275property("required", true)("errorMessage", ctx.getFieldError("owner"));
      \u0275\u0275advance();
      \u0275\u0275property("options", ctx.lifecycleStatusOptions)("required", true)("errorMessage", ctx.getFieldError("lifecycleStatus"));
      \u0275\u0275advance();
      \u0275\u0275property("options", ctx.businessCriticalityOptions)("required", true)("errorMessage", ctx.getFieldError("businessCriticality"));
      \u0275\u0275advance();
      \u0275\u0275property("required", true)("errorMessage", ctx.getFieldError("version"));
      \u0275\u0275advance();
      \u0275\u0275property("errorMessage", ctx.getFieldError("repository"));
      \u0275\u0275advance();
      \u0275\u0275property("errorMessage", ctx.getFieldError("deploymentUrl"));
      \u0275\u0275advance();
      \u0275\u0275property("required", true)("rows", 4)("maxlength", 500)("showCharacterCount", true)("errorMessage", ctx.getFieldError("description"));
      \u0275\u0275advance(8);
      \u0275\u0275property("ngIf", ctx.dependencies.length > 0);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.dependencies.length === 0);
      \u0275\u0275advance(8);
      \u0275\u0275property("ngIf", ctx.techStack.length > 0);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.techStack.length === 0);
      \u0275\u0275advance(8);
      \u0275\u0275property("ngIf", ctx.stakeholders.length > 0);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.stakeholders.length === 0);
      \u0275\u0275advance(8);
      \u0275\u0275property("ngIf", ctx.documentation.length > 0);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.documentation.length === 0);
      \u0275\u0275advance(8);
      \u0275\u0275property("ngIf", ctx.vulnerabilities.length > 0);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.vulnerabilities.length === 0);
      \u0275\u0275advance();
      \u0275\u0275property("isOpen", ctx.showDependencyModal)("editMode", ctx.dependencyEditMode)("initialData", ctx.dependencyEditData)("loading", ctx.modalLoading);
      \u0275\u0275advance();
      \u0275\u0275property("isOpen", ctx.showTechStackModal)("editMode", ctx.techStackEditMode)("initialData", ctx.techStackEditData)("loading", ctx.modalLoading);
      \u0275\u0275advance();
      \u0275\u0275property("isOpen", ctx.showStakeholderModal)("editMode", ctx.stakeholderEditMode)("initialData", ctx.stakeholderEditData)("loading", ctx.modalLoading);
      \u0275\u0275advance();
      \u0275\u0275property("isOpen", ctx.showDocumentationModal)("editMode", ctx.documentationEditMode)("initialData", ctx.documentationEditData)("loading", ctx.modalLoading);
      \u0275\u0275advance();
      \u0275\u0275property("isOpen", ctx.showVulnerabilityModal)("editMode", ctx.vulnerabilityEditMode)("initialData", ctx.vulnerabilityEditData)("loading", ctx.modalLoading);
    }
  }, dependencies: [
    CommonModule,
    NgForOf,
    NgIf,
    RouterModule,
    RouterLink,
    ReactiveFormsModule,
    \u0275NgNoValidate,
    NgControlStatus,
    NgControlStatusGroup,
    RequiredValidator,
    MaxLengthValidator,
    FormGroupDirective,
    FormControlName,
    FormsModule,
    ButtonComponent,
    TabsComponent,
    TabComponent,
    FormInputComponent,
    DependencyModalComponent,
    TechStackModalComponent,
    StakeholderModalComponent,
    DocumentationModalComponent,
    VulnerabilityModalComponent
  ], styles: ["\n\n.application-form-page[_ngcontent-%COMP%] {\n  min-height: 100%;\n}\n.page-header[_ngcontent-%COMP%] {\n  margin-bottom: var(--spacing-xl);\n}\n.header-content[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: flex-start;\n  justify-content: space-between;\n  gap: var(--spacing-lg);\n}\n.header-info[_ngcontent-%COMP%] {\n  flex: 1;\n}\n.page-title[_ngcontent-%COMP%] {\n  margin: 0 0 var(--spacing-xs) 0;\n  font-size: var(--text-3xl);\n  font-weight: 700;\n  color: var(--secondary-900);\n}\n.page-subtitle[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: var(--text-base);\n  color: var(--secondary-600);\n  line-height: var(--leading-relaxed);\n}\n.header-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: var(--spacing-sm);\n  flex-shrink: 0;\n}\n.form-placeholder[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: var(--spacing-3xl);\n  text-align: center;\n  color: var(--secondary-600);\n}\n.placeholder-icon[_ngcontent-%COMP%] {\n  width: 64px;\n  height: 64px;\n  margin-bottom: var(--spacing-lg);\n  color: var(--secondary-400);\n}\n.form-placeholder[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin-bottom: var(--spacing-md);\n  color: var(--secondary-800);\n}\n.form-placeholder[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin-bottom: var(--spacing-md);\n  max-width: 500px;\n}\n.form-placeholder[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\n  text-align: left;\n  margin-bottom: var(--spacing-lg);\n}\n.form-placeholder[_ngcontent-%COMP%]   .note[_ngcontent-%COMP%] {\n  font-style: italic;\n  color: var(--secondary-500);\n}\n.form-section[_ngcontent-%COMP%] {\n  padding: var(--spacing-xl);\n}\n.form-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: var(--spacing-xl);\n}\n.form-group[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-sm);\n}\n.form-group.full-width[_ngcontent-%COMP%] {\n  grid-column: 1/-1;\n}\n.form-label[_ngcontent-%COMP%] {\n  font-size: var(--text-sm);\n  font-weight: 500;\n  color: var(--secondary-700);\n  margin-bottom: var(--spacing-sm);\n}\n.form-select[_ngcontent-%COMP%] {\n  padding: var(--spacing-md);\n  border: 1px solid var(--secondary-300);\n  border-radius: var(--radius-md);\n  background: white;\n  font-size: var(--text-sm);\n  color: var(--secondary-800);\n  transition: all 0.2s ease;\n}\n.form-select[_ngcontent-%COMP%]:focus {\n  outline: none;\n  border-color: var(--primary-500);\n  box-shadow: 0 0 0 3px var(--primary-100);\n}\n.error-message[_ngcontent-%COMP%] {\n  font-size: var(--text-xs);\n  color: var(--error-600);\n  margin-top: var(--spacing-xs);\n}\n.section-header[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: var(--spacing-xl);\n  padding-bottom: var(--spacing-md);\n  border-bottom: 1px solid var(--secondary-200);\n}\n.section-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: var(--text-lg);\n  font-weight: 600;\n  color: var(--secondary-900);\n}\n.entity-list[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-lg);\n}\n.entity-item[_ngcontent-%COMP%] {\n  padding: var(--spacing-lg);\n  border: 1px solid var(--secondary-200);\n  border-radius: var(--radius-lg);\n  background: white;\n  position: relative;\n}\n.entity-header[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: var(--spacing-md);\n}\n.entity-title[_ngcontent-%COMP%] {\n  font-size: var(--text-base);\n  font-weight: 600;\n  color: var(--secondary-900);\n  margin: 0;\n}\n.entity-meta[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-md);\n  flex-wrap: wrap;\n}\n.entity-badge[_ngcontent-%COMP%] {\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border-radius: var(--radius-sm);\n  font-size: var(--text-xs);\n  font-weight: 500;\n  text-transform: capitalize;\n}\n.entity-badge.type-library[_ngcontent-%COMP%] {\n  background: var(--blue-100);\n  color: var(--blue-700);\n}\n.entity-badge.type-framework[_ngcontent-%COMP%] {\n  background: var(--purple-100);\n  color: var(--purple-700);\n}\n.entity-badge.type-service[_ngcontent-%COMP%] {\n  background: var(--green-100);\n  color: var(--green-700);\n}\n.entity-badge.type-database[_ngcontent-%COMP%] {\n  background: var(--orange-100);\n  color: var(--orange-700);\n}\n.entity-badge.criticality-critical[_ngcontent-%COMP%] {\n  background: var(--error-100);\n  color: var(--error-700);\n}\n.entity-badge.criticality-high[_ngcontent-%COMP%] {\n  background: var(--warning-100);\n  color: var(--warning-700);\n}\n.entity-badge.criticality-medium[_ngcontent-%COMP%] {\n  background: var(--primary-100);\n  color: var(--primary-700);\n}\n.entity-badge.criticality-low[_ngcontent-%COMP%] {\n  background: var(--success-100);\n  color: var(--success-700);\n}\n.entity-badge.role-product_owner[_ngcontent-%COMP%] {\n  background: var(--purple-100);\n  color: var(--purple-700);\n}\n.entity-badge.role-developer[_ngcontent-%COMP%] {\n  background: var(--blue-100);\n  color: var(--blue-700);\n}\n.entity-badge.role-tech_lead[_ngcontent-%COMP%] {\n  background: var(--indigo-100);\n  color: var(--indigo-700);\n}\n.entity-description[_ngcontent-%COMP%] {\n  margin: var(--spacing-sm) 0 0 0;\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n  line-height: var(--leading-relaxed);\n}\n.entity-version[_ngcontent-%COMP%] {\n  font-size: var(--text-xs);\n  color: var(--secondary-600);\n  font-weight: 500;\n}\n.entity-link[_ngcontent-%COMP%] {\n  font-size: var(--text-xs);\n  color: var(--primary-600);\n  text-decoration: none;\n  font-weight: 500;\n}\n.entity-link[_ngcontent-%COMP%]:hover {\n  color: var(--primary-700);\n  text-decoration: underline;\n}\n.remove-button[_ngcontent-%COMP%] {\n  background: none;\n  border: none;\n  color: var(--error-500);\n  cursor: pointer;\n  padding: var(--spacing-xs);\n  border-radius: var(--radius-sm);\n  transition: all 0.2s ease;\n}\n.remove-button[_ngcontent-%COMP%]:hover {\n  background: var(--error-50);\n  color: var(--error-600);\n}\n.remove-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\n  width: 16px;\n  height: 16px;\n}\n.empty-state[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: var(--spacing-3xl);\n  color: var(--secondary-500);\n  background: var(--neutral-50);\n  border-radius: var(--radius-lg);\n  border: 2px dashed var(--secondary-200);\n}\n.empty-state[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: var(--text-sm);\n}\n@media (max-width: 768px) {\n  .header-content[_ngcontent-%COMP%] {\n    flex-direction: column;\n    align-items: stretch;\n    gap: var(--spacing-md);\n  }\n  .header-actions[_ngcontent-%COMP%] {\n    justify-content: flex-end;\n  }\n  .form-grid[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-lg);\n  }\n  .form-section[_ngcontent-%COMP%] {\n    padding: var(--spacing-lg);\n  }\n  .entity-meta[_ngcontent-%COMP%] {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: var(--spacing-sm);\n  }\n}\n/*# sourceMappingURL=application-form.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ApplicationFormComponent, [{
    type: Component,
    args: [{ selector: "app-application-form", standalone: true, imports: [
      CommonModule,
      RouterModule,
      ReactiveFormsModule,
      FormsModule,
      ButtonComponent,
      TabsComponent,
      TabComponent,
      FormInputComponent,
      DependencyModalComponent,
      TechStackModalComponent,
      StakeholderModalComponent,
      DocumentationModalComponent,
      VulnerabilityModalComponent
    ], template: `
    <div class="application-form-page">
      <div class="page-header">
        <div class="header-content">
          <div class="header-info">
            <h1 class="page-title">{{ isEditMode ? 'Edit Application' : 'New Application' }}</h1>
            <p class="page-subtitle">
              {{ isEditMode ? 'Update application details and configuration.' : 'Register a new application in the catalog.' }}
            </p>
          </div>
          <div class="header-actions">
            <app-button variant="outline" routerLink="/applications">
              Cancel
            </app-button>
            <app-button
              variant="primary"
              [loading]="isSaving"
              (clicked)="saveApplication()"
            >
              {{ isEditMode ? 'Update' : 'Create' }} Application
            </app-button>
          </div>
        </div>
      </div>

      <div class="form-content">
        <form [formGroup]="applicationForm" (ngSubmit)="saveApplication()">
          <app-tabs
            [tabs]="tabItems"
            [activeTabId]="activeTabId"
            (tabChange)="onTabChange($event)"
            #tabsComponent
          >
            <!-- Basic Information Tab -->
            <app-tab id="basic" label="Basic Information">
              <div class="form-section">
                <div class="form-grid">
                  <app-form-input
                    label="Application Name"
                    placeholder="Enter application name"
                    formControlName="name"
                    [required]="true"
                    [errorMessage]="getFieldError('name')"
                  ></app-form-input>

                  <app-form-input
                    label="Owner"
                    placeholder="Enter owner name"
                    formControlName="owner"
                    [required]="true"
                    [errorMessage]="getFieldError('owner')"
                  ></app-form-input>

                  <app-form-input
                    type="select"
                    label="Lifecycle Status"
                    placeholder="Select status"
                    formControlName="lifecycleStatus"
                    [options]="lifecycleStatusOptions"
                    [required]="true"
                    [errorMessage]="getFieldError('lifecycleStatus')"
                  ></app-form-input>

                  <app-form-input
                    type="select"
                    label="Business Criticality"
                    placeholder="Select criticality"
                    formControlName="businessCriticality"
                    [options]="businessCriticalityOptions"
                    [required]="true"
                    [errorMessage]="getFieldError('businessCriticality')"
                  ></app-form-input>

                  <app-form-input
                    label="Version"
                    placeholder="e.g., 1.0.0"
                    formControlName="version"
                    [required]="true"
                    [errorMessage]="getFieldError('version')"
                  ></app-form-input>

                  <app-form-input
                    type="url"
                    label="Repository URL"
                    placeholder="https://github.com/..."
                    formControlName="repository"
                    [errorMessage]="getFieldError('repository')"
                  ></app-form-input>

                  <app-form-input
                    type="url"
                    label="Deployment URL"
                    placeholder="https://app.company.com"
                    formControlName="deploymentUrl"
                    [errorMessage]="getFieldError('deploymentUrl')"
                  ></app-form-input>
                </div>

                <app-form-input
                  type="textarea"
                  label="Description"
                  placeholder="Describe the application's purpose and functionality"
                  formControlName="description"
                  [required]="true"
                  [rows]="4"
                  [maxlength]="500"
                  [showCharacterCount]="true"
                  [errorMessage]="getFieldError('description')"
                ></app-form-input>
              </div>
            </app-tab>

            <!-- Dependencies Tab -->
            <app-tab id="dependencies" label="Dependencies">
              <div class="form-section">
                <div class="section-header">
                  <h3>Application Dependencies</h3>
                  <app-button variant="outline" size="sm" (clicked)="openDependencyModal()">
                    Add Dependency
                  </app-button>
                </div>

                <div class="entity-list" *ngIf="dependencies.length > 0">
                  <div class="entity-item" *ngFor="let dep of dependencies; let i = index">
                    <div class="entity-header">
                      <h4 class="entity-title">{{ dep.name }}</h4>
                      <button class="remove-button" (click)="removeDependency(i)" type="button">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                          <polyline points="3,6 5,6 21,6"></polyline>
                          <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                        </svg>
                      </button>
                    </div>
                    <div class="entity-meta">
                      <span class="entity-badge type-{{ dep.type }}">{{ dep.type }}</span>
                      <span class="entity-badge criticality-{{ dep.criticality }}">{{ dep.criticality }}</span>
                      <span class="entity-version">v{{ dep.version }}</span>
                      <span class="entity-badge" *ngIf="dep.isInternal">Internal</span>
                    </div>
                    <p class="entity-description" *ngIf="dep.description">{{ dep.description }}</p>
                  </div>
                </div>

                <div class="empty-state" *ngIf="dependencies.length === 0">
                  <p>No dependencies added yet. Click "Add Dependency" to get started.</p>
                </div>
              </div>
            </app-tab>

            <!-- Tech Stack Tab -->
            <app-tab id="techstack" label="Tech Stack">
              <div class="form-section">
                <div class="section-header">
                  <h3>Technology Stack</h3>
                  <app-button variant="outline" size="sm" (clicked)="openTechStackModal()">
                    Add Technology
                  </app-button>
                </div>

                <div class="entity-list" *ngIf="techStack.length > 0">
                  <div class="entity-item" *ngFor="let tech of techStack; let i = index">
                    <div class="entity-header">
                      <h4 class="entity-title">{{ tech.technology }}</h4>
                      <button class="remove-button" (click)="removeTechStack(i)" type="button">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                          <polyline points="3,6 5,6 21,6"></polyline>
                          <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                        </svg>
                      </button>
                    </div>
                    <div class="entity-meta">
                      <span class="entity-badge">{{ tech.category }}</span>
                      <span class="entity-version">v{{ tech.version }}</span>
                      <span class="entity-badge" *ngIf="tech.isCore">Core</span>
                    </div>
                    <p class="entity-description" *ngIf="tech.purpose">{{ tech.purpose }}</p>
                  </div>
                </div>

                <div class="empty-state" *ngIf="techStack.length === 0">
                  <p>No technologies added yet. Click "Add Technology" to get started.</p>
                </div>
              </div>
            </app-tab>

            <!-- Stakeholders Tab -->
            <app-tab id="stakeholders" label="Stakeholders">
              <div class="form-section">
                <div class="section-header">
                  <h3>Stakeholders & Teams</h3>
                  <app-button variant="outline" size="sm" (clicked)="openStakeholderModal()">
                    Add Stakeholder
                  </app-button>
                </div>

                <div class="entity-list" *ngIf="stakeholders.length > 0">
                  <div class="entity-item" *ngFor="let stakeholder of stakeholders; let i = index">
                    <div class="entity-header">
                      <h4 class="entity-title">{{ stakeholder.name }}</h4>
                      <button class="remove-button" (click)="removeStakeholder(i)" type="button">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                          <polyline points="3,6 5,6 21,6"></polyline>
                          <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                        </svg>
                      </button>
                    </div>
                    <div class="entity-meta">
                      <span class="entity-badge role-{{ stakeholder.role }}">{{ stakeholder.role.replace('_', ' ') }}</span>
                      <span class="entity-badge">{{ stakeholder.department }}</span>
                      <span class="entity-badge" *ngIf="stakeholder.isPrimary">Primary Contact</span>
                    </div>
                    <p class="entity-description" *ngIf="stakeholder.email">{{ stakeholder.email }}</p>
                    <p class="entity-description" *ngIf="stakeholder.responsibility">{{ stakeholder.responsibility }}</p>
                  </div>
                </div>

                <div class="empty-state" *ngIf="stakeholders.length === 0">
                  <p>No stakeholders added yet. Click "Add Stakeholder" to get started.</p>
                </div>
              </div>
            </app-tab>

            <!-- Documentation Tab -->
            <app-tab id="documentation" label="Documentation">
              <div class="form-section">
                <div class="section-header">
                  <h3>Documentation & Resources</h3>
                  <app-button variant="outline" size="sm" (clicked)="openDocumentationModal()">
                    Add Document
                  </app-button>
                </div>

                <div class="entity-list" *ngIf="documentation.length > 0">
                  <div class="entity-item" *ngFor="let doc of documentation; let i = index">
                    <div class="entity-header">
                      <h4 class="entity-title">{{ doc.title }}</h4>
                      <button class="remove-button" (click)="removeDocumentation(i)" type="button">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                          <polyline points="3,6 5,6 21,6"></polyline>
                          <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                        </svg>
                      </button>
                    </div>
                    <div class="entity-meta">
                      <span class="entity-badge">{{ doc.type.replace('_', ' ') }}</span>
                      <span class="entity-version">v{{ doc.version }}</span>
                      <span class="entity-badge" *ngIf="doc.isPublic">Public</span>
                      <a [href]="doc.url" target="_blank" class="entity-link" *ngIf="doc.url">View Document</a>
                    </div>
                    <p class="entity-description" *ngIf="doc.description">{{ doc.description }}</p>
                  </div>
                </div>

                <div class="empty-state" *ngIf="documentation.length === 0">
                  <p>No documentation added yet. Click "Add Document" to get started.</p>
                </div>
              </div>
            </app-tab>

            <!-- Security Tab -->
            <app-tab id="security" label="Security">
              <div class="form-section">
                <div class="section-header">
                  <h3>Security & Vulnerabilities</h3>
                  <app-button variant="outline" size="sm" (clicked)="openVulnerabilityModal()">
                    Add Vulnerability
                  </app-button>
                </div>

                <div class="entity-list" *ngIf="vulnerabilities.length > 0">
                  <div class="entity-item" *ngFor="let vuln of vulnerabilities; let i = index">
                    <div class="entity-header">
                      <h4 class="entity-title">{{ vuln.title }}</h4>
                      <button class="remove-button" (click)="removeVulnerability(i)" type="button">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                          <polyline points="3,6 5,6 21,6"></polyline>
                          <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                        </svg>
                      </button>
                    </div>
                    <div class="entity-meta">
                      <span class="entity-badge criticality-{{ vuln.severity }}">{{ vuln.severity }}</span>
                      <span class="entity-badge">{{ vuln.status.replace('_', ' ') }}</span>
                      <span class="entity-version" *ngIf="vuln.cvssScore > 0">CVSS: {{ vuln.cvssScore }}</span>
                      <span class="entity-badge" *ngIf="vuln.cveId">{{ vuln.cveId }}</span>
                    </div>
                    <p class="entity-description" *ngIf="vuln.description">{{ vuln.description }}</p>
                  </div>
                </div>

                <div class="empty-state" *ngIf="vulnerabilities.length === 0">
                  <p>No vulnerabilities added yet. Click "Add Vulnerability" to get started.</p>
                </div>
              </div>
            </app-tab>
          </app-tabs>
        </form>
      </div>

      <!-- Modals -->
      <app-dependency-modal
        [isOpen]="showDependencyModal"
        [editMode]="dependencyEditMode"
        [initialData]="dependencyEditData"
        [loading]="modalLoading"
        (closed)="closeDependencyModal()"
        (saved)="saveDependency($event)"
      ></app-dependency-modal>

      <app-tech-stack-modal
        [isOpen]="showTechStackModal"
        [editMode]="techStackEditMode"
        [initialData]="techStackEditData"
        [loading]="modalLoading"
        (closed)="closeTechStackModal()"
        (saved)="saveTechStack($event)"
      ></app-tech-stack-modal>

      <app-stakeholder-modal
        [isOpen]="showStakeholderModal"
        [editMode]="stakeholderEditMode"
        [initialData]="stakeholderEditData"
        [loading]="modalLoading"
        (closed)="closeStakeholderModal()"
        (saved)="saveStakeholder($event)"
      ></app-stakeholder-modal>

      <app-documentation-modal
        [isOpen]="showDocumentationModal"
        [editMode]="documentationEditMode"
        [initialData]="documentationEditData"
        [loading]="modalLoading"
        (closed)="closeDocumentationModal()"
        (saved)="saveDocumentation($event)"
      ></app-documentation-modal>

      <app-vulnerability-modal
        [isOpen]="showVulnerabilityModal"
        [editMode]="vulnerabilityEditMode"
        [initialData]="vulnerabilityEditData"
        [loading]="modalLoading"
        (closed)="closeVulnerabilityModal()"
        (saved)="saveVulnerability($event)"
      ></app-vulnerability-modal>
    </div>
  `, styles: ["/* angular:styles/component:scss;8d7397a2193107337767642810b184ac6c21b7ea3e3d41adfc77ece58461ba3d;C:/Users/<USER>/Documents/VibeCoding/AppCatalog/app-catalog/frontend/src/app/modules/applications/application-form/application-form.component.ts */\n.application-form-page {\n  min-height: 100%;\n}\n.page-header {\n  margin-bottom: var(--spacing-xl);\n}\n.header-content {\n  display: flex;\n  align-items: flex-start;\n  justify-content: space-between;\n  gap: var(--spacing-lg);\n}\n.header-info {\n  flex: 1;\n}\n.page-title {\n  margin: 0 0 var(--spacing-xs) 0;\n  font-size: var(--text-3xl);\n  font-weight: 700;\n  color: var(--secondary-900);\n}\n.page-subtitle {\n  margin: 0;\n  font-size: var(--text-base);\n  color: var(--secondary-600);\n  line-height: var(--leading-relaxed);\n}\n.header-actions {\n  display: flex;\n  gap: var(--spacing-sm);\n  flex-shrink: 0;\n}\n.form-placeholder {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: var(--spacing-3xl);\n  text-align: center;\n  color: var(--secondary-600);\n}\n.placeholder-icon {\n  width: 64px;\n  height: 64px;\n  margin-bottom: var(--spacing-lg);\n  color: var(--secondary-400);\n}\n.form-placeholder h3 {\n  margin-bottom: var(--spacing-md);\n  color: var(--secondary-800);\n}\n.form-placeholder p {\n  margin-bottom: var(--spacing-md);\n  max-width: 500px;\n}\n.form-placeholder ul {\n  text-align: left;\n  margin-bottom: var(--spacing-lg);\n}\n.form-placeholder .note {\n  font-style: italic;\n  color: var(--secondary-500);\n}\n.form-section {\n  padding: var(--spacing-xl);\n}\n.form-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: var(--spacing-xl);\n}\n.form-group {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-sm);\n}\n.form-group.full-width {\n  grid-column: 1/-1;\n}\n.form-label {\n  font-size: var(--text-sm);\n  font-weight: 500;\n  color: var(--secondary-700);\n  margin-bottom: var(--spacing-sm);\n}\n.form-select {\n  padding: var(--spacing-md);\n  border: 1px solid var(--secondary-300);\n  border-radius: var(--radius-md);\n  background: white;\n  font-size: var(--text-sm);\n  color: var(--secondary-800);\n  transition: all 0.2s ease;\n}\n.form-select:focus {\n  outline: none;\n  border-color: var(--primary-500);\n  box-shadow: 0 0 0 3px var(--primary-100);\n}\n.error-message {\n  font-size: var(--text-xs);\n  color: var(--error-600);\n  margin-top: var(--spacing-xs);\n}\n.section-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: var(--spacing-xl);\n  padding-bottom: var(--spacing-md);\n  border-bottom: 1px solid var(--secondary-200);\n}\n.section-header h3 {\n  margin: 0;\n  font-size: var(--text-lg);\n  font-weight: 600;\n  color: var(--secondary-900);\n}\n.entity-list {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-lg);\n}\n.entity-item {\n  padding: var(--spacing-lg);\n  border: 1px solid var(--secondary-200);\n  border-radius: var(--radius-lg);\n  background: white;\n  position: relative;\n}\n.entity-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: var(--spacing-md);\n}\n.entity-title {\n  font-size: var(--text-base);\n  font-weight: 600;\n  color: var(--secondary-900);\n  margin: 0;\n}\n.entity-meta {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-md);\n  flex-wrap: wrap;\n}\n.entity-badge {\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border-radius: var(--radius-sm);\n  font-size: var(--text-xs);\n  font-weight: 500;\n  text-transform: capitalize;\n}\n.entity-badge.type-library {\n  background: var(--blue-100);\n  color: var(--blue-700);\n}\n.entity-badge.type-framework {\n  background: var(--purple-100);\n  color: var(--purple-700);\n}\n.entity-badge.type-service {\n  background: var(--green-100);\n  color: var(--green-700);\n}\n.entity-badge.type-database {\n  background: var(--orange-100);\n  color: var(--orange-700);\n}\n.entity-badge.criticality-critical {\n  background: var(--error-100);\n  color: var(--error-700);\n}\n.entity-badge.criticality-high {\n  background: var(--warning-100);\n  color: var(--warning-700);\n}\n.entity-badge.criticality-medium {\n  background: var(--primary-100);\n  color: var(--primary-700);\n}\n.entity-badge.criticality-low {\n  background: var(--success-100);\n  color: var(--success-700);\n}\n.entity-badge.role-product_owner {\n  background: var(--purple-100);\n  color: var(--purple-700);\n}\n.entity-badge.role-developer {\n  background: var(--blue-100);\n  color: var(--blue-700);\n}\n.entity-badge.role-tech_lead {\n  background: var(--indigo-100);\n  color: var(--indigo-700);\n}\n.entity-description {\n  margin: var(--spacing-sm) 0 0 0;\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n  line-height: var(--leading-relaxed);\n}\n.entity-version {\n  font-size: var(--text-xs);\n  color: var(--secondary-600);\n  font-weight: 500;\n}\n.entity-link {\n  font-size: var(--text-xs);\n  color: var(--primary-600);\n  text-decoration: none;\n  font-weight: 500;\n}\n.entity-link:hover {\n  color: var(--primary-700);\n  text-decoration: underline;\n}\n.remove-button {\n  background: none;\n  border: none;\n  color: var(--error-500);\n  cursor: pointer;\n  padding: var(--spacing-xs);\n  border-radius: var(--radius-sm);\n  transition: all 0.2s ease;\n}\n.remove-button:hover {\n  background: var(--error-50);\n  color: var(--error-600);\n}\n.remove-button svg {\n  width: 16px;\n  height: 16px;\n}\n.empty-state {\n  text-align: center;\n  padding: var(--spacing-3xl);\n  color: var(--secondary-500);\n  background: var(--neutral-50);\n  border-radius: var(--radius-lg);\n  border: 2px dashed var(--secondary-200);\n}\n.empty-state p {\n  margin: 0;\n  font-size: var(--text-sm);\n}\n@media (max-width: 768px) {\n  .header-content {\n    flex-direction: column;\n    align-items: stretch;\n    gap: var(--spacing-md);\n  }\n  .header-actions {\n    justify-content: flex-end;\n  }\n  .form-grid {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-lg);\n  }\n  .form-section {\n    padding: var(--spacing-lg);\n  }\n  .entity-meta {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: var(--spacing-sm);\n  }\n}\n/*# sourceMappingURL=application-form.component.css.map */\n"] }]
  }], () => [{ type: FormBuilder }, { type: ActivatedRoute }], { tabsComponent: [{
    type: ViewChild,
    args: ["tabsComponent"]
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(ApplicationFormComponent, { className: "ApplicationFormComponent", filePath: "src/app/modules/applications/application-form/application-form.component.ts", lineNumber: 669 });
})();
export {
  ApplicationFormComponent
};
//# sourceMappingURL=chunk-XVU5TYLD.mjs.map
