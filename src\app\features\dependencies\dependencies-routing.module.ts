import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DependenciesListComponent } from './dependencies-list/dependencies-list.component';
import { DependencyDetailComponent } from './dependency-detail/dependency-detail.component';
import { DependencyCreateComponent } from './dependency-create/dependency-create.component';
import { DependencyEditComponent } from './dependency-edit/dependency-edit.component';

const routes: Routes = [
  {
    path: '',
    component: DependenciesListComponent
  },
  {
    path: 'new',
    component: DependencyCreateComponent
  },
  {
    path: ':id/edit',
    component: DependencyEditComponent
  },
  {
    path: ':id',
    component: DependencyDetailComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class DependenciesRoutingModule { }
