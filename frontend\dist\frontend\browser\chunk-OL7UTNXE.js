import {
  ApplicationSelectionService
} from "./chunk-7XI6LZQT.js";
import {
  TableComponent
} from "./chunk-QQYNLSVO.js";
import {
  ChartComponent
} from "./chunk-HCVU2C6O.js";
import {
  FormInputComponent
} from "./chunk-WQFVHIJL.js";
import {
  SlideModalComponent
} from "./chunk-6DLBFET6.js";
import {
  Default<PERSON><PERSON>ueAccessor,
  FormBuilder,
  FormControlName,
  FormGroupDirective,
  NgControlStatus,
  NgControlStatusGroup,
  NgSelectOption,
  ReactiveFormsModule,
  RequiredValidator,
  SelectControlValueAccessor,
  Validators,
  ɵNgNoValidate,
  ɵNgSelectMultipleOption
} from "./chunk-SM75SJZE.js";
import {
  CardComponent
} from "./chunk-SLWZQAXJ.js";
import {
  ButtonComponent
} from "./chunk-QMVBAXS7.js";
import {
  RouterModule
} from "./chunk-IKU57TF7.js";
import {
  AsyncPipe,
  CommonModule,
  Component,
  EventEmitter,
  Input,
  NgForOf,
  NgIf,
  Output,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵNgOnChangesFeature,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind1,
  ɵɵproperty,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtextInterpolate2
} from "./chunk-3JQLQ36P.js";
import "./chunk-WDMUDEB6.js";

// src/app/shared/components/modals/shared-tech-stack-modal/shared-tech-stack-modal.component.ts
function SharedTechStackModalComponent_div_2_option_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "option", 50);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const app_r1 = ctx.$implicit;
    \u0275\u0275property("value", app_r1.id);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate2(" ", app_r1.name, " - ", app_r1.department, " ");
  }
}
function SharedTechStackModalComponent_div_2_div_8_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 51);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r1.getFieldError("applicationId"), " ");
  }
}
function SharedTechStackModalComponent_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 4)(1, "label", 5);
    \u0275\u0275text(2, "Application *");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "select", 46)(4, "option", 47);
    \u0275\u0275text(5, "Select an application");
    \u0275\u0275elementEnd();
    \u0275\u0275template(6, SharedTechStackModalComponent_div_2_option_6_Template, 2, 3, "option", 48);
    \u0275\u0275pipe(7, "async");
    \u0275\u0275elementEnd();
    \u0275\u0275template(8, SharedTechStackModalComponent_div_2_div_8_Template, 2, 1, "div", 49);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(6);
    \u0275\u0275property("ngForOf", \u0275\u0275pipeBind1(7, 2, ctx_r1.applicationOptions$));
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", ctx_r1.getFieldError("applicationId"));
  }
}
var SharedTechStackModalComponent = class _SharedTechStackModalComponent {
  fb;
  applicationSelectionService;
  isOpen = false;
  editMode = false;
  initialData = null;
  loading = false;
  showApplicationSelection = true;
  // Hide when used within applications module
  closed = new EventEmitter();
  saved = new EventEmitter();
  techStackForm;
  applicationOptions$;
  constructor(fb, applicationSelectionService) {
    this.fb = fb;
    this.applicationSelectionService = applicationSelectionService;
    this.applicationOptions$ = this.applicationSelectionService.getApplicationOptions();
  }
  ngOnInit() {
    this.initializeForm();
  }
  ngOnChanges() {
    if (this.techStackForm && this.initialData) {
      this.techStackForm.patchValue(this.initialData);
    }
  }
  initializeForm() {
    const formConfig = {
      category: ["frontend", [Validators.required]],
      name: ["", [Validators.required, Validators.minLength(2)]],
      version: ["", [Validators.required]],
      description: [""],
      purpose: ["core", [Validators.required]],
      status: ["active", [Validators.required]],
      maintainer: ["", [Validators.required]],
      licenseType: ["MIT", [Validators.required]],
      repository: [""],
      documentation: [""]
    };
    if (this.showApplicationSelection) {
      formConfig.applicationId = ["", [Validators.required]];
    }
    this.techStackForm = this.fb.group(formConfig);
    if (this.initialData) {
      this.techStackForm.patchValue(this.initialData);
    }
  }
  getFieldError(fieldName) {
    const field = this.techStackForm.get(fieldName);
    if (field && field.invalid && field.touched) {
      if (field.errors?.["required"]) {
        return `${fieldName} is required`;
      }
      if (field.errors?.["minlength"]) {
        return `${fieldName} must be at least ${field.errors["minlength"].requiredLength} characters`;
      }
      if (field.errors?.["url"]) {
        return `${fieldName} must be a valid URL`;
      }
    }
    return "";
  }
  onSave() {
    if (this.techStackForm.valid) {
      const formData = this.techStackForm.value;
      this.saved.emit(formData);
    }
  }
  onClose() {
    this.closed.emit();
  }
  static \u0275fac = function SharedTechStackModalComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _SharedTechStackModalComponent)(\u0275\u0275directiveInject(FormBuilder), \u0275\u0275directiveInject(ApplicationSelectionService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _SharedTechStackModalComponent, selectors: [["app-shared-tech-stack-modal"]], inputs: { isOpen: "isOpen", editMode: "editMode", initialData: "initialData", loading: "loading", showApplicationSelection: "showApplicationSelection" }, outputs: { closed: "closed", saved: "saved" }, features: [\u0275\u0275NgOnChangesFeature], decls: 95, vars: 15, consts: [["confirmText", "Save Technology", 3, "closed", "confirmed", "cancelled", "isOpen", "title", "subtitle", "loading", "canConfirm"], [1, "tech-stack-form", 3, "formGroup"], ["class", "form-group", 4, "ngIf"], [1, "form-grid"], [1, "form-group"], [1, "form-label"], ["formControlName", "category", 1, "form-select"], ["value", "frontend"], ["value", "backend"], ["value", "database"], ["value", "infrastructure"], ["value", "devops"], ["value", "testing"], ["value", "monitoring"], ["value", "security"], ["value", "analytics"], ["value", "communication"], ["label", "Technology Name", "placeholder", "e.g., React, Node.js, PostgreSQL", "formControlName", "name", 3, "required", "errorMessage"], ["label", "Version", "placeholder", "e.g., 18.2.0, 16.x, latest", "formControlName", "version", 3, "required", "errorMessage"], ["formControlName", "purpose", 1, "form-select"], ["value", "core"], ["value", "development"], ["value", "deployment"], ["value", "performance"], ["value", "integration"], ["value", "other"], ["formControlName", "status", 1, "form-select"], ["value", "active"], ["value", "planned"], ["value", "deprecated"], ["value", "evaluating"], ["value", "migrating"], ["label", "Maintainer/Vendor", "placeholder", "e.g., Facebook, Google, Internal Team", "formControlName", "maintainer", 3, "required", "errorMessage"], ["formControlName", "licenseType", 1, "form-select"], ["value", "MIT"], ["value", "Apache-2.0"], ["value", "GPL-3.0"], ["value", "BSD-3-Clause"], ["value", "ISC"], ["value", "MPL-2.0"], ["value", "proprietary"], ["value", "commercial"], ["label", "Repository URL", "placeholder", "https://github.com/...", "formControlName", "repository", 3, "errorMessage"], ["label", "Documentation URL", "placeholder", "https://docs.example.com/...", "formControlName", "documentation", 3, "errorMessage"], [1, "form-group", "full-width"], ["formControlName", "description", "placeholder", "Describe how this technology is used in the application...", "rows", "3", 1, "form-textarea"], ["formControlName", "applicationId", 1, "form-select"], ["value", ""], [3, "value", 4, "ngFor", "ngForOf"], ["class", "field-error", 4, "ngIf"], [3, "value"], [1, "field-error"]], template: function SharedTechStackModalComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "app-slide-modal", 0);
      \u0275\u0275listener("closed", function SharedTechStackModalComponent_Template_app_slide_modal_closed_0_listener() {
        return ctx.onClose();
      })("confirmed", function SharedTechStackModalComponent_Template_app_slide_modal_confirmed_0_listener() {
        return ctx.onSave();
      })("cancelled", function SharedTechStackModalComponent_Template_app_slide_modal_cancelled_0_listener() {
        return ctx.onClose();
      });
      \u0275\u0275elementStart(1, "form", 1);
      \u0275\u0275template(2, SharedTechStackModalComponent_div_2_Template, 9, 4, "div", 2);
      \u0275\u0275elementStart(3, "div", 3)(4, "div", 4)(5, "label", 5);
      \u0275\u0275text(6, "Category");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(7, "select", 6)(8, "option", 7);
      \u0275\u0275text(9, "Frontend");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(10, "option", 8);
      \u0275\u0275text(11, "Backend");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(12, "option", 9);
      \u0275\u0275text(13, "Database");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(14, "option", 10);
      \u0275\u0275text(15, "Infrastructure");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(16, "option", 11);
      \u0275\u0275text(17, "DevOps");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(18, "option", 12);
      \u0275\u0275text(19, "Testing");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(20, "option", 13);
      \u0275\u0275text(21, "Monitoring");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(22, "option", 14);
      \u0275\u0275text(23, "Security");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(24, "option", 15);
      \u0275\u0275text(25, "Analytics");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(26, "option", 16);
      \u0275\u0275text(27, "Communication");
      \u0275\u0275elementEnd()()();
      \u0275\u0275element(28, "app-form-input", 17)(29, "app-form-input", 18);
      \u0275\u0275elementStart(30, "div", 4)(31, "label", 5);
      \u0275\u0275text(32, "Purpose");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(33, "select", 19)(34, "option", 20);
      \u0275\u0275text(35, "Core Technology");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(36, "option", 21);
      \u0275\u0275text(37, "Development Tool");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(38, "option", 12);
      \u0275\u0275text(39, "Testing Framework");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(40, "option", 22);
      \u0275\u0275text(41, "Deployment Tool");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(42, "option", 13);
      \u0275\u0275text(43, "Monitoring Solution");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(44, "option", 14);
      \u0275\u0275text(45, "Security Tool");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(46, "option", 23);
      \u0275\u0275text(47, "Performance Optimization");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(48, "option", 24);
      \u0275\u0275text(49, "Integration");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(50, "option", 25);
      \u0275\u0275text(51, "Other");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(52, "div", 4)(53, "label", 5);
      \u0275\u0275text(54, "Status");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(55, "select", 26)(56, "option", 27);
      \u0275\u0275text(57, "Active");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(58, "option", 28);
      \u0275\u0275text(59, "Planned");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(60, "option", 29);
      \u0275\u0275text(61, "Deprecated");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(62, "option", 30);
      \u0275\u0275text(63, "Evaluating");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(64, "option", 31);
      \u0275\u0275text(65, "Migrating");
      \u0275\u0275elementEnd()()();
      \u0275\u0275element(66, "app-form-input", 32);
      \u0275\u0275elementStart(67, "div", 4)(68, "label", 5);
      \u0275\u0275text(69, "License Type");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(70, "select", 33)(71, "option", 34);
      \u0275\u0275text(72, "MIT");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(73, "option", 35);
      \u0275\u0275text(74, "Apache 2.0");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(75, "option", 36);
      \u0275\u0275text(76, "GPL 3.0");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(77, "option", 37);
      \u0275\u0275text(78, "BSD 3-Clause");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(79, "option", 38);
      \u0275\u0275text(80, "ISC");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(81, "option", 39);
      \u0275\u0275text(82, "Mozilla Public License 2.0");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(83, "option", 40);
      \u0275\u0275text(84, "Proprietary");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(85, "option", 41);
      \u0275\u0275text(86, "Commercial");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(87, "option", 25);
      \u0275\u0275text(88, "Other");
      \u0275\u0275elementEnd()()();
      \u0275\u0275element(89, "app-form-input", 42)(90, "app-form-input", 43);
      \u0275\u0275elementStart(91, "div", 44)(92, "label", 5);
      \u0275\u0275text(93, "Description");
      \u0275\u0275elementEnd();
      \u0275\u0275element(94, "textarea", 45);
      \u0275\u0275elementEnd()()()();
    }
    if (rf & 2) {
      \u0275\u0275property("isOpen", ctx.isOpen)("title", ctx.editMode ? "Edit Technology" : "Add Technology")("subtitle", ctx.showApplicationSelection ? "Configure technology stack details and select application" : "Configure technology stack details")("loading", ctx.loading)("canConfirm", ctx.techStackForm.valid);
      \u0275\u0275advance();
      \u0275\u0275property("formGroup", ctx.techStackForm);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.showApplicationSelection);
      \u0275\u0275advance(26);
      \u0275\u0275property("required", true)("errorMessage", ctx.getFieldError("name"));
      \u0275\u0275advance();
      \u0275\u0275property("required", true)("errorMessage", ctx.getFieldError("version"));
      \u0275\u0275advance(37);
      \u0275\u0275property("required", true)("errorMessage", ctx.getFieldError("maintainer"));
      \u0275\u0275advance(23);
      \u0275\u0275property("errorMessage", ctx.getFieldError("repository"));
      \u0275\u0275advance();
      \u0275\u0275property("errorMessage", ctx.getFieldError("documentation"));
    }
  }, dependencies: [CommonModule, NgForOf, NgIf, AsyncPipe, ReactiveFormsModule, \u0275NgNoValidate, NgSelectOption, \u0275NgSelectMultipleOption, DefaultValueAccessor, SelectControlValueAccessor, NgControlStatus, NgControlStatusGroup, RequiredValidator, FormGroupDirective, FormControlName, SlideModalComponent, FormInputComponent], styles: ["\n\n.tech-stack-form[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-lg);\n}\n.form-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: var(--spacing-lg);\n}\n.form-group[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-sm);\n}\n.form-group.full-width[_ngcontent-%COMP%] {\n  grid-column: 1/-1;\n}\n.form-label[_ngcontent-%COMP%] {\n  font-size: var(--text-sm);\n  font-weight: 500;\n  color: var(--secondary-700);\n}\n.form-select[_ngcontent-%COMP%], \n.form-textarea[_ngcontent-%COMP%] {\n  height: 40px;\n  padding: 0 var(--spacing-md);\n  border: 1px solid var(--secondary-300);\n  border-radius: var(--radius-md);\n  background: white;\n  font-size: var(--text-sm);\n  color: var(--secondary-800);\n  transition: all 0.2s ease;\n}\n.form-select[_ngcontent-%COMP%]:focus, \n.form-textarea[_ngcontent-%COMP%]:focus {\n  outline: none;\n  border-color: var(--primary-500);\n  box-shadow: 0 0 0 3px var(--primary-100);\n}\n.form-textarea[_ngcontent-%COMP%] {\n  height: auto;\n  padding: var(--spacing-sm) var(--spacing-md);\n  resize: vertical;\n  min-height: 80px;\n}\n.field-error[_ngcontent-%COMP%] {\n  font-size: var(--text-sm);\n  color: var(--error-600);\n}\n@media (max-width: 768px) {\n  .form-grid[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-md);\n  }\n}\n/*# sourceMappingURL=shared-tech-stack-modal.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(SharedTechStackModalComponent, [{
    type: Component,
    args: [{ selector: "app-shared-tech-stack-modal", standalone: true, imports: [CommonModule, ReactiveFormsModule, SlideModalComponent, FormInputComponent], template: `
    <app-slide-modal
      [isOpen]="isOpen"
      [title]="editMode ? 'Edit Technology' : 'Add Technology'"
      [subtitle]="showApplicationSelection ? 'Configure technology stack details and select application' : 'Configure technology stack details'"
      [loading]="loading"
      [canConfirm]="techStackForm.valid"
      confirmText="Save Technology"
      (closed)="onClose()"
      (confirmed)="onSave()"
      (cancelled)="onClose()"
    >
      <form [formGroup]="techStackForm" class="tech-stack-form">
        <!-- Application Selection (only when not in applications module) -->
        <div class="form-group" *ngIf="showApplicationSelection">
          <label class="form-label">Application *</label>
          <select formControlName="applicationId" class="form-select">
            <option value="">Select an application</option>
            <option *ngFor="let app of applicationOptions$ | async" [value]="app.id">
              {{ app.name }} - {{ app.department }}
            </option>
          </select>
          <div class="field-error" *ngIf="getFieldError('applicationId')">
            {{ getFieldError('applicationId') }}
          </div>
        </div>

        <div class="form-grid">
          <div class="form-group">
            <label class="form-label">Category</label>
            <select formControlName="category" class="form-select">
              <option value="frontend">Frontend</option>
              <option value="backend">Backend</option>
              <option value="database">Database</option>
              <option value="infrastructure">Infrastructure</option>
              <option value="devops">DevOps</option>
              <option value="testing">Testing</option>
              <option value="monitoring">Monitoring</option>
              <option value="security">Security</option>
              <option value="analytics">Analytics</option>
              <option value="communication">Communication</option>
            </select>
          </div>

          <app-form-input
            label="Technology Name"
            placeholder="e.g., React, Node.js, PostgreSQL"
            formControlName="name"
            [required]="true"
            [errorMessage]="getFieldError('name')"
          ></app-form-input>

          <app-form-input
            label="Version"
            placeholder="e.g., 18.2.0, 16.x, latest"
            formControlName="version"
            [required]="true"
            [errorMessage]="getFieldError('version')"
          ></app-form-input>

          <div class="form-group">
            <label class="form-label">Purpose</label>
            <select formControlName="purpose" class="form-select">
              <option value="core">Core Technology</option>
              <option value="development">Development Tool</option>
              <option value="testing">Testing Framework</option>
              <option value="deployment">Deployment Tool</option>
              <option value="monitoring">Monitoring Solution</option>
              <option value="security">Security Tool</option>
              <option value="performance">Performance Optimization</option>
              <option value="integration">Integration</option>
              <option value="other">Other</option>
            </select>
          </div>

          <div class="form-group">
            <label class="form-label">Status</label>
            <select formControlName="status" class="form-select">
              <option value="active">Active</option>
              <option value="planned">Planned</option>
              <option value="deprecated">Deprecated</option>
              <option value="evaluating">Evaluating</option>
              <option value="migrating">Migrating</option>
            </select>
          </div>

          <app-form-input
            label="Maintainer/Vendor"
            placeholder="e.g., Facebook, Google, Internal Team"
            formControlName="maintainer"
            [required]="true"
            [errorMessage]="getFieldError('maintainer')"
          ></app-form-input>

          <div class="form-group">
            <label class="form-label">License Type</label>
            <select formControlName="licenseType" class="form-select">
              <option value="MIT">MIT</option>
              <option value="Apache-2.0">Apache 2.0</option>
              <option value="GPL-3.0">GPL 3.0</option>
              <option value="BSD-3-Clause">BSD 3-Clause</option>
              <option value="ISC">ISC</option>
              <option value="MPL-2.0">Mozilla Public License 2.0</option>
              <option value="proprietary">Proprietary</option>
              <option value="commercial">Commercial</option>
              <option value="other">Other</option>
            </select>
          </div>

          <app-form-input
            label="Repository URL"
            placeholder="https://github.com/..."
            formControlName="repository"
            [errorMessage]="getFieldError('repository')"
          ></app-form-input>

          <app-form-input
            label="Documentation URL"
            placeholder="https://docs.example.com/..."
            formControlName="documentation"
            [errorMessage]="getFieldError('documentation')"
          ></app-form-input>

          <div class="form-group full-width">
            <label class="form-label">Description</label>
            <textarea 
              formControlName="description"
              class="form-textarea"
              placeholder="Describe how this technology is used in the application..."
              rows="3"
            ></textarea>
          </div>
        </div>
      </form>
    </app-slide-modal>
  `, styles: ["/* angular:styles/component:scss;2d39c501113fcd9d1a64c3667733cbea564e089b53933a8eb1e0bbc788564433;C:/Users/<USER>/Documents/VibeCoding/AppCatalog/app-catalog/frontend/src/app/shared/components/modals/shared-tech-stack-modal/shared-tech-stack-modal.component.ts */\n.tech-stack-form {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-lg);\n}\n.form-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: var(--spacing-lg);\n}\n.form-group {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-sm);\n}\n.form-group.full-width {\n  grid-column: 1/-1;\n}\n.form-label {\n  font-size: var(--text-sm);\n  font-weight: 500;\n  color: var(--secondary-700);\n}\n.form-select,\n.form-textarea {\n  height: 40px;\n  padding: 0 var(--spacing-md);\n  border: 1px solid var(--secondary-300);\n  border-radius: var(--radius-md);\n  background: white;\n  font-size: var(--text-sm);\n  color: var(--secondary-800);\n  transition: all 0.2s ease;\n}\n.form-select:focus,\n.form-textarea:focus {\n  outline: none;\n  border-color: var(--primary-500);\n  box-shadow: 0 0 0 3px var(--primary-100);\n}\n.form-textarea {\n  height: auto;\n  padding: var(--spacing-sm) var(--spacing-md);\n  resize: vertical;\n  min-height: 80px;\n}\n.field-error {\n  font-size: var(--text-sm);\n  color: var(--error-600);\n}\n@media (max-width: 768px) {\n  .form-grid {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-md);\n  }\n}\n/*# sourceMappingURL=shared-tech-stack-modal.component.css.map */\n"] }]
  }], () => [{ type: FormBuilder }, { type: ApplicationSelectionService }], { isOpen: [{
    type: Input
  }], editMode: [{
    type: Input
  }], initialData: [{
    type: Input
  }], loading: [{
    type: Input
  }], showApplicationSelection: [{
    type: Input
  }], closed: [{
    type: Output
  }], saved: [{
    type: Output
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(SharedTechStackModalComponent, { className: "SharedTechStackModalComponent", filePath: "src/app/shared/components/modals/shared-tech-stack-modal/shared-tech-stack-modal.component.ts", lineNumber: 230 });
})();

// src/app/modules/tech-stack/tech-stack.component.ts
var TechStackComponent = class _TechStackComponent {
  fb;
  techStack = [];
  filteredTechStack = [];
  loading = false;
  // Modal state
  showModal = false;
  editMode = false;
  editData = null;
  modalLoading = false;
  // Statistics
  stats = {
    total: 0,
    core: 0,
    endOfLife: 0,
    categories: 0,
    totalApplications: 0,
    corePercentage: 0
  };
  // Chart data
  categoryChartData = null;
  supportChartData = null;
  chartOptions = {
    responsive: true,
    maintainAspectRatio: false
  };
  // Table configuration
  tableColumns = [
    { key: "technology", label: "Technology", sortable: true },
    { key: "category", label: "Category", type: "badge", sortable: true },
    { key: "version", label: "Version", sortable: true },
    { key: "purpose", label: "Purpose", sortable: true },
    { key: "supportLevel", label: "Support", type: "badge", sortable: true },
    { key: "applications", label: "Apps", type: "number", sortable: true },
    { key: "isCore", label: "Core", type: "boolean", sortable: true },
    { key: "licenseType", label: "License", sortable: true }
  ];
  tableActions = [
    {
      label: "Edit",
      icon: "M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",
      variant: "ghost",
      action: (item) => this.editTechStack(item)
    },
    {
      label: "Delete",
      icon: "M3 6h18m-2 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",
      variant: "error",
      action: (item) => this.deleteTechStack(item)
    }
  ];
  constructor(fb) {
    this.fb = fb;
  }
  ngOnInit() {
    this.loadTechStack();
  }
  loadTechStack() {
    this.loading = true;
    setTimeout(() => {
      this.techStack = this.generateMockTechStack();
      this.filteredTechStack = [...this.techStack];
      this.updateStats();
      this.updateChartData();
      this.loading = false;
    }, 1e3);
  }
  generateMockTechStack() {
    const categories = ["frontend", "backend", "database", "infrastructure", "devops", "testing", "monitoring"];
    const supportLevels = ["active", "maintenance", "deprecated", "end_of_life"];
    const licenses = ["MIT", "Apache 2.0", "GPL", "BSD", "Commercial", "Open Source"];
    return Array.from({ length: 30 }, (_, i) => ({
      id: i + 1,
      category: categories[Math.floor(Math.random() * categories.length)],
      technology: `Technology ${i + 1}`,
      version: `${Math.floor(Math.random() * 5) + 1}.${Math.floor(Math.random() * 10)}.${Math.floor(Math.random() * 10)}`,
      purpose: `Purpose for technology ${i + 1}`,
      isCore: Math.random() > 0.7,
      applications: Math.floor(Math.random() * 15) + 1,
      supportLevel: supportLevels[Math.floor(Math.random() * supportLevels.length)],
      endOfLife: Math.random() > 0.8 ? new Date(Date.now() + Math.random() * 365 * 24 * 60 * 60 * 1e3) : void 0,
      licenseType: licenses[Math.floor(Math.random() * licenses.length)]
    }));
  }
  updateStats() {
    this.stats.total = this.techStack.length;
    this.stats.core = this.techStack.filter((t) => t.isCore).length;
    this.stats.endOfLife = this.techStack.filter((t) => t.supportLevel === "end_of_life" || t.supportLevel === "deprecated").length;
    this.stats.categories = new Set(this.techStack.map((t) => t.category)).size;
    this.stats.totalApplications = this.techStack.reduce((sum, t) => sum + t.applications, 0);
    this.stats.corePercentage = Math.round(this.stats.core / this.stats.total * 100);
  }
  updateChartData() {
    const categoryCount = this.techStack.reduce((acc, tech) => {
      acc[tech.category] = (acc[tech.category] || 0) + 1;
      return acc;
    }, {});
    this.categoryChartData = {
      labels: Object.keys(categoryCount),
      datasets: [{
        data: Object.values(categoryCount),
        backgroundColor: [
          "#0ea5e9",
          "#22c55e",
          "#f59e0b",
          "#ef4444",
          "#8b5cf6",
          "#06b6d4",
          "#84cc16"
        ]
      }]
    };
    const supportCount = this.techStack.reduce((acc, tech) => {
      acc[tech.supportLevel] = (acc[tech.supportLevel] || 0) + 1;
      return acc;
    }, {});
    this.supportChartData = {
      labels: Object.keys(supportCount),
      datasets: [{
        label: "Technologies",
        data: Object.values(supportCount),
        backgroundColor: ["#22c55e", "#f59e0b", "#f97316", "#ef4444"]
      }]
    };
  }
  openAddModal() {
    this.editMode = false;
    this.editData = null;
    this.showModal = true;
  }
  editTechStack(techStack) {
    this.editMode = true;
    this.editData = {
      name: techStack.technology,
      category: techStack.category,
      version: techStack.version,
      description: "",
      purpose: techStack.purpose,
      status: "active",
      maintainer: "Unknown",
      licenseType: techStack.licenseType || "MIT"
    };
    this.showModal = true;
  }
  deleteTechStack(techStack) {
    if (confirm(`Are you sure you want to delete ${techStack.technology}?`)) {
      this.techStack = this.techStack.filter((t) => t.id !== techStack.id);
      this.filteredTechStack = this.filteredTechStack.filter((t) => t.id !== techStack.id);
      this.updateStats();
      this.updateChartData();
    }
  }
  closeModal() {
    this.showModal = false;
    this.editMode = false;
    this.editData = null;
  }
  onTechStackSaved(techStackData) {
    this.modalLoading = true;
    setTimeout(() => {
      console.log("Tech stack saved:", techStackData);
      this.modalLoading = false;
      this.closeModal();
      this.loadTechStack();
    }, 1e3);
  }
  onSearchInput(event) {
    const target = event.target;
    this.onSearchChanged(target.value);
  }
  onSearchChanged(searchTerm) {
    this.applyFilters();
  }
  onFilterChanged(filterType, event) {
    const target = event.target;
    this.applyFilters();
  }
  onSortChanged(sort) {
    console.log("Sort changed:", sort);
  }
  applyFilters() {
    this.filteredTechStack = [...this.techStack];
  }
  static \u0275fac = function TechStackComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _TechStackComponent)(\u0275\u0275directiveInject(FormBuilder));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _TechStackComponent, selectors: [["app-tech-stack"]], decls: 75, vars: 20, consts: [[1, "tech-stack-page"], [1, "page-content"], [1, "page-header"], [1, "header-content"], [1, "page-subtitle"], [1, "header-actions"], ["variant", "primary", "leftIcon", "M12 4v16m8-8H4", 3, "clicked"], [1, "stats-grid"], ["title", "Total Technologies"], [1, "stat-content"], [1, "stat-number"], [1, "stat-change"], ["title", "Core Technologies"], [1, "stat-number", "core"], ["title", "End of Life Soon"], [1, "stat-number", "warning"], ["title", "Applications Using"], [1, "charts-section"], ["title", "Technologies by Category", "subtitle", "Distribution across categories"], ["type", "doughnut", "height", "300px", 3, "data", "options"], ["title", "Support Level Distribution", "subtitle", "Technologies by support level"], ["type", "bar", "height", "300px", 3, "data", "options"], ["title", "Technology Stack", "subtitle", "All technologies in use"], [1, "table-controls"], [1, "search-controls"], ["type", "text", "placeholder", "Search technologies...", 1, "search-input", 3, "input"], [1, "filter-select", 3, "change"], ["value", ""], ["value", "frontend"], ["value", "backend"], ["value", "database"], ["value", "infrastructure"], ["value", "devops"], ["value", "testing"], ["value", "monitoring"], ["value", "active"], ["value", "maintenance"], ["value", "deprecated"], ["value", "end_of_life"], [3, "sortChanged", "columns", "data", "actions", "loading", "sortable"], [3, "closed", "saved", "isOpen", "editMode", "initialData", "loading", "showApplicationSelection"]], template: function TechStackComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "div", 3)(4, "h1");
      \u0275\u0275text(5, "Technology Stack");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(6, "p", 4);
      \u0275\u0275text(7, "Manage and monitor technology stack across applications");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(8, "div", 5)(9, "app-button", 6);
      \u0275\u0275listener("clicked", function TechStackComponent_Template_app_button_clicked_9_listener() {
        return ctx.openAddModal();
      });
      \u0275\u0275text(10, " Add Technology ");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(11, "div", 7)(12, "app-card", 8)(13, "div", 9)(14, "div", 10);
      \u0275\u0275text(15);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(16, "div", 11);
      \u0275\u0275text(17);
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(18, "app-card", 12)(19, "div", 9)(20, "div", 13);
      \u0275\u0275text(21);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(22, "div", 11);
      \u0275\u0275text(23);
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(24, "app-card", 14)(25, "div", 9)(26, "div", 15);
      \u0275\u0275text(27);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(28, "div", 11);
      \u0275\u0275text(29, "Need replacement");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(30, "app-card", 16)(31, "div", 9)(32, "div", 10);
      \u0275\u0275text(33);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(34, "div", 11);
      \u0275\u0275text(35, "Total usage");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(36, "div", 17)(37, "app-card", 18);
      \u0275\u0275element(38, "app-chart", 19);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(39, "app-card", 20);
      \u0275\u0275element(40, "app-chart", 21);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(41, "app-card", 22)(42, "div", 23)(43, "div", 24)(44, "input", 25);
      \u0275\u0275listener("input", function TechStackComponent_Template_input_input_44_listener($event) {
        return ctx.onSearchInput($event);
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(45, "select", 26);
      \u0275\u0275listener("change", function TechStackComponent_Template_select_change_45_listener($event) {
        return ctx.onFilterChanged("category", $event);
      });
      \u0275\u0275elementStart(46, "option", 27);
      \u0275\u0275text(47, "All Categories");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(48, "option", 28);
      \u0275\u0275text(49, "Frontend");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(50, "option", 29);
      \u0275\u0275text(51, "Backend");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(52, "option", 30);
      \u0275\u0275text(53, "Database");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(54, "option", 31);
      \u0275\u0275text(55, "Infrastructure");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(56, "option", 32);
      \u0275\u0275text(57, "DevOps");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(58, "option", 33);
      \u0275\u0275text(59, "Testing");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(60, "option", 34);
      \u0275\u0275text(61, "Monitoring");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(62, "select", 26);
      \u0275\u0275listener("change", function TechStackComponent_Template_select_change_62_listener($event) {
        return ctx.onFilterChanged("supportLevel", $event);
      });
      \u0275\u0275elementStart(63, "option", 27);
      \u0275\u0275text(64, "All Support Levels");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(65, "option", 35);
      \u0275\u0275text(66, "Active");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(67, "option", 36);
      \u0275\u0275text(68, "Maintenance");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(69, "option", 37);
      \u0275\u0275text(70, "Deprecated");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(71, "option", 38);
      \u0275\u0275text(72, "End of Life");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(73, "app-table", 39);
      \u0275\u0275listener("sortChanged", function TechStackComponent_Template_app_table_sortChanged_73_listener($event) {
        return ctx.onSortChanged($event);
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(74, "app-shared-tech-stack-modal", 40);
      \u0275\u0275listener("closed", function TechStackComponent_Template_app_shared_tech_stack_modal_closed_74_listener() {
        return ctx.closeModal();
      })("saved", function TechStackComponent_Template_app_shared_tech_stack_modal_saved_74_listener($event) {
        return ctx.onTechStackSaved($event);
      });
      \u0275\u0275elementEnd()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(15);
      \u0275\u0275textInterpolate(ctx.stats.total);
      \u0275\u0275advance(2);
      \u0275\u0275textInterpolate1("Across ", ctx.stats.categories, " categories");
      \u0275\u0275advance(4);
      \u0275\u0275textInterpolate(ctx.stats.core);
      \u0275\u0275advance(2);
      \u0275\u0275textInterpolate1("", ctx.stats.corePercentage, "% of total");
      \u0275\u0275advance(4);
      \u0275\u0275textInterpolate(ctx.stats.endOfLife);
      \u0275\u0275advance(6);
      \u0275\u0275textInterpolate(ctx.stats.totalApplications);
      \u0275\u0275advance(5);
      \u0275\u0275property("data", ctx.categoryChartData)("options", ctx.chartOptions);
      \u0275\u0275advance(2);
      \u0275\u0275property("data", ctx.supportChartData)("options", ctx.chartOptions);
      \u0275\u0275advance(33);
      \u0275\u0275property("columns", ctx.tableColumns)("data", ctx.filteredTechStack)("actions", ctx.tableActions)("loading", ctx.loading)("sortable", true);
      \u0275\u0275advance();
      \u0275\u0275property("isOpen", ctx.showModal)("editMode", ctx.editMode)("initialData", ctx.editData)("loading", ctx.modalLoading)("showApplicationSelection", true);
    }
  }, dependencies: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    NgSelectOption,
    \u0275NgSelectMultipleOption,
    CardComponent,
    ButtonComponent,
    TableComponent,
    ChartComponent,
    SharedTechStackModalComponent
  ], styles: [`

.tech-stack-page[_ngcontent-%COMP%] {
  min-height: 100%;
}
.page-content[_ngcontent-%COMP%] {
  padding: var(--spacing-xl);
}
.page-header[_ngcontent-%COMP%] {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-xl);
}
.header-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--secondary-900);
}
.page-subtitle[_ngcontent-%COMP%] {
  margin: 0;
  font-size: var(--text-lg);
  color: var(--secondary-600);
}
.stats-grid[_ngcontent-%COMP%] {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}
.stat-content[_ngcontent-%COMP%] {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
}
.stat-number[_ngcontent-%COMP%] {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--secondary-900);
}
.stat-number.core[_ngcontent-%COMP%] {
  color: var(--primary-600);
}
.stat-number.warning[_ngcontent-%COMP%] {
  color: var(--warning-600);
}
.stat-change[_ngcontent-%COMP%] {
  font-size: var(--text-sm);
  color: var(--secondary-600);
}
.charts-section[_ngcontent-%COMP%] {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}
.table-controls[_ngcontent-%COMP%] {
  margin-bottom: var(--spacing-lg);
}
.search-controls[_ngcontent-%COMP%] {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}
.search-input[_ngcontent-%COMP%], 
.filter-select[_ngcontent-%COMP%] {
  height: 40px;
  padding: 0 var(--spacing-md);
  border: 1px solid var(--secondary-300);
  border-radius: var(--radius-md);
  background: white;
  font-size: var(--text-sm);
  color: var(--secondary-800);
  transition: all 0.2s ease;
}
.search-input[_ngcontent-%COMP%]:focus, 
.filter-select[_ngcontent-%COMP%]:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}
.search-input[_ngcontent-%COMP%] {
  flex: 1;
  min-width: 200px;
}
.filter-select[_ngcontent-%COMP%] {
  min-width: 150px;
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-sm) center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
  appearance: none;
}
@media (max-width: 768px) {
  .page-header[_ngcontent-%COMP%] {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }
  .stats-grid[_ngcontent-%COMP%] {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  .charts-section[_ngcontent-%COMP%] {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  .search-controls[_ngcontent-%COMP%] {
    flex-direction: column;
    align-items: stretch;
  }
  .search-input[_ngcontent-%COMP%], 
   .filter-select[_ngcontent-%COMP%] {
    min-width: auto;
  }
}
/*# sourceMappingURL=tech-stack.component.css.map */`] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TechStackComponent, [{
    type: Component,
    args: [{ selector: "app-tech-stack", standalone: true, imports: [
      CommonModule,
      RouterModule,
      ReactiveFormsModule,
      CardComponent,
      ButtonComponent,
      TableComponent,
      ChartComponent,
      SharedTechStackModalComponent
    ], template: `
    <div class="tech-stack-page">
      <div class="page-content">
        <div class="page-header">
          <div class="header-content">
            <h1>Technology Stack</h1>
            <p class="page-subtitle">Manage and monitor technology stack across applications</p>
          </div>
          <div class="header-actions">
            <app-button
              variant="primary"
              leftIcon="M12 4v16m8-8H4"
              (clicked)="openAddModal()"
            >
              Add Technology
            </app-button>
          </div>
        </div>

      <!-- Statistics Cards -->
      <div class="stats-grid">
        <app-card title="Total Technologies">
          <div class="stat-content">
            <div class="stat-number">{{ stats.total }}</div>
            <div class="stat-change">Across {{ stats.categories }} categories</div>
          </div>
        </app-card>

        <app-card title="Core Technologies">
          <div class="stat-content">
            <div class="stat-number core">{{ stats.core }}</div>
            <div class="stat-change">{{ stats.corePercentage }}% of total</div>
          </div>
        </app-card>

        <app-card title="End of Life Soon">
          <div class="stat-content">
            <div class="stat-number warning">{{ stats.endOfLife }}</div>
            <div class="stat-change">Need replacement</div>
          </div>
        </app-card>

        <app-card title="Applications Using">
          <div class="stat-content">
            <div class="stat-number">{{ stats.totalApplications }}</div>
            <div class="stat-change">Total usage</div>
          </div>
        </app-card>
      </div>

      <!-- Charts Section -->
      <div class="charts-section">
        <app-card title="Technologies by Category" subtitle="Distribution across categories">
          <app-chart
            type="doughnut"
            [data]="categoryChartData"
            [options]="chartOptions"
            height="300px"
          ></app-chart>
        </app-card>

        <app-card title="Support Level Distribution" subtitle="Technologies by support level">
          <app-chart
            type="bar"
            [data]="supportChartData"
            [options]="chartOptions"
            height="300px"
          ></app-chart>
        </app-card>
      </div>

      <!-- Technology Stack Table -->
      <app-card title="Technology Stack" subtitle="All technologies in use">
        <div class="table-controls">
          <div class="search-controls">
            <input
              type="text"
              placeholder="Search technologies..."
              class="search-input"
              (input)="onSearchInput($event)"
            >
            <select class="filter-select" (change)="onFilterChanged('category', $event)">
              <option value="">All Categories</option>
              <option value="frontend">Frontend</option>
              <option value="backend">Backend</option>
              <option value="database">Database</option>
              <option value="infrastructure">Infrastructure</option>
              <option value="devops">DevOps</option>
              <option value="testing">Testing</option>
              <option value="monitoring">Monitoring</option>
            </select>
            <select class="filter-select" (change)="onFilterChanged('supportLevel', $event)">
              <option value="">All Support Levels</option>
              <option value="active">Active</option>
              <option value="maintenance">Maintenance</option>
              <option value="deprecated">Deprecated</option>
              <option value="end_of_life">End of Life</option>
            </select>
          </div>
        </div>

        <app-table
          [columns]="tableColumns"
          [data]="filteredTechStack"
          [actions]="tableActions"
          [loading]="loading"
          [sortable]="true"
          (sortChanged)="onSortChanged($event)"
        ></app-table>
      </app-card>

      <!-- Add/Edit Modal -->
      <app-shared-tech-stack-modal
        [isOpen]="showModal"
        [editMode]="editMode"
        [initialData]="editData"
        [loading]="modalLoading"
        [showApplicationSelection]="true"
        (closed)="closeModal()"
        (saved)="onTechStackSaved($event)"
      ></app-shared-tech-stack-modal>
      </div>
    </div>
  `, styles: [`/* angular:styles/component:scss;022cedfffc7d860933bde846d51434032d182623f8d454268648d32ddd0eed92;C:/Users/<USER>/Documents/VibeCoding/AppCatalog/app-catalog/frontend/src/app/modules/tech-stack/tech-stack.component.ts */
.tech-stack-page {
  min-height: 100%;
}
.page-content {
  padding: var(--spacing-xl);
}
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-xl);
}
.header-content h1 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--secondary-900);
}
.page-subtitle {
  margin: 0;
  font-size: var(--text-lg);
  color: var(--secondary-600);
}
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}
.stat-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
}
.stat-number {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--secondary-900);
}
.stat-number.core {
  color: var(--primary-600);
}
.stat-number.warning {
  color: var(--warning-600);
}
.stat-change {
  font-size: var(--text-sm);
  color: var(--secondary-600);
}
.charts-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}
.table-controls {
  margin-bottom: var(--spacing-lg);
}
.search-controls {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}
.search-input,
.filter-select {
  height: 40px;
  padding: 0 var(--spacing-md);
  border: 1px solid var(--secondary-300);
  border-radius: var(--radius-md);
  background: white;
  font-size: var(--text-sm);
  color: var(--secondary-800);
  transition: all 0.2s ease;
}
.search-input:focus,
.filter-select:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}
.search-input {
  flex: 1;
  min-width: 200px;
}
.filter-select {
  min-width: 150px;
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-sm) center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
  appearance: none;
}
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }
  .stats-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  .charts-section {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  .search-controls {
    flex-direction: column;
    align-items: stretch;
  }
  .search-input,
  .filter-select {
    min-width: auto;
  }
}
/*# sourceMappingURL=tech-stack.component.css.map */
`] }]
  }], () => [{ type: FormBuilder }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(TechStackComponent, { className: "TechStackComponent", filePath: "src/app/modules/tech-stack/tech-stack.component.ts", lineNumber: 303 });
})();
export {
  TechStackComponent
};
//# sourceMappingURL=chunk-OL7UTNXE.js.map
