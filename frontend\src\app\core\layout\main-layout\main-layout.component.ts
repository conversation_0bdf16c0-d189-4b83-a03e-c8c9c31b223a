import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HostL<PERSON><PERSON>, Inject, PLATFORM_ID } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { RouterOutlet, Router } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil, tap } from 'rxjs/operators';

import { HeaderComponent } from '../header/header.component';
import { SidebarComponent } from '../sidebar/sidebar.component';
import { AuthService } from '../../../modules/auth/services/auth.service';

@Component({
  selector: 'app-main-layout',
  standalone: true,
  imports: [CommonModule, RouterOutlet, HeaderComponent, SidebarComponent],
  template: `
    <div class="layout-container">
      @if (isAuthenticated) {
        <!-- Header -->
        <app-header
          [sidebarOpen]="sidebarOpen"
          (sidebarToggle)="toggleSidebar()"
          (searchPerformed)="onSearch($event)"
        ></app-header>

        <!-- Sidebar -->
        <app-sidebar
          [isOpen]="sidebarOpen"
          [isMobile]="isMobile"
          (sidebarClose)="closeSidebar()"
        ></app-sidebar>

        <!-- Main Content -->
        <main class="main-content" [class.sidebar-open]="sidebarOpen && !isMobile">
          <div class="content-wrapper">
            <router-outlet></router-outlet>
          </div>
        </main>
      } @else if (isInitialAuthCheck) {
        <!-- Show loading overlay during initial auth check -->
        <div class="loading-overlay">
          <div class="loading-spinner">
            <div class="spinner"></div>
            <p class="loading-text">Loading...</p>
          </div>
        </div>
      } @else {
        <!-- Auth Content -->  
        <main class="auth-content">
          <router-outlet></router-outlet>
        </main>
      }

      <!-- General loading overlay -->
      @if (isLoading) {
        <div class="loading-overlay">
          <div class="loading-spinner">
            <div class="spinner"></div>
            <p class="loading-text">Loading...</p>
          </div>
        </div>
      }
    </div>
  `,
  styleUrls: ['./main-layout.component.scss']
})
export class MainLayoutComponent implements OnInit, OnDestroy {
  sidebarOpen = true;
  isMobile = false;
  isLoading = false;
  isAuthenticated = false;
  isInitialAuthCheck = true;

  private destroy$ = new Subject<void>();

  constructor(
    @Inject(PLATFORM_ID) private platformId: Object,
    public authService: AuthService,
    private router: Router
  ) {
    // Initialize authentication status
    this.isAuthenticated = this.authService.isAuthenticated();

    // Subscribe to auth state changes
    this.authService.currentUser$
      .pipe(
        takeUntil(this.destroy$),
        tap(() => this.isInitialAuthCheck = false)
      )
      .subscribe(user => {
        this.isAuthenticated = !!user;
        if (!user && !this.isInitialAuthCheck) {
          this.router.navigate(['/auth/login']);
        }
      });

    // Subscribe to loading state
    this.authService.isLoading$
      .pipe(takeUntil(this.destroy$))
      .subscribe(loading => {
        this.isLoading = loading;
      });
  }

  ngOnInit(): void {
    this.checkScreenSize();
    this.initializeSidebar();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  @HostListener('window:resize', ['$event'])
  onResize(event: any): void {
    this.checkScreenSize();
  }

  private checkScreenSize(): void {
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }

    const previousIsMobile = this.isMobile;
    this.isMobile = window.innerWidth < 768;

    // If switching from mobile to desktop, open sidebar only if authenticated
    if (previousIsMobile && !this.isMobile && this.isAuthenticated) {
      this.sidebarOpen = true;
    }
    // If switching from desktop to mobile, close sidebar
    else if (!previousIsMobile && this.isMobile) {
      this.sidebarOpen = false;
    }
  }

  private initializeSidebar(): void {
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }

    // On mobile, always start with sidebar closed
    if (this.isMobile) {
      this.sidebarOpen = false;
      return;
    }

    // Get saved sidebar state from localStorage
    const savedSidebarState = localStorage.getItem('sidebarOpen');
    if (savedSidebarState !== null) {
      this.sidebarOpen = JSON.parse(savedSidebarState);
    }
  }

  toggleSidebar(): void {
    this.sidebarOpen = !this.sidebarOpen;
    this.saveSidebarState();
  }

  closeSidebar(): void {
    this.sidebarOpen = false;
    this.saveSidebarState();
  }

  private saveSidebarState(): void {
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }

    // Only save state for desktop
    if (!this.isMobile) {
      localStorage.setItem('sidebarOpen', JSON.stringify(this.sidebarOpen));
    }
  }

  onSearch(searchTerm: string): void {
    // Implement global search functionality
    console.log('Global search:', searchTerm);
  }

  setLoading(loading: boolean): void {
    this.isLoading = loading;
  }
}
