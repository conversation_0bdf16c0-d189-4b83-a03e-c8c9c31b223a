{"version": 3, "sources": ["src/app/shared/models/dashboard.model.ts", "src/app/modules/dashboard/dashboard.service.ts", "src/app/modules/dashboard/dashboard.component.ts"], "sourcesContent": ["export interface DashboardMetrics {\n  totalApplications: number;\n  applicationsByStatus: ApplicationStatusMetric[];\n  applicationsByCriticality: ApplicationCriticalityMetric[];\n  applicationsByDepartment: DepartmentMetric[];\n  techStackDistribution: TechStackMetric[];\n  dependencyHealth: DependencyHealthMetric;\n  securityMetrics: SecurityMetrics;\n  recentActivity: ActivityItem[];\n  upcomingTasks: TaskItem[];\n  systemHealth: SystemHealthMetric[];\n}\n\nexport interface ApplicationStatusMetric {\n  status: string;\n  count: number;\n  percentage: number;\n  color: string;\n}\n\nexport interface ApplicationCriticalityMetric {\n  criticality: string;\n  count: number;\n  percentage: number;\n  color: string;\n}\n\nexport interface DepartmentMetric {\n  department: string;\n  applicationCount: number;\n  percentage: number;\n  averageSecurityScore: number;\n  criticalVulnerabilities: number;\n}\n\nexport interface TechStackMetric {\n  technology: string;\n  category: string;\n  usageCount: number;\n  percentage: number;\n  averageVersion: string;\n  endOfLifeCount: number;\n  securityIssues: number;\n}\n\nexport interface DependencyHealthMetric {\n  totalDependencies: number;\n  healthyDependencies: number;\n  outdatedDependencies: number;\n  vulnerableDependencies: number;\n  endOfLifeDependencies: number;\n  healthScore: number;\n  criticalUpdatesRequired: number;\n}\n\nexport interface SecurityMetrics {\n  averageSecurityScore: number;\n  totalVulnerabilities: number;\n  criticalVulnerabilities: number;\n  highVulnerabilities: number;\n  mediumVulnerabilities: number;\n  lowVulnerabilities: number;\n  resolvedVulnerabilities: number;\n  openVulnerabilities: number;\n  averageResolutionTime: number; // in days\n  complianceScore: number;\n  lastAssessmentDate: Date;\n  overdueAssessments: number;\n}\n\nexport interface ActivityItem {\n  id: string;\n  type: ActivityType;\n  title: string;\n  description: string;\n  applicationName: string;\n  applicationId: number;\n  timestamp: Date;\n  user: string;\n  severity?: string;\n  icon: string;\n  color: string;\n}\n\nexport interface TaskItem {\n  id: string;\n  type: TaskType;\n  title: string;\n  description: string;\n  applicationName: string;\n  applicationId: number;\n  dueDate: Date;\n  priority: TaskPriority;\n  assignee: string;\n  status: TaskStatus;\n  estimatedEffort?: number; // in hours\n}\n\nexport interface SystemHealthMetric {\n  component: string;\n  status: HealthStatus;\n  uptime: number;\n  responseTime: number;\n  errorRate: number;\n  lastChecked: Date;\n  issues: string[];\n}\n\nexport interface ChartData {\n  labels: string[];\n  datasets: ChartDataset[];\n}\n\nexport interface ChartDataset {\n  label: string;\n  data: number[];\n  backgroundColor?: string | string[];\n  borderColor?: string | string[];\n  borderWidth?: number;\n  fill?: boolean;\n  tension?: number;\n}\n\nexport interface FilterOptions {\n  departments: string[];\n  lifecycleStatuses: string[];\n  businessCriticalities: string[];\n  techStackCategories: string[];\n  dateRange: DateRange;\n  securityScoreRange: NumberRange;\n  searchTerm: string;\n}\n\nexport interface DateRange {\n  startDate: Date | null;\n  endDate: Date | null;\n}\n\nexport interface NumberRange {\n  min: number;\n  max: number;\n}\n\nexport interface DashboardWidget {\n  id: string;\n  title: string;\n  type: WidgetType;\n  size: WidgetSize;\n  position: WidgetPosition;\n  data: any;\n  refreshInterval?: number; // in seconds\n  lastUpdated: Date;\n  isVisible: boolean;\n  configuration: WidgetConfiguration;\n}\n\nexport interface WidgetPosition {\n  x: number;\n  y: number;\n  width: number;\n  height: number;\n}\n\nexport interface WidgetConfiguration {\n  showLegend?: boolean;\n  showDataLabels?: boolean;\n  colorScheme?: string;\n  chartType?: string;\n  aggregationType?: string;\n  timeframe?: string;\n  filters?: any;\n}\n\n// Enums\nexport enum ActivityType {\n  APPLICATION_CREATED = 'application_created',\n  APPLICATION_UPDATED = 'application_updated',\n  APPLICATION_DELETED = 'application_deleted',\n  DEPENDENCY_ADDED = 'dependency_added',\n  DEPENDENCY_UPDATED = 'dependency_updated',\n  VULNERABILITY_DISCOVERED = 'vulnerability_discovered',\n  VULNERABILITY_RESOLVED = 'vulnerability_resolved',\n  SECURITY_ASSESSMENT = 'security_assessment',\n  DOCUMENTATION_UPLOADED = 'documentation_uploaded',\n  STAKEHOLDER_ADDED = 'stakeholder_added',\n  TECH_STACK_UPDATED = 'tech_stack_updated'\n}\n\nexport enum TaskType {\n  SECURITY_ASSESSMENT = 'security_assessment',\n  DEPENDENCY_UPDATE = 'dependency_update',\n  VULNERABILITY_REMEDIATION = 'vulnerability_remediation',\n  DOCUMENTATION_UPDATE = 'documentation_update',\n  COMPLIANCE_REVIEW = 'compliance_review',\n  TECH_STACK_UPGRADE = 'tech_stack_upgrade',\n  STAKEHOLDER_REVIEW = 'stakeholder_review'\n}\n\nexport enum TaskPriority {\n  LOW = 'low',\n  MEDIUM = 'medium',\n  HIGH = 'high',\n  CRITICAL = 'critical'\n}\n\nexport enum TaskStatus {\n  PENDING = 'pending',\n  IN_PROGRESS = 'in_progress',\n  COMPLETED = 'completed',\n  OVERDUE = 'overdue',\n  CANCELLED = 'cancelled'\n}\n\nexport enum HealthStatus {\n  HEALTHY = 'healthy',\n  WARNING = 'warning',\n  CRITICAL = 'critical',\n  UNKNOWN = 'unknown'\n}\n\nexport enum WidgetType {\n  CHART = 'chart',\n  METRIC = 'metric',\n  TABLE = 'table',\n  LIST = 'list',\n  GAUGE = 'gauge',\n  MAP = 'map'\n}\n\nexport enum WidgetSize {\n  SMALL = 'small',\n  MEDIUM = 'medium',\n  LARGE = 'large',\n  EXTRA_LARGE = 'extra_large'\n}\n\nexport interface ExportOptions {\n  format: ExportFormat;\n  includeCharts: boolean;\n  includeRawData: boolean;\n  dateRange: DateRange;\n  filters: FilterOptions;\n}\n\nexport enum ExportFormat {\n  PDF = 'pdf',\n  EXCEL = 'excel',\n  CSV = 'csv',\n  JSON = 'json'\n}\n\nexport interface NotificationSettings {\n  emailNotifications: boolean;\n  slackNotifications: boolean;\n  criticalVulnerabilities: boolean;\n  overdueAssessments: boolean;\n  dependencyUpdates: boolean;\n  systemAlerts: boolean;\n  weeklyReports: boolean;\n  monthlyReports: boolean;\n}\n\nexport interface UserPreferences {\n  defaultDashboard: string;\n  theme: 'light' | 'dark' | 'auto';\n  timezone: string;\n  dateFormat: string;\n  numberFormat: string;\n  defaultFilters: FilterOptions;\n  notifications: NotificationSettings;\n  widgetLayout: DashboardWidget[];\n}\n", "import { Injectable } from '@angular/core';\nimport { Observable, of, BehaviorSubject } from 'rxjs';\nimport { map, delay } from 'rxjs/operators';\n\nimport { BaseApiService } from '../../shared/services/base-api.service';\nimport {\n  DashboardMetrics,\n  ApplicationStatusMetric,\n  ApplicationCriticalityMetric,\n  DepartmentMetric,\n  TechStackMetric,\n  DependencyHealthMetric,\n  SecurityMetrics,\n  ActivityItem,\n  TaskItem,\n  SystemHealthMetric,\n  FilterOptions,\n  ChartData,\n  HealthStatus,\n  ActivityType,\n  TaskType,\n  TaskPriority,\n  TaskStatus\n} from '../../shared/models/dashboard.model';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class DashboardService {\n  private metricsSubject = new BehaviorSubject<DashboardMetrics | null>(null);\n  public metrics$ = this.metricsSubject.asObservable();\n\n  constructor(private apiService: BaseApiService) {}\n\n  /**\n   * Get dashboard metrics\n   */\n  getDashboardMetrics(filters?: FilterOptions): Observable<DashboardMetrics> {\n    // In a real application, this would call the API\n    // return this.apiService.get<DashboardMetrics>('dashboard/metrics', { filters });\n\n    // For now, return mock data\n    return this.getMockDashboardMetrics().pipe(\n      delay(500), // Simulate API delay\n      map(metrics => {\n        this.metricsSubject.next(metrics);\n        return metrics;\n      })\n    );\n  }\n\n  /**\n   * Get application status distribution\n   */\n  getApplicationStatusDistribution(): Observable<ChartData> {\n    return this.getMockApplicationStatusData();\n  }\n\n  /**\n   * Get tech stack distribution\n   */\n  getTechStackDistribution(): Observable<ChartData> {\n    return this.getMockTechStackData();\n  }\n\n  /**\n   * Get security metrics over time\n   */\n  getSecurityMetricsOverTime(timeframe: string = '30d'): Observable<ChartData> {\n    return this.getMockSecurityTrendsData();\n  }\n\n  /**\n   * Get dependency health trends\n   */\n  getDependencyHealthTrends(timeframe: string = '30d'): Observable<ChartData> {\n    return this.getMockDependencyTrendsData();\n  }\n\n  /**\n   * Get recent activity\n   */\n  getRecentActivity(limit: number = 10): Observable<ActivityItem[]> {\n    return this.getMockActivityData().pipe(\n      map(activities => activities.slice(0, limit))\n    );\n  }\n\n  /**\n   * Get upcoming tasks\n   */\n  getUpcomingTasks(limit: number = 10): Observable<TaskItem[]> {\n    return this.getMockTaskData().pipe(\n      map(tasks => tasks.slice(0, limit))\n    );\n  }\n\n  /**\n   * Export dashboard data\n   */\n  exportDashboardData(format: 'pdf' | 'excel' | 'csv'): Observable<Blob> {\n    // In a real application, this would call the API\n    // return this.apiService.downloadFile(`dashboard/export?format=${format}`);\n\n    // Mock implementation\n    return of(new Blob(['Mock export data'], { type: 'text/plain' }));\n  }\n\n\n\n  /**\n   * Mock data methods (replace with real API calls)\n   */\n  private getMockDashboardMetrics(): Observable<DashboardMetrics> {\n    const mockMetrics: DashboardMetrics = {\n      totalApplications: 127,\n      applicationsByStatus: [\n        { status: 'Production', count: 85, percentage: 66.9, color: '#22c55e' },\n        { status: 'Development', count: 25, percentage: 19.7, color: '#3b82f6' },\n        { status: 'Testing', count: 12, percentage: 9.4, color: '#f59e0b' },\n        { status: 'Deprecated', count: 5, percentage: 3.9, color: '#ef4444' }\n      ],\n      applicationsByCriticality: [\n        { criticality: 'Critical', count: 15, percentage: 11.8, color: '#ef4444' },\n        { criticality: 'High', count: 35, percentage: 27.6, color: '#f59e0b' },\n        { criticality: 'Medium', count: 52, percentage: 40.9, color: '#3b82f6' },\n        { criticality: 'Low', count: 25, percentage: 19.7, color: '#22c55e' }\n      ],\n      applicationsByDepartment: [\n        {\n          department: 'Engineering',\n          applicationCount: 45,\n          percentage: 35.4,\n          averageSecurityScore: 8.2,\n          criticalVulnerabilities: 3\n        },\n        {\n          department: 'Product',\n          applicationCount: 28,\n          percentage: 22.0,\n          averageSecurityScore: 7.8,\n          criticalVulnerabilities: 2\n        },\n        {\n          department: 'Marketing',\n          applicationCount: 18,\n          percentage: 14.2,\n          averageSecurityScore: 8.5,\n          criticalVulnerabilities: 0\n        },\n        {\n          department: 'Sales',\n          applicationCount: 22,\n          percentage: 17.3,\n          averageSecurityScore: 7.9,\n          criticalVulnerabilities: 1\n        },\n        {\n          department: 'Operations',\n          applicationCount: 14,\n          percentage: 11.0,\n          averageSecurityScore: 8.7,\n          criticalVulnerabilities: 0\n        }\n      ],\n      techStackDistribution: [\n        {\n          technology: 'Angular',\n          category: 'Frontend',\n          usageCount: 42,\n          percentage: 33.1,\n          averageVersion: '16.2',\n          endOfLifeCount: 2,\n          securityIssues: 1\n        },\n        {\n          technology: '.NET Core',\n          category: 'Backend',\n          usageCount: 38,\n          percentage: 29.9,\n          averageVersion: '7.0',\n          endOfLifeCount: 0,\n          securityIssues: 0\n        },\n        {\n          technology: 'SQL Server',\n          category: 'Database',\n          usageCount: 35,\n          percentage: 27.6,\n          averageVersion: '2019',\n          endOfLifeCount: 3,\n          securityIssues: 2\n        },\n        {\n          technology: 'Docker',\n          category: 'Infrastructure',\n          usageCount: 48,\n          percentage: 37.8,\n          averageVersion: '24.0',\n          endOfLifeCount: 0,\n          securityIssues: 0\n        }\n      ],\n      dependencyHealth: {\n        totalDependencies: 1247,\n        healthyDependencies: 1089,\n        outdatedDependencies: 98,\n        vulnerableDependencies: 45,\n        endOfLifeDependencies: 15,\n        healthScore: 87.3,\n        criticalUpdatesRequired: 12\n      },\n      securityMetrics: {\n        averageSecurityScore: 8.1,\n        totalVulnerabilities: 67,\n        criticalVulnerabilities: 8,\n        highVulnerabilities: 15,\n        mediumVulnerabilities: 28,\n        lowVulnerabilities: 16,\n        resolvedVulnerabilities: 145,\n        openVulnerabilities: 67,\n        averageResolutionTime: 5.2,\n        complianceScore: 92.5,\n        lastAssessmentDate: new Date('2024-01-15'),\n        overdueAssessments: 3\n      },\n      recentActivity: this.getMockActivityItems(),\n      upcomingTasks: this.getMockTaskItems(),\n      systemHealth: [\n        {\n          component: 'API Gateway',\n          status: HealthStatus.HEALTHY,\n          uptime: 99.9,\n          responseTime: 145,\n          errorRate: 0.1,\n          lastChecked: new Date(),\n          issues: []\n        },\n        {\n          component: 'Database',\n          status: HealthStatus.HEALTHY,\n          uptime: 99.8,\n          responseTime: 23,\n          errorRate: 0.0,\n          lastChecked: new Date(),\n          issues: []\n        },\n        {\n          component: 'Authentication Service',\n          status: HealthStatus.WARNING,\n          uptime: 98.5,\n          responseTime: 890,\n          errorRate: 1.2,\n          lastChecked: new Date(),\n          issues: ['High response time']\n        }\n      ]\n    };\n\n    return of(mockMetrics);\n  }\n\n  private getMockApplicationStatusData(): Observable<ChartData> {\n    return of({\n      labels: ['Production', 'Development', 'Testing', 'Staging', 'Deprecated'],\n      datasets: [{\n        label: 'Applications',\n        data: [85, 25, 12, 8, 5],\n        backgroundColor: ['#22c55e', '#3b82f6', '#f59e0b', '#8b5cf6', '#ef4444']\n      }]\n    });\n  }\n\n  private getMockTechStackData(): Observable<ChartData> {\n    return of({\n      labels: ['Angular', '.NET Core', 'SQL Server', 'Docker', 'Redis', 'MongoDB'],\n      datasets: [{\n        label: 'Usage Count',\n        data: [42, 38, 35, 48, 22, 18],\n        backgroundColor: ['#0ea5e9', '#22c55e', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4']\n      }]\n    });\n  }\n\n  private getMockSecurityTrendsData(): Observable<ChartData> {\n    return of({\n      labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],\n      datasets: [\n        {\n          label: 'Security Score',\n          data: [7.8, 8.0, 8.1, 8.1],\n          borderColor: '#22c55e',\n          backgroundColor: 'rgba(34, 197, 94, 0.1)',\n          fill: true\n        },\n        {\n          label: 'Vulnerabilities',\n          data: [78, 72, 69, 67],\n          borderColor: '#ef4444',\n          backgroundColor: 'rgba(239, 68, 68, 0.1)',\n          fill: true\n        }\n      ]\n    });\n  }\n\n  private getMockDependencyTrendsData(): Observable<ChartData> {\n    return of({\n      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],\n      datasets: [\n        {\n          label: 'Healthy',\n          data: [1020, 1045, 1067, 1078, 1085, 1089],\n          borderColor: '#22c55e',\n          backgroundColor: '#22c55e'\n        },\n        {\n          label: 'Outdated',\n          data: [125, 118, 110, 105, 102, 98],\n          borderColor: '#f59e0b',\n          backgroundColor: '#f59e0b'\n        },\n        {\n          label: 'Vulnerable',\n          data: [58, 52, 48, 47, 46, 45],\n          borderColor: '#ef4444',\n          backgroundColor: '#ef4444'\n        }\n      ]\n    });\n  }\n\n  private getMockActivityData(): Observable<ActivityItem[]> {\n    return of(this.getMockActivityItems());\n  }\n\n  private getMockTaskData(): Observable<TaskItem[]> {\n    return of(this.getMockTaskItems());\n  }\n\n  private getMockActivityItems(): ActivityItem[] {\n    return [\n      {\n        id: '1',\n        type: ActivityType.VULNERABILITY_DISCOVERED,\n        title: 'Critical Vulnerability Detected',\n        description: 'SQL injection vulnerability found in Payment Service',\n        applicationName: 'Payment Service',\n        applicationId: 1,\n        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),\n        user: 'Security Scanner',\n        severity: 'critical',\n        icon: 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z',\n        color: '#ef4444'\n      },\n      {\n        id: '2',\n        type: ActivityType.APPLICATION_UPDATED,\n        title: 'Application Updated',\n        description: 'User Management system updated to version 2.1.0',\n        applicationName: 'User Management',\n        applicationId: 2,\n        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),\n        user: 'John Doe',\n        icon: 'M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12',\n        color: '#22c55e'\n      },\n      {\n        id: '3',\n        type: ActivityType.DEPENDENCY_UPDATED,\n        title: 'Dependency Updated',\n        description: 'Angular updated from 16.1 to 16.2 in CRM System',\n        applicationName: 'CRM System',\n        applicationId: 3,\n        timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),\n        user: 'Jane Smith',\n        icon: 'M8 16l2.879-2.879m0 0a3 3 0 104.243-4.242 3 3 0 00-4.243 4.242zM21 12a9 9 0 11-18 0 9 9 0 0118 0z',\n        color: '#3b82f6'\n      }\n    ];\n  }\n\n  private getMockTaskItems(): TaskItem[] {\n    return [\n      {\n        id: '1',\n        type: TaskType.SECURITY_ASSESSMENT,\n        title: 'Security Assessment Due',\n        description: 'Quarterly security assessment for E-commerce Platform',\n        applicationName: 'E-commerce Platform',\n        applicationId: 4,\n        dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),\n        priority: TaskPriority.HIGH,\n        assignee: 'Security Team',\n        status: TaskStatus.PENDING,\n        estimatedEffort: 8\n      },\n      {\n        id: '2',\n        type: TaskType.DEPENDENCY_UPDATE,\n        title: 'Critical Dependency Update',\n        description: 'Update vulnerable lodash library in Analytics Dashboard',\n        applicationName: 'Analytics Dashboard',\n        applicationId: 5,\n        dueDate: new Date(Date.now() + 1 * 24 * 60 * 60 * 1000),\n        priority: TaskPriority.CRITICAL,\n        assignee: 'Development Team',\n        status: TaskStatus.IN_PROGRESS,\n        estimatedEffort: 4\n      },\n      {\n        id: '3',\n        type: TaskType.DOCUMENTATION_UPDATE,\n        title: 'API Documentation Update',\n        description: 'Update API documentation for Notification Service v3.0',\n        applicationName: 'Notification Service',\n        applicationId: 6,\n        dueDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000),\n        priority: TaskPriority.MEDIUM,\n        assignee: 'Technical Writer',\n        status: TaskStatus.PENDING,\n        estimatedEffort: 6\n      }\n    ];\n  }\n}\n", "import { Component, OnIni<PERSON>, OnD<PERSON>roy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\n\nimport { DashboardService } from './dashboard.service';\nimport { CardComponent } from '../../shared/components/card/card.component';\nimport { ButtonComponent } from '../../shared/components/button/button.component';\nimport { ChartComponent, ChartData } from '../../shared/components/chart/chart.component';\nimport { DashboardMetrics, ActivityItem, TaskItem } from '../../shared/models/dashboard.model';\n\n@Component({\n  selector: 'app-dashboard',\n  standalone: true,\n  imports: [CommonModule, RouterModule, CardComponent, ButtonComponent, ChartComponent],\n  template: `\n    <div class=\"dashboard\">\n      <!-- Dashboard Header -->\n      <div class=\"dashboard-header\">\n        <div class=\"header-content\">\n          <div class=\"header-info\">\n            <h1 class=\"dashboard-title\">Dashboard</h1>\n            <p class=\"dashboard-subtitle\">\n              Welcome back! Here's what's happening with your applications.\n            </p>\n          </div>\n          <div class=\"header-actions\">\n            <app-button\n              variant=\"outline\"\n              size=\"sm\"\n              leftIcon=\"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l4-4m-4 4V4\"\n              (clicked)=\"exportDashboard()\"\n              [loading]=\"isExporting\"\n            >\n              Export\n            </app-button>\n            <app-button\n              variant=\"primary\"\n              size=\"sm\"\n              leftIcon=\"M12 4v16m8-8H4\"\n              routerLink=\"/applications/new\"\n            >\n              Add Application\n            </app-button>\n          </div>\n        </div>\n      </div>\n\n      <!-- Loading State -->\n      <div *ngIf=\"isLoading\" class=\"dashboard-loading\">\n        <div class=\"loading-grid\">\n          <div class=\"loading-card\" *ngFor=\"let item of [1,2,3,4,5,6,7,8]\"></div>\n        </div>\n      </div>\n\n      <!-- Dashboard Content -->\n      <div *ngIf=\"!isLoading && metrics\" class=\"dashboard-content\">\n        <!-- Key Metrics Row -->\n        <div class=\"metrics-grid\">\n          <app-card variant=\"default\" size=\"sm\" class=\"metric-card\">\n            <div class=\"metric-content\">\n              <div class=\"metric-value\">{{ metrics.totalApplications }}</div>\n              <div class=\"metric-label\">Total Applications</div>\n              <div class=\"metric-change positive\">\n                <svg class=\"change-icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n                  <polyline points=\"23,6 13.5,15.5 8.5,10.5 1,18\"></polyline>\n                  <polyline points=\"17,6 23,6 23,12\"></polyline>\n                </svg>\n                +12% from last month\n              </div>\n            </div>\n          </app-card>\n\n          <app-card variant=\"default\" size=\"sm\" class=\"metric-card\">\n            <div class=\"metric-content\">\n              <div class=\"metric-value\">{{ metrics.securityMetrics.averageSecurityScore }}/10</div>\n              <div class=\"metric-label\">Security Score</div>\n              <div class=\"metric-change positive\">\n                <svg class=\"change-icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n                  <polyline points=\"23,6 13.5,15.5 8.5,10.5 1,18\"></polyline>\n                  <polyline points=\"17,6 23,6 23,12\"></polyline>\n                </svg>\n                +0.3 from last week\n              </div>\n            </div>\n          </app-card>\n\n          <app-card variant=\"default\" size=\"sm\" class=\"metric-card\">\n            <div class=\"metric-content\">\n              <div class=\"metric-value\">{{ metrics.dependencyHealth.healthScore }}%</div>\n              <div class=\"metric-label\">Dependency Health</div>\n              <div class=\"metric-change neutral\">\n                <svg class=\"change-icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n                  <line x1=\"5\" y1=\"12\" x2=\"19\" y2=\"12\"></line>\n                </svg>\n                No change\n              </div>\n            </div>\n          </app-card>\n\n          <app-card variant=\"default\" size=\"sm\" class=\"metric-card\">\n            <div class=\"metric-content\">\n              <div class=\"metric-value\">{{ metrics.securityMetrics.criticalVulnerabilities }}</div>\n              <div class=\"metric-label\">Critical Vulnerabilities</div>\n              <div class=\"metric-change negative\">\n                <svg class=\"change-icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n                  <polyline points=\"23,18 13.5,8.5 8.5,13.5 1,6\"></polyline>\n                  <polyline points=\"17,18 23,18 23,12\"></polyline>\n                </svg>\n                +2 from last week\n              </div>\n            </div>\n          </app-card>\n        </div>\n\n        <!-- Charts and Analytics Row -->\n        <div class=\"analytics-grid\">\n          <!-- Application Status Distribution -->\n          <app-card title=\"Application Status\" subtitle=\"Distribution by lifecycle status\" class=\"chart-card\">\n            <app-chart\n              type=\"doughnut\"\n              [data]=\"applicationStatusChartData\"\n              [loading]=\"isLoading\"\n              height=\"300px\"\n            ></app-chart>\n          </app-card>\n\n          <!-- Tech Stack Distribution -->\n          <app-card title=\"Tech Stack\" subtitle=\"Most used technologies\" class=\"chart-card\">\n            <app-chart\n              type=\"bar\"\n              [data]=\"techStackChartData\"\n              [loading]=\"isLoading\"\n              height=\"300px\"\n            ></app-chart>\n          </app-card>\n\n          <!-- Security Trends -->\n          <app-card title=\"Security Trends\" subtitle=\"Security score and vulnerabilities over time\" class=\"chart-card\">\n            <app-chart\n              type=\"line\"\n              [data]=\"securityTrendsChartData\"\n              [loading]=\"isLoading\"\n              height=\"300px\"\n            ></app-chart>\n            <div slot=\"footer\">\n              <app-button variant=\"outline\" size=\"sm\" [fullWidth]=\"true\" routerLink=\"/security/vulnerabilities\">\n                View All Vulnerabilities\n              </app-button>\n            </div>\n          </app-card>\n        </div>\n\n        <!-- Activity and Tasks Row -->\n        <div class=\"activity-grid\">\n          <!-- Recent Activity -->\n          <app-card title=\"Recent Activity\" subtitle=\"Latest updates and changes\" class=\"activity-card\">\n            <div class=\"activity-list\">\n              <div class=\"activity-item\" *ngFor=\"let activity of recentActivity\">\n                <div class=\"activity-icon\" [style.color]=\"activity.color\">\n                  <svg viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" [attr.d]=\"activity.icon\" />\n                  </svg>\n                </div>\n                <div class=\"activity-content\">\n                  <div class=\"activity-title\">{{ activity.title }}</div>\n                  <div class=\"activity-description\">{{ activity.description }}</div>\n                  <div class=\"activity-meta\">\n                    <span class=\"activity-app\">{{ activity.applicationName }}</span>\n                    <span class=\"activity-time\">{{ activity.timestamp | date:'short' }}</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div slot=\"footer\">\n              <app-button variant=\"ghost\" size=\"sm\" [fullWidth]=\"true\">\n                View All Activity\n              </app-button>\n            </div>\n          </app-card>\n\n          <!-- Upcoming Tasks -->\n          <app-card title=\"Upcoming Tasks\" subtitle=\"Items requiring attention\" class=\"tasks-card\">\n            <div class=\"tasks-list\">\n              <div class=\"task-item\" *ngFor=\"let task of upcomingTasks\">\n                <div class=\"task-priority\" [class]=\"'priority-' + task.priority\"></div>\n                <div class=\"task-content\">\n                  <div class=\"task-title\">{{ task.title }}</div>\n                  <div class=\"task-description\">{{ task.description }}</div>\n                  <div class=\"task-meta\">\n                    <span class=\"task-app\">{{ task.applicationName }}</span>\n                    <span class=\"task-due\">Due {{ task.dueDate | date:'MMM d' }}</span>\n                    <span class=\"task-assignee\">{{ task.assignee }}</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <div slot=\"footer\">\n              <app-button variant=\"ghost\" size=\"sm\" [fullWidth]=\"true\">\n                View All Tasks\n              </app-button>\n            </div>\n          </app-card>\n        </div>\n      </div>\n\n      <!-- Error State -->\n      <div *ngIf=\"hasError\" class=\"dashboard-error\">\n        <div class=\"error-content\">\n          <svg class=\"error-icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n            <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n            <line x1=\"15\" y1=\"9\" x2=\"9\" y2=\"15\"></line>\n            <line x1=\"9\" y1=\"9\" x2=\"15\" y2=\"15\"></line>\n          </svg>\n          <h3>Failed to load dashboard</h3>\n          <p>There was an error loading the dashboard data. Please try again.</p>\n          <app-button variant=\"primary\" (clicked)=\"loadDashboard()\">\n            Retry\n          </app-button>\n        </div>\n      </div>\n    </div>\n  `,\n  styleUrls: ['./dashboard.component.scss']\n})\nexport class DashboardComponent implements OnInit, OnDestroy {\n  metrics: DashboardMetrics | null = null;\n  recentActivity: ActivityItem[] = [];\n  upcomingTasks: TaskItem[] = [];\n\n  isLoading = true;\n  hasError = false;\n  isExporting = false;\n\n  // Chart data\n  applicationStatusChartData: ChartData | null = null;\n  techStackChartData: ChartData | null = null;\n  securityTrendsChartData: ChartData | null = null;\n\n\n\n  private destroy$ = new Subject<void>();\n\n  constructor(private dashboardService: DashboardService) {}\n\n  ngOnInit(): void {\n    this.loadDashboard();\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  loadDashboard(): void {\n    this.isLoading = true;\n    this.hasError = false;\n\n    // Load dashboard metrics\n    this.dashboardService.getDashboardMetrics()\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (metrics) => {\n          this.metrics = metrics;\n          this.recentActivity = metrics.recentActivity.slice(0, 5);\n          this.upcomingTasks = metrics.upcomingTasks.slice(0, 5);\n          this.loadChartData();\n          this.isLoading = false;\n        },\n        error: (error) => {\n          console.error('Error loading dashboard:', error);\n          this.hasError = true;\n          this.isLoading = false;\n        }\n      });\n  }\n\n  private loadChartData(): void {\n    // Load application status chart data\n    this.dashboardService.getApplicationStatusDistribution()\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(data => {\n        this.applicationStatusChartData = data;\n      });\n\n    // Load tech stack chart data\n    this.dashboardService.getTechStackDistribution()\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(data => {\n        this.techStackChartData = data;\n      });\n\n    // Load security trends chart data\n    this.dashboardService.getSecurityMetricsOverTime()\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(data => {\n        this.securityTrendsChartData = data;\n      });\n  }\n\n  exportDashboard(): void {\n    this.isExporting = true;\n\n    this.dashboardService.exportDashboardData('pdf')\n      .pipe(takeUntil(this.destroy$))\n      .subscribe({\n        next: (blob) => {\n          // Handle file download\n          const url = window.URL.createObjectURL(blob);\n          const link = document.createElement('a');\n          link.href = url;\n          link.download = `dashboard-export-${new Date().toISOString().split('T')[0]}.pdf`;\n          link.click();\n          window.URL.revokeObjectURL(url);\n          this.isExporting = false;\n        },\n        error: (error) => {\n          console.error('Error exporting dashboard:', error);\n          this.isExporting = false;\n        }\n      });\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8KA,IAAY;CAAZ,SAAYA,eAAY;AACtB,EAAAA,cAAA,qBAAA,IAAA;AACA,EAAAA,cAAA,qBAAA,IAAA;AACA,EAAAA,cAAA,qBAAA,IAAA;AACA,EAAAA,cAAA,kBAAA,IAAA;AACA,EAAAA,cAAA,oBAAA,IAAA;AACA,EAAAA,cAAA,0BAAA,IAAA;AACA,EAAAA,cAAA,wBAAA,IAAA;AACA,EAAAA,cAAA,qBAAA,IAAA;AACA,EAAAA,cAAA,wBAAA,IAAA;AACA,EAAAA,cAAA,mBAAA,IAAA;AACA,EAAAA,cAAA,oBAAA,IAAA;AACF,GAZY,iBAAA,eAAY,CAAA,EAAA;AAcxB,IAAY;CAAZ,SAAYC,WAAQ;AAClB,EAAAA,UAAA,qBAAA,IAAA;AACA,EAAAA,UAAA,mBAAA,IAAA;AACA,EAAAA,UAAA,2BAAA,IAAA;AACA,EAAAA,UAAA,sBAAA,IAAA;AACA,EAAAA,UAAA,mBAAA,IAAA;AACA,EAAAA,UAAA,oBAAA,IAAA;AACA,EAAAA,UAAA,oBAAA,IAAA;AACF,GARY,aAAA,WAAQ,CAAA,EAAA;AAUpB,IAAY;CAAZ,SAAYC,eAAY;AACtB,EAAAA,cAAA,KAAA,IAAA;AACA,EAAAA,cAAA,QAAA,IAAA;AACA,EAAAA,cAAA,MAAA,IAAA;AACA,EAAAA,cAAA,UAAA,IAAA;AACF,GALY,iBAAA,eAAY,CAAA,EAAA;AAOxB,IAAY;CAAZ,SAAYC,aAAU;AACpB,EAAAA,YAAA,SAAA,IAAA;AACA,EAAAA,YAAA,aAAA,IAAA;AACA,EAAAA,YAAA,WAAA,IAAA;AACA,EAAAA,YAAA,SAAA,IAAA;AACA,EAAAA,YAAA,WAAA,IAAA;AACF,GANY,eAAA,aAAU,CAAA,EAAA;AAQtB,IAAY;CAAZ,SAAYC,eAAY;AACtB,EAAAA,cAAA,SAAA,IAAA;AACA,EAAAA,cAAA,SAAA,IAAA;AACA,EAAAA,cAAA,UAAA,IAAA;AACA,EAAAA,cAAA,SAAA,IAAA;AACF,GALY,iBAAA,eAAY,CAAA,EAAA;AAOxB,IAAY;CAAZ,SAAYC,aAAU;AACpB,EAAAA,YAAA,OAAA,IAAA;AACA,EAAAA,YAAA,QAAA,IAAA;AACA,EAAAA,YAAA,OAAA,IAAA;AACA,EAAAA,YAAA,MAAA,IAAA;AACA,EAAAA,YAAA,OAAA,IAAA;AACA,EAAAA,YAAA,KAAA,IAAA;AACF,GAPY,eAAA,aAAU,CAAA,EAAA;AAStB,IAAY;CAAZ,SAAYC,aAAU;AACpB,EAAAA,YAAA,OAAA,IAAA;AACA,EAAAA,YAAA,QAAA,IAAA;AACA,EAAAA,YAAA,OAAA,IAAA;AACA,EAAAA,YAAA,aAAA,IAAA;AACF,GALY,eAAA,aAAU,CAAA,EAAA;AAetB,IAAY;CAAZ,SAAYC,eAAY;AACtB,EAAAA,cAAA,KAAA,IAAA;AACA,EAAAA,cAAA,OAAA,IAAA;AACA,EAAAA,cAAA,KAAA,IAAA;AACA,EAAAA,cAAA,MAAA,IAAA;AACF,GALY,iBAAA,eAAY,CAAA,EAAA;;;ACxNlB,IAAO,mBAAP,MAAO,kBAAgB;EAIP;EAHZ,iBAAiB,IAAI,gBAAyC,IAAI;EACnE,WAAW,KAAK,eAAe,aAAY;EAElD,YAAoB,YAA0B;AAA1B,SAAA,aAAA;EAA6B;;;;EAKjD,oBAAoB,SAAuB;AAKzC,WAAO,KAAK,wBAAuB,EAAG;MACpC,MAAM,GAAG;;MACT,IAAI,aAAU;AACZ,aAAK,eAAe,KAAK,OAAO;AAChC,eAAO;MACT,CAAC;IAAC;EAEN;;;;EAKA,mCAAgC;AAC9B,WAAO,KAAK,6BAA4B;EAC1C;;;;EAKA,2BAAwB;AACtB,WAAO,KAAK,qBAAoB;EAClC;;;;EAKA,2BAA2B,YAAoB,OAAK;AAClD,WAAO,KAAK,0BAAyB;EACvC;;;;EAKA,0BAA0B,YAAoB,OAAK;AACjD,WAAO,KAAK,4BAA2B;EACzC;;;;EAKA,kBAAkB,QAAgB,IAAE;AAClC,WAAO,KAAK,oBAAmB,EAAG,KAChC,IAAI,gBAAc,WAAW,MAAM,GAAG,KAAK,CAAC,CAAC;EAEjD;;;;EAKA,iBAAiB,QAAgB,IAAE;AACjC,WAAO,KAAK,gBAAe,EAAG,KAC5B,IAAI,WAAS,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC;EAEvC;;;;EAKA,oBAAoB,QAA+B;AAKjD,WAAO,GAAG,IAAI,KAAK,CAAC,kBAAkB,GAAG,EAAE,MAAM,aAAY,CAAE,CAAC;EAClE;;;;EAOQ,0BAAuB;AAC7B,UAAM,cAAgC;MACpC,mBAAmB;MACnB,sBAAsB;QACpB,EAAE,QAAQ,cAAc,OAAO,IAAI,YAAY,MAAM,OAAO,UAAS;QACrE,EAAE,QAAQ,eAAe,OAAO,IAAI,YAAY,MAAM,OAAO,UAAS;QACtE,EAAE,QAAQ,WAAW,OAAO,IAAI,YAAY,KAAK,OAAO,UAAS;QACjE,EAAE,QAAQ,cAAc,OAAO,GAAG,YAAY,KAAK,OAAO,UAAS;;MAErE,2BAA2B;QACzB,EAAE,aAAa,YAAY,OAAO,IAAI,YAAY,MAAM,OAAO,UAAS;QACxE,EAAE,aAAa,QAAQ,OAAO,IAAI,YAAY,MAAM,OAAO,UAAS;QACpE,EAAE,aAAa,UAAU,OAAO,IAAI,YAAY,MAAM,OAAO,UAAS;QACtE,EAAE,aAAa,OAAO,OAAO,IAAI,YAAY,MAAM,OAAO,UAAS;;MAErE,0BAA0B;QACxB;UACE,YAAY;UACZ,kBAAkB;UAClB,YAAY;UACZ,sBAAsB;UACtB,yBAAyB;;QAE3B;UACE,YAAY;UACZ,kBAAkB;UAClB,YAAY;UACZ,sBAAsB;UACtB,yBAAyB;;QAE3B;UACE,YAAY;UACZ,kBAAkB;UAClB,YAAY;UACZ,sBAAsB;UACtB,yBAAyB;;QAE3B;UACE,YAAY;UACZ,kBAAkB;UAClB,YAAY;UACZ,sBAAsB;UACtB,yBAAyB;;QAE3B;UACE,YAAY;UACZ,kBAAkB;UAClB,YAAY;UACZ,sBAAsB;UACtB,yBAAyB;;;MAG7B,uBAAuB;QACrB;UACE,YAAY;UACZ,UAAU;UACV,YAAY;UACZ,YAAY;UACZ,gBAAgB;UAChB,gBAAgB;UAChB,gBAAgB;;QAElB;UACE,YAAY;UACZ,UAAU;UACV,YAAY;UACZ,YAAY;UACZ,gBAAgB;UAChB,gBAAgB;UAChB,gBAAgB;;QAElB;UACE,YAAY;UACZ,UAAU;UACV,YAAY;UACZ,YAAY;UACZ,gBAAgB;UAChB,gBAAgB;UAChB,gBAAgB;;QAElB;UACE,YAAY;UACZ,UAAU;UACV,YAAY;UACZ,YAAY;UACZ,gBAAgB;UAChB,gBAAgB;UAChB,gBAAgB;;;MAGpB,kBAAkB;QAChB,mBAAmB;QACnB,qBAAqB;QACrB,sBAAsB;QACtB,wBAAwB;QACxB,uBAAuB;QACvB,aAAa;QACb,yBAAyB;;MAE3B,iBAAiB;QACf,sBAAsB;QACtB,sBAAsB;QACtB,yBAAyB;QACzB,qBAAqB;QACrB,uBAAuB;QACvB,oBAAoB;QACpB,yBAAyB;QACzB,qBAAqB;QACrB,uBAAuB;QACvB,iBAAiB;QACjB,oBAAoB,oBAAI,KAAK,YAAY;QACzC,oBAAoB;;MAEtB,gBAAgB,KAAK,qBAAoB;MACzC,eAAe,KAAK,iBAAgB;MACpC,cAAc;QACZ;UACE,WAAW;UACX,QAAQ,aAAa;UACrB,QAAQ;UACR,cAAc;UACd,WAAW;UACX,aAAa,oBAAI,KAAI;UACrB,QAAQ,CAAA;;QAEV;UACE,WAAW;UACX,QAAQ,aAAa;UACrB,QAAQ;UACR,cAAc;UACd,WAAW;UACX,aAAa,oBAAI,KAAI;UACrB,QAAQ,CAAA;;QAEV;UACE,WAAW;UACX,QAAQ,aAAa;UACrB,QAAQ;UACR,cAAc;UACd,WAAW;UACX,aAAa,oBAAI,KAAI;UACrB,QAAQ,CAAC,oBAAoB;;;;AAKnC,WAAO,GAAG,WAAW;EACvB;EAEQ,+BAA4B;AAClC,WAAO,GAAG;MACR,QAAQ,CAAC,cAAc,eAAe,WAAW,WAAW,YAAY;MACxE,UAAU,CAAC;QACT,OAAO;QACP,MAAM,CAAC,IAAI,IAAI,IAAI,GAAG,CAAC;QACvB,iBAAiB,CAAC,WAAW,WAAW,WAAW,WAAW,SAAS;OACxE;KACF;EACH;EAEQ,uBAAoB;AAC1B,WAAO,GAAG;MACR,QAAQ,CAAC,WAAW,aAAa,cAAc,UAAU,SAAS,SAAS;MAC3E,UAAU,CAAC;QACT,OAAO;QACP,MAAM,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;QAC7B,iBAAiB,CAAC,WAAW,WAAW,WAAW,WAAW,WAAW,SAAS;OACnF;KACF;EACH;EAEQ,4BAAyB;AAC/B,WAAO,GAAG;MACR,QAAQ,CAAC,UAAU,UAAU,UAAU,QAAQ;MAC/C,UAAU;QACR;UACE,OAAO;UACP,MAAM,CAAC,KAAK,GAAK,KAAK,GAAG;UACzB,aAAa;UACb,iBAAiB;UACjB,MAAM;;QAER;UACE,OAAO;UACP,MAAM,CAAC,IAAI,IAAI,IAAI,EAAE;UACrB,aAAa;UACb,iBAAiB;UACjB,MAAM;;;KAGX;EACH;EAEQ,8BAA2B;AACjC,WAAO,GAAG;MACR,QAAQ,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;MACjD,UAAU;QACR;UACE,OAAO;UACP,MAAM,CAAC,MAAM,MAAM,MAAM,MAAM,MAAM,IAAI;UACzC,aAAa;UACb,iBAAiB;;QAEnB;UACE,OAAO;UACP,MAAM,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,EAAE;UAClC,aAAa;UACb,iBAAiB;;QAEnB;UACE,OAAO;UACP,MAAM,CAAC,IAAI,IAAI,IAAI,IAAI,IAAI,EAAE;UAC7B,aAAa;UACb,iBAAiB;;;KAGtB;EACH;EAEQ,sBAAmB;AACzB,WAAO,GAAG,KAAK,qBAAoB,CAAE;EACvC;EAEQ,kBAAe;AACrB,WAAO,GAAG,KAAK,iBAAgB,CAAE;EACnC;EAEQ,uBAAoB;AAC1B,WAAO;MACL;QACE,IAAI;QACJ,MAAM,aAAa;QACnB,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,eAAe;QACf,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,GAAI;QACnD,MAAM;QACN,UAAU;QACV,MAAM;QACN,OAAO;;MAET;QACE,IAAI;QACJ,MAAM,aAAa;QACnB,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,eAAe;QACf,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,GAAI;QACnD,MAAM;QACN,MAAM;QACN,OAAO;;MAET;QACE,IAAI;QACJ,MAAM,aAAa;QACnB,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,eAAe;QACf,WAAW,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,GAAI;QACnD,MAAM;QACN,MAAM;QACN,OAAO;;;EAGb;EAEQ,mBAAgB;AACtB,WAAO;MACL;QACE,IAAI;QACJ,MAAM,SAAS;QACf,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,eAAe;QACf,SAAS,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,KAAK,GAAI;QACtD,UAAU,aAAa;QACvB,UAAU;QACV,QAAQ,WAAW;QACnB,iBAAiB;;MAEnB;QACE,IAAI;QACJ,MAAM,SAAS;QACf,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,eAAe;QACf,SAAS,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,KAAK,GAAI;QACtD,UAAU,aAAa;QACvB,UAAU;QACV,QAAQ,WAAW;QACnB,iBAAiB;;MAEnB;QACE,IAAI;QACJ,MAAM,SAAS;QACf,OAAO;QACP,aAAa;QACb,iBAAiB;QACjB,eAAe;QACf,SAAS,IAAI,KAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,KAAK,GAAI;QACtD,UAAU,aAAa;QACvB,UAAU;QACV,QAAQ,WAAW;QACnB,iBAAiB;;;EAGvB;;qCA5YW,mBAAgB,mBAAA,cAAA,CAAA;EAAA;4EAAhB,mBAAgB,SAAhB,kBAAgB,WAAA,YAFf,OAAM,CAAA;;;sEAEP,kBAAgB,CAAA;UAH5B;WAAW;MACV,YAAY;KACb;;;;;;;;ACyBS,IAAA,oBAAA,GAAA,OAAA,EAAA;;;;;AAFJ,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAiD,GAAA,OAAA,EAAA;AAE7C,IAAA,qBAAA,GAAA,0CAAA,GAAA,GAAA,OAAA,EAAA;AACF,IAAA,uBAAA,EAAM;;;AADuC,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,0BAAA,GAAA,GAAA,CAAA;;;;;AA2GvC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAmE,GAAA,OAAA,EAAA;;AAE/D,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,QAAA,EAAA;AACF,IAAA,uBAAA,EAAM;;AAER,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA8B,GAAA,OAAA,EAAA;AACA,IAAA,iBAAA,CAAA;AAAoB,IAAA,uBAAA;AAChD,IAAA,yBAAA,GAAA,OAAA,EAAA;AAAkC,IAAA,iBAAA,CAAA;AAA0B,IAAA,uBAAA;AAC5D,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA2B,IAAA,QAAA,EAAA;AACE,IAAA,iBAAA,EAAA;AAA8B,IAAA,uBAAA;AACzD,IAAA,yBAAA,IAAA,QAAA,EAAA;AAA4B,IAAA,iBAAA,EAAA;;AAAuC,IAAA,uBAAA,EAAO,EACtE,EACF;;;;AAZqB,IAAA,oBAAA;AAAA,IAAA,sBAAA,SAAA,YAAA,KAAA;AAE+C,IAAA,oBAAA,CAAA;;AAI5C,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,YAAA,KAAA;AACM,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,YAAA,WAAA;AAEL,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,YAAA,eAAA;AACC,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,sBAAA,IAAA,GAAA,YAAA,WAAA,OAAA,CAAA;;;;;AAelC,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,OAAA,EAAA;AACA,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA0B,GAAA,OAAA,EAAA;AACA,IAAA,iBAAA,CAAA;AAAgB,IAAA,uBAAA;AACxC,IAAA,yBAAA,GAAA,OAAA,EAAA;AAA8B,IAAA,iBAAA,CAAA;AAAsB,IAAA,uBAAA;AACpD,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAuB,GAAA,QAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AAA0B,IAAA,uBAAA;AACjD,IAAA,yBAAA,IAAA,QAAA,EAAA;AAAuB,IAAA,iBAAA,EAAA;;AAAqC,IAAA,uBAAA;AAC5D,IAAA,yBAAA,IAAA,QAAA,EAAA;AAA4B,IAAA,iBAAA,EAAA;AAAmB,IAAA,uBAAA,EAAO,EAClD,EACF;;;;AATqB,IAAA,oBAAA;AAAA,IAAA,qBAAA,cAAA,QAAA,QAAA;AAED,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,QAAA,KAAA;AACM,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,QAAA,WAAA;AAEL,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,QAAA,eAAA;AACA,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,QAAA,sBAAA,IAAA,GAAA,QAAA,SAAA,OAAA,GAAA,EAAA;AACK,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,QAAA,QAAA;;;;;AAxI1C,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA6D,GAAA,OAAA,EAAA,EAEjC,GAAA,YAAA,EAAA,EACkC,GAAA,OAAA,EAAA,EAC5B,GAAA,OAAA,EAAA;AACA,IAAA,iBAAA,CAAA;AAA+B,IAAA,uBAAA;AACzD,IAAA,yBAAA,GAAA,OAAA,EAAA;AAA0B,IAAA,iBAAA,GAAA,oBAAA;AAAkB,IAAA,uBAAA;AAC5C,IAAA,yBAAA,GAAA,OAAA,EAAA;;AACE,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,IAAA,YAAA,EAAA,EAA2D,IAAA,YAAA,EAAA;AAE7D,IAAA,uBAAA;AACA,IAAA,iBAAA,IAAA,wBAAA;AACF,IAAA,uBAAA,EAAM,EACF;;AAGR,IAAA,yBAAA,IAAA,YAAA,EAAA,EAA0D,IAAA,OAAA,EAAA,EAC5B,IAAA,OAAA,EAAA;AACA,IAAA,iBAAA,EAAA;AAAqD,IAAA,uBAAA;AAC/E,IAAA,yBAAA,IAAA,OAAA,EAAA;AAA0B,IAAA,iBAAA,IAAA,gBAAA;AAAc,IAAA,uBAAA;AACxC,IAAA,yBAAA,IAAA,OAAA,EAAA;;AACE,IAAA,yBAAA,IAAA,OAAA,EAAA;AACE,IAAA,oBAAA,IAAA,YAAA,EAAA,EAA2D,IAAA,YAAA,EAAA;AAE7D,IAAA,uBAAA;AACA,IAAA,iBAAA,IAAA,uBAAA;AACF,IAAA,uBAAA,EAAM,EACF;;AAGR,IAAA,yBAAA,IAAA,YAAA,EAAA,EAA0D,IAAA,OAAA,EAAA,EAC5B,IAAA,OAAA,EAAA;AACA,IAAA,iBAAA,EAAA;AAA2C,IAAA,uBAAA;AACrE,IAAA,yBAAA,IAAA,OAAA,EAAA;AAA0B,IAAA,iBAAA,IAAA,mBAAA;AAAiB,IAAA,uBAAA;AAC3C,IAAA,yBAAA,IAAA,OAAA,EAAA;;AACE,IAAA,yBAAA,IAAA,OAAA,EAAA;AACE,IAAA,oBAAA,IAAA,QAAA,EAAA;AACF,IAAA,uBAAA;AACA,IAAA,iBAAA,IAAA,aAAA;AACF,IAAA,uBAAA,EAAM,EACF;;AAGR,IAAA,yBAAA,IAAA,YAAA,EAAA,EAA0D,IAAA,OAAA,EAAA,EAC5B,IAAA,OAAA,EAAA;AACA,IAAA,iBAAA,EAAA;AAAqD,IAAA,uBAAA;AAC/E,IAAA,yBAAA,IAAA,OAAA,EAAA;AAA0B,IAAA,iBAAA,IAAA,0BAAA;AAAwB,IAAA,uBAAA;AAClD,IAAA,yBAAA,IAAA,OAAA,EAAA;;AACE,IAAA,yBAAA,IAAA,OAAA,EAAA;AACE,IAAA,oBAAA,IAAA,YAAA,EAAA,EAA0D,IAAA,YAAA,EAAA;AAE5D,IAAA,uBAAA;AACA,IAAA,iBAAA,IAAA,qBAAA;AACF,IAAA,uBAAA,EAAM,EACF,EACG;;AAIb,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA4B,IAAA,YAAA,EAAA;AAGxB,IAAA,oBAAA,IAAA,aAAA,EAAA;AAMF,IAAA,uBAAA;AAGA,IAAA,yBAAA,IAAA,YAAA,EAAA;AACE,IAAA,oBAAA,IAAA,aAAA,EAAA;AAMF,IAAA,uBAAA;AAGA,IAAA,yBAAA,IAAA,YAAA,EAAA;AACE,IAAA,oBAAA,IAAA,aAAA,EAAA;AAMA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAmB,IAAA,cAAA,EAAA;AAEf,IAAA,iBAAA,IAAA,4BAAA;AACF,IAAA,uBAAA,EAAa,EACT,EACG;AAIb,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA2B,IAAA,YAAA,EAAA,EAEqE,IAAA,OAAA,EAAA;AAE1F,IAAA,qBAAA,IAAA,2CAAA,IAAA,IAAA,OAAA,EAAA;AAeF,IAAA,uBAAA;AACA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAmB,IAAA,cAAA,EAAA;AAEf,IAAA,iBAAA,IAAA,qBAAA;AACF,IAAA,uBAAA,EAAa,EACT;AAIR,IAAA,yBAAA,IAAA,YAAA,EAAA,EAAyF,IAAA,OAAA,EAAA;AAErF,IAAA,qBAAA,IAAA,2CAAA,IAAA,IAAA,OAAA,EAAA;AAYF,IAAA,uBAAA;AACA,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAmB,IAAA,cAAA,EAAA;AAEf,IAAA,iBAAA,IAAA,kBAAA;AACF,IAAA,uBAAA,EAAa,EACT,EACG,EACP;;;;AA9I0B,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,QAAA,iBAAA;AAcA,IAAA,oBAAA,EAAA;AAAA,IAAA,6BAAA,IAAA,OAAA,QAAA,gBAAA,sBAAA,KAAA;AAcA,IAAA,oBAAA,EAAA;AAAA,IAAA,6BAAA,IAAA,OAAA,QAAA,iBAAA,aAAA,GAAA;AAaA,IAAA,oBAAA,EAAA;AAAA,IAAA,4BAAA,OAAA,QAAA,gBAAA,uBAAA;AAmB1B,IAAA,oBAAA,EAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,0BAAA,EAAmC,WAAA,OAAA,SAAA;AAUnC,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,kBAAA,EAA2B,WAAA,OAAA,SAAA;AAU3B,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,uBAAA,EAAgC,WAAA,OAAA,SAAA;AAKQ,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,aAAA,IAAA;AAYQ,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,cAAA;AAiBV,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,aAAA,IAAA;AASE,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,aAAA;AAcF,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,aAAA,IAAA;;;;;;AAS9C,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA8C,GAAA,OAAA,EAAA;;AAE1C,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,UAAA,EAAA,EAAwC,GAAA,QAAA,EAAA,EACG,GAAA,QAAA,EAAA;AAE7C,IAAA,uBAAA;;AACA,IAAA,yBAAA,GAAA,IAAA;AAAI,IAAA,iBAAA,GAAA,0BAAA;AAAwB,IAAA,uBAAA;AAC5B,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,GAAA,kEAAA;AAAgE,IAAA,uBAAA;AACnE,IAAA,yBAAA,IAAA,cAAA,EAAA;AAA8B,IAAA,qBAAA,WAAA,SAAA,oEAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAW,OAAA,cAAA,CAAe;IAAA,CAAA;AACtD,IAAA,iBAAA,IAAA,SAAA;AACF,IAAA,uBAAA,EAAa,EACT;;;AAMR,IAAO,qBAAP,MAAO,oBAAkB;EAkBT;EAjBpB,UAAmC;EACnC,iBAAiC,CAAA;EACjC,gBAA4B,CAAA;EAE5B,YAAY;EACZ,WAAW;EACX,cAAc;;EAGd,6BAA+C;EAC/C,qBAAuC;EACvC,0BAA4C;EAIpC,WAAW,IAAI,QAAO;EAE9B,YAAoB,kBAAkC;AAAlC,SAAA,mBAAA;EAAqC;EAEzD,WAAQ;AACN,SAAK,cAAa;EACpB;EAEA,cAAW;AACT,SAAK,SAAS,KAAI;AAClB,SAAK,SAAS,SAAQ;EACxB;EAEA,gBAAa;AACX,SAAK,YAAY;AACjB,SAAK,WAAW;AAGhB,SAAK,iBAAiB,oBAAmB,EACtC,KAAK,UAAU,KAAK,QAAQ,CAAC,EAC7B,UAAU;MACT,MAAM,CAAC,YAAW;AAChB,aAAK,UAAU;AACf,aAAK,iBAAiB,QAAQ,eAAe,MAAM,GAAG,CAAC;AACvD,aAAK,gBAAgB,QAAQ,cAAc,MAAM,GAAG,CAAC;AACrD,aAAK,cAAa;AAClB,aAAK,YAAY;MACnB;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,4BAA4B,KAAK;AAC/C,aAAK,WAAW;AAChB,aAAK,YAAY;MACnB;KACD;EACL;EAEQ,gBAAa;AAEnB,SAAK,iBAAiB,iCAAgC,EACnD,KAAK,UAAU,KAAK,QAAQ,CAAC,EAC7B,UAAU,UAAO;AAChB,WAAK,6BAA6B;IACpC,CAAC;AAGH,SAAK,iBAAiB,yBAAwB,EAC3C,KAAK,UAAU,KAAK,QAAQ,CAAC,EAC7B,UAAU,UAAO;AAChB,WAAK,qBAAqB;IAC5B,CAAC;AAGH,SAAK,iBAAiB,2BAA0B,EAC7C,KAAK,UAAU,KAAK,QAAQ,CAAC,EAC7B,UAAU,UAAO;AAChB,WAAK,0BAA0B;IACjC,CAAC;EACL;EAEA,kBAAe;AACb,SAAK,cAAc;AAEnB,SAAK,iBAAiB,oBAAoB,KAAK,EAC5C,KAAK,UAAU,KAAK,QAAQ,CAAC,EAC7B,UAAU;MACT,MAAM,CAAC,SAAQ;AAEb,cAAM,MAAM,OAAO,IAAI,gBAAgB,IAAI;AAC3C,cAAM,OAAO,SAAS,cAAc,GAAG;AACvC,aAAK,OAAO;AACZ,aAAK,WAAW,qBAAoB,oBAAI,KAAI,GAAG,YAAW,EAAG,MAAM,GAAG,EAAE,CAAC,CAAC;AAC1E,aAAK,MAAK;AACV,eAAO,IAAI,gBAAgB,GAAG;AAC9B,aAAK,cAAc;MACrB;MACA,OAAO,CAAC,UAAS;AACf,gBAAQ,MAAM,8BAA8B,KAAK;AACjD,aAAK,cAAc;MACrB;KACD;EACL;;qCAhGW,qBAAkB,4BAAA,gBAAA,CAAA;EAAA;yEAAlB,qBAAkB,WAAA,CAAA,CAAA,eAAA,CAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,oBAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,WAAA,WAAA,QAAA,MAAA,YAAA,kEAAA,GAAA,WAAA,SAAA,GAAA,CAAA,WAAA,WAAA,QAAA,MAAA,YAAA,kBAAA,cAAA,mBAAA,GAAA,CAAA,SAAA,qBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,qBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,mBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,SAAA,gBAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,WAAA,WAAA,QAAA,MAAA,GAAA,aAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,iBAAA,UAAA,GAAA,CAAA,WAAA,aAAA,QAAA,QAAA,UAAA,gBAAA,GAAA,aAAA,GAAA,CAAA,UAAA,8BAAA,GAAA,CAAA,UAAA,iBAAA,GAAA,CAAA,GAAA,iBAAA,SAAA,GAAA,CAAA,MAAA,KAAA,MAAA,MAAA,MAAA,MAAA,MAAA,IAAA,GAAA,CAAA,GAAA,iBAAA,UAAA,GAAA,CAAA,UAAA,6BAAA,GAAA,CAAA,UAAA,mBAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,SAAA,sBAAA,YAAA,oCAAA,GAAA,YAAA,GAAA,CAAA,QAAA,YAAA,UAAA,SAAA,GAAA,QAAA,SAAA,GAAA,CAAA,SAAA,cAAA,YAAA,0BAAA,GAAA,YAAA,GAAA,CAAA,QAAA,OAAA,UAAA,SAAA,GAAA,QAAA,SAAA,GAAA,CAAA,SAAA,mBAAA,YAAA,gDAAA,GAAA,YAAA,GAAA,CAAA,QAAA,QAAA,UAAA,SAAA,GAAA,QAAA,SAAA,GAAA,CAAA,QAAA,QAAA,GAAA,CAAA,WAAA,WAAA,QAAA,MAAA,cAAA,6BAAA,GAAA,WAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,SAAA,mBAAA,YAAA,8BAAA,GAAA,eAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,SAAA,SAAA,GAAA,CAAA,WAAA,SAAA,QAAA,MAAA,GAAA,WAAA,GAAA,CAAA,SAAA,kBAAA,YAAA,6BAAA,GAAA,YAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,SAAA,aAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,WAAA,aAAA,QAAA,QAAA,UAAA,cAAA,GAAA,CAAA,kBAAA,SAAA,mBAAA,SAAA,gBAAA,GAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,sBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,UAAA,GAAA,CAAA,GAAA,UAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,WAAA,aAAA,QAAA,QAAA,UAAA,gBAAA,GAAA,YAAA,GAAA,CAAA,MAAA,MAAA,MAAA,MAAA,KAAA,IAAA,GAAA,CAAA,MAAA,MAAA,MAAA,KAAA,MAAA,KAAA,MAAA,IAAA,GAAA,CAAA,MAAA,KAAA,MAAA,KAAA,MAAA,MAAA,MAAA,IAAA,GAAA,CAAA,WAAA,WAAA,GAAA,SAAA,CAAA,GAAA,UAAA,SAAA,4BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAjN3B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAuB,GAAA,OAAA,CAAA,EAES,GAAA,OAAA,CAAA,EACA,GAAA,OAAA,CAAA,EACD,GAAA,MAAA,CAAA;AACK,MAAA,iBAAA,GAAA,WAAA;AAAS,MAAA,uBAAA;AACrC,MAAA,yBAAA,GAAA,KAAA,CAAA;AACE,MAAA,iBAAA,GAAA,iEAAA;AACF,MAAA,uBAAA,EAAI;AAEN,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA4B,GAAA,cAAA,CAAA;AAKxB,MAAA,qBAAA,WAAA,SAAA,4DAAA;AAAA,eAAW,IAAA,gBAAA;MAAiB,CAAA;AAG5B,MAAA,iBAAA,IAAA,UAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,cAAA,CAAA;AAME,MAAA,iBAAA,IAAA,mBAAA;AACF,MAAA,uBAAA,EAAa,EACT,EACF;AAIR,MAAA,qBAAA,IAAA,oCAAA,GAAA,GAAA,OAAA,CAAA,EAAiD,IAAA,oCAAA,IAAA,IAAA,OAAA,EAAA,EAOY,IAAA,oCAAA,IAAA,GAAA,OAAA,EAAA;AAqK/D,MAAA,uBAAA;;;AA7LU,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,WAAA,IAAA,WAAA;AAiBF,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,SAAA;AAOA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,aAAA,IAAA,OAAA;AAuJA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,QAAA;;oBAjMA,cAAY,SAAA,MAAA,UAAE,cAAY,YAAE,eAAe,iBAAiB,cAAc,GAAA,QAAA,CAAA,67WAAA,EAAA,CAAA;;;sEAmNzE,oBAAkB,CAAA;UAtN9B;uBACW,iBAAe,YACb,MAAI,SACP,CAAC,cAAc,cAAc,eAAe,iBAAiB,cAAc,GAAC,UAC3E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA+MT,QAAA,CAAA,k2SAAA,EAAA,CAAA;;;;6EAGU,oBAAkB,EAAA,WAAA,sBAAA,UAAA,oDAAA,YAAA,IAAA,CAAA;AAAA,GAAA;", "names": ["ActivityType", "TaskType", "TaskPriority", "TaskStatus", "HealthStatus", "WidgetType", "WidgetSize", "ExportFormat"]}