import { Component, Input, Output, EventEmitter, OnInit, OnChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { SlideModalComponent } from '../../slide-modal/slide-modal.component';
import { FileUploadComponent, FileUploadData, UploadedDocument } from '../../file-upload/file-upload.component';
import { ApplicationSelectionService, ApplicationOption } from '../../../services/application-selection.service';
import { Observable } from 'rxjs';

export interface SharedDocumentationFormData {
  applicationId?: number;
  documents: UploadedDocument[];
}

@Component({
  selector: 'app-shared-documentation-modal',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, SlideModalComponent, FileUploadComponent],
  template: `
    <app-slide-modal
      [isOpen]="isOpen"
      [title]="editMode ? 'Edit Documentation' : 'Add Documentation'"
      [subtitle]="showApplicationSelection ? 'Upload documents and select application' : 'Upload documents'"
      [loading]="loading"
      [canConfirm]="canConfirm"
      confirmText="Save Documentation"
      (closed)="onClose()"
      (confirmed)="onSave()"
      (cancelled)="onClose()"
    >
      <form [formGroup]="documentationForm" class="documentation-form">
        <!-- Application Selection (only when not in applications module) -->
        <div class="form-group" *ngIf="showApplicationSelection">
          <label class="form-label">Application *</label>
          <select formControlName="applicationId" class="form-select">
            <option value="">Select an application</option>
            <option *ngFor="let app of applicationOptions$ | async" [value]="app.id">
              {{ app.name }} - {{ app.department }}
            </option>
          </select>
          <div class="field-error" *ngIf="getFieldError('applicationId')">
            {{ getFieldError('applicationId') }}
          </div>
        </div>

        <!-- File Upload Section -->
        <div class="form-section">
          <h4 class="section-title">Upload Documents</h4>
          <app-file-upload
            [showDescription]="true"
            (documentAdded)="onDocumentAdded($event)"
          ></app-file-upload>
        </div>

        <!-- Uploaded Documents List -->
        <div class="form-section" *ngIf="uploadedDocuments.length > 0">
          <h4 class="section-title">Uploaded Documents ({{ uploadedDocuments.length }})</h4>
          <div class="documents-list">
            <div class="document-item" *ngFor="let doc of uploadedDocuments; let i = index">
              <div class="document-icon">{{ doc.icon }}</div>
              <div class="document-info">
                <div class="document-header">
                  <span class="document-name">{{ doc.fileName }}</span>
                  <button class="remove-button" (click)="removeDocument(i)" type="button">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <polyline points="3,6 5,6 21,6"></polyline>
                      <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                    </svg>
                  </button>
                </div>
                <div class="document-meta">
                  <span class="document-type">{{ getDocumentTypeLabel(doc.type) }}</span>
                  <span class="document-size">{{ formatFileSize(doc.fileSize) }}</span>
                  <span class="document-mime">{{ doc.mimeType }}</span>
                </div>
                <div class="document-upload-info">
                  <span class="upload-date">{{ formatDate(doc.uploadedAt) }}</span>
                  <span class="uploaded-by">by {{ doc.uploadedBy }}</span>
                </div>
                <div class="document-description" *ngIf="doc.description">
                  {{ doc.description }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="empty-state" *ngIf="uploadedDocuments.length === 0">
          <p>No documents uploaded yet. Use the upload section above to add documents.</p>
        </div>
      </form>
    </app-slide-modal>
  `,
  styles: [`
    .documentation-form {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-lg);
    }

    .form-group {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-sm);
    }

    .form-label {
      font-size: var(--text-sm);
      font-weight: 500;
      color: var(--secondary-700);
    }

    .form-select {
      height: 40px;
      padding: 0 var(--spacing-md);
      border: 1px solid var(--secondary-300);
      border-radius: var(--radius-md);
      background: white;
      font-size: var(--text-sm);
      color: var(--secondary-800);
      transition: all 0.2s ease;

      &:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px var(--primary-100);
      }
    }

    .field-error {
      font-size: var(--text-sm);
      color: var(--error-600);
    }

    .form-section {
      padding: var(--spacing-lg);
      background: var(--secondary-50);
      border-radius: var(--radius-md);
      border: 1px solid var(--secondary-200);
    }

    .section-title {
      margin: 0 0 var(--spacing-md) 0;
      font-size: var(--text-lg);
      font-weight: 600;
      color: var(--secondary-900);
    }

    .documents-list {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-md);
    }

    .document-item {
      display: flex;
      gap: var(--spacing-md);
      padding: var(--spacing-md);
      background: white;
      border-radius: var(--radius-md);
      border: 1px solid var(--secondary-200);
    }

    .document-icon {
      font-size: 24px;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--primary-100);
      border-radius: var(--radius-md);
    }

    .document-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: var(--spacing-xs);
    }

    .document-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .document-name {
      font-weight: 500;
      color: var(--secondary-900);
    }

    .remove-button {
      padding: var(--spacing-xs);
      background: none;
      border: none;
      color: var(--error-600);
      cursor: pointer;
      border-radius: var(--radius-sm);
      transition: all 0.2s ease;

      &:hover {
        background: var(--error-100);
      }

      svg {
        width: 16px;
        height: 16px;
        stroke-width: 2;
      }
    }

    .document-meta {
      display: flex;
      gap: var(--spacing-md);
      font-size: var(--text-sm);
      color: var(--secondary-600);
    }

    .document-type {
      padding: 2px 8px;
      background: var(--primary-100);
      color: var(--primary-700);
      border-radius: var(--radius-sm);
      font-size: var(--text-xs);
      font-weight: 500;
    }

    .document-upload-info {
      display: flex;
      gap: var(--spacing-sm);
      font-size: var(--text-xs);
      color: var(--secondary-500);
    }

    .document-description {
      font-size: var(--text-sm);
      color: var(--secondary-700);
      font-style: italic;
    }

    .empty-state {
      text-align: center;
      padding: var(--spacing-xl);
      color: var(--secondary-600);
    }

    @media (max-width: 768px) {
      .document-item {
        flex-direction: column;
        gap: var(--spacing-sm);
      }

      .document-meta {
        flex-direction: column;
        gap: var(--spacing-xs);
      }
    }
  `]
})
export class SharedDocumentationModalComponent implements OnInit, OnChanges {
  @Input() isOpen = false;
  @Input() editMode = false;
  @Input() initialData: SharedDocumentationFormData | null = null;
  @Input() loading = false;
  @Input() showApplicationSelection = true; // Hide when used within applications module

  @Output() closed = new EventEmitter<void>();
  @Output() saved = new EventEmitter<SharedDocumentationFormData>();

  documentationForm!: FormGroup;
  uploadedDocuments: UploadedDocument[] = [];
  applicationOptions$: Observable<ApplicationOption[]>;

  constructor(
    private fb: FormBuilder,
    private applicationSelectionService: ApplicationSelectionService
  ) {
    this.applicationOptions$ = this.applicationSelectionService.getApplicationOptions();
  }

  ngOnInit(): void {
    this.initializeForm();
  }

  ngOnChanges(): void {
    if (this.documentationForm && this.initialData) {
      this.documentationForm.patchValue({
        applicationId: this.initialData.applicationId
      });
      this.uploadedDocuments = [...(this.initialData.documents || [])];
    }
  }

  private initializeForm(): void {
    const formConfig: any = {};
    
    if (this.showApplicationSelection) {
      formConfig.applicationId = ['', [Validators.required]];
    }

    this.documentationForm = this.fb.group(formConfig);

    if (this.initialData) {
      this.documentationForm.patchValue({
        applicationId: this.initialData.applicationId
      });
      this.uploadedDocuments = [...(this.initialData.documents || [])];
    }
  }

  get canConfirm(): boolean {
    const formValid = this.documentationForm.valid;
    const hasDocuments = this.uploadedDocuments.length > 0;
    return formValid && hasDocuments;
  }

  onDocumentAdded(fileData: FileUploadData): void {
    const uploadedDoc: UploadedDocument = {
      fileName: fileData.file.name,
      fileSize: fileData.file.size,
      mimeType: fileData.file.type || 'application/octet-stream',
      type: fileData.type,
      description: fileData.description,
      uploadedAt: new Date(),
      uploadedBy: 'Current User', // In real app, get from auth service
      icon: this.getFileIcon(fileData.file.type || 'application/octet-stream')
    };

    this.uploadedDocuments.push(uploadedDoc);
  }

  removeDocument(index: number): void {
    this.uploadedDocuments.splice(index, 1);
  }

  getFieldError(fieldName: string): string {
    const field = this.documentationForm.get(fieldName);
    if (field && field.invalid && field.touched) {
      if (field.errors?.['required']) {
        return `${fieldName} is required`;
      }
    }
    return '';
  }

  getDocumentTypeLabel(type: string): string {
    const typeLabels: { [key: string]: string } = {
      'technical_spec': 'Technical Specification',
      'api_documentation': 'API Documentation',
      'user_manual': 'User Manual',
      'deployment_guide': 'Deployment Guide',
      'architecture_diagram': 'Architecture Diagram',
      'security_policy': 'Security Policy',
      'compliance_report': 'Compliance Report',
      'runbook': 'Runbook',
      'troubleshooting': 'Troubleshooting Guide',
      'changelog': 'Changelog'
    };
    return typeLabels[type] || type;
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  formatDate(date: Date): string {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  }

  getFileIcon(mimeType: string): string {
    if (mimeType.includes('pdf')) return '📄';
    if (mimeType.includes('word') || mimeType.includes('document')) return '📝';
    if (mimeType.includes('image')) return '🖼️';
    if (mimeType.includes('video')) return '🎥';
    if (mimeType.includes('audio')) return '🎵';
    if (mimeType.includes('zip') || mimeType.includes('archive')) return '📦';
    if (mimeType.includes('text')) return '📄';
    return '📁';
  }

  onSave(): void {
    if (this.canConfirm) {
      const formData: SharedDocumentationFormData = {
        documents: this.uploadedDocuments
      };

      if (this.showApplicationSelection) {
        formData.applicationId = this.documentationForm.get('applicationId')?.value;
      }

      this.saved.emit(formData);
    }
  }

  onClose(): void {
    this.closed.emit();
  }
}
