import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { CardComponent } from '../../../shared/components/card/card.component';
import { ButtonComponent } from '../../../shared/components/button/button.component';
import { TabsComponent, TabComponent, TabItem } from '../../../shared/components/tabs/tabs.component';
import { FormInputComponent } from '../../../shared/components/form-input/form-input.component';
import { DependencyModalComponent, DependencyFormData } from '../components/dependency-modal/dependency-modal.component';
import { TechStackModalComponent, TechStackFormData } from '../components/tech-stack-modal/tech-stack-modal.component';
import { StakeholderModalComponent, StakeholderFormData } from '../components/stakeholder-modal/stakeholder-modal.component';
import { DocumentationModalComponent, DocumentationFormData } from '../components/documentation-modal/documentation-modal.component';
import { VulnerabilityModalComponent, VulnerabilityFormData } from '../components/vulnerability-modal/vulnerability-modal.component';
import { Application, LifecycleStatus, BusinessCriticality } from '../../../shared/models/application.model';

@Component({
  selector: 'app-application-form',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    FormsModule,
    ButtonComponent,
    TabsComponent,
    TabComponent,
    FormInputComponent,
    DependencyModalComponent,
    TechStackModalComponent,
    StakeholderModalComponent,
    DocumentationModalComponent,
    VulnerabilityModalComponent
  ],
  template: `
    <div class="application-form-page">
      <div class="page-header">
        <div class="header-content">
          <div class="header-info">
            <h1 class="page-title">{{ isEditMode ? 'Edit Application' : 'New Application' }}</h1>
            <p class="page-subtitle">
              {{ isEditMode ? 'Update application details and configuration.' : 'Register a new application in the catalog.' }}
            </p>
          </div>
          <div class="header-actions">
            <app-button variant="outline" routerLink="/applications">
              Cancel
            </app-button>
            <app-button
              variant="primary"
              [loading]="isSaving"
              (clicked)="saveApplication()"
            >
              {{ isEditMode ? 'Update' : 'Create' }} Application
            </app-button>
          </div>
        </div>
      </div>

      <div class="form-content">
        <form [formGroup]="applicationForm" (ngSubmit)="saveApplication()">
          <app-tabs
            [tabs]="tabItems"
            [activeTabId]="activeTabId"
            (tabChange)="onTabChange($event)"
            #tabsComponent
          >
            <!-- Basic Information Tab -->
            <app-tab id="basic" label="Basic Information">
              <div class="form-section">
                <div class="form-grid">
                  <app-form-input
                    label="Application Name"
                    placeholder="Enter application name"
                    formControlName="name"
                    [required]="true"
                    [errorMessage]="getFieldError('name')"
                  ></app-form-input>

                  <app-form-input
                    label="Owner"
                    placeholder="Enter owner name"
                    formControlName="owner"
                    [required]="true"
                    [errorMessage]="getFieldError('owner')"
                  ></app-form-input>

                  <app-form-input
                    type="select"
                    label="Lifecycle Status"
                    placeholder="Select status"
                    formControlName="lifecycleStatus"
                    [options]="lifecycleStatusOptions"
                    [required]="true"
                    [errorMessage]="getFieldError('lifecycleStatus')"
                  ></app-form-input>

                  <app-form-input
                    type="select"
                    label="Business Criticality"
                    placeholder="Select criticality"
                    formControlName="businessCriticality"
                    [options]="businessCriticalityOptions"
                    [required]="true"
                    [errorMessage]="getFieldError('businessCriticality')"
                  ></app-form-input>

                  <app-form-input
                    label="Version"
                    placeholder="e.g., 1.0.0"
                    formControlName="version"
                    [required]="true"
                    [errorMessage]="getFieldError('version')"
                  ></app-form-input>

                  <app-form-input
                    type="url"
                    label="Repository URL"
                    placeholder="https://github.com/..."
                    formControlName="repository"
                    [errorMessage]="getFieldError('repository')"
                  ></app-form-input>

                  <app-form-input
                    type="url"
                    label="Deployment URL"
                    placeholder="https://app.company.com"
                    formControlName="deploymentUrl"
                    [errorMessage]="getFieldError('deploymentUrl')"
                  ></app-form-input>
                </div>

                <app-form-input
                  type="textarea"
                  label="Description"
                  placeholder="Describe the application's purpose and functionality"
                  formControlName="description"
                  [required]="true"
                  [rows]="4"
                  [maxlength]="500"
                  [showCharacterCount]="true"
                  [errorMessage]="getFieldError('description')"
                ></app-form-input>
              </div>
            </app-tab>

            <!-- Dependencies Tab -->
            <app-tab id="dependencies" label="Dependencies">
              <div class="form-section">
                <div class="section-header">
                  <h3>Application Dependencies</h3>
                  <app-button variant="outline" size="sm" (clicked)="openDependencyModal()">
                    Add Dependency
                  </app-button>
                </div>

                <div class="entity-list" *ngIf="dependencies.length > 0">
                  <div class="entity-item" *ngFor="let dep of dependencies; let i = index">
                    <div class="entity-header">
                      <h4 class="entity-title">{{ dep.name }}</h4>
                      <button class="remove-button" (click)="removeDependency(i)" type="button">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                          <polyline points="3,6 5,6 21,6"></polyline>
                          <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                        </svg>
                      </button>
                    </div>
                    <div class="entity-meta">
                      <span class="entity-badge type-{{ dep.type }}">{{ dep.type }}</span>
                      <span class="entity-badge criticality-{{ dep.criticality }}">{{ dep.criticality }}</span>
                      <span class="entity-version">v{{ dep.version }}</span>
                      <span class="entity-badge" *ngIf="dep.isInternal">Internal</span>
                    </div>
                    <p class="entity-description" *ngIf="dep.description">{{ dep.description }}</p>
                  </div>
                </div>

                <div class="empty-state" *ngIf="dependencies.length === 0">
                  <p>No dependencies added yet. Click "Add Dependency" to get started.</p>
                </div>
              </div>
            </app-tab>

            <!-- Tech Stack Tab -->
            <app-tab id="techstack" label="Tech Stack">
              <div class="form-section">
                <div class="section-header">
                  <h3>Technology Stack</h3>
                  <app-button variant="outline" size="sm" (clicked)="openTechStackModal()">
                    Add Technology
                  </app-button>
                </div>

                <div class="entity-list" *ngIf="techStack.length > 0">
                  <div class="entity-item" *ngFor="let tech of techStack; let i = index">
                    <div class="entity-header">
                      <h4 class="entity-title">{{ tech.technology }}</h4>
                      <button class="remove-button" (click)="removeTechStack(i)" type="button">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                          <polyline points="3,6 5,6 21,6"></polyline>
                          <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                        </svg>
                      </button>
                    </div>
                    <div class="entity-meta">
                      <span class="entity-badge">{{ tech.category }}</span>
                      <span class="entity-version">v{{ tech.version }}</span>
                      <span class="entity-badge" *ngIf="tech.isCore">Core</span>
                    </div>
                    <p class="entity-description" *ngIf="tech.purpose">{{ tech.purpose }}</p>
                  </div>
                </div>

                <div class="empty-state" *ngIf="techStack.length === 0">
                  <p>No technologies added yet. Click "Add Technology" to get started.</p>
                </div>
              </div>
            </app-tab>

            <!-- Stakeholders Tab -->
            <app-tab id="stakeholders" label="Stakeholders">
              <div class="form-section">
                <div class="section-header">
                  <h3>Stakeholders & Teams</h3>
                  <app-button variant="outline" size="sm" (clicked)="openStakeholderModal()">
                    Add Stakeholder
                  </app-button>
                </div>

                <div class="entity-list" *ngIf="stakeholders.length > 0">
                  <div class="entity-item" *ngFor="let stakeholder of stakeholders; let i = index">
                    <div class="entity-header">
                      <h4 class="entity-title">{{ stakeholder.name }}</h4>
                      <button class="remove-button" (click)="removeStakeholder(i)" type="button">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                          <polyline points="3,6 5,6 21,6"></polyline>
                          <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                        </svg>
                      </button>
                    </div>
                    <div class="entity-meta">
                      <span class="entity-badge role-{{ stakeholder.role }}">{{ stakeholder.role.replace('_', ' ') }}</span>
                      <span class="entity-badge">{{ stakeholder.department }}</span>
                      <span class="entity-badge" *ngIf="stakeholder.isPrimary">Primary Contact</span>
                    </div>
                    <p class="entity-description" *ngIf="stakeholder.email">{{ stakeholder.email }}</p>
                    <p class="entity-description" *ngIf="stakeholder.responsibility">{{ stakeholder.responsibility }}</p>
                  </div>
                </div>

                <div class="empty-state" *ngIf="stakeholders.length === 0">
                  <p>No stakeholders added yet. Click "Add Stakeholder" to get started.</p>
                </div>
              </div>
            </app-tab>

            <!-- Documentation Tab -->
            <app-tab id="documentation" label="Documentation">
              <div class="form-section">
                <div class="section-header">
                  <h3>Documentation & Resources</h3>
                  <app-button variant="outline" size="sm" (clicked)="openDocumentationModal()">
                    Add Document
                  </app-button>
                </div>

                <div class="entity-list" *ngIf="documentation.length > 0">
                  <div class="entity-item" *ngFor="let doc of documentation; let i = index">
                    <div class="entity-header">
                      <h4 class="entity-title">{{ doc.title }}</h4>
                      <button class="remove-button" (click)="removeDocumentation(i)" type="button">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                          <polyline points="3,6 5,6 21,6"></polyline>
                          <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                        </svg>
                      </button>
                    </div>
                    <div class="entity-meta">
                      <span class="entity-badge">{{ doc.type.replace('_', ' ') }}</span>
                      <span class="entity-version">v{{ doc.version }}</span>
                      <span class="entity-badge" *ngIf="doc.isPublic">Public</span>
                      <a [href]="doc.url" target="_blank" class="entity-link" *ngIf="doc.url">View Document</a>
                    </div>
                    <p class="entity-description" *ngIf="doc.description">{{ doc.description }}</p>
                  </div>
                </div>

                <div class="empty-state" *ngIf="documentation.length === 0">
                  <p>No documentation added yet. Click "Add Document" to get started.</p>
                </div>
              </div>
            </app-tab>

            <!-- Security Tab -->
            <app-tab id="security" label="Security">
              <div class="form-section">
                <div class="section-header">
                  <h3>Security & Vulnerabilities</h3>
                  <app-button variant="outline" size="sm" (clicked)="openVulnerabilityModal()">
                    Add Vulnerability
                  </app-button>
                </div>

                <div class="entity-list" *ngIf="vulnerabilities.length > 0">
                  <div class="entity-item" *ngFor="let vuln of vulnerabilities; let i = index">
                    <div class="entity-header">
                      <h4 class="entity-title">{{ vuln.title }}</h4>
                      <button class="remove-button" (click)="removeVulnerability(i)" type="button">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                          <polyline points="3,6 5,6 21,6"></polyline>
                          <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                        </svg>
                      </button>
                    </div>
                    <div class="entity-meta">
                      <span class="entity-badge criticality-{{ vuln.severity }}">{{ vuln.severity }}</span>
                      <span class="entity-badge">{{ vuln.status.replace('_', ' ') }}</span>
                      <span class="entity-version" *ngIf="vuln.cvssScore > 0">CVSS: {{ vuln.cvssScore }}</span>
                      <span class="entity-badge" *ngIf="vuln.cveId">{{ vuln.cveId }}</span>
                    </div>
                    <p class="entity-description" *ngIf="vuln.description">{{ vuln.description }}</p>
                  </div>
                </div>

                <div class="empty-state" *ngIf="vulnerabilities.length === 0">
                  <p>No vulnerabilities added yet. Click "Add Vulnerability" to get started.</p>
                </div>
              </div>
            </app-tab>
          </app-tabs>
        </form>
      </div>

      <!-- Modals -->
      <app-dependency-modal
        [isOpen]="showDependencyModal"
        [editMode]="dependencyEditMode"
        [initialData]="dependencyEditData"
        [loading]="modalLoading"
        (closed)="closeDependencyModal()"
        (saved)="saveDependency($event)"
      ></app-dependency-modal>

      <app-tech-stack-modal
        [isOpen]="showTechStackModal"
        [editMode]="techStackEditMode"
        [initialData]="techStackEditData"
        [loading]="modalLoading"
        (closed)="closeTechStackModal()"
        (saved)="saveTechStack($event)"
      ></app-tech-stack-modal>

      <app-stakeholder-modal
        [isOpen]="showStakeholderModal"
        [editMode]="stakeholderEditMode"
        [initialData]="stakeholderEditData"
        [loading]="modalLoading"
        (closed)="closeStakeholderModal()"
        (saved)="saveStakeholder($event)"
      ></app-stakeholder-modal>

      <app-documentation-modal
        [isOpen]="showDocumentationModal"
        [editMode]="documentationEditMode"
        [initialData]="documentationEditData"
        [loading]="modalLoading"
        (closed)="closeDocumentationModal()"
        (saved)="saveDocumentation($event)"
      ></app-documentation-modal>

      <app-vulnerability-modal
        [isOpen]="showVulnerabilityModal"
        [editMode]="vulnerabilityEditMode"
        [initialData]="vulnerabilityEditData"
        [loading]="modalLoading"
        (closed)="closeVulnerabilityModal()"
        (saved)="saveVulnerability($event)"
      ></app-vulnerability-modal>
    </div>
  `,
  styles: [`
    .application-form-page {
      min-height: 100%;
    }

    .page-header {
      margin-bottom: var(--spacing-xl);
    }

    .header-content {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      gap: var(--spacing-lg);
    }

    .header-info {
      flex: 1;
    }

    .page-title {
      margin: 0 0 var(--spacing-xs) 0;
      font-size: var(--text-3xl);
      font-weight: 700;
      color: var(--secondary-900);
    }

    .page-subtitle {
      margin: 0;
      font-size: var(--text-base);
      color: var(--secondary-600);
      line-height: var(--leading-relaxed);
    }

    .header-actions {
      display: flex;
      gap: var(--spacing-sm);
      flex-shrink: 0;
    }

    .form-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: var(--spacing-3xl);
      text-align: center;
      color: var(--secondary-600);
    }

    .placeholder-icon {
      width: 64px;
      height: 64px;
      margin-bottom: var(--spacing-lg);
      color: var(--secondary-400);
    }

    .form-placeholder h3 {
      margin-bottom: var(--spacing-md);
      color: var(--secondary-800);
    }

    .form-placeholder p {
      margin-bottom: var(--spacing-md);
      max-width: 500px;
    }

    .form-placeholder ul {
      text-align: left;
      margin-bottom: var(--spacing-lg);
    }

    .form-placeholder .note {
      font-style: italic;
      color: var(--secondary-500);
    }

    /* Form Styles */
    .form-section {
      padding: var(--spacing-xl);
    }

    .form-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-xl);
    }

    .form-group {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-sm);
    }

    .form-group.full-width {
      grid-column: 1 / -1;
    }

    .form-label {
      font-size: var(--text-sm);
      font-weight: 500;
      color: var(--secondary-700);
      margin-bottom: var(--spacing-sm);
    }

    .form-select {
      padding: var(--spacing-md);
      border: 1px solid var(--secondary-300);
      border-radius: var(--radius-md);
      background: white;
      font-size: var(--text-sm);
      color: var(--secondary-800);
      transition: all 0.2s ease;

      &:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px var(--primary-100);
      }
    }

    .error-message {
      font-size: var(--text-xs);
      color: var(--error-600);
      margin-top: var(--spacing-xs);
    }

    /* Section Headers */
    .section-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: var(--spacing-xl);
      padding-bottom: var(--spacing-md);
      border-bottom: 1px solid var(--secondary-200);
    }

    .section-header h3 {
      margin: 0;
      font-size: var(--text-lg);
      font-weight: 600;
      color: var(--secondary-900);
    }

    /* Entity Lists */
    .entity-list {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-lg);
    }

    .entity-item {
      padding: var(--spacing-lg);
      border: 1px solid var(--secondary-200);
      border-radius: var(--radius-lg);
      background: white;
      position: relative;
    }

    .entity-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: var(--spacing-md);
    }

    .entity-title {
      font-size: var(--text-base);
      font-weight: 600;
      color: var(--secondary-900);
      margin: 0;
    }

    .entity-meta {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      flex-wrap: wrap;
    }

    .entity-badge {
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--radius-sm);
      font-size: var(--text-xs);
      font-weight: 500;
      text-transform: capitalize;

      &.type-library { background: var(--blue-100); color: var(--blue-700); }
      &.type-framework { background: var(--purple-100); color: var(--purple-700); }
      &.type-service { background: var(--green-100); color: var(--green-700); }
      &.type-database { background: var(--orange-100); color: var(--orange-700); }

      &.criticality-critical { background: var(--error-100); color: var(--error-700); }
      &.criticality-high { background: var(--warning-100); color: var(--warning-700); }
      &.criticality-medium { background: var(--primary-100); color: var(--primary-700); }
      &.criticality-low { background: var(--success-100); color: var(--success-700); }

      &.role-product_owner { background: var(--purple-100); color: var(--purple-700); }
      &.role-developer { background: var(--blue-100); color: var(--blue-700); }
      &.role-tech_lead { background: var(--indigo-100); color: var(--indigo-700); }
    }

    .entity-description {
      margin: var(--spacing-sm) 0 0 0;
      font-size: var(--text-sm);
      color: var(--secondary-600);
      line-height: var(--leading-relaxed);
    }

    .entity-version {
      font-size: var(--text-xs);
      color: var(--secondary-600);
      font-weight: 500;
    }

    .entity-link {
      font-size: var(--text-xs);
      color: var(--primary-600);
      text-decoration: none;
      font-weight: 500;

      &:hover {
        color: var(--primary-700);
        text-decoration: underline;
      }
    }

    .remove-button {
      background: none;
      border: none;
      color: var(--error-500);
      cursor: pointer;
      padding: var(--spacing-xs);
      border-radius: var(--radius-sm);
      transition: all 0.2s ease;

      &:hover {
        background: var(--error-50);
        color: var(--error-600);
      }

      svg {
        width: 16px;
        height: 16px;
      }
    }

    .empty-state {
      text-align: center;
      padding: var(--spacing-3xl);
      color: var(--secondary-500);
      background: var(--neutral-50);
      border-radius: var(--radius-lg);
      border: 2px dashed var(--secondary-200);
    }

    .empty-state p {
      margin: 0;
      font-size: var(--text-sm);
    }

    @media (max-width: 768px) {
      .header-content {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-md);
      }

      .header-actions {
        justify-content: flex-end;
      }

      .form-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
      }

      .form-section {
        padding: var(--spacing-lg);
      }

      .entity-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
      }
    }
  `]
})
export class ApplicationFormComponent implements OnInit {
  @ViewChild('tabsComponent') tabsComponent!: TabsComponent;

  isEditMode = false;
  isSaving = false;
  applicationId: number | null = null;

  // Form and data
  applicationForm!: FormGroup;
  activeTabId = 'basic';

  // Tab configuration
  tabItems: TabItem[] = [
    { id: 'basic', label: 'Basic Information', icon: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z' },
    { id: 'dependencies', label: 'Dependencies', icon: 'M8 16l2.879-2.879m0 0a3 3 0 104.243-4.242 3 3 0 00-4.243 4.242zM21 12a9 9 0 11-18 0 9 9 0 0118 0z' },
    { id: 'techstack', label: 'Tech Stack', icon: 'M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z' },
    { id: 'stakeholders', label: 'Stakeholders', icon: 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a4 4 0 11-8 0 4 4 0 018 0z' },
    { id: 'documentation', label: 'Documentation', icon: 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z' },
    { id: 'security', label: 'Security', icon: 'M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z' }
  ];

  // Dynamic arrays for related entities
  dependencies: any[] = [];
  techStack: any[] = [];
  stakeholders: any[] = [];
  documentation: any[] = [];
  vulnerabilities: any[] = [];

  // Modal state
  showDependencyModal = false;
  dependencyEditMode = false;
  dependencyEditData: DependencyFormData | null = null;
  dependencyEditIndex = -1;

  showTechStackModal = false;
  techStackEditMode = false;
  techStackEditData: TechStackFormData | null = null;
  techStackEditIndex = -1;

  showStakeholderModal = false;
  stakeholderEditMode = false;
  stakeholderEditData: StakeholderFormData | null = null;
  stakeholderEditIndex = -1;

  showDocumentationModal = false;
  documentationEditMode = false;
  documentationEditData: DocumentationFormData | null = null;
  documentationEditIndex = -1;

  showVulnerabilityModal = false;
  vulnerabilityEditMode = false;
  vulnerabilityEditData: VulnerabilityFormData | null = null;
  vulnerabilityEditIndex = -1;

  modalLoading = false;

  // Dropdown options
  lifecycleStatusOptions = [
    { value: 'planning', label: 'Planning' },
    { value: 'development', label: 'Development' },
    { value: 'testing', label: 'Testing' },
    { value: 'staging', label: 'Staging' },
    { value: 'production', label: 'Production' },
    { value: 'maintenance', label: 'Maintenance' },
    { value: 'deprecated', label: 'Deprecated' },
    { value: 'retired', label: 'Retired' }
  ];

  businessCriticalityOptions = [
    { value: 'low', label: 'Low' },
    { value: 'medium', label: 'Medium' },
    { value: 'high', label: 'High' },
    { value: 'critical', label: 'Critical' }
  ];

  dependencyTypeOptions = [
    { value: 'library', label: 'Library' },
    { value: 'framework', label: 'Framework' },
    { value: 'service', label: 'Service' },
    { value: 'database', label: 'Database' },
    { value: 'api', label: 'API' },
    { value: 'tool', label: 'Tool' },
    { value: 'infrastructure', label: 'Infrastructure' }
  ];

  dependencyCriticalityOptions = [
    { value: 'low', label: 'Low' },
    { value: 'medium', label: 'Medium' },
    { value: 'high', label: 'High' },
    { value: 'critical', label: 'Critical' }
  ];

  techStackCategoryOptions = [
    { value: 'frontend', label: 'Frontend' },
    { value: 'backend', label: 'Backend' },
    { value: 'database', label: 'Database' },
    { value: 'infrastructure', label: 'Infrastructure' },
    { value: 'monitoring', label: 'Monitoring' },
    { value: 'security', label: 'Security' },
    { value: 'testing', label: 'Testing' },
    { value: 'deployment', label: 'Deployment' },
    { value: 'communication', label: 'Communication' }
  ];

  stakeholderRoleOptions = [
    { value: 'product_owner', label: 'Product Owner' },
    { value: 'tech_lead', label: 'Tech Lead' },
    { value: 'developer', label: 'Developer' },
    { value: 'devops_engineer', label: 'DevOps Engineer' },
    { value: 'security_officer', label: 'Security Officer' },
    { value: 'business_analyst', label: 'Business Analyst' },
    { value: 'project_manager', label: 'Project Manager' },
    { value: 'architect', label: 'Architect' },
    { value: 'qa_engineer', label: 'QA Engineer' },
    { value: 'support_engineer', label: 'Support Engineer' }
  ];

  documentationTypeOptions = [
    { value: 'technical_spec', label: 'Technical Specification' },
    { value: 'api_documentation', label: 'API Documentation' },
    { value: 'user_manual', label: 'User Manual' },
    { value: 'deployment_guide', label: 'Deployment Guide' },
    { value: 'architecture_diagram', label: 'Architecture Diagram' },
    { value: 'security_policy', label: 'Security Policy' },
    { value: 'compliance_report', label: 'Compliance Report' },
    { value: 'runbook', label: 'Runbook' },
    { value: 'troubleshooting', label: 'Troubleshooting' },
    { value: 'changelog', label: 'Changelog' }
  ];

  vulnerabilitySeverityOptions = [
    { value: 'critical', label: 'Critical' },
    { value: 'high', label: 'High' },
    { value: 'medium', label: 'Medium' },
    { value: 'low', label: 'Low' },
    { value: 'info', label: 'Info' }
  ];

  vulnerabilityStatusOptions = [
    { value: 'open', label: 'Open' },
    { value: 'in_progress', label: 'In Progress' },
    { value: 'resolved', label: 'Resolved' },
    { value: 'accepted_risk', label: 'Accepted Risk' },
    { value: 'false_positive', label: 'False Positive' }
  ];

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    this.initializeForm();
    this.checkEditMode();
  }

  private initializeForm(): void {
    this.applicationForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      description: ['', [Validators.required, Validators.minLength(10)]],
      owner: ['', [Validators.required]],
      lifecycleStatus: ['', [Validators.required]],
      businessCriticality: ['', [Validators.required]],
      version: ['', [Validators.required]],
      repository: [''],
      deploymentUrl: ['']
    });
  }

  private checkEditMode(): void {
    this.route.params.subscribe(params => {
      if (params['id']) {
        this.isEditMode = true;
        this.applicationId = +params['id'];
        this.loadApplication(this.applicationId);
      }
    });
  }

  private loadApplication(id: number): void {
    // In a real application, this would load data from a service
    // For now, we'll populate with mock data
    this.applicationForm.patchValue({
      name: 'E-commerce Platform',
      description: 'Main customer-facing e-commerce application with shopping cart, payment processing, and order management.',
      owner: 'John Doe',
      lifecycleStatus: 'production',
      businessCriticality: 'critical',
      version: '2.1.0',
      repository: 'https://github.com/company/ecommerce',
      deploymentUrl: 'https://shop.company.com'
    });

    // Load related entities
    this.dependencies = [
      { name: 'Angular', type: 'framework', version: '16.2.0', criticality: 'high', isInternal: false, description: 'Frontend framework' }
    ];
  }

  onTabChange(tabId: string): void {
    this.activeTabId = tabId;
  }

  getFieldError(fieldName: string): string {
    const field = this.applicationForm.get(fieldName);
    if (field && field.invalid && field.touched) {
      if (field.errors?.['required']) {
        return `${fieldName} is required`;
      }
      if (field.errors?.['minlength']) {
        return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;
      }
      if (field.errors?.['email']) {
        return 'Please enter a valid email address';
      }
      if (field.errors?.['url']) {
        return 'Please enter a valid URL';
      }
    }
    return '';
  }

  // Modal methods for dependencies
  openDependencyModal(): void {
    this.dependencyEditMode = false;
    this.dependencyEditData = null;
    this.dependencyEditIndex = -1;
    this.showDependencyModal = true;
  }

  closeDependencyModal(): void {
    this.showDependencyModal = false;
    this.dependencyEditMode = false;
    this.dependencyEditData = null;
    this.dependencyEditIndex = -1;
  }

  saveDependency(data: DependencyFormData): void {
    if (this.dependencyEditMode && this.dependencyEditIndex >= 0) {
      this.dependencies[this.dependencyEditIndex] = data;
    } else {
      this.dependencies.push(data);
    }
    this.closeDependencyModal();
  }

  removeDependency(index: number): void {
    this.dependencies.splice(index, 1);
  }

  // Modal methods for tech stack
  openTechStackModal(): void {
    this.techStackEditMode = false;
    this.techStackEditData = null;
    this.techStackEditIndex = -1;
    this.showTechStackModal = true;
  }

  closeTechStackModal(): void {
    this.showTechStackModal = false;
    this.techStackEditMode = false;
    this.techStackEditData = null;
    this.techStackEditIndex = -1;
  }

  saveTechStack(data: TechStackFormData): void {
    if (this.techStackEditMode && this.techStackEditIndex >= 0) {
      this.techStack[this.techStackEditIndex] = data;
    } else {
      this.techStack.push(data);
    }
    this.closeTechStackModal();
  }

  removeTechStack(index: number): void {
    this.techStack.splice(index, 1);
  }

  // Modal methods for stakeholders
  openStakeholderModal(): void {
    this.stakeholderEditMode = false;
    this.stakeholderEditData = null;
    this.stakeholderEditIndex = -1;
    this.showStakeholderModal = true;
  }

  closeStakeholderModal(): void {
    this.showStakeholderModal = false;
    this.stakeholderEditMode = false;
    this.stakeholderEditData = null;
    this.stakeholderEditIndex = -1;
  }

  saveStakeholder(data: StakeholderFormData): void {
    if (this.stakeholderEditMode && this.stakeholderEditIndex >= 0) {
      this.stakeholders[this.stakeholderEditIndex] = data;
    } else {
      this.stakeholders.push(data);
    }
    this.closeStakeholderModal();
  }

  removeStakeholder(index: number): void {
    this.stakeholders.splice(index, 1);
  }

  // Modal methods for documentation
  openDocumentationModal(): void {
    this.documentationEditMode = false;
    this.documentationEditData = null;
    this.documentationEditIndex = -1;
    this.showDocumentationModal = true;
  }

  closeDocumentationModal(): void {
    this.showDocumentationModal = false;
    this.documentationEditMode = false;
    this.documentationEditData = null;
    this.documentationEditIndex = -1;
  }

  saveDocumentation(data: DocumentationFormData): void {
    if (this.documentationEditMode && this.documentationEditIndex >= 0) {
      this.documentation[this.documentationEditIndex] = data;
    } else {
      this.documentation.push(data);
    }
    this.closeDocumentationModal();
  }

  removeDocumentation(index: number): void {
    this.documentation.splice(index, 1);
  }

  // Modal methods for vulnerabilities
  openVulnerabilityModal(): void {
    this.vulnerabilityEditMode = false;
    this.vulnerabilityEditData = null;
    this.vulnerabilityEditIndex = -1;
    this.showVulnerabilityModal = true;
  }

  closeVulnerabilityModal(): void {
    this.showVulnerabilityModal = false;
    this.vulnerabilityEditMode = false;
    this.vulnerabilityEditData = null;
    this.vulnerabilityEditIndex = -1;
  }

  saveVulnerability(data: VulnerabilityFormData): void {
    if (this.vulnerabilityEditMode && this.vulnerabilityEditIndex >= 0) {
      this.vulnerabilities[this.vulnerabilityEditIndex] = data;
    } else {
      this.vulnerabilities.push(data);
    }
    this.closeVulnerabilityModal();
  }

  removeVulnerability(index: number): void {
    this.vulnerabilities.splice(index, 1);
  }

  saveApplication(): void {
    if (this.applicationForm.valid) {
      this.isSaving = true;

      // Prepare the complete application data
      const applicationData = {
        ...this.applicationForm.value,
        dependencies: this.dependencies,
        techStack: this.techStack,
        stakeholders: this.stakeholders,
        documentation: this.documentation,
        vulnerabilities: this.vulnerabilities
      };

      // In a real application, this would call a service to save the data
      console.log('Saving application:', applicationData);

      // Simulate API call
      setTimeout(() => {
        this.isSaving = false;
        // Navigate back to applications list or show success message
        console.log('Application saved successfully!');
      }, 2000);
    } else {
      // Mark all fields as touched to show validation errors
      this.markFormGroupTouched(this.applicationForm);

      // Find the first tab with errors and switch to it
      this.switchToFirstErrorTab();
    }
  }

  private markFormGroupTouched(formGroup: FormGroup): void {
    Object.keys(formGroup.controls).forEach(key => {
      const control = formGroup.get(key);
      control?.markAsTouched();
    });
  }

  private switchToFirstErrorTab(): void {
    // Check basic information tab
    if (this.applicationForm.invalid) {
      this.activeTabId = 'basic';
      this.tabsComponent?.setActiveTab('basic');
      return;
    }

    // Add logic to check other tabs for errors if needed
  }
}
