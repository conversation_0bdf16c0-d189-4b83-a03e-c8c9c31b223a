import './polyfills.server.mjs';
import {
  __spreadValues
} from "./chunk-5WKMABBB.mjs";

// src/app/modules/documentation/documentation.routes.ts
var routes = [
  __spreadValues({
    path: "",
    loadComponent: () => import("./chunk-HWBPUK5Q.mjs").then((m) => m.DocumentationComponent)
  }, true ? { \u0275entryName: "src/app/modules/documentation/documentation.component.ts" } : {})
];
export {
  routes
};
//# sourceMappingURL=chunk-XVI5KJ7Q.mjs.map
