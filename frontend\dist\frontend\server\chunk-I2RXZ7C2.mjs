import './polyfills.server.mjs';
import {
  __spreadValues
} from "./chunk-5WKMABBB.mjs";

// src/app/modules/applications/applications.routes.ts
var routes = [
  __spreadValues({
    path: "",
    loadComponent: () => import("./chunk-FW7H54BJ.mjs").then((m) => m.ApplicationsListComponent)
  }, true ? { \u0275entryName: "src/app/modules/applications/applications-list/applications-list.component.ts" } : {}),
  __spreadValues({
    path: "new",
    loadComponent: () => import("./chunk-XVU5TYLD.mjs").then((m) => m.ApplicationFormComponent)
  }, true ? { \u0275entryName: "src/app/modules/applications/application-form/application-form.component.ts" } : {}),
  __spreadValues({
    path: ":id",
    loadComponent: () => import("./chunk-5EC7TU5L.mjs").then((m) => m.ApplicationDetailComponent)
  }, true ? { \u0275entryName: "src/app/modules/applications/application-detail/application-detail.component.ts" } : {}),
  __spreadValues({
    path: ":id/edit",
    loadComponent: () => import("./chunk-XVU5TYLD.mjs").then((m) => m.ApplicationFormComponent)
  }, true ? { \u0275entryName: "src/app/modules/applications/application-form/application-form.component.ts" } : {})
];
export {
  routes
};
//# sourceMappingURL=chunk-I2RXZ7C2.mjs.map
