{"version": 3, "sources": ["src/app/shared/components/card/card.component.ts"], "sourcesContent": ["import { Component, Input } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\nexport type CardVariant = 'default' | 'outlined' | 'elevated' | 'filled';\nexport type CardSize = 'sm' | 'md' | 'lg';\n\n@Component({\n  selector: 'app-card',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <div [class]=\"cardClasses\">\n      <!-- Card Header -->\n      <div class=\"card-header\" *ngIf=\"hasHeader\">\n        <div class=\"card-header-content\">\n          <!-- Title and Subtitle -->\n          <div class=\"card-title-section\" *ngIf=\"title || subtitle\">\n            <h3 class=\"card-title\" *ngIf=\"title\">{{ title }}</h3>\n            <p class=\"card-subtitle\" *ngIf=\"subtitle\">{{ subtitle }}</p>\n          </div>\n\n          <!-- Header Slot -->\n          <div class=\"card-header-slot\">\n            <ng-content select=\"[slot=header]\"></ng-content>\n          </div>\n        </div>\n\n        <!-- Header Actions -->\n        <div class=\"card-header-actions\" *ngIf=\"hasHeaderActions\">\n          <ng-content select=\"[slot=header-actions]\"></ng-content>\n        </div>\n      </div>\n\n      <!-- Card Content -->\n      <div class=\"card-content\" [class.card-content-padded]=\"padded\">\n        <ng-content></ng-content>\n      </div>\n\n      <!-- Card Footer -->\n      <div class=\"card-footer\" *ngIf=\"hasFooter\">\n        <ng-content select=\"[slot=footer]\"></ng-content>\n      </div>\n    </div>\n  `,\n  styleUrls: ['./card.component.scss']\n})\nexport class CardComponent {\n  @Input() variant: CardVariant = 'default';\n  @Input() size: CardSize = 'md';\n  @Input() title = '';\n  @Input() subtitle = '';\n  @Input() padded = true;\n  @Input() hoverable = false;\n  @Input() clickable = false;\n\n  get cardClasses(): string {\n    const classes = [\n      'card',\n      `card-${this.variant}`,\n      `card-${this.size}`\n    ];\n\n    if (this.hoverable) classes.push('card-hoverable');\n    if (this.clickable) classes.push('card-clickable');\n\n    return classes.join(' ');\n  }\n\n  get hasHeader(): boolean {\n    return !!(this.title || this.subtitle || this.hasHeaderSlot || this.hasHeaderActions);\n  }\n\n  get hasHeaderSlot(): boolean {\n    // This would need to be implemented with ViewChild or content projection detection\n    // For now, we'll assume it exists if title/subtitle are not provided\n    return !this.title && !this.subtitle;\n  }\n\n  get hasHeaderActions(): boolean {\n    // This would need to be implemented with ViewChild or content projection detection\n    return true; // Simplified for now\n  }\n\n  get hasFooter(): boolean {\n    // This would need to be implemented with ViewChild or content projection detection\n    return true; // Simplified for now\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBY,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAqC,IAAA,iBAAA,CAAA;AAAW,IAAA,uBAAA;;;;AAAX,IAAA,oBAAA;AAAA,IAAA,4BAAA,OAAA,KAAA;;;;;AACrC,IAAA,yBAAA,GAAA,KAAA,EAAA;AAA0C,IAAA,iBAAA,CAAA;AAAc,IAAA,uBAAA;;;;AAAd,IAAA,oBAAA;AAAA,IAAA,4BAAA,OAAA,QAAA;;;;;AAF5C,IAAA,yBAAA,GAAA,OAAA,CAAA;AACE,IAAA,qBAAA,GAAA,yCAAA,GAAA,GAAA,MAAA,CAAA,EAAqC,GAAA,wCAAA,GAAA,GAAA,KAAA,EAAA;AAEvC,IAAA,uBAAA;;;;AAF0B,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,KAAA;AACE,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,QAAA;;;;;AAU9B,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,uBAAA,GAAA,CAAA;AACF,IAAA,uBAAA;;;;;AAjBF,IAAA,yBAAA,GAAA,OAAA,CAAA,EAA2C,GAAA,OAAA,CAAA;AAGvC,IAAA,qBAAA,GAAA,oCAAA,GAAA,GAAA,OAAA,CAAA;AAMA,IAAA,yBAAA,GAAA,OAAA,CAAA;AACE,IAAA,uBAAA,GAAA,CAAA;AACF,IAAA,uBAAA,EAAM;AAIR,IAAA,qBAAA,GAAA,oCAAA,GAAA,GAAA,OAAA,CAAA;AAGF,IAAA,uBAAA;;;;AAfqC,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,SAAA,OAAA,QAAA;AAYD,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,gBAAA;;;;;AAWpC,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,uBAAA,GAAA,CAAA;AACF,IAAA,uBAAA;;;AAKA,IAAO,gBAAP,MAAO,eAAa;EACf,UAAuB;EACvB,OAAiB;EACjB,QAAQ;EACR,WAAW;EACX,SAAS;EACT,YAAY;EACZ,YAAY;EAErB,IAAI,cAAW;AACb,UAAM,UAAU;MACd;MACA,QAAQ,KAAK,OAAO;MACpB,QAAQ,KAAK,IAAI;;AAGnB,QAAI,KAAK;AAAW,cAAQ,KAAK,gBAAgB;AACjD,QAAI,KAAK;AAAW,cAAQ,KAAK,gBAAgB;AAEjD,WAAO,QAAQ,KAAK,GAAG;EACzB;EAEA,IAAI,YAAS;AACX,WAAO,CAAC,EAAE,KAAK,SAAS,KAAK,YAAY,KAAK,iBAAiB,KAAK;EACtE;EAEA,IAAI,gBAAa;AAGf,WAAO,CAAC,KAAK,SAAS,CAAC,KAAK;EAC9B;EAEA,IAAI,mBAAgB;AAElB,WAAO;EACT;EAEA,IAAI,YAAS;AAEX,WAAO;EACT;;qCAxCW,gBAAa;EAAA;yEAAb,gBAAa,WAAA,CAAA,CAAA,UAAA,CAAA,GAAA,QAAA,EAAA,SAAA,WAAA,MAAA,QAAA,OAAA,SAAA,UAAA,YAAA,QAAA,UAAA,WAAA,aAAA,WAAA,YAAA,GAAA,oBAAA,KAAA,OAAA,GAAA,MAAA,GAAA,QAAA,CAAA,CAAA,SAAA,eAAA,GAAA,MAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,SAAA,eAAA,GAAA,MAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,SAAA,sBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,SAAA,uBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,oBAAA,GAAA,CAAA,SAAA,cAAA,GAAA,MAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,GAAA,aAAA,CAAA,GAAA,UAAA,SAAA,uBAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;;AAnCtB,MAAA,yBAAA,GAAA,KAAA;AAEE,MAAA,qBAAA,GAAA,8BAAA,GAAA,GAAA,OAAA,CAAA;AAqBA,MAAA,yBAAA,GAAA,OAAA,CAAA;AACE,MAAA,uBAAA,CAAA;AACF,MAAA,uBAAA;AAGA,MAAA,qBAAA,GAAA,8BAAA,GAAA,GAAA,OAAA,CAAA;AAGF,MAAA,uBAAA;;;AA/BK,MAAA,qBAAA,IAAA,WAAA;AAEuB,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,SAAA;AAqBA,MAAA,oBAAA;AAAA,MAAA,sBAAA,uBAAA,IAAA,MAAA;AAKA,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,SAAA;;oBA9BpB,cAAY,IAAA,GAAA,QAAA,CAAA,wmQAAA,EAAA,CAAA;;;sEAqCX,eAAa,CAAA;UAxCzB;uBACW,YAAU,YACR,MAAI,SACP,CAAC,YAAY,GAAC,UACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAiCT,QAAA,CAAA,+oMAAA,EAAA,CAAA;cAIQ,SAAO,CAAA;UAAf;MACQ,MAAI,CAAA;UAAZ;MACQ,OAAK,CAAA;UAAb;MACQ,UAAQ,CAAA;UAAhB;MACQ,QAAM,CAAA;UAAd;MACQ,WAAS,CAAA;UAAjB;MACQ,WAAS,CAAA;UAAjB;;;;6EAPU,eAAa,EAAA,WAAA,iBAAA,UAAA,oDAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}