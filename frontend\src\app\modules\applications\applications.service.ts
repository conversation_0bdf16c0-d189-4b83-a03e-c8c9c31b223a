import { Injectable } from '@angular/core';
import { Observable, of, delay, map } from 'rxjs';
import {
  Application,
  ApplicationStatus,
  ApplicationCriticality,
  DependencyType,
  DependencyCriticality,
  DependencyStatus,
  StakeholderRole,
  ContactPreference,
  DocumentationType,
  VulnerabilitySeverity,
  VulnerabilityStatus
} from '../../shared/models/application.model';

export interface ApplicationsFilter {
  search?: string;
  status?: ApplicationStatus[];
  criticality?: ApplicationCriticality[];
  owner?: string[];
  department?: string[];
}

export interface ApplicationsResponse {
  applications: Application[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

@Injectable({
  providedIn: 'root'
})
export class ApplicationsService {

  constructor() { }

  /**
   * Get applications with filtering and pagination
   */
  getApplications(
    page: number = 1,
    pageSize: number = 12,
    filter: ApplicationsFilter = {}
  ): Observable<ApplicationsResponse> {
    return this.getMockApplications().pipe(
      delay(500),
      map(applications => {
        // Apply filters
        let filteredApps = this.applyFilters(applications, filter);

        // Calculate pagination
        const total = filteredApps.length;
        const totalPages = Math.ceil(total / pageSize);
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;

        // Apply pagination
        const paginatedApps = filteredApps.slice(startIndex, endIndex);

        return {
          applications: paginatedApps,
          total,
          page,
          pageSize,
          totalPages
        };
      })
    );
  }

  /**
   * Get single application by ID
   */
  getApplication(id: number): Observable<Application | null> {
    return this.getMockApplications().pipe(
      delay(300),
      map(applications => applications.find(app => app.id === id) || null)
    );
  }

  /**
   * Create new application
   */
  createApplication(application: Partial<Application>): Observable<Application> {
    const newApp: Application = {
      id: Date.now(), // Mock ID generation
      name: application.name || '',
      description: application.description || '',
      owner: application.owner || '',
      department: application.department || '',
      status: application.status || ApplicationStatus.DEVELOPMENT,
      criticality: application.criticality || ApplicationCriticality.MEDIUM,
      version: application.version || '1.0.0',
      lastUpdated: new Date(),
      createdDate: new Date(),
      url: application.url || '',
      repository: application.repository || '',
      documentation: application.documentation || '',
      healthScore: 85,
      securityScore: 80,
      performanceScore: 90,
      tags: application.tags || [],
      techStack: application.techStack || [],
      dependencies: application.dependencies || [],
      vulnerabilities: application.vulnerabilities || [],
      documents: application.documents || []
    };

    // In a real app, this would make an API call
    return of(newApp).pipe(delay(1000));
  }

  /**
   * Update existing application
   */
  updateApplication(id: number, updates: Partial<Application>): Observable<Application> {
    return this.getApplication(id).pipe(
      delay(800),
      map(app => {
        if (!app) {
          throw new Error('Application not found');
        }
        return {
          ...app,
          ...updates,
          lastUpdated: new Date()
        };
      })
    );
  }

  /**
   * Delete application
   */
  deleteApplication(id: number): Observable<boolean> {
    return of(true).pipe(delay(500));
  }

  /**
   * Get application statistics
   */
  getApplicationStats(): Observable<any> {
    return this.getMockApplications().pipe(
      delay(300),
      map(applications => {
        const stats = {
          total: applications.length,
          byStatus: this.groupBy(applications, 'status'),
          byCriticality: this.groupBy(applications, 'criticality'),
          byDepartment: this.groupBy(applications, 'department'),
          averageHealthScore: this.calculateAverage(applications, 'healthScore'),
          averageSecurityScore: this.calculateAverage(applications, 'securityScore'),
          averagePerformanceScore: this.calculateAverage(applications, 'performanceScore')
        };
        return stats;
      })
    );
  }

  /**
   * Get filter options for dropdowns
   */
  getFilterOptions(): Observable<any> {
    return this.getMockApplications().pipe(
      delay(200),
      map(applications => ({
        owners: [...new Set(applications.map(app => app.owner))].sort(),
        departments: [...new Set(applications.map(app => app.department))].sort(),
        statuses: Object.values(ApplicationStatus),
        criticalities: Object.values(ApplicationCriticality)
      }))
    );
  }

  /**
   * Search applications by name or description
   */
  searchApplications(query: string): Observable<Application[]> {
    return this.getMockApplications().pipe(
      delay(300),
      map(applications =>
        applications.filter(app =>
          app.name.toLowerCase().includes(query.toLowerCase()) ||
          app.description.toLowerCase().includes(query.toLowerCase())
        ).slice(0, 10) // Limit to 10 results for autocomplete
      )
    );
  }

  private applyFilters(applications: Application[], filter: ApplicationsFilter): Application[] {
    let filtered = [...applications];

    if (filter.search) {
      const searchTerm = filter.search.toLowerCase();
      filtered = filtered.filter(app =>
        app.name.toLowerCase().includes(searchTerm) ||
        app.description.toLowerCase().includes(searchTerm) ||
        app.owner.toLowerCase().includes(searchTerm)
      );
    }

    if (filter.status && filter.status.length > 0) {
      filtered = filtered.filter(app => filter.status!.includes(app.status));
    }

    if (filter.criticality && filter.criticality.length > 0) {
      filtered = filtered.filter(app => filter.criticality!.includes(app.criticality));
    }

    if (filter.owner && filter.owner.length > 0) {
      filtered = filtered.filter(app => filter.owner!.includes(app.owner));
    }

    if (filter.department && filter.department.length > 0) {
      filtered = filtered.filter(app => filter.department!.includes(app.department));
    }

    return filtered;
  }

  private groupBy(array: any[], key: string): any {
    return array.reduce((groups, item) => {
      const value = item[key];
      groups[value] = (groups[value] || 0) + 1;
      return groups;
    }, {});
  }

  private calculateAverage(array: any[], key: string): number {
    const sum = array.reduce((total, item) => total + (item[key] || 0), 0);
    return Math.round((sum / array.length) * 10) / 10;
  }

  private getMockApplications(): Observable<Application[]> {
    const mockApps: Application[] = [
      {
        id: 1,
        name: 'E-commerce Platform',
        description: 'Main customer-facing e-commerce application with shopping cart, payment processing, and order management.',
        owner: 'John Doe',
        department: 'Engineering',
        status: ApplicationStatus.PRODUCTION,
        criticality: ApplicationCriticality.CRITICAL,
        version: '2.1.0',
        lastUpdated: new Date('2024-01-15'),
        createdDate: new Date('2022-03-10'),
        url: 'https://shop.company.com',
        repository: 'https://github.com/company/ecommerce',
        documentation: 'https://docs.company.com/ecommerce',
        healthScore: 92,
        securityScore: 88,
        performanceScore: 95,
        tags: ['customer-facing', 'revenue-critical', 'high-traffic'],
        techStack: [
          { name: 'Angular', version: '16.2.0', category: 'Frontend' },
          { name: 'Node.js', version: '18.17.0', category: 'Backend' },
          { name: 'PostgreSQL', version: '15.0', category: 'Database' },
          { name: 'Redis', version: '7.0', category: 'Cache' },
          { name: 'Docker', version: '24.0', category: 'Infrastructure' }
        ],
        dependencies: [
          {
            id: 1,
            applicationId: 1,
            name: 'Angular',
            type: DependencyType.FRAMEWORK,
            version: '16.2.0',
            isInternal: false,
            description: 'Frontend framework for building the user interface',
            criticality: DependencyCriticality.HIGH,
            status: DependencyStatus.ACTIVE,
            lastUpdated: new Date('2024-01-10'),
            maintainer: 'Google',
            repository: 'https://github.com/angular/angular'
          },
          {
            id: 2,
            applicationId: 1,
            name: 'Express.js',
            type: DependencyType.FRAMEWORK,
            version: '4.18.2',
            isInternal: false,
            description: 'Backend web framework for Node.js',
            criticality: DependencyCriticality.CRITICAL,
            status: DependencyStatus.ACTIVE,
            lastUpdated: new Date('2024-01-05'),
            maintainer: 'Express Team',
            repository: 'https://github.com/expressjs/express'
          },
          {
            id: 3,
            applicationId: 1,
            name: 'Payment Service',
            type: DependencyType.SERVICE,
            version: '1.5.3',
            isInternal: true,
            description: 'Internal payment processing service',
            criticality: DependencyCriticality.CRITICAL,
            status: DependencyStatus.ACTIVE,
            lastUpdated: new Date('2024-01-14'),
            maintainer: 'Finance Team'
          }
        ],
        stakeholders: [
          {
            id: 1,
            applicationId: 1,
            name: 'John Doe',
            email: '<EMAIL>',
            role: StakeholderRole.PRODUCT_OWNER,
            department: 'Engineering',
            responsibility: 'Overall product strategy and technical direction',
            isPrimary: true,
            contactPreference: ContactPreference.EMAIL,
            phone: '******-0123'
          },
          {
            id: 2,
            applicationId: 1,
            name: 'Sarah Wilson',
            email: '<EMAIL>',
            role: StakeholderRole.DEVELOPER,
            department: 'Engineering',
            responsibility: 'Frontend development and user experience',
            isPrimary: false,
            contactPreference: ContactPreference.SLACK,
            slackHandle: '@sarah.wilson'
          },
          {
            id: 3,
            applicationId: 1,
            name: 'Mike Chen',
            email: '<EMAIL>',
            role: StakeholderRole.BUSINESS_ANALYST,
            department: 'Product',
            responsibility: 'Business requirements and user acceptance testing',
            isPrimary: false,
            contactPreference: ContactPreference.EMAIL
          }
        ],
        documents: [
          {
            id: 1,
            applicationId: 1,
            title: 'API Documentation',
            type: DocumentationType.API_DOCUMENTATION,
            description: 'Complete API documentation for all endpoints',
            url: 'https://docs.company.com/ecommerce/api',
            version: '2.1.0',
            isPublic: false,
            uploadedAt: new Date('2024-01-10'),
            uploadedBy: 'John Doe'
          },
          {
            id: 2,
            applicationId: 1,
            title: 'User Manual',
            type: DocumentationType.USER_MANUAL,
            description: 'End-user guide for the e-commerce platform',
            fileName: 'user-manual-v2.1.pdf',
            fileSize: 2048576,
            mimeType: 'application/pdf',
            version: '2.1.0',
            isPublic: true,
            uploadedAt: new Date('2024-01-12'),
            uploadedBy: 'Sarah Wilson'
          },
          {
            id: 3,
            applicationId: 1,
            title: 'Architecture Overview',
            type: DocumentationType.TECHNICAL_SPEC,
            description: 'High-level system architecture and design decisions',
            url: 'https://docs.company.com/ecommerce/architecture',
            version: '2.0.0',
            isPublic: false,
            uploadedAt: new Date('2023-12-15'),
            uploadedBy: 'John Doe'
          }
        ],
        vulnerabilities: [
          {
            id: 1,
            applicationId: 1,
            cveId: 'CVE-2024-0001',
            title: 'SQL Injection in User Search',
            description: 'Potential SQL injection vulnerability in the user search functionality that could allow unauthorized data access.',
            severity: VulnerabilitySeverity.HIGH,
            cvssScore: 7.5,
            status: VulnerabilityStatus.IN_PROGRESS,
            discoveredAt: new Date('2024-01-08'),
            discoveredBy: 'Security Team',
            affectedComponent: 'User Search API',
            patchAvailable: true,
            patchVersion: '2.1.1'
          },
          {
            id: 2,
            applicationId: 1,
            title: 'Outdated Dependencies',
            description: 'Several npm packages are using outdated versions with known security vulnerabilities.',
            severity: VulnerabilitySeverity.MEDIUM,
            cvssScore: 5.3,
            status: VulnerabilityStatus.OPEN,
            discoveredAt: new Date('2024-01-12'),
            discoveredBy: 'Automated Security Scan',
            affectedComponent: 'Frontend Dependencies',
            patchAvailable: true
          },
          {
            id: 3,
            applicationId: 1,
            title: 'Cross-Site Scripting (XSS)',
            description: 'Potential XSS vulnerability in product review comments.',
            severity: VulnerabilitySeverity.CRITICAL,
            cvssScore: 9.1,
            status: VulnerabilityStatus.RESOLVED,
            discoveredAt: new Date('2023-12-20'),
            discoveredBy: 'Penetration Testing',
            resolvedAt: new Date('2024-01-05'),
            resolvedBy: 'Sarah Wilson',
            affectedComponent: 'Product Reviews',
            patchAvailable: false
          }
        ],
        securityAssessments: []
      },
      {
        id: 2,
        name: 'User Management System',
        description: 'Centralized user authentication and authorization service for all company applications.',
        owner: 'Jane Smith',
        department: 'Security',
        status: ApplicationStatus.PRODUCTION,
        criticality: ApplicationCriticality.HIGH,
        version: '1.8.2',
        lastUpdated: new Date('2024-01-12'),
        createdDate: new Date('2021-11-05'),
        url: 'https://auth.company.com',
        repository: 'https://github.com/company/auth-service',
        documentation: 'https://docs.company.com/auth',
        healthScore: 96,
        securityScore: 98,
        performanceScore: 89,
        tags: ['security', 'authentication', 'microservice'],
        techStack: [
          { name: 'Node.js', version: '18.17.0', category: 'Backend' },
          { name: 'PostgreSQL', version: '15.0', category: 'Database' },
          { name: 'Redis', version: '7.0', category: 'Cache' }
        ],
        dependencies: [],
        vulnerabilities: [],
        documents: []
      },
      {
        id: 3,
        name: 'Analytics Dashboard',
        description: 'Business intelligence dashboard providing insights into sales, user behavior, and system performance.',
        owner: 'Mike Johnson',
        department: 'Product',
        status: ApplicationStatus.PRODUCTION,
        criticality: ApplicationCriticality.MEDIUM,
        version: '3.2.1',
        lastUpdated: new Date('2024-01-10'),
        createdDate: new Date('2022-08-15'),
        url: 'https://analytics.company.com',
        repository: 'https://github.com/company/analytics',
        documentation: 'https://docs.company.com/analytics',
        healthScore: 87,
        securityScore: 85,
        performanceScore: 92,
        tags: ['analytics', 'business-intelligence', 'reporting'],
        techStack: [
          { name: 'React', version: '18.2.0', category: 'Frontend' },
          { name: 'Python', version: '3.11', category: 'Backend' },
          { name: 'MongoDB', version: '6.0', category: 'Database' }
        ],
        dependencies: [],
        vulnerabilities: [],
        documents: []
      },
      {
        id: 4,
        name: 'Payment Service',
        description: 'Microservice handling payment processing, refunds, and financial transaction management.',
        owner: 'Sarah Wilson',
        department: 'Finance',
        status: ApplicationStatus.PRODUCTION,
        criticality: ApplicationCriticality.CRITICAL,
        version: '1.5.3',
        lastUpdated: new Date('2024-01-14'),
        createdDate: new Date('2022-01-20'),
        url: 'https://payments.company.com',
        repository: 'https://github.com/company/payments',
        documentation: 'https://docs.company.com/payments',
        healthScore: 94,
        securityScore: 96,
        performanceScore: 88,
        tags: ['payments', 'financial', 'pci-compliant'],
        techStack: [
          { name: 'Java', version: '17', category: 'Backend' },
          { name: 'Spring Boot', version: '3.0', category: 'Framework' },
          { name: 'PostgreSQL', version: '15.0', category: 'Database' }
        ],
        dependencies: [],
        vulnerabilities: [],
        documents: []
      },
      {
        id: 5,
        name: 'Notification Service',
        description: 'Service responsible for sending emails, SMS, and push notifications to users.',
        owner: 'David Brown',
        department: 'Engineering',
        status: ApplicationStatus.DEVELOPMENT,
        criticality: ApplicationCriticality.MEDIUM,
        version: '0.8.0',
        lastUpdated: new Date('2024-01-16'),
        createdDate: new Date('2023-09-01'),
        url: 'https://notifications.company.com',
        repository: 'https://github.com/company/notifications',
        documentation: 'https://docs.company.com/notifications',
        healthScore: 78,
        securityScore: 82,
        performanceScore: 85,
        tags: ['notifications', 'messaging', 'microservice'],
        techStack: [
          { name: 'Node.js', version: '18.17.0', category: 'Backend' },
          { name: 'RabbitMQ', version: '3.12', category: 'Message Queue' },
          { name: 'Redis', version: '7.0', category: 'Cache' }
        ],
        dependencies: [],
        vulnerabilities: [],
        documents: []
      },
      {
        id: 6,
        name: 'Inventory Management',
        description: 'System for tracking product inventory, stock levels, and warehouse management.',
        owner: 'Lisa Davis',
        department: 'Operations',
        status: ApplicationStatus.TESTING,
        criticality: ApplicationCriticality.HIGH,
        version: '2.0.0-beta',
        lastUpdated: new Date('2024-01-13'),
        createdDate: new Date('2023-05-10'),
        url: 'https://inventory.company.com',
        repository: 'https://github.com/company/inventory',
        documentation: 'https://docs.company.com/inventory',
        healthScore: 83,
        securityScore: 87,
        performanceScore: 91,
        tags: ['inventory', 'warehouse', 'supply-chain'],
        techStack: [
          { name: 'Vue.js', version: '3.3.0', category: 'Frontend' },
          { name: '.NET Core', version: '7.0', category: 'Backend' },
          { name: 'MySQL', version: '8.0', category: 'Database' }
        ],
        dependencies: [],
        vulnerabilities: [],
        documents: []
      },
      {
        id: 7,
        name: 'Customer Support Portal',
        description: 'Self-service portal for customers to submit tickets, track issues, and access knowledge base.',
        owner: 'Tom Anderson',
        department: 'Customer Success',
        status: ApplicationStatus.PRODUCTION,
        criticality: ApplicationCriticality.MEDIUM,
        version: '1.9.1',
        lastUpdated: new Date('2024-01-11'),
        createdDate: new Date('2022-06-15'),
        url: 'https://support.company.com',
        repository: 'https://github.com/company/support-portal',
        documentation: 'https://docs.company.com/support',
        healthScore: 89,
        securityScore: 91,
        performanceScore: 86,
        tags: ['customer-support', 'self-service', 'knowledge-base'],
        techStack: [
          { name: 'Angular', version: '16.2.0', category: 'Frontend' },
          { name: 'PHP', version: '8.2', category: 'Backend' },
          { name: 'MySQL', version: '8.0', category: 'Database' }
        ],
        dependencies: [],
        vulnerabilities: [],
        documents: []
      },
      {
        id: 8,
        name: 'Mobile API Gateway',
        description: 'API gateway providing unified access to backend services for mobile applications.',
        owner: 'Emma Thompson',
        department: 'Mobile',
        status: ApplicationStatus.PRODUCTION,
        criticality: ApplicationCriticality.HIGH,
        version: '2.3.0',
        lastUpdated: new Date('2024-01-09'),
        createdDate: new Date('2022-11-20'),
        url: 'https://api-mobile.company.com',
        repository: 'https://github.com/company/mobile-gateway',
        documentation: 'https://docs.company.com/mobile-api',
        healthScore: 91,
        securityScore: 93,
        performanceScore: 94,
        tags: ['api-gateway', 'mobile', 'microservices'],
        techStack: [
          { name: 'Kong', version: '3.4', category: 'Infrastructure' },
          { name: 'Lua', version: '5.4', category: 'Backend' },
          { name: 'PostgreSQL', version: '15.0', category: 'Database' }
        ],
        dependencies: [],
        vulnerabilities: [],
        documents: []
      }
    ];

    return of(mockApps);
  }
}
