import {
  CardComponent
} from "./chunk-SLWZQAXJ.js";
import {
  CommonModule,
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵdefineComponent,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵtext
} from "./chunk-3JQLQ36P.js";
import "./chunk-WDMUDEB6.js";

// src/app/modules/dependencies/dependency-health/dependency-health.component.ts
var DependencyHealthComponent = class _DependencyHealthComponent {
  static \u0275fac = function DependencyHealthComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _DependencyHealthComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _DependencyHealthComponent, selectors: [["app-dependency-health"]], decls: 7, vars: 0, consts: [[1, "dependency-health-page"], ["title", "Health Check", "subtitle", "Monitor dependency health status"], [1, "placeholder"]], template: function DependencyHealthComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "h1");
      \u0275\u0275text(2, "Dependency Health");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(3, "app-card", 1)(4, "div", 2)(5, "p");
      \u0275\u0275text(6, "Dependency health monitoring coming soon...");
      \u0275\u0275elementEnd()()()();
    }
  }, dependencies: [CommonModule, CardComponent], styles: ["\n\n.dependency-health-page[_ngcontent-%COMP%] {\n  min-height: 100%;\n}\n.placeholder[_ngcontent-%COMP%] {\n  padding: var(--spacing-xl);\n  text-align: center;\n  color: var(--secondary-600);\n}\n/*# sourceMappingURL=dependency-health.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(DependencyHealthComponent, [{
    type: Component,
    args: [{ selector: "app-dependency-health", standalone: true, imports: [CommonModule, CardComponent], template: `
    <div class="dependency-health-page">
      <h1>Dependency Health</h1>
      <app-card title="Health Check" subtitle="Monitor dependency health status">
        <div class="placeholder">
          <p>Dependency health monitoring coming soon...</p>
        </div>
      </app-card>
    </div>
  `, styles: ["/* angular:styles/component:scss;ed97d04d0c882099bfa9f1e2bc8a325877cda7997b2c4ee35036f34a89b25d0d;C:/Users/<USER>/Documents/VibeCoding/AppCatalog/app-catalog/frontend/src/app/modules/dependencies/dependency-health/dependency-health.component.ts */\n.dependency-health-page {\n  min-height: 100%;\n}\n.placeholder {\n  padding: var(--spacing-xl);\n  text-align: center;\n  color: var(--secondary-600);\n}\n/*# sourceMappingURL=dependency-health.component.css.map */\n"] }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(DependencyHealthComponent, { className: "DependencyHealthComponent", filePath: "src/app/modules/dependencies/dependency-health/dependency-health.component.ts", lineNumber: 30 });
})();
export {
  DependencyHealthComponent
};
//# sourceMappingURL=chunk-O4C46WCI.js.map
