import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ReactiveFormsModule, FormBuilder } from '@angular/forms';
import { CardComponent } from '../../shared/components/card/card.component';
import { ButtonComponent } from '../../shared/components/button/button.component';
import { TableComponent, TableColumn, TableAction } from '../../shared/components/table/table.component';
import { ChartComponent } from '../../shared/components/chart/chart.component';
import { StakeholderModalComponent } from '../applications/components/stakeholder-modal/stakeholder-modal.component';

interface Stakeholder {
  id: number;
  name: string;
  email: string;
  role: string;
  department: string;
  isActive: boolean;
  applications: number;
  lastActivity: Date;
  phone?: string;
  responsibilities?: string;
}

@Component({
  selector: 'app-stakeholders',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    CardComponent,
    ButtonComponent,
    TableComponent,
    ChartComponent,
    StakeholderModalComponent
  ],
  template: `
    <div class="stakeholders-page">
      <div class="page-content">
        <div class="page-header">
          <div class="header-content">
            <h1>Stakeholders</h1>
            <p class="page-subtitle">Manage team members and project stakeholders</p>
          </div>
          <div class="header-actions">
            <app-button
              variant="primary"
              leftIcon="M12 4v16m8-8H4"
              (clicked)="openAddModal()"
            >
              Add Stakeholder
            </app-button>
          </div>
        </div>

      <!-- Statistics Cards -->
      <div class="stats-grid">
        <app-card title="Total Stakeholders">
          <div class="stat-content">
            <div class="stat-number">{{ stats.total }}</div>
            <div class="stat-change">Across {{ stats.departments }} departments</div>
          </div>
        </app-card>

        <app-card title="Active Members">
          <div class="stat-content">
            <div class="stat-number active">{{ stats.active }}</div>
            <div class="stat-change">{{ stats.activePercentage }}% of total</div>
          </div>
        </app-card>

        <app-card title="Project Owners">
          <div class="stat-content">
            <div class="stat-number owner">{{ stats.owners }}</div>
            <div class="stat-change">Leading projects</div>
          </div>
        </app-card>

        <app-card title="Recent Activity">
          <div class="stat-content">
            <div class="stat-number">{{ stats.recentActivity }}</div>
            <div class="stat-change">Active this week</div>
          </div>
        </app-card>
      </div>

      <!-- Charts Section -->
      <div class="charts-section">
        <app-card title="Stakeholders by Department" subtitle="Distribution across departments">
          <app-chart
            type="doughnut"
            [data]="departmentChartData"
            [options]="chartOptions"
            height="300px"
          ></app-chart>
        </app-card>

        <app-card title="Roles Distribution" subtitle="Stakeholders by role">
          <app-chart
            type="bar"
            [data]="roleChartData"
            [options]="chartOptions"
            height="300px"
          ></app-chart>
        </app-card>
      </div>

      <!-- Stakeholders Table -->
      <app-card title="Team Members" subtitle="All project stakeholders">
        <div class="table-controls">
          <div class="search-controls">
            <input
              type="text"
              placeholder="Search stakeholders..."
              class="search-input"
              (input)="onSearchInput($event)"
            >
            <select class="filter-select" (change)="onFilterChanged('department', $event)">
              <option value="">All Departments</option>
              <option value="engineering">Engineering</option>
              <option value="product">Product</option>
              <option value="design">Design</option>
              <option value="qa">QA</option>
              <option value="devops">DevOps</option>
              <option value="management">Management</option>
            </select>
            <select class="filter-select" (change)="onFilterChanged('role', $event)">
              <option value="">All Roles</option>
              <option value="owner">Owner</option>
              <option value="developer">Developer</option>
              <option value="architect">Architect</option>
              <option value="manager">Manager</option>
              <option value="stakeholder">Stakeholder</option>
            </select>
          </div>
        </div>

        <app-table
          [columns]="tableColumns"
          [data]="filteredStakeholders"
          [actions]="tableActions"
          [loading]="loading"
          [sortable]="true"
          (sortChanged)="onSortChanged($event)"
        ></app-table>
      </app-card>

      <!-- Add/Edit Modal -->
      <app-stakeholder-modal
        [isOpen]="showModal"
        [editMode]="editMode"
        [initialData]="editData"
        [loading]="modalLoading"
        (closed)="closeModal()"
        (saved)="onStakeholderSaved($event)"
      ></app-stakeholder-modal>
      </div>
    </div>
  `,
  styles: [`
    .stakeholders-page {
      min-height: 100%;
    }

    .page-content {
      padding: var(--spacing-xl);
    }

    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: var(--spacing-xl);
    }

    .header-content h1 {
      margin: 0 0 var(--spacing-sm) 0;
      font-size: var(--text-3xl);
      font-weight: 700;
      color: var(--secondary-900);
    }

    .page-subtitle {
      margin: 0;
      font-size: var(--text-lg);
      color: var(--secondary-600);
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: var(--spacing-lg);
      margin-bottom: var(--spacing-xl);
    }

    .stat-content {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-sm);
      padding: var(--spacing-lg);
    }

    .stat-number {
      font-size: var(--text-3xl);
      font-weight: 700;
      color: var(--secondary-900);

      &.active {
        color: var(--success-600);
      }

      &.owner {
        color: var(--primary-600);
      }
    }

    .stat-change {
      font-size: var(--text-sm);
      color: var(--secondary-600);
    }

    .charts-section {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: var(--spacing-lg);
      margin-bottom: var(--spacing-xl);
    }

    .table-controls {
      margin-bottom: var(--spacing-lg);
    }

    .search-controls {
      display: flex;
      gap: var(--spacing-md);
      align-items: center;
    }

    .search-input,
    .filter-select {
      height: 40px;
      padding: 0 var(--spacing-md);
      border: 1px solid var(--secondary-300);
      border-radius: var(--radius-md);
      background: white;
      font-size: var(--text-sm);
      color: var(--secondary-800);
      transition: all 0.2s ease;

      &:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px var(--primary-100);
      }
    }

    .search-input {
      flex: 1;
      min-width: 200px;
    }

    .filter-select {
      min-width: 150px;
      cursor: pointer;
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
      background-position: right var(--spacing-sm) center;
      background-repeat: no-repeat;
      background-size: 16px;
      padding-right: 40px;
      appearance: none;
    }

    @media (max-width: 768px) {
      .page-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
      }

      .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
      }

      .charts-section {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
      }

      .search-controls {
        flex-direction: column;
        align-items: stretch;
      }

      .search-input,
      .filter-select {
        min-width: auto;
      }
    }
  `]
})
export class StakeholdersComponent implements OnInit {
  stakeholders: Stakeholder[] = [];
  filteredStakeholders: Stakeholder[] = [];
  loading = false;

  // Modal state
  showModal = false;
  editMode = false;
  editData: any = null;
  modalLoading = false;

  // Statistics
  stats = {
    total: 0,
    active: 0,
    owners: 0,
    departments: 0,
    recentActivity: 0,
    activePercentage: 0
  };

  // Chart data
  departmentChartData: any = null;
  roleChartData: any = null;
  chartOptions = {
    responsive: true,
    maintainAspectRatio: false
  };

  // Table configuration
  tableColumns: TableColumn[] = [
    { key: 'name', label: 'Name', sortable: true },
    { key: 'email', label: 'Email', sortable: true },
    { key: 'role', label: 'Role', type: 'badge', sortable: true },
    { key: 'department', label: 'Department', type: 'badge', sortable: true },
    { key: 'applications', label: 'Apps', type: 'number', sortable: true },
    { key: 'isActive', label: 'Status', type: 'boolean', sortable: true },
    { key: 'lastActivity', label: 'Last Activity', type: 'date', sortable: true }
  ];

  tableActions: TableAction[] = [
    {
      label: 'Edit',
      icon: 'M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7',
      variant: 'ghost',
      action: (item) => this.editStakeholder(item)
    },
    {
      label: 'Delete',
      icon: 'M3 6h18m-2 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2',
      variant: 'error',
      action: (item) => this.deleteStakeholder(item)
    }
  ];

  constructor(private fb: FormBuilder) {}

  ngOnInit(): void {
    this.loadStakeholders();
  }

  private loadStakeholders(): void {
    this.loading = true;

    // Mock data - replace with actual API call
    setTimeout(() => {
      this.stakeholders = this.generateMockStakeholders();
      this.filteredStakeholders = [...this.stakeholders];
      this.updateStats();
      this.updateChartData();
      this.loading = false;
    }, 1000);
  }

  private generateMockStakeholders(): Stakeholder[] {
    const departments = ['engineering', 'product', 'design', 'qa', 'devops', 'management'];
    const roles = ['owner', 'developer', 'architect', 'manager', 'stakeholder'];

    return Array.from({ length: 25 }, (_, i) => ({
      id: i + 1,
      name: `Stakeholder ${i + 1}`,
      email: `stakeholder${i + 1}@company.com`,
      role: roles[Math.floor(Math.random() * roles.length)],
      department: departments[Math.floor(Math.random() * departments.length)],
      isActive: Math.random() > 0.2,
      applications: Math.floor(Math.random() * 10) + 1,
      lastActivity: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
      phone: `******-${Math.floor(Math.random() * 900) + 100}-${Math.floor(Math.random() * 9000) + 1000}`,
      responsibilities: `Responsibilities for stakeholder ${i + 1}`
    }));
  }

  private updateStats(): void {
    this.stats.total = this.stakeholders.length;
    this.stats.active = this.stakeholders.filter(s => s.isActive).length;
    this.stats.owners = this.stakeholders.filter(s => s.role === 'owner').length;
    this.stats.departments = new Set(this.stakeholders.map(s => s.department)).size;
    this.stats.recentActivity = this.stakeholders.filter(s =>
      new Date(s.lastActivity).getTime() > Date.now() - 7 * 24 * 60 * 60 * 1000
    ).length;
    this.stats.activePercentage = Math.round((this.stats.active / this.stats.total) * 100);
  }

  private updateChartData(): void {
    // Department distribution chart
    const departmentCount = this.stakeholders.reduce((acc, stakeholder) => {
      acc[stakeholder.department] = (acc[stakeholder.department] || 0) + 1;
      return acc;
    }, {} as any);

    this.departmentChartData = {
      labels: Object.keys(departmentCount),
      datasets: [{
        data: Object.values(departmentCount),
        backgroundColor: [
          '#0ea5e9', '#22c55e', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'
        ]
      }]
    };

    // Role distribution chart
    const roleCount = this.stakeholders.reduce((acc, stakeholder) => {
      acc[stakeholder.role] = (acc[stakeholder.role] || 0) + 1;
      return acc;
    }, {} as any);

    this.roleChartData = {
      labels: Object.keys(roleCount),
      datasets: [{
        label: 'Stakeholders',
        data: Object.values(roleCount),
        backgroundColor: ['#22c55e', '#0ea5e9', '#f59e0b', '#ef4444', '#8b5cf6']
      }]
    };
  }

  openAddModal(): void {
    this.editMode = false;
    this.editData = null;
    this.showModal = true;
  }

  editStakeholder(stakeholder: Stakeholder): void {
    this.editMode = true;
    this.editData = { ...stakeholder };
    this.showModal = true;
  }

  deleteStakeholder(stakeholder: Stakeholder): void {
    if (confirm(`Are you sure you want to delete ${stakeholder.name}?`)) {
      this.stakeholders = this.stakeholders.filter(s => s.id !== stakeholder.id);
      this.filteredStakeholders = this.filteredStakeholders.filter(s => s.id !== stakeholder.id);
      this.updateStats();
      this.updateChartData();
    }
  }

  closeModal(): void {
    this.showModal = false;
    this.editMode = false;
    this.editData = null;
  }

  onStakeholderSaved(stakeholderData: any): void {
    this.modalLoading = true;

    setTimeout(() => {
      if (this.editMode) {
        const index = this.stakeholders.findIndex(s => s.id === this.editData.id);
        if (index !== -1) {
          this.stakeholders[index] = { ...this.stakeholders[index], ...stakeholderData };
        }
      } else {
        const newStakeholder: Stakeholder = {
          id: Math.max(...this.stakeholders.map(s => s.id)) + 1,
          ...stakeholderData,
          applications: 0,
          lastActivity: new Date()
        };
        this.stakeholders.push(newStakeholder);
      }

      this.filteredStakeholders = [...this.stakeholders];
      this.updateStats();
      this.updateChartData();
      this.modalLoading = false;
      this.closeModal();
    }, 1000);
  }

  onSearchInput(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.onSearchChanged(target.value);
  }

  onSearchChanged(searchTerm: string): void {
    this.applyFilters();
  }

  onFilterChanged(filterType: string, event: Event): void {
    const target = event.target as HTMLSelectElement;
    // Apply filter logic here
    this.applyFilters();
  }

  onSortChanged(sort: any): void {
    // Implement sorting logic
    console.log('Sort changed:', sort);
  }

  private applyFilters(): void {
    // Implement filtering logic
    this.filteredStakeholders = [...this.stakeholders];
  }
}
