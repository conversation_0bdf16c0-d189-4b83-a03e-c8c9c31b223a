{"version": 3, "sources": ["src/environments/environment.ts", "src/app/shared/services/base-api.service.ts"], "sourcesContent": ["export const environment = {\n  production: false,\n  apiUrl: 'http://localhost:5000/api',\n  appName: 'Application Catalog System',\n  version: '1.0.0',\n  features: {\n    enableMockData: true,\n    enableAnalytics: false,\n    enableNotifications: true,\n    enableFileUpload: true,\n    enableExport: true\n  },\n  api: {\n    timeout: 30000,\n    retryAttempts: 3,\n    pageSize: 20\n  },\n  chart: {\n    defaultColors: [\n      '#0ea5e9', '#22c55e', '#f59e0b', '#ef4444', '#8b5cf6',\n      '#06b6d4', '#84cc16', '#f97316', '#ec4899', '#6366f1'\n    ],\n    animationDuration: 750\n  },\n  storage: {\n    prefix: 'app_catalog_',\n    tokenKey: 'auth_token',\n    userPreferencesKey: 'user_preferences'\n  }\n};\n", "import { Injectable } from '@angular/core';\nimport { HttpClient, HttpParams, HttpHeaders, HttpErrorResponse } from '@angular/common/http';\nimport { Observable, throwError, BehaviorSubject } from 'rxjs';\nimport { catchError, map, retry, timeout } from 'rxjs/operators';\nimport { environment } from '../../../environments/environment';\n\nexport interface ApiResponse<T> {\n  data: T;\n  message?: string;\n  success: boolean;\n  errors?: string[];\n  pagination?: PaginationInfo;\n}\n\nexport interface PaginationInfo {\n  currentPage: number;\n  totalPages: number;\n  pageSize: number;\n  totalItems: number;\n  hasNext: boolean;\n  hasPrevious: boolean;\n}\n\nexport interface QueryParams {\n  page?: number;\n  pageSize?: number;\n  sortBy?: string;\n  sortOrder?: 'asc' | 'desc';\n  search?: string;\n  filters?: { [key: string]: any };\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class BaseApiService {\n  private readonly baseUrl = environment.apiUrl || 'http://localhost:5000/api';\n  private readonly defaultTimeout = 30000; // 30 seconds\n  private readonly maxRetries = 3;\n\n  private loadingSubject = new BehaviorSubject<boolean>(false);\n  public loading$ = this.loadingSubject.asObservable();\n\n  constructor(private http: HttpClient) {}\n\n  /**\n   * GET request with error handling and loading state\n   */\n  get<T>(endpoint: string, params?: QueryParams): Observable<ApiResponse<T>> {\n    this.setLoading(true);\n\n    const httpParams = this.buildHttpParams(params);\n    const options = {\n      params: httpParams,\n      headers: this.getHeaders()\n    };\n\n    return this.http.get<ApiResponse<T>>(`${this.baseUrl}/${endpoint}`, options)\n      .pipe(\n        timeout(this.defaultTimeout),\n        retry(this.maxRetries),\n        map(response => this.handleSuccess(response)),\n        catchError(error => this.handleError(error))\n      );\n  }\n\n  /**\n   * POST request with error handling and loading state\n   */\n  post<T>(endpoint: string, data: any): Observable<ApiResponse<T>> {\n    this.setLoading(true);\n\n    const options = {\n      headers: this.getHeaders()\n    };\n\n    return this.http.post<ApiResponse<T>>(`${this.baseUrl}/${endpoint}`, data, options)\n      .pipe(\n        timeout(this.defaultTimeout),\n        map(response => this.handleSuccess(response)),\n        catchError(error => this.handleError(error))\n      );\n  }\n\n  /**\n   * PUT request with error handling and loading state\n   */\n  put<T>(endpoint: string, data: any): Observable<ApiResponse<T>> {\n    this.setLoading(true);\n\n    const options = {\n      headers: this.getHeaders()\n    };\n\n    return this.http.put<ApiResponse<T>>(`${this.baseUrl}/${endpoint}`, data, options)\n      .pipe(\n        timeout(this.defaultTimeout),\n        map(response => this.handleSuccess(response)),\n        catchError(error => this.handleError(error))\n      );\n  }\n\n  /**\n   * PATCH request with error handling and loading state\n   */\n  patch<T>(endpoint: string, data: any): Observable<ApiResponse<T>> {\n    this.setLoading(true);\n\n    const options = {\n      headers: this.getHeaders()\n    };\n\n    return this.http.patch<ApiResponse<T>>(`${this.baseUrl}/${endpoint}`, data, options)\n      .pipe(\n        timeout(this.defaultTimeout),\n        map(response => this.handleSuccess(response)),\n        catchError(error => this.handleError(error))\n      );\n  }\n\n  /**\n   * DELETE request with error handling and loading state\n   */\n  delete<T>(endpoint: string): Observable<ApiResponse<T>> {\n    this.setLoading(true);\n\n    const options = {\n      headers: this.getHeaders()\n    };\n\n    return this.http.delete<ApiResponse<T>>(`${this.baseUrl}/${endpoint}`, options)\n      .pipe(\n        timeout(this.defaultTimeout),\n        map(response => this.handleSuccess(response)),\n        catchError(error => this.handleError(error))\n      );\n  }\n\n  /**\n   * Upload file with progress tracking\n   */\n  uploadFile<T>(endpoint: string, file: File, additionalData?: any): Observable<ApiResponse<T>> {\n    this.setLoading(true);\n\n    const formData = new FormData();\n    formData.append('file', file);\n\n    if (additionalData) {\n      Object.keys(additionalData).forEach(key => {\n        formData.append(key, additionalData[key]);\n      });\n    }\n\n    const headers = new HttpHeaders();\n    // Don't set Content-Type header for FormData, let browser set it with boundary\n\n    return this.http.post<ApiResponse<T>>(`${this.baseUrl}/${endpoint}`, formData, { headers })\n      .pipe(\n        timeout(60000), // Longer timeout for file uploads\n        map(response => this.handleSuccess(response)),\n        catchError(error => this.handleError(error))\n      );\n  }\n\n  /**\n   * Download file\n   */\n  downloadFile(endpoint: string, filename?: string): Observable<Blob> {\n    this.setLoading(true);\n\n    const options = {\n      headers: this.getHeaders(),\n      responseType: 'blob' as 'json'\n    };\n\n    return this.http.get(`${this.baseUrl}/${endpoint}`, options)\n      .pipe(\n        timeout(60000), // Longer timeout for downloads\n        map((blob: any) => {\n          this.setLoading(false);\n          return blob as Blob;\n        }),\n        catchError(error => this.handleError(error))\n      );\n  }\n\n  /**\n   * Build HTTP parameters from query params\n   */\n  private buildHttpParams(params?: QueryParams): HttpParams {\n    let httpParams = new HttpParams();\n\n    if (params) {\n      if (params.page !== undefined) {\n        httpParams = httpParams.set('page', params.page.toString());\n      }\n      if (params.pageSize !== undefined) {\n        httpParams = httpParams.set('pageSize', params.pageSize.toString());\n      }\n      if (params.sortBy) {\n        httpParams = httpParams.set('sortBy', params.sortBy);\n      }\n      if (params.sortOrder) {\n        httpParams = httpParams.set('sortOrder', params.sortOrder);\n      }\n      if (params.search) {\n        httpParams = httpParams.set('search', params.search);\n      }\n      if (params.filters) {\n        Object.keys(params.filters).forEach(key => {\n          const value = params.filters![key];\n          if (value !== null && value !== undefined && value !== '') {\n            if (Array.isArray(value)) {\n              value.forEach(v => {\n                httpParams = httpParams.append(`filters.${key}`, v.toString());\n              });\n            } else {\n              httpParams = httpParams.set(`filters.${key}`, value.toString());\n            }\n          }\n        });\n      }\n    }\n\n    return httpParams;\n  }\n\n  /**\n   * Get default headers\n   */\n  private getHeaders(): HttpHeaders {\n    let headers = new HttpHeaders({\n      'Content-Type': 'application/json',\n      'Accept': 'application/json'\n    });\n\n    // Add authorization header if token exists\n    const token = this.getAuthToken();\n    if (token) {\n      headers = headers.set('Authorization', `Bearer ${token}`);\n    }\n\n    return headers;\n  }\n\n  /**\n   * Get authentication token from storage\n   */\n  private getAuthToken(): string | null {\n    return localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');\n  }\n\n  /**\n   * Handle successful response\n   */\n  private handleSuccess<T>(response: ApiResponse<T>): ApiResponse<T> {\n    this.setLoading(false);\n    return response;\n  }\n\n  /**\n   * Handle error response\n   */\n  private handleError(error: HttpErrorResponse): Observable<never> {\n    this.setLoading(false);\n\n    let errorMessage = 'An unexpected error occurred';\n\n    if (error.error instanceof ErrorEvent) {\n      // Client-side error\n      errorMessage = `Client Error: ${error.error.message}`;\n    } else {\n      // Server-side error\n      switch (error.status) {\n        case 400:\n          errorMessage = 'Bad Request: Please check your input';\n          break;\n        case 401:\n          errorMessage = 'Unauthorized: Please log in again';\n          // Optionally redirect to login\n          break;\n        case 403:\n          errorMessage = 'Forbidden: You do not have permission to perform this action';\n          break;\n        case 404:\n          errorMessage = 'Not Found: The requested resource was not found';\n          break;\n        case 409:\n          errorMessage = 'Conflict: The resource already exists or there is a conflict';\n          break;\n        case 422:\n          errorMessage = 'Validation Error: Please check your input';\n          break;\n        case 500:\n          errorMessage = 'Internal Server Error: Please try again later';\n          break;\n        case 503:\n          errorMessage = 'Service Unavailable: The server is temporarily unavailable';\n          break;\n        default:\n          errorMessage = `Server Error: ${error.status} - ${error.message}`;\n      }\n\n      // If the server provides a custom error message, use it\n      if (error.error && error.error.message) {\n        errorMessage = error.error.message;\n      }\n    }\n\n    console.error('API Error:', error);\n    return throwError(() => new Error(errorMessage));\n  }\n\n  /**\n   * Set loading state\n   */\n  private setLoading(loading: boolean): void {\n    this.loadingSubject.next(loading);\n  }\n\n  /**\n   * Get current loading state\n   */\n  get isLoading(): boolean {\n    return this.loadingSubject.value;\n  }\n}\n "], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAO,IAAM,cAAc;EACzB,YAAY;EACZ,QAAQ;EACR,SAAS;EACT,SAAS;EACT,UAAU;IACR,gBAAgB;IAChB,iBAAiB;IACjB,qBAAqB;IACrB,kBAAkB;IAClB,cAAc;;EAEhB,KAAK;IACH,SAAS;IACT,eAAe;IACf,UAAU;;EAEZ,OAAO;IACL,eAAe;MACb;MAAW;MAAW;MAAW;MAAW;MAC5C;MAAW;MAAW;MAAW;MAAW;;IAE9C,mBAAmB;;EAErB,SAAS;IACP,QAAQ;IACR,UAAU;IACV,oBAAoB;;;;;ACQlB,IAAO,iBAAP,MAAO,gBAAc;EAQL;EAPH,UAAU,YAAY,UAAU;EAChC,iBAAiB;;EACjB,aAAa;EAEtB,iBAAiB,IAAI,gBAAyB,KAAK;EACpD,WAAW,KAAK,eAAe,aAAY;EAElD,YAAoB,MAAgB;AAAhB,SAAA,OAAA;EAAmB;;;;EAKvC,IAAO,UAAkB,QAAoB;AAC3C,SAAK,WAAW,IAAI;AAEpB,UAAM,aAAa,KAAK,gBAAgB,MAAM;AAC9C,UAAM,UAAU;MACd,QAAQ;MACR,SAAS,KAAK,WAAU;;AAG1B,WAAO,KAAK,KAAK,IAAoB,GAAG,KAAK,OAAO,IAAI,QAAQ,IAAI,OAAO,EACxE,KACC,QAAQ,KAAK,cAAc,GAC3B,MAAM,KAAK,UAAU,GACrB,IAAI,cAAY,KAAK,cAAc,QAAQ,CAAC,GAC5C,WAAW,WAAS,KAAK,YAAY,KAAK,CAAC,CAAC;EAElD;;;;EAKA,KAAQ,UAAkB,MAAS;AACjC,SAAK,WAAW,IAAI;AAEpB,UAAM,UAAU;MACd,SAAS,KAAK,WAAU;;AAG1B,WAAO,KAAK,KAAK,KAAqB,GAAG,KAAK,OAAO,IAAI,QAAQ,IAAI,MAAM,OAAO,EAC/E,KACC,QAAQ,KAAK,cAAc,GAC3B,IAAI,cAAY,KAAK,cAAc,QAAQ,CAAC,GAC5C,WAAW,WAAS,KAAK,YAAY,KAAK,CAAC,CAAC;EAElD;;;;EAKA,IAAO,UAAkB,MAAS;AAChC,SAAK,WAAW,IAAI;AAEpB,UAAM,UAAU;MACd,SAAS,KAAK,WAAU;;AAG1B,WAAO,KAAK,KAAK,IAAoB,GAAG,KAAK,OAAO,IAAI,QAAQ,IAAI,MAAM,OAAO,EAC9E,KACC,QAAQ,KAAK,cAAc,GAC3B,IAAI,cAAY,KAAK,cAAc,QAAQ,CAAC,GAC5C,WAAW,WAAS,KAAK,YAAY,KAAK,CAAC,CAAC;EAElD;;;;EAKA,MAAS,UAAkB,MAAS;AAClC,SAAK,WAAW,IAAI;AAEpB,UAAM,UAAU;MACd,SAAS,KAAK,WAAU;;AAG1B,WAAO,KAAK,KAAK,MAAsB,GAAG,KAAK,OAAO,IAAI,QAAQ,IAAI,MAAM,OAAO,EAChF,KACC,QAAQ,KAAK,cAAc,GAC3B,IAAI,cAAY,KAAK,cAAc,QAAQ,CAAC,GAC5C,WAAW,WAAS,KAAK,YAAY,KAAK,CAAC,CAAC;EAElD;;;;EAKA,OAAU,UAAgB;AACxB,SAAK,WAAW,IAAI;AAEpB,UAAM,UAAU;MACd,SAAS,KAAK,WAAU;;AAG1B,WAAO,KAAK,KAAK,OAAuB,GAAG,KAAK,OAAO,IAAI,QAAQ,IAAI,OAAO,EAC3E,KACC,QAAQ,KAAK,cAAc,GAC3B,IAAI,cAAY,KAAK,cAAc,QAAQ,CAAC,GAC5C,WAAW,WAAS,KAAK,YAAY,KAAK,CAAC,CAAC;EAElD;;;;EAKA,WAAc,UAAkB,MAAY,gBAAoB;AAC9D,SAAK,WAAW,IAAI;AAEpB,UAAM,WAAW,IAAI,SAAQ;AAC7B,aAAS,OAAO,QAAQ,IAAI;AAE5B,QAAI,gBAAgB;AAClB,aAAO,KAAK,cAAc,EAAE,QAAQ,SAAM;AACxC,iBAAS,OAAO,KAAK,eAAe,GAAG,CAAC;MAC1C,CAAC;IACH;AAEA,UAAM,UAAU,IAAI,YAAW;AAG/B,WAAO,KAAK,KAAK,KAAqB,GAAG,KAAK,OAAO,IAAI,QAAQ,IAAI,UAAU,EAAE,QAAO,CAAE,EACvF;MACC,QAAQ,GAAK;;MACb,IAAI,cAAY,KAAK,cAAc,QAAQ,CAAC;MAC5C,WAAW,WAAS,KAAK,YAAY,KAAK,CAAC;IAAC;EAElD;;;;EAKA,aAAa,UAAkB,UAAiB;AAC9C,SAAK,WAAW,IAAI;AAEpB,UAAM,UAAU;MACd,SAAS,KAAK,WAAU;MACxB,cAAc;;AAGhB,WAAO,KAAK,KAAK,IAAI,GAAG,KAAK,OAAO,IAAI,QAAQ,IAAI,OAAO,EACxD;MACC,QAAQ,GAAK;;MACb,IAAI,CAAC,SAAa;AAChB,aAAK,WAAW,KAAK;AACrB,eAAO;MACT,CAAC;MACD,WAAW,WAAS,KAAK,YAAY,KAAK,CAAC;IAAC;EAElD;;;;EAKQ,gBAAgB,QAAoB;AAC1C,QAAI,aAAa,IAAI,WAAU;AAE/B,QAAI,QAAQ;AACV,UAAI,OAAO,SAAS,QAAW;AAC7B,qBAAa,WAAW,IAAI,QAAQ,OAAO,KAAK,SAAQ,CAAE;MAC5D;AACA,UAAI,OAAO,aAAa,QAAW;AACjC,qBAAa,WAAW,IAAI,YAAY,OAAO,SAAS,SAAQ,CAAE;MACpE;AACA,UAAI,OAAO,QAAQ;AACjB,qBAAa,WAAW,IAAI,UAAU,OAAO,MAAM;MACrD;AACA,UAAI,OAAO,WAAW;AACpB,qBAAa,WAAW,IAAI,aAAa,OAAO,SAAS;MAC3D;AACA,UAAI,OAAO,QAAQ;AACjB,qBAAa,WAAW,IAAI,UAAU,OAAO,MAAM;MACrD;AACA,UAAI,OAAO,SAAS;AAClB,eAAO,KAAK,OAAO,OAAO,EAAE,QAAQ,SAAM;AACxC,gBAAM,QAAQ,OAAO,QAAS,GAAG;AACjC,cAAI,UAAU,QAAQ,UAAU,UAAa,UAAU,IAAI;AACzD,gBAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,oBAAM,QAAQ,OAAI;AAChB,6BAAa,WAAW,OAAO,WAAW,GAAG,IAAI,EAAE,SAAQ,CAAE;cAC/D,CAAC;YACH,OAAO;AACL,2BAAa,WAAW,IAAI,WAAW,GAAG,IAAI,MAAM,SAAQ,CAAE;YAChE;UACF;QACF,CAAC;MACH;IACF;AAEA,WAAO;EACT;;;;EAKQ,aAAU;AAChB,QAAI,UAAU,IAAI,YAAY;MAC5B,gBAAgB;MAChB,UAAU;KACX;AAGD,UAAM,QAAQ,KAAK,aAAY;AAC/B,QAAI,OAAO;AACT,gBAAU,QAAQ,IAAI,iBAAiB,UAAU,KAAK,EAAE;IAC1D;AAEA,WAAO;EACT;;;;EAKQ,eAAY;AAClB,WAAO,aAAa,QAAQ,YAAY,KAAK,eAAe,QAAQ,YAAY;EAClF;;;;EAKQ,cAAiB,UAAwB;AAC/C,SAAK,WAAW,KAAK;AACrB,WAAO;EACT;;;;EAKQ,YAAY,OAAwB;AAC1C,SAAK,WAAW,KAAK;AAErB,QAAI,eAAe;AAEnB,QAAI,MAAM,iBAAiB,YAAY;AAErC,qBAAe,iBAAiB,MAAM,MAAM,OAAO;IACrD,OAAO;AAEL,cAAQ,MAAM,QAAQ;QACpB,KAAK;AACH,yBAAe;AACf;QACF,KAAK;AACH,yBAAe;AAEf;QACF,KAAK;AACH,yBAAe;AACf;QACF,KAAK;AACH,yBAAe;AACf;QACF,KAAK;AACH,yBAAe;AACf;QACF,KAAK;AACH,yBAAe;AACf;QACF,KAAK;AACH,yBAAe;AACf;QACF,KAAK;AACH,yBAAe;AACf;QACF;AACE,yBAAe,iBAAiB,MAAM,MAAM,MAAM,MAAM,OAAO;MACnE;AAGA,UAAI,MAAM,SAAS,MAAM,MAAM,SAAS;AACtC,uBAAe,MAAM,MAAM;MAC7B;IACF;AAEA,YAAQ,MAAM,cAAc,KAAK;AACjC,WAAO,WAAW,MAAM,IAAI,MAAM,YAAY,CAAC;EACjD;;;;EAKQ,WAAW,SAAgB;AACjC,SAAK,eAAe,KAAK,OAAO;EAClC;;;;EAKA,IAAI,YAAS;AACX,WAAO,KAAK,eAAe;EAC7B;;qCAlSW,iBAAc,mBAAA,UAAA,CAAA;EAAA;4EAAd,iBAAc,SAAd,gBAAc,WAAA,YAFb,OAAM,CAAA;;;sEAEP,gBAAc,CAAA;UAH1B;WAAW;MACV,YAAY;KACb;;;", "names": []}