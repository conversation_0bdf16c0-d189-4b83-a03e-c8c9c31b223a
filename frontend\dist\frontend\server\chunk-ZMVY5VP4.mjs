import './polyfills.server.mjs';
import {
  DefaultValueAccessor,
  FormsModule,
  NG_VALUE_ACCESSOR,
  NgControlStatus,
  NgModel,
  NgSelectOption,
  RequiredValidator,
  SelectControlValueAccessor,
  ɵNgSelectMultipleOption
} from "./chunk-22WSPHJT.mjs";
import {
  CommonModule,
  Component,
  EventEmitter,
  Input,
  NgForOf,
  NgIf,
  Output,
  forwardRef,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵProvidersFeature,
  ɵɵadvance,
  ɵɵattribute,
  ɵɵclassMap,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnamespaceSVG,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtextInterpolate2,
  ɵɵtwoWayBindingSet,
  ɵɵtwoWayListener,
  ɵɵtwoWayProperty
} from "./chunk-IPMSWJNG.mjs";

// src/app/shared/components/form-input/form-input.component.ts
function FormInputComponent_label_1_span_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 12);
    \u0275\u0275text(1, "*");
    \u0275\u0275elementEnd();
  }
}
function FormInputComponent_label_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "label", 10);
    \u0275\u0275text(1);
    \u0275\u0275template(2, FormInputComponent_label_1_span_2_Template, 2, 0, "span", 11);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275property("for", ctx_r0.inputId);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.label, " ");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.required);
  }
}
function FormInputComponent_textarea_4_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "textarea", 13);
    \u0275\u0275twoWayListener("ngModelChange", function FormInputComponent_textarea_4_Template_textarea_ngModelChange_0_listener($event) {
      \u0275\u0275restoreView(_r2);
      const ctx_r0 = \u0275\u0275nextContext();
      \u0275\u0275twoWayBindingSet(ctx_r0.value, $event) || (ctx_r0.value = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275listener("input", function FormInputComponent_textarea_4_Template_textarea_input_0_listener($event) {
      \u0275\u0275restoreView(_r2);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.onInput($event));
    })("blur", function FormInputComponent_textarea_4_Template_textarea_blur_0_listener() {
      \u0275\u0275restoreView(_r2);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.onBlur());
    })("focus", function FormInputComponent_textarea_4_Template_textarea_focus_0_listener() {
      \u0275\u0275restoreView(_r2);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.onFocus());
    });
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275classMap(ctx_r0.inputClasses);
    \u0275\u0275property("id", ctx_r0.inputId)("placeholder", ctx_r0.placeholder)("disabled", ctx_r0.disabled)("readonly", ctx_r0.readonly)("required", ctx_r0.required)("rows", ctx_r0.rows);
    \u0275\u0275twoWayProperty("ngModel", ctx_r0.value);
    \u0275\u0275attribute("maxlength", ctx_r0.maxlength);
  }
}
function FormInputComponent_select_5_option_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "option", 17);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.placeholder);
  }
}
function FormInputComponent_select_5_option_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "option", 18);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const option_r4 = ctx.$implicit;
    \u0275\u0275property("value", option_r4.value);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", option_r4.label, " ");
  }
}
function FormInputComponent_select_5_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "select", 14);
    \u0275\u0275twoWayListener("ngModelChange", function FormInputComponent_select_5_Template_select_ngModelChange_0_listener($event) {
      \u0275\u0275restoreView(_r3);
      const ctx_r0 = \u0275\u0275nextContext();
      \u0275\u0275twoWayBindingSet(ctx_r0.value, $event) || (ctx_r0.value = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275listener("change", function FormInputComponent_select_5_Template_select_change_0_listener($event) {
      \u0275\u0275restoreView(_r3);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.onInput($event));
    })("blur", function FormInputComponent_select_5_Template_select_blur_0_listener() {
      \u0275\u0275restoreView(_r3);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.onBlur());
    })("focus", function FormInputComponent_select_5_Template_select_focus_0_listener() {
      \u0275\u0275restoreView(_r3);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.onFocus());
    });
    \u0275\u0275template(1, FormInputComponent_select_5_option_1_Template, 2, 1, "option", 15)(2, FormInputComponent_select_5_option_2_Template, 2, 2, "option", 16);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275classMap(ctx_r0.inputClasses);
    \u0275\u0275property("id", ctx_r0.inputId)("disabled", ctx_r0.disabled)("required", ctx_r0.required);
    \u0275\u0275twoWayProperty("ngModel", ctx_r0.value);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.placeholder);
    \u0275\u0275advance();
    \u0275\u0275property("ngForOf", ctx_r0.options);
  }
}
function FormInputComponent_div_6_button_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r5 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 22);
    \u0275\u0275listener("click", function FormInputComponent_div_6_button_1_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r5);
      const ctx_r0 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r0.clearValue());
    });
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(1, "svg", 23);
    \u0275\u0275element(2, "line", 24)(3, "line", 25);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275attribute("aria-label", "Clear " + (ctx_r0.label || "input"));
  }
}
function FormInputComponent_div_6__svg_svg_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(0, "svg", 26);
    \u0275\u0275element(1, "path", 27);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275attribute("d", ctx_r0.rightIcon);
  }
}
function FormInputComponent_div_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 19);
    \u0275\u0275template(1, FormInputComponent_div_6_button_1_Template, 4, 1, "button", 20)(2, FormInputComponent_div_6__svg_svg_2_Template, 2, 1, "svg", 21);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.clearable && ctx_r0.value && !ctx_r0.disabled);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.rightIcon);
  }
}
function FormInputComponent_div_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 28);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.helpText, " ");
  }
}
function FormInputComponent_div_8_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 29);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(1, "svg", 30);
    \u0275\u0275element(2, "circle", 31)(3, "line", 32)(4, "line", 33);
    \u0275\u0275elementEnd();
    \u0275\u0275text(5);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate1(" ", ctx_r0.errorMessage, " ");
  }
}
function FormInputComponent_div_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 34);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate2(" ", (ctx_r0.value || "").length, "/", ctx_r0.maxlength, " ");
  }
}
var FormInputComponent = class _FormInputComponent {
  type = "text";
  label = "";
  placeholder = "";
  helpText = "";
  errorMessage = "";
  leftIcon = "";
  rightIcon = "";
  disabled = false;
  readonly = false;
  required = false;
  clearable = false;
  showCharacterCount = false;
  // Input-specific attributes
  min;
  max;
  step;
  maxlength;
  pattern;
  autocomplete;
  rows = 4;
  // For textarea
  options = [];
  // For select
  inputChange = new EventEmitter();
  inputFocus = new EventEmitter();
  inputBlur = new EventEmitter();
  enterPressed = new EventEmitter();
  value = "";
  inputId = `input-${Math.random().toString(36).substr(2, 9)}`;
  // ControlValueAccessor implementation
  onChange = (value) => {
  };
  onTouched = () => {
  };
  writeValue(value) {
    this.value = value;
  }
  registerOnChange(fn) {
    this.onChange = fn;
  }
  registerOnTouched(fn) {
    this.onTouched = fn;
  }
  setDisabledState(isDisabled) {
    this.disabled = isDisabled;
  }
  get containerClasses() {
    const classes = [
      "form-input"
    ];
    if (this.errorMessage)
      classes.push("form-input-error");
    if (this.disabled)
      classes.push("form-input-disabled");
    if (this.readonly)
      classes.push("form-input-readonly");
    return classes.join(" ");
  }
  get inputClasses() {
    const classes = ["input-field"];
    if (this.leftIcon)
      classes.push("has-left-icon");
    if (this.rightIcon || this.clearable)
      classes.push("has-right-icon");
    return classes.join(" ");
  }
  onInput(event) {
    const target = event.target;
    this.value = target.value;
    this.onChange(this.value);
    this.inputChange.emit(this.value);
  }
  onFocus() {
    this.inputFocus.emit();
  }
  onBlur() {
    this.onTouched();
    this.inputBlur.emit();
  }
  onEnter() {
    this.enterPressed.emit();
  }
  clearValue() {
    this.value = "";
    this.onChange(this.value);
    this.inputChange.emit(this.value);
  }
  static \u0275fac = function FormInputComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _FormInputComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _FormInputComponent, selectors: [["app-form-input"]], inputs: { type: "type", label: "label", placeholder: "placeholder", helpText: "helpText", errorMessage: "errorMessage", leftIcon: "leftIcon", rightIcon: "rightIcon", disabled: "disabled", readonly: "readonly", required: "required", clearable: "clearable", showCharacterCount: "showCharacterCount", min: "min", max: "max", step: "step", maxlength: "maxlength", pattern: "pattern", autocomplete: "autocomplete", rows: "rows", options: "options" }, outputs: { inputChange: "inputChange", inputFocus: "inputFocus", inputBlur: "inputBlur", enterPressed: "enterPressed" }, features: [\u0275\u0275ProvidersFeature([
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => _FormInputComponent),
      multi: true
    }
  ])], decls: 10, vars: 22, consts: [[1, "form-group"], ["class", "block text-sm font-medium text-gray-700 mb-1", 3, "for", 4, "ngIf"], [1, "relative", "rounded-md", "shadow-sm"], [1, "block", "w-full", "px-3", "py-2", "border", "rounded-md", "focus:outline-none", "focus:ring-indigo-500", "focus:border-indigo-500", "sm:text-sm", 3, "ngModelChange", "input", "blur", "focus", "keyup.enter", "id", "type", "placeholder", "disabled", "readonly", "required", "ngModel"], [3, "id", "placeholder", "disabled", "readonly", "required", "rows", "class", "ngModel", "ngModelChange", "input", "blur", "focus", 4, "ngIf"], [3, "id", "disabled", "required", "class", "ngModel", "ngModelChange", "change", "blur", "focus", 4, "ngIf"], ["class", "input-icon-right", 4, "ngIf"], ["class", "form-help", 4, "ngIf"], ["class", "form-error", 4, "ngIf"], ["class", "character-count", 4, "ngIf"], [1, "block", "text-sm", "font-medium", "text-gray-700", "mb-1", 3, "for"], ["class", "text-red-500", 4, "ngIf"], [1, "text-red-500"], [3, "ngModelChange", "input", "blur", "focus", "id", "placeholder", "disabled", "readonly", "required", "rows", "ngModel"], [3, "ngModelChange", "change", "blur", "focus", "id", "disabled", "required", "ngModel"], ["value", "", "disabled", "", 4, "ngIf"], [3, "value", 4, "ngFor", "ngForOf"], ["value", "", "disabled", ""], [3, "value"], [1, "input-icon-right"], ["type", "button", "class", "clear-button", 3, "click", 4, "ngIf"], ["class", "input-icon", "viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", 4, "ngIf"], ["type", "button", 1, "clear-button", 3, "click"], ["viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", 1, "clear-icon"], ["x1", "18", "y1", "6", "x2", "6", "y2", "18"], ["x1", "6", "y1", "6", "x2", "18", "y2", "18"], ["viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", 1, "input-icon"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2"], [1, "form-help"], [1, "form-error"], ["viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", 1, "error-icon"], ["cx", "12", "cy", "12", "r", "10"], ["x1", "15", "y1", "9", "x2", "9", "y2", "15"], ["x1", "9", "y1", "9", "x2", "15", "y2", "15"], [1, "character-count"]], template: function FormInputComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0);
      \u0275\u0275template(1, FormInputComponent_label_1_Template, 3, 3, "label", 1);
      \u0275\u0275elementStart(2, "div", 2)(3, "input", 3);
      \u0275\u0275twoWayListener("ngModelChange", function FormInputComponent_Template_input_ngModelChange_3_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.value, $event) || (ctx.value = $event);
        return $event;
      });
      \u0275\u0275listener("input", function FormInputComponent_Template_input_input_3_listener($event) {
        return ctx.onInput($event);
      })("blur", function FormInputComponent_Template_input_blur_3_listener() {
        return ctx.onBlur();
      })("focus", function FormInputComponent_Template_input_focus_3_listener() {
        return ctx.onFocus();
      })("keyup.enter", function FormInputComponent_Template_input_keyup_enter_3_listener() {
        return ctx.onEnter();
      });
      \u0275\u0275elementEnd();
      \u0275\u0275template(4, FormInputComponent_textarea_4_Template, 1, 10, "textarea", 4)(5, FormInputComponent_select_5_Template, 3, 8, "select", 5)(6, FormInputComponent_div_6_Template, 3, 2, "div", 6);
      \u0275\u0275elementEnd();
      \u0275\u0275template(7, FormInputComponent_div_7_Template, 2, 1, "div", 7)(8, FormInputComponent_div_8_Template, 6, 1, "div", 8)(9, FormInputComponent_div_9_Template, 2, 2, "div", 9);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.label);
      \u0275\u0275advance(2);
      \u0275\u0275classProp("error", !!ctx.errorMessage)("border-red-300", !!ctx.errorMessage)("text-red-900", !!ctx.errorMessage)("placeholder-red-300", !!ctx.errorMessage);
      \u0275\u0275property("id", ctx.inputId)("type", ctx.type)("placeholder", ctx.placeholder)("disabled", ctx.disabled)("readonly", ctx.readonly)("required", ctx.required);
      \u0275\u0275twoWayProperty("ngModel", ctx.value);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.type === "textarea");
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.type === "select");
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.rightIcon || ctx.clearable && ctx.value);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.helpText && !ctx.errorMessage);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.errorMessage);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.maxlength && ctx.showCharacterCount);
    }
  }, dependencies: [CommonModule, NgForOf, NgIf, FormsModule, NgSelectOption, \u0275NgSelectMultipleOption, DefaultValueAccessor, SelectControlValueAccessor, NgControlStatus, RequiredValidator, NgModel], styles: [`

.form-input-container[_ngcontent-%COMP%] {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}
.form-label[_ngcontent-%COMP%] {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--secondary-700);
  cursor: pointer;
}
.required-indicator[_ngcontent-%COMP%] {
  color: var(--error-500);
  margin-left: var(--spacing-xs);
}
.input-wrapper[_ngcontent-%COMP%] {
  position: relative;
  display: flex;
  align-items: center;
}
.input-field[_ngcontent-%COMP%] {
  width: 100%;
  border: 1px solid var(--secondary-300);
  border-radius: var(--radius-md);
  background: white;
  font-size: var(--text-sm);
  color: var(--secondary-800);
  transition: all 0.2s ease;
  outline: none;
}
.input-field[_ngcontent-%COMP%]::placeholder {
  color: var(--secondary-400);
}
.input-field[_ngcontent-%COMP%]:focus {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}
.input-field[_ngcontent-%COMP%]:disabled {
  background: var(--secondary-50);
  color: var(--secondary-500);
  cursor: not-allowed;
}
.input-field[_ngcontent-%COMP%]:readonly {
  background: var(--secondary-50);
  cursor: default;
}
.input-field.has-left-icon[_ngcontent-%COMP%] {
  padding-left: 40px;
}
.input-field.has-right-icon[_ngcontent-%COMP%] {
  padding-right: 40px;
}
.form-input-sm[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%] {
  height: 32px;
  padding: 0 var(--spacing-sm);
  font-size: var(--text-xs);
}
.form-input-md[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%] {
  height: 40px;
  padding: 0 var(--spacing-md);
  font-size: var(--text-sm);
}
.form-input-lg[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%] {
  height: 48px;
  padding: 0 var(--spacing-lg);
  font-size: var(--text-base);
}
textarea.input-field[_ngcontent-%COMP%] {
  height: auto;
  min-height: 80px;
  padding: var(--spacing-md);
  resize: vertical;
  line-height: var(--leading-relaxed);
}
.form-input-sm[_ngcontent-%COMP%]   textarea.input-field[_ngcontent-%COMP%] {
  padding: var(--spacing-sm);
  min-height: 64px;
}
.form-input-lg[_ngcontent-%COMP%]   textarea.input-field[_ngcontent-%COMP%] {
  padding: var(--spacing-lg);
  min-height: 96px;
}
select.input-field[_ngcontent-%COMP%] {
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-sm) center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
  appearance: none;
}
select.input-field[_ngcontent-%COMP%]:disabled {
  cursor: not-allowed;
}
.input-icon[_ngcontent-%COMP%] {
  position: absolute;
  width: 16px;
  height: 16px;
  color: var(--secondary-400);
  pointer-events: none;
}
.input-icon-left[_ngcontent-%COMP%] {
  left: var(--spacing-md);
}
.input-icon-right[_ngcontent-%COMP%] {
  right: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}
.form-input-sm[_ngcontent-%COMP%]   .input-icon[_ngcontent-%COMP%] {
  width: 14px;
  height: 14px;
}
.form-input-sm[_ngcontent-%COMP%]   .input-icon-left[_ngcontent-%COMP%] {
  left: var(--spacing-sm);
}
.form-input-sm[_ngcontent-%COMP%]   .input-icon-right[_ngcontent-%COMP%] {
  right: var(--spacing-sm);
}
.form-input-lg[_ngcontent-%COMP%]   .input-icon[_ngcontent-%COMP%] {
  width: 18px;
  height: 18px;
}
.form-input-lg[_ngcontent-%COMP%]   .input-icon-left[_ngcontent-%COMP%] {
  left: var(--spacing-lg);
}
.form-input-lg[_ngcontent-%COMP%]   .input-icon-right[_ngcontent-%COMP%] {
  right: var(--spacing-lg);
}
.clear-button[_ngcontent-%COMP%] {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border: none;
  background: none;
  border-radius: var(--radius-sm);
  color: var(--secondary-400);
  cursor: pointer;
  transition: all 0.2s ease;
}
.clear-button[_ngcontent-%COMP%]:hover {
  background: var(--secondary-100);
  color: var(--secondary-600);
}
.clear-button[_ngcontent-%COMP%]:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 1px;
}
.clear-icon[_ngcontent-%COMP%] {
  width: 12px;
  height: 12px;
}
.form-help[_ngcontent-%COMP%] {
  font-size: var(--text-xs);
  color: var(--secondary-500);
  line-height: var(--leading-relaxed);
}
.form-error[_ngcontent-%COMP%] {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--text-xs);
  color: var(--error-600);
  line-height: var(--leading-relaxed);
}
.error-icon[_ngcontent-%COMP%] {
  width: 14px;
  height: 14px;
  flex-shrink: 0;
}
.character-count[_ngcontent-%COMP%] {
  font-size: var(--text-xs);
  color: var(--secondary-500);
  text-align: right;
}
.form-input-error[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%] {
  border-color: var(--error-500);
}
.form-input-error[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%]:focus {
  border-color: var(--error-500);
  box-shadow: 0 0 0 3px var(--error-100);
}
.form-input-error[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {
  color: var(--error-600);
}
.form-input-error[_ngcontent-%COMP%]   .input-icon[_ngcontent-%COMP%] {
  color: var(--error-500);
}
.form-input-disabled[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {
  color: var(--secondary-500);
}
.form-input-disabled[_ngcontent-%COMP%]   .input-icon[_ngcontent-%COMP%] {
  color: var(--secondary-300);
}
.form-input-readonly[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%] {
  border-color: var(--secondary-200);
}
.input-wrapper[_ngcontent-%COMP%]:focus-within   .input-icon[_ngcontent-%COMP%] {
  color: var(--primary-500);
}
.form-input-error[_ngcontent-%COMP%]   .input-wrapper[_ngcontent-%COMP%]:focus-within   .input-icon[_ngcontent-%COMP%] {
  color: var(--error-500);
}
@media (max-width: 768px) {
  .form-input-lg[_ngcontent-%COMP%]   .input-field[_ngcontent-%COMP%] {
    height: 44px;
    padding: 0 var(--spacing-md);
  }
  .form-input-lg[_ngcontent-%COMP%]   textarea.input-field[_ngcontent-%COMP%] {
    padding: var(--spacing-md);
  }
}
@media (prefers-contrast: high) {
  .input-field[_ngcontent-%COMP%] {
    border-width: 2px;
  }
  .input-field[_ngcontent-%COMP%]:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
  }
}
@media (prefers-reduced-motion: reduce) {
  .input-field[_ngcontent-%COMP%], 
   .clear-button[_ngcontent-%COMP%], 
   .input-icon[_ngcontent-%COMP%] {
    transition: none;
  }
}
@media print {
  .input-field[_ngcontent-%COMP%] {
    border: 1px solid var(--secondary-400);
    background: white;
  }
  .clear-button[_ngcontent-%COMP%] {
    display: none;
  }
}
/*# sourceMappingURL=form-input.component.css.map */`] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(FormInputComponent, [{
    type: Component,
    args: [{ selector: "app-form-input", standalone: true, imports: [CommonModule, FormsModule], providers: [
      {
        provide: NG_VALUE_ACCESSOR,
        useExisting: forwardRef(() => FormInputComponent),
        multi: true
      }
    ], template: `
    <div class="form-group">
      <label *ngIf="label" [for]="inputId" class="block text-sm font-medium text-gray-700 mb-1">
        {{ label }}
        <span class="text-red-500" *ngIf="required">*</span>
      </label>
      <div class="relative rounded-md shadow-sm">
        <input
          [id]="inputId"
          [type]="type"
          [placeholder]="placeholder"
          [disabled]="disabled"
          [readonly]="readonly"
          [required]="required"
          [class.error]="!!errorMessage"
          class="block w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          [class.border-red-300]="!!errorMessage"
          [class.text-red-900]="!!errorMessage"
          [class.placeholder-red-300]="!!errorMessage"
          [(ngModel)]="value"
          (input)="onInput($event)"
          (blur)="onBlur()"
          (focus)="onFocus()"
          (keyup.enter)="onEnter()"
        />

        <!-- Textarea -->
        <textarea
          *ngIf="type === 'textarea'"
          [id]="inputId"
          [placeholder]="placeholder"
          [disabled]="disabled"
          [readonly]="readonly"
          [required]="required"
          [attr.maxlength]="maxlength"
          [rows]="rows"
          [class]="inputClasses"
          [(ngModel)]="value"
          (input)="onInput($event)"
          (blur)="onBlur()"
          (focus)="onFocus()"
        ></textarea>

        <!-- Select -->
        <select
          *ngIf="type === 'select'"
          [id]="inputId"
          [disabled]="disabled"
          [required]="required"
          [class]="inputClasses"
          [(ngModel)]="value"
          (change)="onInput($event)"
          (blur)="onBlur()"
          (focus)="onFocus()"
        >
          <option value="" *ngIf="placeholder" disabled>{{ placeholder }}</option>
          <option *ngFor="let option of options" [value]="option.value">
            {{ option.label }}
          </option>
        </select>

        <!-- Right Icon / Clear Button -->
        <div class="input-icon-right" *ngIf="rightIcon || (clearable && value)">
          <!-- Clear Button -->
          <button
            *ngIf="clearable && value && !disabled"
            type="button"
            class="clear-button"
            (click)="clearValue()"
            [attr.aria-label]="'Clear ' + (label || 'input')"
          >
            <svg class="clear-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>

          <!-- Right Icon -->
          <svg *ngIf="rightIcon" class="input-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" [attr.d]="rightIcon" />
          </svg>
        </div>
      </div>

      <!-- Help Text -->
      <div class="form-help" *ngIf="helpText && !errorMessage">
        {{ helpText }}
      </div>

      <!-- Error Message -->
      <div class="form-error" *ngIf="errorMessage">
        <svg class="error-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <circle cx="12" cy="12" r="10"></circle>
          <line x1="15" y1="9" x2="9" y2="15"></line>
          <line x1="9" y1="9" x2="15" y2="15"></line>
        </svg>
        {{ errorMessage }}
      </div>

      <!-- Character Count -->
      <div class="character-count" *ngIf="maxlength && showCharacterCount">
        {{ (value || '').length }}/{{ maxlength }}
      </div>
    </div>
  `, styles: [`/* src/app/shared/components/form-input/form-input.component.scss */
.form-input-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}
.form-label {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--secondary-700);
  cursor: pointer;
}
.required-indicator {
  color: var(--error-500);
  margin-left: var(--spacing-xs);
}
.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}
.input-field {
  width: 100%;
  border: 1px solid var(--secondary-300);
  border-radius: var(--radius-md);
  background: white;
  font-size: var(--text-sm);
  color: var(--secondary-800);
  transition: all 0.2s ease;
  outline: none;
}
.input-field::placeholder {
  color: var(--secondary-400);
}
.input-field:focus {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}
.input-field:disabled {
  background: var(--secondary-50);
  color: var(--secondary-500);
  cursor: not-allowed;
}
.input-field:readonly {
  background: var(--secondary-50);
  cursor: default;
}
.input-field.has-left-icon {
  padding-left: 40px;
}
.input-field.has-right-icon {
  padding-right: 40px;
}
.form-input-sm .input-field {
  height: 32px;
  padding: 0 var(--spacing-sm);
  font-size: var(--text-xs);
}
.form-input-md .input-field {
  height: 40px;
  padding: 0 var(--spacing-md);
  font-size: var(--text-sm);
}
.form-input-lg .input-field {
  height: 48px;
  padding: 0 var(--spacing-lg);
  font-size: var(--text-base);
}
textarea.input-field {
  height: auto;
  min-height: 80px;
  padding: var(--spacing-md);
  resize: vertical;
  line-height: var(--leading-relaxed);
}
.form-input-sm textarea.input-field {
  padding: var(--spacing-sm);
  min-height: 64px;
}
.form-input-lg textarea.input-field {
  padding: var(--spacing-lg);
  min-height: 96px;
}
select.input-field {
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-sm) center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
  appearance: none;
}
select.input-field:disabled {
  cursor: not-allowed;
}
.input-icon {
  position: absolute;
  width: 16px;
  height: 16px;
  color: var(--secondary-400);
  pointer-events: none;
}
.input-icon-left {
  left: var(--spacing-md);
}
.input-icon-right {
  right: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}
.form-input-sm .input-icon {
  width: 14px;
  height: 14px;
}
.form-input-sm .input-icon-left {
  left: var(--spacing-sm);
}
.form-input-sm .input-icon-right {
  right: var(--spacing-sm);
}
.form-input-lg .input-icon {
  width: 18px;
  height: 18px;
}
.form-input-lg .input-icon-left {
  left: var(--spacing-lg);
}
.form-input-lg .input-icon-right {
  right: var(--spacing-lg);
}
.clear-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border: none;
  background: none;
  border-radius: var(--radius-sm);
  color: var(--secondary-400);
  cursor: pointer;
  transition: all 0.2s ease;
}
.clear-button:hover {
  background: var(--secondary-100);
  color: var(--secondary-600);
}
.clear-button:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 1px;
}
.clear-icon {
  width: 12px;
  height: 12px;
}
.form-help {
  font-size: var(--text-xs);
  color: var(--secondary-500);
  line-height: var(--leading-relaxed);
}
.form-error {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: var(--text-xs);
  color: var(--error-600);
  line-height: var(--leading-relaxed);
}
.error-icon {
  width: 14px;
  height: 14px;
  flex-shrink: 0;
}
.character-count {
  font-size: var(--text-xs);
  color: var(--secondary-500);
  text-align: right;
}
.form-input-error .input-field {
  border-color: var(--error-500);
}
.form-input-error .input-field:focus {
  border-color: var(--error-500);
  box-shadow: 0 0 0 3px var(--error-100);
}
.form-input-error .form-label {
  color: var(--error-600);
}
.form-input-error .input-icon {
  color: var(--error-500);
}
.form-input-disabled .form-label {
  color: var(--secondary-500);
}
.form-input-disabled .input-icon {
  color: var(--secondary-300);
}
.form-input-readonly .input-field {
  border-color: var(--secondary-200);
}
.input-wrapper:focus-within .input-icon {
  color: var(--primary-500);
}
.form-input-error .input-wrapper:focus-within .input-icon {
  color: var(--error-500);
}
@media (max-width: 768px) {
  .form-input-lg .input-field {
    height: 44px;
    padding: 0 var(--spacing-md);
  }
  .form-input-lg textarea.input-field {
    padding: var(--spacing-md);
  }
}
@media (prefers-contrast: high) {
  .input-field {
    border-width: 2px;
  }
  .input-field:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
  }
}
@media (prefers-reduced-motion: reduce) {
  .input-field,
  .clear-button,
  .input-icon {
    transition: none;
  }
}
@media print {
  .input-field {
    border: 1px solid var(--secondary-400);
    background: white;
  }
  .clear-button {
    display: none;
  }
}
/*# sourceMappingURL=form-input.component.css.map */
`] }]
  }], null, { type: [{
    type: Input
  }], label: [{
    type: Input
  }], placeholder: [{
    type: Input
  }], helpText: [{
    type: Input
  }], errorMessage: [{
    type: Input
  }], leftIcon: [{
    type: Input
  }], rightIcon: [{
    type: Input
  }], disabled: [{
    type: Input
  }], readonly: [{
    type: Input
  }], required: [{
    type: Input
  }], clearable: [{
    type: Input
  }], showCharacterCount: [{
    type: Input
  }], min: [{
    type: Input
  }], max: [{
    type: Input
  }], step: [{
    type: Input
  }], maxlength: [{
    type: Input
  }], pattern: [{
    type: Input
  }], autocomplete: [{
    type: Input
  }], rows: [{
    type: Input
  }], options: [{
    type: Input
  }], inputChange: [{
    type: Output
  }], inputFocus: [{
    type: Output
  }], inputBlur: [{
    type: Output
  }], enterPressed: [{
    type: Output
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(FormInputComponent, { className: "FormInputComponent", filePath: "src/app/shared/components/form-input/form-input.component.ts", lineNumber: 123 });
})();

export {
  FormInputComponent
};
//# sourceMappingURL=chunk-ZMVY5VP4.mjs.map
