import './polyfills.server.mjs';
import {
  Injectable,
  delay,
  map,
  of,
  setClassMetadata,
  ɵɵdefineInjectable
} from "./chunk-IPMSWJNG.mjs";
import {
  __spreadProps,
  __spreadValues
} from "./chunk-5WKMABBB.mjs";

// src/app/shared/models/application.model.ts
var ApplicationStatus;
(function(ApplicationStatus2) {
  ApplicationStatus2["DEVELOPMENT"] = "Development";
  ApplicationStatus2["TESTING"] = "Testing";
  ApplicationStatus2["STAGING"] = "Staging";
  ApplicationStatus2["PRODUCTION"] = "Production";
  ApplicationStatus2["MAINTENANCE"] = "Maintenance";
  ApplicationStatus2["DEPRECATED"] = "Deprecated";
})(ApplicationStatus || (ApplicationStatus = {}));
var ApplicationCriticality;
(function(ApplicationCriticality2) {
  ApplicationCriticality2["LOW"] = "Low";
  ApplicationCriticality2["MEDIUM"] = "Medium";
  ApplicationCriticality2["HIGH"] = "High";
  ApplicationCriticality2["CRITICAL"] = "Critical";
})(ApplicationCriticality || (ApplicationCriticality = {}));
var LifecycleStatus;
(function(LifecycleStatus2) {
  LifecycleStatus2["PLANNING"] = "planning";
  LifecycleStatus2["DEVELOPMENT"] = "development";
  LifecycleStatus2["TESTING"] = "testing";
  LifecycleStatus2["STAGING"] = "staging";
  LifecycleStatus2["PRODUCTION"] = "production";
  LifecycleStatus2["MAINTENANCE"] = "maintenance";
  LifecycleStatus2["DEPRECATED"] = "deprecated";
  LifecycleStatus2["RETIRED"] = "retired";
})(LifecycleStatus || (LifecycleStatus = {}));
var BusinessCriticality;
(function(BusinessCriticality2) {
  BusinessCriticality2["LOW"] = "low";
  BusinessCriticality2["MEDIUM"] = "medium";
  BusinessCriticality2["HIGH"] = "high";
  BusinessCriticality2["CRITICAL"] = "critical";
})(BusinessCriticality || (BusinessCriticality = {}));
var DependencyType;
(function(DependencyType2) {
  DependencyType2["LIBRARY"] = "library";
  DependencyType2["FRAMEWORK"] = "framework";
  DependencyType2["SERVICE"] = "service";
  DependencyType2["DATABASE"] = "database";
  DependencyType2["API"] = "api";
  DependencyType2["TOOL"] = "tool";
  DependencyType2["INFRASTRUCTURE"] = "infrastructure";
})(DependencyType || (DependencyType = {}));
var DependencyCriticality;
(function(DependencyCriticality2) {
  DependencyCriticality2["LOW"] = "low";
  DependencyCriticality2["MEDIUM"] = "medium";
  DependencyCriticality2["HIGH"] = "high";
  DependencyCriticality2["CRITICAL"] = "critical";
})(DependencyCriticality || (DependencyCriticality = {}));
var DependencyStatus;
(function(DependencyStatus2) {
  DependencyStatus2["ACTIVE"] = "active";
  DependencyStatus2["DEPRECATED"] = "deprecated";
  DependencyStatus2["END_OF_LIFE"] = "end_of_life";
  DependencyStatus2["SECURITY_RISK"] = "security_risk";
  DependencyStatus2["UPDATE_REQUIRED"] = "update_required";
})(DependencyStatus || (DependencyStatus = {}));
var TechStackCategory;
(function(TechStackCategory2) {
  TechStackCategory2["FRONTEND"] = "frontend";
  TechStackCategory2["BACKEND"] = "backend";
  TechStackCategory2["DATABASE"] = "database";
  TechStackCategory2["INFRASTRUCTURE"] = "infrastructure";
  TechStackCategory2["MONITORING"] = "monitoring";
  TechStackCategory2["SECURITY"] = "security";
  TechStackCategory2["TESTING"] = "testing";
  TechStackCategory2["DEPLOYMENT"] = "deployment";
  TechStackCategory2["COMMUNICATION"] = "communication";
})(TechStackCategory || (TechStackCategory = {}));
var SupportLevel;
(function(SupportLevel2) {
  SupportLevel2["FULL"] = "full";
  SupportLevel2["LIMITED"] = "limited";
  SupportLevel2["COMMUNITY"] = "community";
  SupportLevel2["DEPRECATED"] = "deprecated";
  SupportLevel2["NONE"] = "none";
})(SupportLevel || (SupportLevel = {}));
var StakeholderRole;
(function(StakeholderRole2) {
  StakeholderRole2["PRODUCT_OWNER"] = "product_owner";
  StakeholderRole2["TECH_LEAD"] = "tech_lead";
  StakeholderRole2["DEVELOPER"] = "developer";
  StakeholderRole2["DEVOPS_ENGINEER"] = "devops_engineer";
  StakeholderRole2["SECURITY_OFFICER"] = "security_officer";
  StakeholderRole2["BUSINESS_ANALYST"] = "business_analyst";
  StakeholderRole2["PROJECT_MANAGER"] = "project_manager";
  StakeholderRole2["ARCHITECT"] = "architect";
  StakeholderRole2["QA_ENGINEER"] = "qa_engineer";
  StakeholderRole2["SUPPORT_ENGINEER"] = "support_engineer";
})(StakeholderRole || (StakeholderRole = {}));
var ContactPreference;
(function(ContactPreference2) {
  ContactPreference2["EMAIL"] = "email";
  ContactPreference2["SLACK"] = "slack";
  ContactPreference2["PHONE"] = "phone";
  ContactPreference2["TEAMS"] = "teams";
})(ContactPreference || (ContactPreference = {}));
var DocumentationType;
(function(DocumentationType2) {
  DocumentationType2["TECHNICAL_SPEC"] = "technical_spec";
  DocumentationType2["API_DOCUMENTATION"] = "api_documentation";
  DocumentationType2["USER_MANUAL"] = "user_manual";
  DocumentationType2["DEPLOYMENT_GUIDE"] = "deployment_guide";
  DocumentationType2["ARCHITECTURE_DIAGRAM"] = "architecture_diagram";
  DocumentationType2["SECURITY_POLICY"] = "security_policy";
  DocumentationType2["COMPLIANCE_REPORT"] = "compliance_report";
  DocumentationType2["RUNBOOK"] = "runbook";
  DocumentationType2["TROUBLESHOOTING"] = "troubleshooting";
  DocumentationType2["CHANGELOG"] = "changelog";
})(DocumentationType || (DocumentationType = {}));
var AssessmentType;
(function(AssessmentType2) {
  AssessmentType2["SECURITY_AUDIT"] = "security_audit";
  AssessmentType2["PENETRATION_TEST"] = "penetration_test";
  AssessmentType2["CODE_REVIEW"] = "code_review";
  AssessmentType2["COMPLIANCE_CHECK"] = "compliance_check";
  AssessmentType2["VULNERABILITY_SCAN"] = "vulnerability_scan";
  AssessmentType2["RISK_ASSESSMENT"] = "risk_assessment";
})(AssessmentType || (AssessmentType = {}));
var AssessmentStatus;
(function(AssessmentStatus2) {
  AssessmentStatus2["SCHEDULED"] = "scheduled";
  AssessmentStatus2["IN_PROGRESS"] = "in_progress";
  AssessmentStatus2["COMPLETED"] = "completed";
  AssessmentStatus2["OVERDUE"] = "overdue";
  AssessmentStatus2["CANCELLED"] = "cancelled";
})(AssessmentStatus || (AssessmentStatus = {}));
var VulnerabilitySeverity;
(function(VulnerabilitySeverity2) {
  VulnerabilitySeverity2["CRITICAL"] = "critical";
  VulnerabilitySeverity2["HIGH"] = "high";
  VulnerabilitySeverity2["MEDIUM"] = "medium";
  VulnerabilitySeverity2["LOW"] = "low";
  VulnerabilitySeverity2["INFO"] = "info";
})(VulnerabilitySeverity || (VulnerabilitySeverity = {}));
var VulnerabilityStatus;
(function(VulnerabilityStatus2) {
  VulnerabilityStatus2["OPEN"] = "open";
  VulnerabilityStatus2["IN_PROGRESS"] = "in_progress";
  VulnerabilityStatus2["RESOLVED"] = "resolved";
  VulnerabilityStatus2["ACCEPTED_RISK"] = "accepted_risk";
  VulnerabilityStatus2["FALSE_POSITIVE"] = "false_positive";
})(VulnerabilityStatus || (VulnerabilityStatus = {}));

// src/app/modules/applications/applications.service.ts
var ApplicationsService = class _ApplicationsService {
  constructor() {
  }
  /**
   * Get applications with filtering and pagination
   */
  getApplications(page = 1, pageSize = 12, filter = {}) {
    return this.getMockApplications().pipe(delay(500), map((applications) => {
      let filteredApps = this.applyFilters(applications, filter);
      const total = filteredApps.length;
      const totalPages = Math.ceil(total / pageSize);
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedApps = filteredApps.slice(startIndex, endIndex);
      return {
        applications: paginatedApps,
        total,
        page,
        pageSize,
        totalPages
      };
    }));
  }
  /**
   * Get single application by ID
   */
  getApplication(id) {
    return this.getMockApplications().pipe(delay(300), map((applications) => applications.find((app) => app.id === id) || null));
  }
  /**
   * Create new application
   */
  createApplication(application) {
    const newApp = {
      id: Date.now(),
      // Mock ID generation
      name: application.name || "",
      description: application.description || "",
      owner: application.owner || "",
      department: application.department || "",
      status: application.status || ApplicationStatus.DEVELOPMENT,
      criticality: application.criticality || ApplicationCriticality.MEDIUM,
      version: application.version || "1.0.0",
      lastUpdated: /* @__PURE__ */ new Date(),
      createdDate: /* @__PURE__ */ new Date(),
      url: application.url || "",
      repository: application.repository || "",
      documentation: application.documentation || "",
      healthScore: 85,
      securityScore: 80,
      performanceScore: 90,
      tags: application.tags || [],
      techStack: application.techStack || [],
      dependencies: application.dependencies || [],
      vulnerabilities: application.vulnerabilities || [],
      documents: application.documents || []
    };
    return of(newApp).pipe(delay(1e3));
  }
  /**
   * Update existing application
   */
  updateApplication(id, updates) {
    return this.getApplication(id).pipe(delay(800), map((app) => {
      if (!app) {
        throw new Error("Application not found");
      }
      return __spreadProps(__spreadValues(__spreadValues({}, app), updates), {
        lastUpdated: /* @__PURE__ */ new Date()
      });
    }));
  }
  /**
   * Delete application
   */
  deleteApplication(id) {
    return of(true).pipe(delay(500));
  }
  /**
   * Get application statistics
   */
  getApplicationStats() {
    return this.getMockApplications().pipe(delay(300), map((applications) => {
      const stats = {
        total: applications.length,
        byStatus: this.groupBy(applications, "status"),
        byCriticality: this.groupBy(applications, "criticality"),
        byDepartment: this.groupBy(applications, "department"),
        averageHealthScore: this.calculateAverage(applications, "healthScore"),
        averageSecurityScore: this.calculateAverage(applications, "securityScore"),
        averagePerformanceScore: this.calculateAverage(applications, "performanceScore")
      };
      return stats;
    }));
  }
  /**
   * Get filter options for dropdowns
   */
  getFilterOptions() {
    return this.getMockApplications().pipe(delay(200), map((applications) => ({
      owners: [...new Set(applications.map((app) => app.owner))].sort(),
      departments: [...new Set(applications.map((app) => app.department))].sort(),
      statuses: Object.values(ApplicationStatus),
      criticalities: Object.values(ApplicationCriticality)
    })));
  }
  /**
   * Search applications by name or description
   */
  searchApplications(query) {
    return this.getMockApplications().pipe(delay(300), map(
      (applications) => applications.filter((app) => app.name.toLowerCase().includes(query.toLowerCase()) || app.description.toLowerCase().includes(query.toLowerCase())).slice(0, 10)
      // Limit to 10 results for autocomplete
    ));
  }
  applyFilters(applications, filter) {
    let filtered = [...applications];
    if (filter.search) {
      const searchTerm = filter.search.toLowerCase();
      filtered = filtered.filter((app) => app.name.toLowerCase().includes(searchTerm) || app.description.toLowerCase().includes(searchTerm) || app.owner.toLowerCase().includes(searchTerm));
    }
    if (filter.status && filter.status.length > 0) {
      filtered = filtered.filter((app) => filter.status.includes(app.status));
    }
    if (filter.criticality && filter.criticality.length > 0) {
      filtered = filtered.filter((app) => filter.criticality.includes(app.criticality));
    }
    if (filter.owner && filter.owner.length > 0) {
      filtered = filtered.filter((app) => filter.owner.includes(app.owner));
    }
    if (filter.department && filter.department.length > 0) {
      filtered = filtered.filter((app) => filter.department.includes(app.department));
    }
    return filtered;
  }
  groupBy(array, key) {
    return array.reduce((groups, item) => {
      const value = item[key];
      groups[value] = (groups[value] || 0) + 1;
      return groups;
    }, {});
  }
  calculateAverage(array, key) {
    const sum = array.reduce((total, item) => total + (item[key] || 0), 0);
    return Math.round(sum / array.length * 10) / 10;
  }
  getMockApplications() {
    const mockApps = [
      {
        id: 1,
        name: "E-commerce Platform",
        description: "Main customer-facing e-commerce application with shopping cart, payment processing, and order management.",
        owner: "John Doe",
        department: "Engineering",
        status: ApplicationStatus.PRODUCTION,
        criticality: ApplicationCriticality.CRITICAL,
        version: "2.1.0",
        lastUpdated: /* @__PURE__ */ new Date("2024-01-15"),
        createdDate: /* @__PURE__ */ new Date("2022-03-10"),
        url: "https://shop.company.com",
        repository: "https://github.com/company/ecommerce",
        documentation: "https://docs.company.com/ecommerce",
        healthScore: 92,
        securityScore: 88,
        performanceScore: 95,
        tags: ["customer-facing", "revenue-critical", "high-traffic"],
        techStack: [
          { name: "Angular", version: "16.2.0", category: "Frontend" },
          { name: "Node.js", version: "18.17.0", category: "Backend" },
          { name: "PostgreSQL", version: "15.0", category: "Database" },
          { name: "Redis", version: "7.0", category: "Cache" },
          { name: "Docker", version: "24.0", category: "Infrastructure" }
        ],
        dependencies: [
          {
            id: 1,
            applicationId: 1,
            name: "Angular",
            type: DependencyType.FRAMEWORK,
            version: "16.2.0",
            isInternal: false,
            description: "Frontend framework for building the user interface",
            criticality: DependencyCriticality.HIGH,
            status: DependencyStatus.ACTIVE,
            lastUpdated: /* @__PURE__ */ new Date("2024-01-10"),
            maintainer: "Google",
            repository: "https://github.com/angular/angular"
          },
          {
            id: 2,
            applicationId: 1,
            name: "Express.js",
            type: DependencyType.FRAMEWORK,
            version: "4.18.2",
            isInternal: false,
            description: "Backend web framework for Node.js",
            criticality: DependencyCriticality.CRITICAL,
            status: DependencyStatus.ACTIVE,
            lastUpdated: /* @__PURE__ */ new Date("2024-01-05"),
            maintainer: "Express Team",
            repository: "https://github.com/expressjs/express"
          },
          {
            id: 3,
            applicationId: 1,
            name: "Payment Service",
            type: DependencyType.SERVICE,
            version: "1.5.3",
            isInternal: true,
            description: "Internal payment processing service",
            criticality: DependencyCriticality.CRITICAL,
            status: DependencyStatus.ACTIVE,
            lastUpdated: /* @__PURE__ */ new Date("2024-01-14"),
            maintainer: "Finance Team"
          }
        ],
        stakeholders: [
          {
            id: 1,
            applicationId: 1,
            name: "John Doe",
            email: "<EMAIL>",
            role: StakeholderRole.PRODUCT_OWNER,
            department: "Engineering",
            responsibility: "Overall product strategy and technical direction",
            isPrimary: true,
            contactPreference: ContactPreference.EMAIL,
            phone: "******-0123"
          },
          {
            id: 2,
            applicationId: 1,
            name: "Sarah Wilson",
            email: "<EMAIL>",
            role: StakeholderRole.DEVELOPER,
            department: "Engineering",
            responsibility: "Frontend development and user experience",
            isPrimary: false,
            contactPreference: ContactPreference.SLACK,
            slackHandle: "@sarah.wilson"
          },
          {
            id: 3,
            applicationId: 1,
            name: "Mike Chen",
            email: "<EMAIL>",
            role: StakeholderRole.BUSINESS_ANALYST,
            department: "Product",
            responsibility: "Business requirements and user acceptance testing",
            isPrimary: false,
            contactPreference: ContactPreference.EMAIL
          }
        ],
        documents: [
          {
            id: 1,
            applicationId: 1,
            title: "API Documentation",
            type: DocumentationType.API_DOCUMENTATION,
            description: "Complete API documentation for all endpoints",
            url: "https://docs.company.com/ecommerce/api",
            version: "2.1.0",
            isPublic: false,
            uploadedAt: /* @__PURE__ */ new Date("2024-01-10"),
            uploadedBy: "John Doe"
          },
          {
            id: 2,
            applicationId: 1,
            title: "User Manual",
            type: DocumentationType.USER_MANUAL,
            description: "End-user guide for the e-commerce platform",
            fileName: "user-manual-v2.1.pdf",
            fileSize: 2048576,
            mimeType: "application/pdf",
            version: "2.1.0",
            isPublic: true,
            uploadedAt: /* @__PURE__ */ new Date("2024-01-12"),
            uploadedBy: "Sarah Wilson"
          },
          {
            id: 3,
            applicationId: 1,
            title: "Architecture Overview",
            type: DocumentationType.TECHNICAL_SPEC,
            description: "High-level system architecture and design decisions",
            url: "https://docs.company.com/ecommerce/architecture",
            version: "2.0.0",
            isPublic: false,
            uploadedAt: /* @__PURE__ */ new Date("2023-12-15"),
            uploadedBy: "John Doe"
          }
        ],
        vulnerabilities: [
          {
            id: 1,
            applicationId: 1,
            cveId: "CVE-2024-0001",
            title: "SQL Injection in User Search",
            description: "Potential SQL injection vulnerability in the user search functionality that could allow unauthorized data access.",
            severity: VulnerabilitySeverity.HIGH,
            cvssScore: 7.5,
            status: VulnerabilityStatus.IN_PROGRESS,
            discoveredAt: /* @__PURE__ */ new Date("2024-01-08"),
            discoveredBy: "Security Team",
            affectedComponent: "User Search API",
            patchAvailable: true,
            patchVersion: "2.1.1"
          },
          {
            id: 2,
            applicationId: 1,
            title: "Outdated Dependencies",
            description: "Several npm packages are using outdated versions with known security vulnerabilities.",
            severity: VulnerabilitySeverity.MEDIUM,
            cvssScore: 5.3,
            status: VulnerabilityStatus.OPEN,
            discoveredAt: /* @__PURE__ */ new Date("2024-01-12"),
            discoveredBy: "Automated Security Scan",
            affectedComponent: "Frontend Dependencies",
            patchAvailable: true
          },
          {
            id: 3,
            applicationId: 1,
            title: "Cross-Site Scripting (XSS)",
            description: "Potential XSS vulnerability in product review comments.",
            severity: VulnerabilitySeverity.CRITICAL,
            cvssScore: 9.1,
            status: VulnerabilityStatus.RESOLVED,
            discoveredAt: /* @__PURE__ */ new Date("2023-12-20"),
            discoveredBy: "Penetration Testing",
            resolvedAt: /* @__PURE__ */ new Date("2024-01-05"),
            resolvedBy: "Sarah Wilson",
            affectedComponent: "Product Reviews",
            patchAvailable: false
          }
        ],
        securityAssessments: []
      },
      {
        id: 2,
        name: "User Management System",
        description: "Centralized user authentication and authorization service for all company applications.",
        owner: "Jane Smith",
        department: "Security",
        status: ApplicationStatus.PRODUCTION,
        criticality: ApplicationCriticality.HIGH,
        version: "1.8.2",
        lastUpdated: /* @__PURE__ */ new Date("2024-01-12"),
        createdDate: /* @__PURE__ */ new Date("2021-11-05"),
        url: "https://auth.company.com",
        repository: "https://github.com/company/auth-service",
        documentation: "https://docs.company.com/auth",
        healthScore: 96,
        securityScore: 98,
        performanceScore: 89,
        tags: ["security", "authentication", "microservice"],
        techStack: [
          { name: "Node.js", version: "18.17.0", category: "Backend" },
          { name: "PostgreSQL", version: "15.0", category: "Database" },
          { name: "Redis", version: "7.0", category: "Cache" }
        ],
        dependencies: [],
        vulnerabilities: [],
        documents: []
      },
      {
        id: 3,
        name: "Analytics Dashboard",
        description: "Business intelligence dashboard providing insights into sales, user behavior, and system performance.",
        owner: "Mike Johnson",
        department: "Product",
        status: ApplicationStatus.PRODUCTION,
        criticality: ApplicationCriticality.MEDIUM,
        version: "3.2.1",
        lastUpdated: /* @__PURE__ */ new Date("2024-01-10"),
        createdDate: /* @__PURE__ */ new Date("2022-08-15"),
        url: "https://analytics.company.com",
        repository: "https://github.com/company/analytics",
        documentation: "https://docs.company.com/analytics",
        healthScore: 87,
        securityScore: 85,
        performanceScore: 92,
        tags: ["analytics", "business-intelligence", "reporting"],
        techStack: [
          { name: "React", version: "18.2.0", category: "Frontend" },
          { name: "Python", version: "3.11", category: "Backend" },
          { name: "MongoDB", version: "6.0", category: "Database" }
        ],
        dependencies: [],
        vulnerabilities: [],
        documents: []
      },
      {
        id: 4,
        name: "Payment Service",
        description: "Microservice handling payment processing, refunds, and financial transaction management.",
        owner: "Sarah Wilson",
        department: "Finance",
        status: ApplicationStatus.PRODUCTION,
        criticality: ApplicationCriticality.CRITICAL,
        version: "1.5.3",
        lastUpdated: /* @__PURE__ */ new Date("2024-01-14"),
        createdDate: /* @__PURE__ */ new Date("2022-01-20"),
        url: "https://payments.company.com",
        repository: "https://github.com/company/payments",
        documentation: "https://docs.company.com/payments",
        healthScore: 94,
        securityScore: 96,
        performanceScore: 88,
        tags: ["payments", "financial", "pci-compliant"],
        techStack: [
          { name: "Java", version: "17", category: "Backend" },
          { name: "Spring Boot", version: "3.0", category: "Framework" },
          { name: "PostgreSQL", version: "15.0", category: "Database" }
        ],
        dependencies: [],
        vulnerabilities: [],
        documents: []
      },
      {
        id: 5,
        name: "Notification Service",
        description: "Service responsible for sending emails, SMS, and push notifications to users.",
        owner: "David Brown",
        department: "Engineering",
        status: ApplicationStatus.DEVELOPMENT,
        criticality: ApplicationCriticality.MEDIUM,
        version: "0.8.0",
        lastUpdated: /* @__PURE__ */ new Date("2024-01-16"),
        createdDate: /* @__PURE__ */ new Date("2023-09-01"),
        url: "https://notifications.company.com",
        repository: "https://github.com/company/notifications",
        documentation: "https://docs.company.com/notifications",
        healthScore: 78,
        securityScore: 82,
        performanceScore: 85,
        tags: ["notifications", "messaging", "microservice"],
        techStack: [
          { name: "Node.js", version: "18.17.0", category: "Backend" },
          { name: "RabbitMQ", version: "3.12", category: "Message Queue" },
          { name: "Redis", version: "7.0", category: "Cache" }
        ],
        dependencies: [],
        vulnerabilities: [],
        documents: []
      },
      {
        id: 6,
        name: "Inventory Management",
        description: "System for tracking product inventory, stock levels, and warehouse management.",
        owner: "Lisa Davis",
        department: "Operations",
        status: ApplicationStatus.TESTING,
        criticality: ApplicationCriticality.HIGH,
        version: "2.0.0-beta",
        lastUpdated: /* @__PURE__ */ new Date("2024-01-13"),
        createdDate: /* @__PURE__ */ new Date("2023-05-10"),
        url: "https://inventory.company.com",
        repository: "https://github.com/company/inventory",
        documentation: "https://docs.company.com/inventory",
        healthScore: 83,
        securityScore: 87,
        performanceScore: 91,
        tags: ["inventory", "warehouse", "supply-chain"],
        techStack: [
          { name: "Vue.js", version: "3.3.0", category: "Frontend" },
          { name: ".NET Core", version: "7.0", category: "Backend" },
          { name: "MySQL", version: "8.0", category: "Database" }
        ],
        dependencies: [],
        vulnerabilities: [],
        documents: []
      },
      {
        id: 7,
        name: "Customer Support Portal",
        description: "Self-service portal for customers to submit tickets, track issues, and access knowledge base.",
        owner: "Tom Anderson",
        department: "Customer Success",
        status: ApplicationStatus.PRODUCTION,
        criticality: ApplicationCriticality.MEDIUM,
        version: "1.9.1",
        lastUpdated: /* @__PURE__ */ new Date("2024-01-11"),
        createdDate: /* @__PURE__ */ new Date("2022-06-15"),
        url: "https://support.company.com",
        repository: "https://github.com/company/support-portal",
        documentation: "https://docs.company.com/support",
        healthScore: 89,
        securityScore: 91,
        performanceScore: 86,
        tags: ["customer-support", "self-service", "knowledge-base"],
        techStack: [
          { name: "Angular", version: "16.2.0", category: "Frontend" },
          { name: "PHP", version: "8.2", category: "Backend" },
          { name: "MySQL", version: "8.0", category: "Database" }
        ],
        dependencies: [],
        vulnerabilities: [],
        documents: []
      },
      {
        id: 8,
        name: "Mobile API Gateway",
        description: "API gateway providing unified access to backend services for mobile applications.",
        owner: "Emma Thompson",
        department: "Mobile",
        status: ApplicationStatus.PRODUCTION,
        criticality: ApplicationCriticality.HIGH,
        version: "2.3.0",
        lastUpdated: /* @__PURE__ */ new Date("2024-01-09"),
        createdDate: /* @__PURE__ */ new Date("2022-11-20"),
        url: "https://api-mobile.company.com",
        repository: "https://github.com/company/mobile-gateway",
        documentation: "https://docs.company.com/mobile-api",
        healthScore: 91,
        securityScore: 93,
        performanceScore: 94,
        tags: ["api-gateway", "mobile", "microservices"],
        techStack: [
          { name: "Kong", version: "3.4", category: "Infrastructure" },
          { name: "Lua", version: "5.4", category: "Backend" },
          { name: "PostgreSQL", version: "15.0", category: "Database" }
        ],
        dependencies: [],
        vulnerabilities: [],
        documents: []
      }
    ];
    return of(mockApps);
  }
  static \u0275fac = function ApplicationsService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ApplicationsService)();
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _ApplicationsService, factory: _ApplicationsService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ApplicationsService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [], null);
})();

export {
  ApplicationsService
};
//# sourceMappingURL=chunk-BBFG7BOY.mjs.map
