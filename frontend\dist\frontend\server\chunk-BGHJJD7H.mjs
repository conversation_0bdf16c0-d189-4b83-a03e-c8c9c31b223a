import './polyfills.server.mjs';
import {
  CardComponent
} from "./chunk-UJ4MSIYR.mjs";
import {
  CommonModule,
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵdefineComponent,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵtext
} from "./chunk-IPMSWJNG.mjs";
import "./chunk-5WKMABBB.mjs";

// src/app/modules/security/vulnerabilities/vulnerabilities.component.ts
var VulnerabilitiesComponent = class _VulnerabilitiesComponent {
  static \u0275fac = function VulnerabilitiesComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _VulnerabilitiesComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _VulnerabilitiesComponent, selectors: [["app-vulnerabilities"]], decls: 7, vars: 0, consts: [[1, "vulnerabilities-page"], ["title", "Vulnerability Management", "subtitle", "Track and manage security vulnerabilities"], [1, "placeholder"]], template: function VulnerabilitiesComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "h1");
      \u0275\u0275text(2, "Vulnerabilities");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(3, "app-card", 1)(4, "div", 2)(5, "p");
      \u0275\u0275text(6, "Vulnerabilities module coming soon...");
      \u0275\u0275elementEnd()()()();
    }
  }, dependencies: [CommonModule, CardComponent], styles: ["\n\n.vulnerabilities-page[_ngcontent-%COMP%] {\n  min-height: 100%;\n}\n.placeholder[_ngcontent-%COMP%] {\n  padding: var(--spacing-xl);\n  text-align: center;\n  color: var(--secondary-600);\n}\n/*# sourceMappingURL=vulnerabilities.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(VulnerabilitiesComponent, [{
    type: Component,
    args: [{ selector: "app-vulnerabilities", standalone: true, imports: [CommonModule, CardComponent], template: `
    <div class="vulnerabilities-page">
      <h1>Vulnerabilities</h1>
      <app-card title="Vulnerability Management" subtitle="Track and manage security vulnerabilities">
        <div class="placeholder">
          <p>Vulnerabilities module coming soon...</p>
        </div>
      </app-card>
    </div>
  `, styles: ["/* angular:styles/component:scss;355597052b5646d477e2dd69b282fc54f956c473f2577c97b81c5b412268b38c;C:/Users/<USER>/Documents/VibeCoding/AppCatalog/app-catalog/frontend/src/app/modules/security/vulnerabilities/vulnerabilities.component.ts */\n.vulnerabilities-page {\n  min-height: 100%;\n}\n.placeholder {\n  padding: var(--spacing-xl);\n  text-align: center;\n  color: var(--secondary-600);\n}\n/*# sourceMappingURL=vulnerabilities.component.css.map */\n"] }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(VulnerabilitiesComponent, { className: "VulnerabilitiesComponent", filePath: "src/app/modules/security/vulnerabilities/vulnerabilities.component.ts", lineNumber: 30 });
})();
export {
  VulnerabilitiesComponent
};
//# sourceMappingURL=chunk-BGHJJD7H.mjs.map
