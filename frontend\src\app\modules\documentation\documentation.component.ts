import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ReactiveFormsModule, FormBuilder } from '@angular/forms';
import { CardComponent } from '../../shared/components/card/card.component';
import { ButtonComponent } from '../../shared/components/button/button.component';
import { TableComponent, TableColumn, TableAction } from '../../shared/components/table/table.component';
import { ChartComponent } from '../../shared/components/chart/chart.component';
import { SharedDocumentationModalComponent, SharedDocumentationFormData } from '../../shared/components/modals/shared-documentation-modal/shared-documentation-modal.component';

interface DocumentationItem {
  id: number;
  title: string;
  type: string;
  category: string;
  status: string;
  application: string;
  author: string;
  createdDate: Date;
  lastModified: Date;
  version: string;
  url?: string;
  description: string;
  tags: string[];
}

@Component({
  selector: 'app-documentation',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    CardComponent,
    ButtonComponent,
    TableComponent,
    ChartComponent,
    SharedDocumentationModalComponent
  ],
  template: `
    <div class="documentation-page">
      <div class="page-content">
        <div class="page-header">
          <div class="header-content">
            <h1>Documentation</h1>
            <p class="page-subtitle">Manage project documentation and resources</p>
          </div>
          <div class="header-actions">
            <app-button
              variant="primary"
              leftIcon="M12 4v16m8-8H4"
              (clicked)="openAddModal()"
            >
              Add Document
            </app-button>
          </div>
        </div>

      <!-- Statistics Cards -->
      <div class="stats-grid">
        <app-card title="Total Documents">
          <div class="stat-content">
            <div class="stat-number">{{ stats.total }}</div>
            <div class="stat-change">Across {{ stats.categories }} categories</div>
          </div>
        </app-card>

        <app-card title="Recent Updates">
          <div class="stat-content">
            <div class="stat-number recent">{{ stats.recentUpdates }}</div>
            <div class="stat-change">Updated this week</div>
          </div>
        </app-card>

        <app-card title="Outdated Docs">
          <div class="stat-content">
            <div class="stat-number warning">{{ stats.outdated }}</div>
            <div class="stat-change">Need review</div>
          </div>
        </app-card>

        <app-card title="External Links">
          <div class="stat-content">
            <div class="stat-number">{{ stats.externalLinks }}</div>
            <div class="stat-change">External resources</div>
          </div>
        </app-card>
      </div>

      <!-- Charts Section -->
      <div class="charts-section">
        <app-card title="Documents by Category" subtitle="Distribution across categories">
          <app-chart
            type="doughnut"
            [data]="categoryChartData"
            [options]="chartOptions"
            height="300px"
          ></app-chart>
        </app-card>

        <app-card title="Document Types" subtitle="Distribution by document type">
          <app-chart
            type="bar"
            [data]="typeChartData"
            [options]="chartOptions"
            height="300px"
          ></app-chart>
        </app-card>
      </div>

      <!-- Documentation Table -->
      <app-card title="Documentation Library" subtitle="All project documentation">
        <div class="table-controls">
          <div class="search-controls">
            <input
              type="text"
              placeholder="Search documentation..."
              class="search-input"
              (input)="onSearchInput($event)"
            >
            <select class="filter-select" (change)="onFilterChanged('category', $event)">
              <option value="">All Categories</option>
              <option value="api">API Documentation</option>
              <option value="user_guide">User Guide</option>
              <option value="technical">Technical Specs</option>
              <option value="architecture">Architecture</option>
              <option value="deployment">Deployment</option>
              <option value="troubleshooting">Troubleshooting</option>
            </select>
            <select class="filter-select" (change)="onFilterChanged('type', $event)">
              <option value="">All Types</option>
              <option value="document">Document</option>
              <option value="link">External Link</option>
              <option value="wiki">Wiki Page</option>
              <option value="video">Video</option>
              <option value="tutorial">Tutorial</option>
            </select>
            <select class="filter-select" (change)="onFilterChanged('status', $event)">
              <option value="">All Statuses</option>
              <option value="current">Current</option>
              <option value="outdated">Outdated</option>
              <option value="draft">Draft</option>
              <option value="archived">Archived</option>
            </select>
          </div>
        </div>

        <app-table
          [columns]="tableColumns"
          [data]="filteredDocumentation"
          [actions]="tableActions"
          [loading]="loading"
          [sortable]="true"
          (sortChanged)="onSortChanged($event)"
        ></app-table>
      </app-card>

      <!-- Documentation Modal -->
      <app-shared-documentation-modal
        [isOpen]="showDocumentationModal"
        [editMode]="editMode"
        [initialData]="editData"
        [loading]="modalLoading"
        [showApplicationSelection]="true"
        (closed)="closeModal()"
        (saved)="onDocumentationSaved($event)"
      ></app-shared-documentation-modal>
      </div>
    </div>
  `,
  styles: [`
    .documentation-page {
      min-height: 100%;
    }

    .page-content {
      padding: var(--spacing-xl);
    }

    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: var(--spacing-xl);
    }

    .header-content h1 {
      margin: 0 0 var(--spacing-sm) 0;
      font-size: var(--text-3xl);
      font-weight: 700;
      color: var(--secondary-900);
    }

    .page-subtitle {
      margin: 0;
      font-size: var(--text-lg);
      color: var(--secondary-600);
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: var(--spacing-lg);
      margin-bottom: var(--spacing-xl);
    }

    .stat-content {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-sm);
      padding: var(--spacing-lg);
    }

    .stat-number {
      font-size: var(--text-3xl);
      font-weight: 700;
      color: var(--secondary-900);

      &.recent {
        color: var(--primary-600);
      }

      &.warning {
        color: var(--warning-600);
      }
    }

    .stat-change {
      font-size: var(--text-sm);
      color: var(--secondary-600);
    }

    .charts-section {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: var(--spacing-lg);
      margin-bottom: var(--spacing-xl);
    }

    .table-controls {
      margin-bottom: var(--spacing-lg);
    }

    .search-controls {
      display: flex;
      gap: var(--spacing-md);
      align-items: center;
      flex-wrap: wrap;
    }

    .search-input,
    .filter-select {
      height: 40px;
      padding: 0 var(--spacing-md);
      border: 1px solid var(--secondary-300);
      border-radius: var(--radius-md);
      background: white;
      font-size: var(--text-sm);
      color: var(--secondary-800);
      transition: all 0.2s ease;

      &:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px var(--primary-100);
      }
    }

    .search-input {
      flex: 1;
      min-width: 200px;
    }

    .filter-select {
      min-width: 120px;
      cursor: pointer;
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
      background-position: right var(--spacing-sm) center;
      background-repeat: no-repeat;
      background-size: 16px;
      padding-right: 40px;
      appearance: none;
    }

    @media (max-width: 768px) {
      .page-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
      }

      .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
      }

      .charts-section {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
      }

      .search-controls {
        flex-direction: column;
        align-items: stretch;
      }

      .search-input,
      .filter-select {
        min-width: auto;
      }
    }
  `]
})
export class DocumentationComponent implements OnInit {
  documentation: DocumentationItem[] = [];
  filteredDocumentation: DocumentationItem[] = [];
  loading = false;

  // Modal state
  showDocumentationModal = false;
  editMode = false;
  editData: SharedDocumentationFormData | null = null;
  modalLoading = false;

  // Statistics
  stats = {
    total: 0,
    recentUpdates: 0,
    outdated: 0,
    categories: 0,
    externalLinks: 0
  };

  // Chart data
  categoryChartData: any = null;
  typeChartData: any = null;
  chartOptions = {
    responsive: true,
    maintainAspectRatio: false
  };

  // Table configuration
  tableColumns: TableColumn[] = [
    { key: 'title', label: 'Title', sortable: true },
    { key: 'type', label: 'Type', type: 'badge', sortable: true },
    { key: 'category', label: 'Category', type: 'badge', sortable: true },
    { key: 'status', label: 'Status', type: 'badge', sortable: true },
    { key: 'author', label: 'Author', sortable: true },
    { key: 'version', label: 'Version', sortable: true },
    { key: 'lastModified', label: 'Last Modified', type: 'date', sortable: true }
  ];

  tableActions: TableAction[] = [
    {
      label: 'View',
      icon: 'M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7Z',
      variant: 'ghost',
      action: (item) => this.viewDocument(item)
    },
    {
      label: 'Edit',
      icon: 'M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7',
      variant: 'ghost',
      action: (item) => this.editDocument(item)
    },
    {
      label: 'Delete',
      icon: 'M3 6h18m-2 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2',
      variant: 'error',
      action: (item) => this.deleteDocument(item)
    }
  ];

  constructor(private fb: FormBuilder) {}

  ngOnInit(): void {
    this.loadDocumentation();
  }

  private loadDocumentation(): void {
    this.loading = true;

    // Mock data - replace with actual API call
    setTimeout(() => {
      this.documentation = this.generateMockDocumentation();
      this.filteredDocumentation = [...this.documentation];
      this.updateStats();
      this.updateChartData();
      this.loading = false;
    }, 1000);
  }

  private generateMockDocumentation(): DocumentationItem[] {
    const types = ['document', 'link', 'wiki', 'video', 'tutorial'];
    const categories = ['api', 'user_guide', 'technical', 'architecture', 'deployment', 'troubleshooting'];
    const statuses = ['current', 'outdated', 'draft', 'archived'];
    const applications = ['App 1', 'App 2', 'App 3', 'App 4', 'App 5'];

    return Array.from({ length: 35 }, (_, i) => ({
      id: i + 1,
      title: `Documentation ${i + 1}`,
      type: types[Math.floor(Math.random() * types.length)],
      category: categories[Math.floor(Math.random() * categories.length)],
      status: statuses[Math.floor(Math.random() * statuses.length)],
      application: applications[Math.floor(Math.random() * applications.length)],
      author: `Author ${Math.floor(Math.random() * 10) + 1}`,
      createdDate: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000),
      lastModified: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
      version: `${Math.floor(Math.random() * 3) + 1}.${Math.floor(Math.random() * 10)}`,
      url: Math.random() > 0.5 ? `https://docs.example.com/doc-${i + 1}` : undefined,
      description: `Description for documentation ${i + 1}`,
      tags: [`tag${Math.floor(Math.random() * 5) + 1}`, `tag${Math.floor(Math.random() * 5) + 6}`]
    }));
  }

  private updateStats(): void {
    this.stats.total = this.documentation.length;
    this.stats.recentUpdates = this.documentation.filter(d =>
      new Date(d.lastModified).getTime() > Date.now() - 7 * 24 * 60 * 60 * 1000
    ).length;
    this.stats.outdated = this.documentation.filter(d => d.status === 'outdated').length;
    this.stats.categories = new Set(this.documentation.map(d => d.category)).size;
    this.stats.externalLinks = this.documentation.filter(d => d.type === 'link').length;
  }

  private updateChartData(): void {
    // Category distribution chart
    const categoryCount = this.documentation.reduce((acc, doc) => {
      acc[doc.category] = (acc[doc.category] || 0) + 1;
      return acc;
    }, {} as any);

    this.categoryChartData = {
      labels: Object.keys(categoryCount),
      datasets: [{
        data: Object.values(categoryCount),
        backgroundColor: [
          '#0ea5e9', '#22c55e', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'
        ]
      }]
    };

    // Type distribution chart
    const typeCount = this.documentation.reduce((acc, doc) => {
      acc[doc.type] = (acc[doc.type] || 0) + 1;
      return acc;
    }, {} as any);

    this.typeChartData = {
      labels: Object.keys(typeCount),
      datasets: [{
        label: 'Documents',
        data: Object.values(typeCount),
        backgroundColor: ['#22c55e', '#0ea5e9', '#f59e0b', '#ef4444', '#8b5cf6']
      }]
    };
  }

  openAddModal(): void {
    this.editMode = false;
    this.editData = null;
    this.showDocumentationModal = true;
  }

  closeModal(): void {
    this.showDocumentationModal = false;
    this.editMode = false;
    this.editData = null;
  }

  onDocumentationSaved(data: SharedDocumentationFormData): void {
    this.modalLoading = true;

    // Simulate API call
    setTimeout(() => {
      console.log('Documentation saved:', data);
      this.modalLoading = false;
      this.closeModal();

      // In a real app, you would refresh the documentation list here
      this.loadDocumentation();
    }, 1000);
  }

  viewDocument(item: DocumentationItem): void {
    if (item.url) {
      window.open(item.url, '_blank');
    } else {
      console.log('View document:', item);
    }
  }

  editDocument(item: DocumentationItem): void {
    // Implement edit functionality
    console.log('Edit document:', item);
  }

  deleteDocument(item: DocumentationItem): void {
    if (confirm(`Are you sure you want to delete ${item.title}?`)) {
      this.documentation = this.documentation.filter(d => d.id !== item.id);
      this.filteredDocumentation = this.filteredDocumentation.filter(d => d.id !== item.id);
      this.updateStats();
      this.updateChartData();
    }
  }

  onSearchInput(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.onSearchChanged(target.value);
  }

  onSearchChanged(searchTerm: string): void {
    this.applyFilters();
  }

  onFilterChanged(filterType: string, event: Event): void {
    const target = event.target as HTMLSelectElement;
    // Apply filter logic here
    this.applyFilters();
  }

  onSortChanged(sort: any): void {
    // Implement sorting logic
    console.log('Sort changed:', sort);
  }

  private applyFilters(): void {
    // Implement filtering logic
    this.filteredDocumentation = [...this.documentation];
  }
}
