import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { CardComponent } from '../../shared/components/card/card.component';
import { FormInputComponent } from '../../shared/components/form-input/form-input.component';
import { ButtonComponent } from '../../shared/components/button/button.component';

interface NotificationSettings {
  emailNotifications: boolean;
  pushNotifications: boolean;
  securityAlerts: boolean;
  dependencyUpdates: boolean;
  weeklyReports: boolean;
}

interface AppearanceSettings {
  theme: 'light' | 'dark' | 'auto';
  compactMode: boolean;
  sidebarCollapsed: boolean;
  language: string;
}

interface SystemSettings {
  autoSave: boolean;
  autoSaveInterval: number;
  sessionTimeout: number;
  enableAnalytics: boolean;
  enableTelemetry: boolean;
}

@Component({
  selector: 'app-settings',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, CardComponent, FormInputComponent, ButtonComponent],
  template: `
    <div class="settings-page">
      <div class="settings-header">
        <h1>Settings</h1>
        <p class="settings-subtitle">Configure your application preferences and system settings</p>
      </div>

      <div class="settings-content">
        <!-- User Profile Settings -->
        <app-card title="User Profile" subtitle="Manage your personal information">
          <form [formGroup]="profileForm" class="settings-form">
            <div class="form-grid">
              <app-form-input
                label="Full Name"
                placeholder="Enter your full name"
                formControlName="fullName"
                [required]="true"
              ></app-form-input>

              <app-form-input
                label="Email Address"
                placeholder="Enter your email"
                formControlName="email"
                type="email"
                [required]="true"
              ></app-form-input>

              <app-form-input
                label="Job Title"
                placeholder="Enter your job title"
                formControlName="jobTitle"
              ></app-form-input>

              <app-form-input
                label="Department"
                placeholder="Enter your department"
                formControlName="department"
              ></app-form-input>
            </div>

            <div class="form-actions">
              <app-button
                variant="primary"
                (clicked)="saveProfile()"
                [loading]="saving.profile"
              >
                Save Profile
              </app-button>
            </div>
          </form>
        </app-card>

        <!-- Notification Settings -->
        <app-card title="Notifications" subtitle="Configure how you receive notifications">
          <form [formGroup]="notificationForm" class="settings-form">
            <div class="checkbox-grid">
              <label class="checkbox-item">
                <input type="checkbox" formControlName="emailNotifications" class="checkbox-input">
                <div class="checkbox-content">
                  <span class="checkbox-title">Email Notifications</span>
                  <span class="checkbox-description">Receive notifications via email</span>
                </div>
              </label>

              <label class="checkbox-item">
                <input type="checkbox" formControlName="pushNotifications" class="checkbox-input">
                <div class="checkbox-content">
                  <span class="checkbox-title">Push Notifications</span>
                  <span class="checkbox-description">Receive browser push notifications</span>
                </div>
              </label>

              <label class="checkbox-item">
                <input type="checkbox" formControlName="securityAlerts" class="checkbox-input">
                <div class="checkbox-content">
                  <span class="checkbox-title">Security Alerts</span>
                  <span class="checkbox-description">Get notified about security vulnerabilities</span>
                </div>
              </label>

              <label class="checkbox-item">
                <input type="checkbox" formControlName="dependencyUpdates" class="checkbox-input">
                <div class="checkbox-content">
                  <span class="checkbox-title">Dependency Updates</span>
                  <span class="checkbox-description">Notifications for dependency updates</span>
                </div>
              </label>

              <label class="checkbox-item">
                <input type="checkbox" formControlName="weeklyReports" class="checkbox-input">
                <div class="checkbox-content">
                  <span class="checkbox-title">Weekly Reports</span>
                  <span class="checkbox-description">Receive weekly summary reports</span>
                </div>
              </label>
            </div>

            <div class="form-actions">
              <app-button
                variant="primary"
                (clicked)="saveNotifications()"
                [loading]="saving.notifications"
              >
                Save Notifications
              </app-button>
            </div>
          </form>
        </app-card>

        <!-- Appearance Settings -->
        <app-card title="Appearance" subtitle="Customize the look and feel of the application">
          <form [formGroup]="appearanceForm" class="settings-form">
            <div class="form-grid">
              <div class="form-group">
                <label class="form-label">Theme</label>
                <select formControlName="theme" class="form-select">
                  <option value="light">Light</option>
                  <option value="dark">Dark</option>
                  <option value="auto">Auto (System)</option>
                </select>
              </div>

              <div class="form-group">
                <label class="form-label">Language</label>
                <select formControlName="language" class="form-select">
                  <option value="en">English</option>
                  <option value="es">Spanish</option>
                  <option value="fr">French</option>
                  <option value="de">German</option>
                </select>
              </div>
            </div>

            <div class="checkbox-grid">
              <label class="checkbox-item">
                <input type="checkbox" formControlName="compactMode" class="checkbox-input">
                <div class="checkbox-content">
                  <span class="checkbox-title">Compact Mode</span>
                  <span class="checkbox-description">Use a more compact interface layout</span>
                </div>
              </label>

              <label class="checkbox-item">
                <input type="checkbox" formControlName="sidebarCollapsed" class="checkbox-input">
                <div class="checkbox-content">
                  <span class="checkbox-title">Collapsed Sidebar</span>
                  <span class="checkbox-description">Start with sidebar collapsed by default</span>
                </div>
              </label>
            </div>

            <div class="form-actions">
              <app-button
                variant="primary"
                (clicked)="saveAppearance()"
                [loading]="saving.appearance"
              >
                Save Appearance
              </app-button>
            </div>
          </form>
        </app-card>

        <!-- System Settings -->
        <app-card title="System" subtitle="Configure system behavior and performance">
          <form [formGroup]="systemForm" class="settings-form">
            <div class="form-grid">
              <app-form-input
                label="Auto-save Interval (minutes)"
                placeholder="5"
                formControlName="autoSaveInterval"
                type="number"
                [min]="1"
                [max]="60"
              ></app-form-input>

              <app-form-input
                label="Session Timeout (minutes)"
                placeholder="30"
                formControlName="sessionTimeout"
                type="number"
                [min]="5"
                [max]="480"
              ></app-form-input>
            </div>

            <div class="checkbox-grid">
              <label class="checkbox-item">
                <input type="checkbox" formControlName="autoSave" class="checkbox-input">
                <div class="checkbox-content">
                  <span class="checkbox-title">Auto-save</span>
                  <span class="checkbox-description">Automatically save changes periodically</span>
                </div>
              </label>

              <label class="checkbox-item">
                <input type="checkbox" formControlName="enableAnalytics" class="checkbox-input">
                <div class="checkbox-content">
                  <span class="checkbox-title">Analytics</span>
                  <span class="checkbox-description">Help improve the application by sharing usage data</span>
                </div>
              </label>

              <label class="checkbox-item">
                <input type="checkbox" formControlName="enableTelemetry" class="checkbox-input">
                <div class="checkbox-content">
                  <span class="checkbox-title">Telemetry</span>
                  <span class="checkbox-description">Share performance and error data</span>
                </div>
              </label>
            </div>

            <div class="form-actions">
              <app-button
                variant="primary"
                (clicked)="saveSystem()"
                [loading]="saving.system"
              >
                Save System Settings
              </app-button>
            </div>
          </form>
        </app-card>

        <!-- Data Management -->
        <app-card title="Data Management" subtitle="Manage your data and privacy settings">
          <div class="settings-form">
            <div class="data-actions">
              <div class="action-item">
                <div class="action-content">
                  <h3 class="action-title">Export Data</h3>
                  <p class="action-description">Download all your application data in JSON format</p>
                </div>
                <app-button
                  variant="secondary"
                  (clicked)="exportData()"
                  [loading]="exporting"
                >
                  Export
                </app-button>
              </div>

              <div class="action-item">
                <div class="action-content">
                  <h3 class="action-title">Clear Cache</h3>
                  <p class="action-description">Clear application cache and temporary data</p>
                </div>
                <app-button
                  variant="secondary"
                  (clicked)="clearCache()"
                  [loading]="clearing"
                >
                  Clear Cache
                </app-button>
              </div>

              <div class="action-item danger">
                <div class="action-content">
                  <h3 class="action-title">Reset Settings</h3>
                  <p class="action-description">Reset all settings to default values</p>
                </div>
                <app-button
                  variant="error"
                  (clicked)="resetSettings()"
                  [loading]="resetting"
                >
                  Reset All
                </app-button>
              </div>
            </div>
          </div>
        </app-card>
      </div>
    </div>
  `,
  styles: [`
    .settings-page {
      min-height: 100%;
    }

    .settings-header {
      margin-bottom: var(--spacing-xl);
    }

    .settings-header h1 {
      margin: 0 0 var(--spacing-sm) 0;
      font-size: var(--text-3xl);
      font-weight: 700;
      color: var(--secondary-900);
    }

    .settings-subtitle {
      margin: 0;
      font-size: var(--text-lg);
      color: var(--secondary-600);
    }

    .settings-content {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-xl);
    }

    .settings-form {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-lg);
    }

    .form-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: var(--spacing-lg);
    }

    .form-group {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-sm);
    }

    .form-label {
      font-size: var(--text-sm);
      font-weight: 500;
      color: var(--secondary-700);
    }

    .form-select {
      height: 40px;
      padding: 0 var(--spacing-md);
      border: 1px solid var(--secondary-300);
      border-radius: var(--radius-md);
      background: white;
      font-size: var(--text-sm);
      color: var(--secondary-800);
      cursor: pointer;
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
      background-position: right var(--spacing-sm) center;
      background-repeat: no-repeat;
      background-size: 16px;
      padding-right: 40px;
      appearance: none;
      transition: all 0.2s ease;

      &:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px var(--primary-100);
      }
    }

    .checkbox-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: var(--spacing-md);
    }

    .checkbox-item {
      display: flex;
      align-items: flex-start;
      gap: var(--spacing-sm);
      padding: var(--spacing-md);
      border: 1px solid var(--secondary-200);
      border-radius: var(--radius-md);
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        border-color: var(--primary-300);
        background: var(--primary-25);
      }

      &:has(.checkbox-input:checked) {
        border-color: var(--primary-500);
        background: var(--primary-50);
      }
    }

    .checkbox-input {
      margin: 0;
      margin-top: 2px;
      accent-color: var(--primary-500);
    }

    .checkbox-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: var(--spacing-xs);
    }

    .checkbox-title {
      font-size: var(--text-sm);
      font-weight: 500;
      color: var(--secondary-900);
    }

    .checkbox-description {
      font-size: var(--text-xs);
      color: var(--secondary-600);
      line-height: var(--leading-relaxed);
    }

    .form-actions {
      display: flex;
      justify-content: flex-end;
      padding-top: var(--spacing-md);
      border-top: 1px solid var(--secondary-200);
    }

    .data-actions {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-lg);
    }

    .action-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: var(--spacing-lg);
      border: 1px solid var(--secondary-200);
      border-radius: var(--radius-md);
      transition: all 0.2s ease;

      &:hover {
        border-color: var(--secondary-300);
        background: var(--secondary-25);
      }

      &.danger {
        border-color: var(--error-200);
        background: var(--error-25);

        &:hover {
          border-color: var(--error-300);
          background: var(--error-50);
        }
      }
    }

    .action-content {
      flex: 1;
      margin-right: var(--spacing-md);
    }

    .action-title {
      margin: 0 0 var(--spacing-xs) 0;
      font-size: var(--text-base);
      font-weight: 600;
      color: var(--secondary-900);
    }

    .action-description {
      margin: 0;
      font-size: var(--text-sm);
      color: var(--secondary-600);
      line-height: var(--leading-relaxed);
    }

    .danger .action-title {
      color: var(--error-700);
    }

    .danger .action-description {
      color: var(--error-600);
    }

    @media (max-width: 768px) {

      .form-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
      }

      .checkbox-grid {
        grid-template-columns: 1fr;
      }

      .action-item {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-md);
      }

      .action-content {
        margin-right: 0;
      }

      .form-actions {
        justify-content: stretch;
      }

      .form-actions app-button {
        width: 100%;
      }
    }
  `]
})
export class SettingsComponent implements OnInit {
  profileForm!: FormGroup;
  notificationForm!: FormGroup;
  appearanceForm!: FormGroup;
  systemForm!: FormGroup;

  saving = {
    profile: false,
    notifications: false,
    appearance: false,
    system: false
  };

  exporting = false;
  clearing = false;
  resetting = false;

  constructor(private fb: FormBuilder) {}

  ngOnInit(): void {
    this.initializeForms();
    this.loadSettings();
  }

  private initializeForms(): void {
    this.profileForm = this.fb.group({
      fullName: ['John Doe', [Validators.required]],
      email: ['<EMAIL>', [Validators.required, Validators.email]],
      jobTitle: ['System Administrator'],
      department: ['IT']
    });

    this.notificationForm = this.fb.group({
      emailNotifications: [true],
      pushNotifications: [true],
      securityAlerts: [true],
      dependencyUpdates: [true],
      weeklyReports: [false]
    });

    this.appearanceForm = this.fb.group({
      theme: ['light'],
      language: ['en'],
      compactMode: [false],
      sidebarCollapsed: [false]
    });

    this.systemForm = this.fb.group({
      autoSave: [true],
      autoSaveInterval: [5, [Validators.min(1), Validators.max(60)]],
      sessionTimeout: [30, [Validators.min(5), Validators.max(480)]],
      enableAnalytics: [true],
      enableTelemetry: [false]
    });
  }

  private loadSettings(): void {
    // Load settings from localStorage or API
    const savedSettings = localStorage.getItem('appSettings');
    if (savedSettings) {
      try {
        const settings = JSON.parse(savedSettings);
        if (settings.profile) this.profileForm.patchValue(settings.profile);
        if (settings.notifications) this.notificationForm.patchValue(settings.notifications);
        if (settings.appearance) this.appearanceForm.patchValue(settings.appearance);
        if (settings.system) this.systemForm.patchValue(settings.system);
      } catch (error) {
        console.error('Error loading settings:', error);
      }
    }
  }

  private saveToStorage(): void {
    const settings = {
      profile: this.profileForm.value,
      notifications: this.notificationForm.value,
      appearance: this.appearanceForm.value,
      system: this.systemForm.value
    };
    localStorage.setItem('appSettings', JSON.stringify(settings));
  }

  saveProfile(): void {
    if (this.profileForm.valid) {
      this.saving.profile = true;
      // Simulate API call
      setTimeout(() => {
        this.saveToStorage();
        this.saving.profile = false;
        console.log('Profile saved:', this.profileForm.value);
      }, 1000);
    }
  }

  saveNotifications(): void {
    this.saving.notifications = true;
    // Simulate API call
    setTimeout(() => {
      this.saveToStorage();
      this.saving.notifications = false;
      console.log('Notifications saved:', this.notificationForm.value);
    }, 1000);
  }

  saveAppearance(): void {
    this.saving.appearance = true;
    // Simulate API call
    setTimeout(() => {
      this.saveToStorage();
      this.saving.appearance = false;
      console.log('Appearance saved:', this.appearanceForm.value);
    }, 1000);
  }

  saveSystem(): void {
    if (this.systemForm.valid) {
      this.saving.system = true;
      // Simulate API call
      setTimeout(() => {
        this.saveToStorage();
        this.saving.system = false;
        console.log('System settings saved:', this.systemForm.value);
      }, 1000);
    }
  }

  exportData(): void {
    this.exporting = true;
    // Simulate data export
    setTimeout(() => {
      const data = {
        profile: this.profileForm.value,
        notifications: this.notificationForm.value,
        appearance: this.appearanceForm.value,
        system: this.systemForm.value,
        exportDate: new Date().toISOString()
      };

      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'app-catalog-settings.json';
      a.click();
      URL.revokeObjectURL(url);

      this.exporting = false;
      console.log('Data exported');
    }, 1500);
  }

  clearCache(): void {
    this.clearing = true;
    // Simulate cache clearing
    setTimeout(() => {
      // Clear various caches
      if ('caches' in window) {
        caches.keys().then(names => {
          names.forEach(name => caches.delete(name));
        });
      }

      this.clearing = false;
      console.log('Cache cleared');
    }, 1000);
  }

  resetSettings(): void {
    if (confirm('Are you sure you want to reset all settings to default values? This action cannot be undone.')) {
      this.resetting = true;
      // Simulate reset
      setTimeout(() => {
        localStorage.removeItem('appSettings');
        this.initializeForms();
        this.resetting = false;
        console.log('Settings reset to defaults');
      }, 1000);
    }
  }
}
