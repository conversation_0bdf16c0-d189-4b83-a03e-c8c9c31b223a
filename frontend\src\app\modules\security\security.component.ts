import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ReactiveFormsModule, FormBuilder } from '@angular/forms';
import { CardComponent } from '../../shared/components/card/card.component';
import { ButtonComponent } from '../../shared/components/button/button.component';
import { TableComponent, TableColumn, TableAction } from '../../shared/components/table/table.component';
import { ChartComponent } from '../../shared/components/chart/chart.component';

interface SecurityItem {
  id: number;
  title: string;
  type: string;
  severity: string;
  status: string;
  application: string;
  discoveredDate: Date;
  resolvedDate?: Date;
  assignee?: string;
  description: string;
  cveId?: string;
  cvssScore?: number;
}

@Component({
  selector: 'app-security',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    CardComponent,
    ButtonComponent,
    TableComponent,
    ChartComponent
  ],
  template: `
    <div class="security-page">
      <div class="page-content">
        <div class="page-header">
          <div class="header-content">
            <h1>Security Overview</h1>
            <p class="page-subtitle">Monitor vulnerabilities and security assessments</p>
          </div>
          <div class="header-actions">
            <app-button
              variant="primary"
              leftIcon="M12 4v16m8-8H4"
              (clicked)="openAddModal()"
            >
              Add Security Item
            </app-button>
          </div>
        </div>

      <!-- Statistics Cards -->
      <div class="stats-grid">
        <app-card title="Total Vulnerabilities">
          <div class="stat-content">
            <div class="stat-number">{{ stats.total }}</div>
            <div class="stat-change">Across {{ stats.applications }} applications</div>
          </div>
        </app-card>

        <app-card title="Critical Issues">
          <div class="stat-content">
            <div class="stat-number critical">{{ stats.critical }}</div>
            <div class="stat-change">{{ stats.criticalPercentage }}% of total</div>
          </div>
        </app-card>

        <app-card title="Open Issues">
          <div class="stat-content">
            <div class="stat-number warning">{{ stats.open }}</div>
            <div class="stat-change">Need attention</div>
          </div>
        </app-card>

        <app-card title="Resolved This Month">
          <div class="stat-content">
            <div class="stat-number success">{{ stats.resolvedThisMonth }}</div>
            <div class="stat-change">Security improvements</div>
          </div>
        </app-card>
      </div>

      <!-- Charts Section -->
      <div class="charts-section">
        <app-card title="Vulnerabilities by Severity" subtitle="Distribution by severity level">
          <app-chart
            type="doughnut"
            [data]="severityChartData"
            [options]="chartOptions"
            height="300px"
          ></app-chart>
        </app-card>

        <app-card title="Security Trends" subtitle="Monthly vulnerability trends">
          <app-chart
            type="line"
            [data]="trendChartData"
            [options]="chartOptions"
            height="300px"
          ></app-chart>
        </app-card>
      </div>

      <!-- Security Items Table -->
      <app-card title="Security Issues" subtitle="All security vulnerabilities and assessments">
        <div class="table-controls">
          <div class="search-controls">
            <input
              type="text"
              placeholder="Search security issues..."
              class="search-input"
              (input)="onSearchInput($event)"
            >
            <select class="filter-select" (change)="onFilterChanged('severity', $event)">
              <option value="">All Severities</option>
              <option value="critical">Critical</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="low">Low</option>
            </select>
            <select class="filter-select" (change)="onFilterChanged('status', $event)">
              <option value="">All Statuses</option>
              <option value="open">Open</option>
              <option value="in_progress">In Progress</option>
              <option value="resolved">Resolved</option>
              <option value="false_positive">False Positive</option>
            </select>
            <select class="filter-select" (change)="onFilterChanged('type', $event)">
              <option value="">All Types</option>
              <option value="vulnerability">Vulnerability</option>
              <option value="compliance">Compliance</option>
              <option value="assessment">Assessment</option>
              <option value="audit">Audit</option>
            </select>
          </div>
        </div>

        <app-table
          [columns]="tableColumns"
          [data]="filteredSecurityItems"
          [actions]="tableActions"
          [loading]="loading"
          [sortable]="true"
          (sortChanged)="onSortChanged($event)"
        ></app-table>
      </app-card>
      </div>
    </div>
  `,
  styles: [`
    .security-page {
      min-height: 100%;
    }

    .page-content {
      padding: var(--spacing-xl);
    }

    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: var(--spacing-xl);
    }

    .header-content h1 {
      margin: 0 0 var(--spacing-sm) 0;
      font-size: var(--text-3xl);
      font-weight: 700;
      color: var(--secondary-900);
    }

    .page-subtitle {
      margin: 0;
      font-size: var(--text-lg);
      color: var(--secondary-600);
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: var(--spacing-lg);
      margin-bottom: var(--spacing-xl);
    }

    .stat-content {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-sm);
      padding: var(--spacing-lg);
    }

    .stat-number {
      font-size: var(--text-3xl);
      font-weight: 700;
      color: var(--secondary-900);

      &.critical {
        color: var(--error-600);
      }

      &.warning {
        color: var(--warning-600);
      }

      &.success {
        color: var(--success-600);
      }
    }

    .stat-change {
      font-size: var(--text-sm);
      color: var(--secondary-600);
    }

    .charts-section {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: var(--spacing-lg);
      margin-bottom: var(--spacing-xl);
    }

    .table-controls {
      margin-bottom: var(--spacing-lg);
    }

    .search-controls {
      display: flex;
      gap: var(--spacing-md);
      align-items: center;
      flex-wrap: wrap;
    }

    .search-input,
    .filter-select {
      height: 40px;
      padding: 0 var(--spacing-md);
      border: 1px solid var(--secondary-300);
      border-radius: var(--radius-md);
      background: white;
      font-size: var(--text-sm);
      color: var(--secondary-800);
      transition: all 0.2s ease;

      &:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px var(--primary-100);
      }
    }

    .search-input {
      flex: 1;
      min-width: 200px;
    }

    .filter-select {
      min-width: 120px;
      cursor: pointer;
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
      background-position: right var(--spacing-sm) center;
      background-repeat: no-repeat;
      background-size: 16px;
      padding-right: 40px;
      appearance: none;
    }

    @media (max-width: 768px) {
      .page-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
      }

      .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
      }

      .charts-section {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
      }

      .search-controls {
        flex-direction: column;
        align-items: stretch;
      }

      .search-input,
      .filter-select {
        min-width: auto;
      }
    }
  `]
})
export class SecurityComponent implements OnInit {
  securityItems: SecurityItem[] = [];
  filteredSecurityItems: SecurityItem[] = [];
  loading = false;

  // Statistics
  stats = {
    total: 0,
    critical: 0,
    open: 0,
    applications: 0,
    resolvedThisMonth: 0,
    criticalPercentage: 0
  };

  // Chart data
  severityChartData: any = null;
  trendChartData: any = null;
  chartOptions = {
    responsive: true,
    maintainAspectRatio: false
  };

  // Table configuration
  tableColumns: TableColumn[] = [
    { key: 'title', label: 'Title', sortable: true },
    { key: 'type', label: 'Type', type: 'badge', sortable: true },
    { key: 'severity', label: 'Severity', type: 'badge', sortable: true },
    { key: 'status', label: 'Status', type: 'badge', sortable: true },
    { key: 'application', label: 'Application', sortable: true },
    { key: 'cvssScore', label: 'CVSS', type: 'number', sortable: true },
    { key: 'assignee', label: 'Assignee', sortable: true },
    { key: 'discoveredDate', label: 'Discovered', type: 'date', sortable: true }
  ];

  tableActions: TableAction[] = [
    {
      label: 'View',
      icon: 'M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7Z',
      variant: 'ghost',
      action: (item) => this.viewSecurityItem(item)
    },
    {
      label: 'Edit',
      icon: 'M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7',
      variant: 'ghost',
      action: (item) => this.editSecurityItem(item)
    },
    {
      label: 'Delete',
      icon: 'M3 6h18m-2 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2',
      variant: 'error',
      action: (item) => this.deleteSecurityItem(item)
    }
  ];

  constructor(private fb: FormBuilder) {}

  ngOnInit(): void {
    this.loadSecurityItems();
  }

  private loadSecurityItems(): void {
    this.loading = true;

    // Mock data - replace with actual API call
    setTimeout(() => {
      this.securityItems = this.generateMockSecurityItems();
      this.filteredSecurityItems = [...this.securityItems];
      this.updateStats();
      this.updateChartData();
      this.loading = false;
    }, 1000);
  }

  private generateMockSecurityItems(): SecurityItem[] {
    const types = ['vulnerability', 'compliance', 'assessment', 'audit'];
    const severities = ['critical', 'high', 'medium', 'low'];
    const statuses = ['open', 'in_progress', 'resolved', 'false_positive'];
    const applications = ['App 1', 'App 2', 'App 3', 'App 4', 'App 5'];

    return Array.from({ length: 40 }, (_, i) => ({
      id: i + 1,
      title: `Security Issue ${i + 1}`,
      type: types[Math.floor(Math.random() * types.length)],
      severity: severities[Math.floor(Math.random() * severities.length)],
      status: statuses[Math.floor(Math.random() * statuses.length)],
      application: applications[Math.floor(Math.random() * applications.length)],
      discoveredDate: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000),
      resolvedDate: Math.random() > 0.6 ? new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000) : undefined,
      assignee: Math.random() > 0.3 ? `User ${Math.floor(Math.random() * 10) + 1}` : undefined,
      description: `Description for security issue ${i + 1}`,
      cveId: Math.random() > 0.7 ? `CVE-2024-${Math.floor(Math.random() * 9999) + 1000}` : undefined,
      cvssScore: Math.random() > 0.5 ? Math.round((Math.random() * 10) * 10) / 10 : undefined
    }));
  }

  private updateStats(): void {
    this.stats.total = this.securityItems.length;
    this.stats.critical = this.securityItems.filter(s => s.severity === 'critical').length;
    this.stats.open = this.securityItems.filter(s => s.status === 'open' || s.status === 'in_progress').length;
    this.stats.applications = new Set(this.securityItems.map(s => s.application)).size;
    this.stats.resolvedThisMonth = this.securityItems.filter(s =>
      s.resolvedDate && new Date(s.resolvedDate).getMonth() === new Date().getMonth()
    ).length;
    this.stats.criticalPercentage = Math.round((this.stats.critical / this.stats.total) * 100);
  }

  private updateChartData(): void {
    // Severity distribution chart
    const severityCount = this.securityItems.reduce((acc, item) => {
      acc[item.severity] = (acc[item.severity] || 0) + 1;
      return acc;
    }, {} as any);

    this.severityChartData = {
      labels: Object.keys(severityCount),
      datasets: [{
        data: Object.values(severityCount),
        backgroundColor: ['#ef4444', '#f97316', '#f59e0b', '#22c55e']
      }]
    };

    // Trend chart (mock data)
    this.trendChartData = {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
      datasets: [
        {
          label: 'New Vulnerabilities',
          data: [12, 19, 8, 15, 10, 7],
          borderColor: '#ef4444',
          backgroundColor: 'rgba(239, 68, 68, 0.1)',
          tension: 0.4
        },
        {
          label: 'Resolved',
          data: [8, 15, 12, 18, 14, 16],
          borderColor: '#22c55e',
          backgroundColor: 'rgba(34, 197, 94, 0.1)',
          tension: 0.4
        }
      ]
    };
  }

  openAddModal(): void {
    // Implement add modal
    console.log('Add security item modal');
  }

  viewSecurityItem(item: SecurityItem): void {
    // Implement view functionality
    console.log('View security item:', item);
  }

  editSecurityItem(item: SecurityItem): void {
    // Implement edit functionality
    console.log('Edit security item:', item);
  }

  deleteSecurityItem(item: SecurityItem): void {
    if (confirm(`Are you sure you want to delete ${item.title}?`)) {
      this.securityItems = this.securityItems.filter(s => s.id !== item.id);
      this.filteredSecurityItems = this.filteredSecurityItems.filter(s => s.id !== item.id);
      this.updateStats();
      this.updateChartData();
    }
  }

  onSearchInput(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.onSearchChanged(target.value);
  }

  onSearchChanged(searchTerm: string): void {
    this.applyFilters();
  }

  onFilterChanged(filterType: string, event: Event): void {
    const target = event.target as HTMLSelectElement;
    // Apply filter logic here
    this.applyFilters();
  }

  onSortChanged(sort: any): void {
    // Implement sorting logic
    console.log('Sort changed:', sort);
  }

  private applyFilters(): void {
    // Implement filtering logic
    this.filteredSecurityItems = [...this.securityItems];
  }
}
