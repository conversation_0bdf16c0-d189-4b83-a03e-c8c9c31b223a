import { Component, EventEmitter, Output, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { AuthService } from '../../../modules/auth/services/auth.service';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [CommonModule, RouterModule, FormsModule],
  template: `
    <header class="header">
      <div class="header-content">
        <!-- Logo and Brand -->
        <div class="header-brand">
          <button
            class="sidebar-toggle"
            (click)="toggleSidebar()"
            [attr.aria-label]="sidebarOpen ? 'Close sidebar' : 'Open sidebar'"
          >
            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>

          <div class="brand-info">
            <h1 class="brand-title">
              <a routerLink="/" class="brand-link">
                <svg class="brand-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                App Catalog
              </a>
            </h1>
            <span class="brand-subtitle">Internal Application Management</span>
          </div>
        </div>

        <!-- Search Bar -->
        <div class="header-search">
          <div class="search-container">
            <svg class="search-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="11" cy="11" r="8"></circle>
              <path d="m21 21-4.35-4.35"></path>
            </svg>
            <input
              type="text"
              class="search-input"
              placeholder="Search applications, dependencies, or documentation..."
              [(ngModel)]="searchTerm"
              (input)="onSearch($event)"
              (keyup.enter)="performSearch()"
            >
            <button
              class="search-clear"
              *ngIf="searchTerm"
              (click)="clearSearch()"
              aria-label="Clear search"
            >
              <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </div>
        </div>

        <!-- Header Actions -->
        <div class="header-actions">
          <!-- Notifications -->
          <div class="action-item">
            <button
              class="action-button"
              [class.has-notifications]="notificationCount > 0"
              (click)="toggleNotifications()"
              [attr.aria-label]="'Notifications' + (notificationCount > 0 ? ' (' + notificationCount + ' unread)' : '')"
            >
              <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
              </svg>
              <span class="notification-badge" *ngIf="notificationCount > 0">
                {{ notificationCount > 99 ? '99+' : notificationCount }}
              </span>
            </button>
          </div>

          <!-- Quick Actions -->
          <div class="action-item">
            <button
              class="action-button"
              (click)="toggleQuickActions()"
              aria-label="Quick actions"
            >
              <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <circle cx="12" cy="12" r="3"></circle>
                <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
              </svg>
            </button>
          </div>

          <!-- User Profile -->
          <div class="action-item">
            <button
              class="user-profile"
              (click)="toggleUserMenu()"
              aria-label="User menu"
            >
              <div class="user-avatar">
                <img
                  [src]="userAvatar || '/assets/images/default-avatar.png'"
                  [alt]="userName + ' avatar'"
                  class="avatar-image"
                  (error)="onAvatarError($event)"
                >
              </div>
              <div class="user-info">
                <span class="user-name">{{ userName }}</span>
                <span class="user-role">{{ userRole }}</span>
              </div>
              <svg class="chevron-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <polyline points="6,9 12,15 18,9"></polyline>
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- Notifications Dropdown -->
      <div class="dropdown notifications-dropdown" *ngIf="showNotifications">
        <div class="dropdown-header">
          <h3>Notifications</h3>
          <button class="mark-all-read" (click)="markAllAsRead()">
            Mark all as read
          </button>
        </div>
        <div class="dropdown-content">
          <div class="notification-item" *ngFor="let notification of notifications">
            <div class="notification-icon" [class]="'notification-' + notification.type">
              <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path [attr.d]="getNotificationIcon(notification.type)"></path>
              </svg>
            </div>
            <div class="notification-content">
              <p class="notification-title">{{ notification.title }}</p>
              <p class="notification-message">{{ notification.message }}</p>
              <span class="notification-time">{{ notification.timestamp | date:'short' }}</span>
            </div>
          </div>
          <div class="no-notifications" *ngIf="notifications.length === 0">
            <p>No new notifications</p>
          </div>
        </div>
        <div class="dropdown-footer">
          <a routerLink="/notifications" class="view-all-link">View all notifications</a>
        </div>
      </div>

      <!-- User Menu Dropdown -->
      <div class="dropdown user-dropdown" *ngIf="showUserMenu">
        <div class="dropdown-content">
          <a routerLink="/profile" class="dropdown-item">
            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
              <circle cx="12" cy="7" r="4"></circle>
            </svg>
            Profile Settings
          </a>
          <a routerLink="/preferences" class="dropdown-item">
            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="12" cy="12" r="3"></circle>
              <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
            </svg>
            Preferences
          </a>
          <div class="dropdown-divider"></div>
          <button class="dropdown-item logout-item" (click)="logout()">
            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
              <polyline points="16,17 21,12 16,7"></polyline>
              <line x1="21" y1="12" x2="9" y2="12"></line>
            </svg>
            Sign Out
          </button>
        </div>
      </div>
    </header>
  `,
  styleUrls: ['./header.component.scss']
})
export class HeaderComponent {
  @Input() sidebarOpen = false;
  @Output() sidebarToggle = new EventEmitter<void>();
  @Output() searchPerformed = new EventEmitter<string>();

  searchTerm = '';
  showNotifications = false;
  showUserMenu = false;
  showQuickActions = false;

  // Mock data - replace with actual service calls
  userName = 'John Doe';
  userRole = 'System Administrator';
  userAvatar = '';
  notificationCount = 3;
  notifications = [
    {
      id: 1,
      type: 'warning',
      title: 'Security Alert',
      message: 'Critical vulnerability detected in Payment Service',
      timestamp: new Date()
    },
    {
      id: 2,
      type: 'info',
      title: 'Dependency Update',
      message: 'Angular 17 is now available for upgrade',
      timestamp: new Date()
    },
    {
      id: 3,
      type: 'success',
      title: 'Assessment Complete',
      message: 'Security assessment for User Management completed',
      timestamp: new Date()
    }
  ];

  toggleSidebar(): void {
    this.sidebarToggle.emit();
  }

  onSearch(event: any): void {
    this.searchTerm = event.target.value;
  }

  performSearch(): void {
    if (this.searchTerm.trim()) {
      this.searchPerformed.emit(this.searchTerm.trim());
    }
  }

  clearSearch(): void {
    this.searchTerm = '';
    this.searchPerformed.emit('');
  }

  toggleNotifications(): void {
    this.showNotifications = !this.showNotifications;
    this.showUserMenu = false;
    this.showQuickActions = false;
  }

  toggleUserMenu(): void {
    this.showUserMenu = !this.showUserMenu;
    this.showNotifications = false;
    this.showQuickActions = false;
  }

  toggleQuickActions(): void {
    this.showQuickActions = !this.showQuickActions;
    this.showNotifications = false;
    this.showUserMenu = false;
  }

  markAllAsRead(): void {
    this.notificationCount = 0;
    this.notifications = [];
  }

  onAvatarError(event: any): void {
    event.target.src = '/assets/images/default-avatar.png';
  }

  getNotificationIcon(type: string): string {
    switch (type) {
      case 'warning':
        return 'M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z';
      case 'success':
        return 'M22 11.08V12a10 10 0 1 1-5.93-9.14';
      case 'error':
        return 'M18 6L6 18M6 6l12 12';
      default:
        return 'M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z';
    }
  }

  constructor(private authService: AuthService) {}

  logout(): void {
    this.authService.logout();
  }
}
