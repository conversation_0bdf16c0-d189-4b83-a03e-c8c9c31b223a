import './polyfills.server.mjs';
import {
  __spreadValues
} from "./chunk-5WKMABBB.mjs";

// src/app/modules/dependencies/dependencies.routes.ts
var routes = [
  __spreadValues({
    path: "",
    loadComponent: () => import("./chunk-2RJRY2UF.mjs").then((m) => m.DependenciesComponent)
  }, true ? { \u0275entryName: "src/app/modules/dependencies/dependencies.component.ts" } : {}),
  __spreadValues({
    path: "health",
    loadComponent: () => import("./chunk-BDRCZGCW.mjs").then((m) => m.DependencyHealthComponent)
  }, true ? { \u0275entryName: "src/app/modules/dependencies/dependency-health/dependency-health.component.ts" } : {})
];
export {
  routes
};
//# sourceMappingURL=chunk-4QZK2SQE.mjs.map
