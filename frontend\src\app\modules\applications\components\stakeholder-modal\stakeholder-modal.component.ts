import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { SlideModalComponent } from '../../../../shared/components/slide-modal/slide-modal.component';
import { FormInputComponent } from '../../../../shared/components/form-input/form-input.component';

export interface StakeholderFormData {
  name: string;
  email: string;
  role: string;
  department: string;
  responsibility: string;
  isPrimary: boolean;
}

@Component({
  selector: 'app-stakeholder-modal',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, SlideModalComponent, FormInputComponent],
  template: `
    <app-slide-modal
      [isOpen]="isOpen"
      [title]="editMode ? 'Edit Stakeholder' : 'Add Stakeholder'"
      subtitle="Configure team member details"
      [loading]="loading"
      [canConfirm]="stakeholderForm.valid"
      confirmText="Save Stakeholder"
      (closed)="onClose()"
      (confirmed)="onSave()"
      (cancelled)="onClose()"
    >
      <form [formGroup]="stakeholderForm" class="stakeholder-form">
        <div class="form-grid">
          <app-form-input
            label="Full Name"
            placeholder="e.g., John Doe"
            formControlName="name"
            [required]="true"
            [errorMessage]="getFieldError('name')"
          ></app-form-input>

          <app-form-input
            label="Email Address"
            placeholder="e.g., <EMAIL>"
            formControlName="email"
            type="email"
            [required]="true"
            [errorMessage]="getFieldError('email')"
          ></app-form-input>

          <div class="form-group">
            <label class="form-label">Role</label>
            <select formControlName="role" class="form-select">
              <option value="product_owner">Product Owner</option>
              <option value="developer">Developer</option>
              <option value="tech_lead">Tech Lead</option>
              <option value="architect">Architect</option>
              <option value="business_analyst">Business Analyst</option>
              <option value="project_manager">Project Manager</option>
              <option value="qa_engineer">QA Engineer</option>
              <option value="devops_engineer">DevOps Engineer</option>
              <option value="security_engineer">Security Engineer</option>
              <option value="stakeholder">Stakeholder</option>
            </select>
          </div>

          <app-form-input
            label="Department"
            placeholder="e.g., Engineering, Product"
            formControlName="department"
            [required]="true"
            [errorMessage]="getFieldError('department')"
          ></app-form-input>

          <div class="form-group full-width">
            <label class="checkbox-label">
              <input
                type="checkbox"
                formControlName="isPrimary"
                class="checkbox-input"
              >
              <span class="checkbox-text">Primary Contact</span>
              <span class="checkbox-description">This person is the primary contact for this application</span>
            </label>
          </div>

          <div class="form-group full-width">
            <label class="form-label">Responsibility</label>
            <textarea
              formControlName="responsibility"
              class="form-textarea"
              placeholder="Describe this person's role and responsibilities for the application..."
              rows="3"
            ></textarea>
          </div>
        </div>
      </form>
    </app-slide-modal>
  `,
  styles: [`
    .stakeholder-form {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-lg);
    }

    .form-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-lg);
    }

    .form-group {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-sm);
    }

    .form-group.full-width {
      grid-column: 1 / -1;
    }

    .form-label {
      font-size: var(--text-sm);
      font-weight: 500;
      color: var(--secondary-700);
    }

    .form-select,
    .form-textarea {
      border: 1px solid var(--secondary-300);
      border-radius: var(--radius-md);
      background: white;
      font-size: var(--text-sm);
      color: var(--secondary-800);
      transition: all 0.2s ease;

      &:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px var(--primary-100);
      }
    }

    .form-select {
      height: 40px;
      padding: 0 var(--spacing-md);
      cursor: pointer;
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
      background-position: right var(--spacing-sm) center;
      background-repeat: no-repeat;
      background-size: 16px;
      padding-right: 40px;
      appearance: none;
    }

    .form-textarea {
      padding: var(--spacing-md);
      resize: vertical;
      min-height: 80px;
      font-family: inherit;
    }

    .checkbox-label {
      display: flex;
      align-items: flex-start;
      gap: var(--spacing-sm);
      cursor: pointer;
      padding: var(--spacing-md);
      border: 1px solid var(--secondary-200);
      border-radius: var(--radius-md);
      transition: all 0.2s ease;

      &:hover {
        border-color: var(--primary-300);
        background: var(--primary-25);
      }
    }

    .checkbox-input {
      margin: 0;
      margin-top: 2px;
    }

    .checkbox-text {
      font-size: var(--text-sm);
      font-weight: 500;
      color: var(--secondary-900);
      margin-bottom: var(--spacing-xs);
    }

    .checkbox-description {
      font-size: var(--text-xs);
      color: var(--secondary-600);
      display: block;
    }

    @media (max-width: 768px) {
      .form-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
      }
    }
  `]
})
export class StakeholderModalComponent implements OnInit {
  @Input() isOpen = false;
  @Input() editMode = false;
  @Input() initialData: StakeholderFormData | null = null;
  @Input() loading = false;

  @Output() closed = new EventEmitter<void>();
  @Output() saved = new EventEmitter<StakeholderFormData>();

  stakeholderForm!: FormGroup;

  constructor(private fb: FormBuilder) {}

  ngOnInit(): void {
    this.initializeForm();
  }

  ngOnChanges(): void {
    if (this.stakeholderForm && this.initialData) {
      this.stakeholderForm.patchValue(this.initialData);
    }
  }

  private initializeForm(): void {
    this.stakeholderForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      role: ['developer', [Validators.required]],
      department: ['', [Validators.required]],
      responsibility: [''],
      isPrimary: [false]
    });

    if (this.initialData) {
      this.stakeholderForm.patchValue(this.initialData);
    }
  }

  getFieldError(fieldName: string): string {
    const field = this.stakeholderForm.get(fieldName);
    if (field && field.invalid && field.touched) {
      if (field.errors?.['required']) {
        return `${fieldName} is required`;
      }
      if (field.errors?.['minlength']) {
        return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;
      }
      if (field.errors?.['email']) {
        return 'Please enter a valid email address';
      }
    }
    return '';
  }

  onClose(): void {
    this.stakeholderForm.reset();
    this.closed.emit();
  }

  onSave(): void {
    if (this.stakeholderForm.valid) {
      this.saved.emit(this.stakeholderForm.value);
    }
  }
}
