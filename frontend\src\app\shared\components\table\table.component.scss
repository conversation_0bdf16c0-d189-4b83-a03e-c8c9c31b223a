.table-container {
  background: white;
  border-radius: var(--radius-lg);
  border: 1px solid var(--secondary-200);
  overflow: hidden;
}

/* Table Header */
.table-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--secondary-200);
  background: var(--neutral-50);
}

.table-title-section {
  flex: 1;
}

.table-title {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--secondary-900);
}

.table-subtitle {
  margin: 0;
  font-size: var(--text-sm);
  color: var(--secondary-600);
}

.table-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

/* Search */
.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: var(--spacing-md);
  width: 16px;
  height: 16px;
  color: var(--secondary-400);
  z-index: 1;
}

.search-input {
  width: 250px;
  height: 36px;
  padding: 0 var(--spacing-md) 0 40px;
  border: 1px solid var(--secondary-200);
  border-radius: var(--radius-md);
  background: white;
  font-size: var(--text-sm);
  color: var(--secondary-800);
  transition: all 0.2s ease;

  &::placeholder {
    color: var(--secondary-400);
  }

  &:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px var(--primary-100);
  }
}

/* Table */
.table-wrapper {
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--text-sm);
}

.table-head {
  background: var(--neutral-50);
  border-bottom: 1px solid var(--secondary-200);
}

.table-header-cell {
  padding: var(--spacing-md) var(--spacing-lg);
  text-align: left;
  font-weight: 600;
  color: var(--secondary-700);
  border-bottom: 1px solid var(--secondary-200);
  white-space: nowrap;

  &.sortable {
    cursor: pointer;
    user-select: none;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: var(--secondary-100);
    }
  }
}

.header-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.sort-indicator {
  display: flex;
  align-items: center;
}

.sort-icon {
  width: 14px;
  height: 14px;
  color: var(--secondary-400);
  transition: all 0.2s ease;

  &.sort-active {
    color: var(--primary-600);
  }

  &.sort-desc {
    transform: rotate(180deg);
  }
}

/* Table Body */
.table-body {
  background: white;
}

.table-row {
  border-bottom: 1px solid var(--secondary-100);
  transition: background-color 0.2s ease;

  &:last-child {
    border-bottom: none;
  }

  &.selected {
    background-color: var(--primary-50);
  }
}

.table.table-hover .table-row:hover {
  background-color: var(--neutral-50);
}

.table.table-striped .table-row:nth-child(even) {
  background-color: var(--neutral-25);
}

.table-cell {
  padding: var(--spacing-md) var(--spacing-lg);
  vertical-align: middle;
  border-bottom: 1px solid var(--secondary-100);
}

/* Cell Types */
.cell-text {
  color: var(--secondary-800);
}

.cell-number {
  color: var(--secondary-800);
  font-variant-numeric: tabular-nums;
}

.cell-date {
  color: var(--secondary-600);
  font-size: var(--text-sm);
}

.cell-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;

  &.badge-default {
    background: var(--secondary-100);
    color: var(--secondary-700);
  }

  &.badge-primary {
    background: var(--primary-100);
    color: var(--primary-700);
  }

  &.badge-success {
    background: var(--success-100);
    color: var(--success-700);
  }

  &.badge-warning {
    background: var(--warning-100);
    color: var(--warning-700);
  }

  &.badge-error {
    background: var(--error-100);
    color: var(--error-700);
  }
}

.cell-boolean {
  display: inline-flex;
  align-items: center;
}

.boolean-indicator {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;

  &.boolean-true {
    background: var(--success-100);
    color: var(--success-700);
  }

  &.boolean-false {
    background: var(--secondary-100);
    color: var(--secondary-700);
  }
}

.cell-actions {
  display: flex;
  gap: var(--spacing-xs);
  align-items: center;
}

/* Loading State */
.loading-row {
  background: white;
}

.loading-cell {
  padding: var(--spacing-3xl);
  text-align: center;
  border: none;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
  color: var(--secondary-600);
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--secondary-200);
  border-top: 3px solid var(--primary-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Empty State */
.empty-row {
  background: white;
}

.empty-cell {
  padding: var(--spacing-3xl);
  text-align: center;
  border: none;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
  color: var(--secondary-500);
}

.empty-icon {
  width: 48px;
  height: 48px;
  color: var(--secondary-300);
}

.empty-content h4 {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--secondary-700);
}

.empty-content p {
  margin: 0;
  font-size: var(--text-sm);
  color: var(--secondary-500);
}

/* Table Footer */
.table-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
  border-top: 1px solid var(--secondary-200);
  background: var(--neutral-50);
}

.footer-info {
  flex: 1;
}

.item-count {
  font-size: var(--text-sm);
  color: var(--secondary-600);
}

/* Pagination */
.pagination {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.page-numbers {
  display: flex;
  gap: var(--spacing-xs);
}

.page-button {
  min-width: 32px;
  height: 32px;
  padding: 0 var(--spacing-sm);
  border: 1px solid var(--secondary-200);
  background: white;
  color: var(--secondary-700);
  font-size: var(--text-sm);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background: var(--secondary-50);
    border-color: var(--secondary-300);
  }

  &.active {
    background: var(--primary-500);
    border-color: var(--primary-500);
    color: white;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .table-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .table-actions {
    justify-content: space-between;
  }

  .search-input {
    width: 200px;
  }

  .table-footer {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .pagination {
    justify-content: center;
  }

  .table-wrapper {
    overflow-x: scroll;
  }

  .table {
    min-width: 600px;
  }

  .table-header-cell,
  .table-cell {
    padding: var(--spacing-sm) var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .search-input {
    width: 150px;
  }

  .page-numbers {
    display: none;
  }
}

/* Print Styles */
@media print {
  .table-container {
    border: 1px solid var(--secondary-300);
    box-shadow: none;
  }

  .table-header,
  .table-footer {
    background: white;
  }

  .search-container,
  .pagination {
    display: none;
  }

  .cell-actions {
    display: none;
  }
}
