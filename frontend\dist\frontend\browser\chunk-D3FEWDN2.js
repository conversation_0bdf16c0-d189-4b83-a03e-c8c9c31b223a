import {
  __spreadValues
} from "./chunk-WDMUDEB6.js";

// src/app/modules/applications/applications.routes.ts
var routes = [
  __spreadValues({
    path: "",
    loadComponent: () => import("./chunk-OEX6K4NW.js").then((m) => m.ApplicationsListComponent)
  }, false ? { \u0275entryName: "src/app/modules/applications/applications-list/applications-list.component.ts" } : {}),
  __spreadValues({
    path: "new",
    loadComponent: () => import("./chunk-424ELUZE.js").then((m) => m.ApplicationFormComponent)
  }, false ? { \u0275entryName: "src/app/modules/applications/application-form/application-form.component.ts" } : {}),
  __spreadValues({
    path: ":id",
    loadComponent: () => import("./chunk-F6OQVEPM.js").then((m) => m.ApplicationDetailComponent)
  }, false ? { \u0275entryName: "src/app/modules/applications/application-detail/application-detail.component.ts" } : {}),
  __spreadValues({
    path: ":id/edit",
    loadComponent: () => import("./chunk-424ELUZE.js").then((m) => m.ApplicationFormComponent)
  }, false ? { \u0275entryName: "src/app/modules/applications/application-form/application-form.component.ts" } : {})
];
export {
  routes
};
//# sourceMappingURL=chunk-D3FEWDN2.js.map
