{"version": 3, "sources": ["src/app/modules/security/vulnerabilities/vulnerabilities.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { CardComponent } from '../../../shared/components/card/card.component';\n\n@Component({\n  selector: 'app-vulnerabilities',\n  standalone: true,\n  imports: [CommonModule, CardComponent],\n  template: `\n    <div class=\"vulnerabilities-page\">\n      <h1>Vulnerabilities</h1>\n      <app-card title=\"Vulnerability Management\" subtitle=\"Track and manage security vulnerabilities\">\n        <div class=\"placeholder\">\n          <p>Vulnerabilities module coming soon...</p>\n        </div>\n      </app-card>\n    </div>\n  `,\n  styles: [`\n    .vulnerabilities-page {\n      min-height: 100%;\n    }\n    .placeholder {\n      padding: var(--spacing-xl);\n      text-align: center;\n      color: var(--secondary-600);\n    }\n  `]\n})\nexport class VulnerabilitiesComponent {}\n"], "mappings": ";;;;;;;;;;;;;;;;;AA6BM,IAAO,2BAAP,MAAO,0BAAwB;;qCAAxB,2BAAwB;EAAA;yEAAxB,2BAAwB,WAAA,CAAA,CAAA,qBAAA,CAAA,GAAA,OAAA,GAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,sBAAA,GAAA,CAAA,SAAA,4BAAA,YAAA,2CAAA,GAAA,CAAA,GAAA,aAAA,CAAA,GAAA,UAAA,SAAA,kCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AApBjC,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAkC,GAAA,IAAA;AAC5B,MAAA,iBAAA,GAAA,iBAAA;AAAe,MAAA,uBAAA;AACnB,MAAA,yBAAA,GAAA,YAAA,CAAA,EAAgG,GAAA,OAAA,CAAA,EACrE,GAAA,GAAA;AACpB,MAAA,iBAAA,GAAA,uCAAA;AAAqC,MAAA,uBAAA,EAAI,EACxC,EACG;;oBARL,cAAc,aAAa,GAAA,QAAA,CAAA,+PAAA,EAAA,CAAA;;;sEAsB1B,0BAAwB,CAAA;UAzBpC;uBACW,uBAAqB,YACnB,MAAI,SACP,CAAC,cAAc,aAAa,GAAC,UAC5B;;;;;;;;;KAST,QAAA,CAAA,wcAAA,EAAA,CAAA;;;;6EAYU,0BAAwB,EAAA,WAAA,4BAAA,UAAA,yEAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}