import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { map, tap, catchError, delay } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import { BaseApiService } from '../../../shared/services/base-api.service';
import { isPlatformBrowser } from '@angular/common';

export interface User {
  id: string;
  email: string;
  name: string;
  role: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();
  private isLoadingSubject = new BehaviorSubject<boolean>(false);
  public isLoading$ = this.isLoadingSubject.asObservable();

  constructor(
    private router: Router,
    private baseApi: BaseApiService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    this.loadUserFromStorage();
  }

  private loadUserFromStorage() {
    if (!isPlatformBrowser(this.platformId)) return;
    const userStr = localStorage.getItem('user') || sessionStorage.getItem('user');
    if (userStr) {
      try {
        const user = JSON.parse(userStr);
        this.currentUserSubject.next(user);
      } catch (e) {
        this.logout();
      }
    }
  }
  login(credentials: LoginCredentials): Observable<User> {
    this.isLoadingSubject.next(true);
    
    // TODO: Replace with real API call
    // return this.baseApi.post<{ user: User; token: string }>('auth/login', credentials)
    const mockResponse: { user: User; token: string } = {
      user: {
        id: '1',
        email: credentials.email,
        name: 'John Doe',
        role: 'admin'
      },
      token: 'mock-jwt-token'
    };
    
    return of(mockResponse).pipe(
      delay(1000),
      map(response => {
        const storage = credentials.rememberMe ? localStorage : sessionStorage;
        if (isPlatformBrowser(this.platformId)) {
          storage.setItem('auth_token', response.token);
          storage.setItem('user', JSON.stringify(response.user));
        }
        this.currentUserSubject.next(response.user);
        return response.user;
      }),
      tap(() => {
        this.isLoadingSubject.next(false);
        this.router.navigate(['/dashboard']);
      }),
      catchError(error => {
        this.isLoadingSubject.next(false);
        throw error;
      })
    );
  }

  logout(): void {
    if (isPlatformBrowser(this.platformId)) {
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user');
      sessionStorage.removeItem('auth_token');
      sessionStorage.removeItem('user');
    }
    this.currentUserSubject.next(null);
    this.router.navigate(['/auth/login']);
  }

  isAuthenticated(): boolean {
    return !!this.currentUserSubject.value;
  }
}
