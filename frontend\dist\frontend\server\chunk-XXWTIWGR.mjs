import './polyfills.server.mjs';
import {
  CardComponent
} from "./chunk-UJ4MSIYR.mjs";
import {
  CommonModule,
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵdefineComponent,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵtext
} from "./chunk-IPMSWJNG.mjs";
import "./chunk-5WKMABBB.mjs";

// src/app/modules/reports/reports.component.ts
var ReportsComponent = class _ReportsComponent {
  static \u0275fac = function ReportsComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ReportsComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _ReportsComponent, selectors: [["app-reports"]], decls: 7, vars: 0, consts: [[1, "reports-page"], ["title", "Reports & Analytics", "subtitle", "Generate and view system reports"], [1, "placeholder"]], template: function ReportsComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "h1");
      \u0275\u0275text(2, "Reports");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(3, "app-card", 1)(4, "div", 2)(5, "p");
      \u0275\u0275text(6, "Reports module coming soon...");
      \u0275\u0275elementEnd()()()();
    }
  }, dependencies: [CommonModule, CardComponent], styles: ["\n\n.reports-page[_ngcontent-%COMP%] {\n  min-height: 100%;\n}\n.placeholder[_ngcontent-%COMP%] {\n  padding: var(--spacing-xl);\n  text-align: center;\n  color: var(--secondary-600);\n}\n/*# sourceMappingURL=reports.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ReportsComponent, [{
    type: Component,
    args: [{ selector: "app-reports", standalone: true, imports: [CommonModule, CardComponent], template: `
    <div class="reports-page">
      <h1>Reports</h1>
      <app-card title="Reports & Analytics" subtitle="Generate and view system reports">
        <div class="placeholder">
          <p>Reports module coming soon...</p>
        </div>
      </app-card>
    </div>
  `, styles: ["/* angular:styles/component:scss;d0c847983cfb1016982fd18d35dc13d234331b31768800c8359a3ca03af06bb4;C:/Users/<USER>/Documents/VibeCoding/AppCatalog/app-catalog/frontend/src/app/modules/reports/reports.component.ts */\n.reports-page {\n  min-height: 100%;\n}\n.placeholder {\n  padding: var(--spacing-xl);\n  text-align: center;\n  color: var(--secondary-600);\n}\n/*# sourceMappingURL=reports.component.css.map */\n"] }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(ReportsComponent, { className: "ReportsComponent", filePath: "src/app/modules/reports/reports.component.ts", lineNumber: 30 });
})();
export {
  ReportsComponent
};
//# sourceMappingURL=chunk-XXWTIWGR.mjs.map
