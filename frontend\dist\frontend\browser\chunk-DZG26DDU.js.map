{"version": 3, "sources": ["src/app/modules/documentation/documentation.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\n\nexport const routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./documentation.component').then(m => m.DocumentationComponent)\n  }\n];\n"], "mappings": ";;;;;AAEO,IAAM,SAAiB;EAC5B;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAA2B,EAAE,KAAK,OAAK,EAAE,sBAAsB;;;", "names": []}