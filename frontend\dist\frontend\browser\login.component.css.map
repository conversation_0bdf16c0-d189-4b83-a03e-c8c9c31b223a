{"version": 3, "sources": ["src/app/modules/auth/components/login/login.component.ts"], "sourcesContent": ["\n    :host {\n      display: block;\n      min-height: 100vh;\n    }\n\n    .login-container {\n      min-height: 100vh;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      padding: var(--spacing-lg);\n      background: linear-gradient(\n        135deg,\n        var(--neutral-50) 0%,\n        var(--neutral-100) 100%\n      );\n    }\n\n    .login-card {\n      width: 100%;\n      max-width: 420px;\n      background: white;\n      border-radius: var(--radius-xl);\n      box-shadow: var(--shadow-xl);\n      border: 1px solid var(--secondary-200);\n      overflow: hidden;\n    }\n\n    .login-header {\n      padding: var(--spacing-2xl) var(--spacing-xl) var(--spacing-xl);\n      text-align: center;\n      background: linear-gradient(\n        135deg,\n        var(--primary-50) 0%,\n        white 100%\n      );\n      border-bottom: 1px solid var(--secondary-100);\n    }\n\n    .logo-container {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: var(--spacing-md);\n      margin-bottom: var(--spacing-lg);\n    }\n\n    .logo-icon {\n      width: 32px;\n      height: 32px;\n      color: var(--primary-600);\n      stroke-width: 2;\n    }\n\n    .logo-text {\n      font-size: var(--text-xl);\n      font-weight: 700;\n      color: var(--secondary-900);\n      margin: 0;\n    }\n\n    .login-title {\n      font-size: var(--text-2xl);\n      font-weight: 600;\n      color: var(--secondary-900);\n      margin: 0 0 var(--spacing-sm) 0;\n      line-height: var(--leading-tight);\n    }\n\n    .login-subtitle {\n      font-size: var(--text-sm);\n      color: var(--secondary-600);\n      margin: 0;\n      line-height: var(--leading-normal);\n    }\n\n    .error-message {\n      margin: var(--spacing-lg) var(--spacing-xl) 0;\n      padding: var(--spacing-md);\n      background: var(--error-50);\n      border: 1px solid var(--error-200);\n      border-radius: var(--radius-md);\n      display: flex;\n      align-items: center;\n      gap: var(--spacing-sm);\n      font-size: var(--text-sm);\n      color: var(--error-700);\n    }\n\n    .error-icon {\n      width: 16px;\n      height: 16px;\n      color: var(--error-500);\n      flex-shrink: 0;\n      stroke-width: 2;\n    }\n\n    .login-form {\n      padding: var(--spacing-xl);\n      display: flex;\n      flex-direction: column;\n      gap: var(--spacing-lg);\n    }\n\n    .form-options {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      gap: var(--spacing-md);\n    }\n\n    .remember-me {\n      display: flex;\n      align-items: center;\n      gap: var(--spacing-sm);\n    }\n\n    .checkbox-input {\n      width: 16px;\n      height: 16px;\n      border: 1px solid var(--secondary-300);\n      border-radius: var(--radius-sm);\n      background: white;\n      cursor: pointer;\n      transition: all 0.2s ease;\n\n      &:checked {\n        background-color: var(--primary-600);\n        border-color: var(--primary-600);\n      }\n\n      &:focus {\n        outline: none;\n        box-shadow: 0 0 0 3px var(--primary-100);\n      }\n\n      &:disabled {\n        opacity: 0.5;\n        cursor: not-allowed;\n      }\n    }\n\n    .checkbox-label {\n      font-size: var(--text-sm);\n      color: var(--secondary-700);\n      cursor: pointer;\n      user-select: none;\n    }\n\n    .forgot-password {\n      flex-shrink: 0;\n    }\n\n    .forgot-link {\n      font-size: var(--text-sm);\n      color: var(--primary-600);\n      text-decoration: none;\n      font-weight: 500;\n      transition: color 0.2s ease;\n\n      &:hover {\n        color: var(--primary-700);\n        text-decoration: underline;\n      }\n\n      &:focus {\n        outline: none;\n        color: var(--primary-700);\n        text-decoration: underline;\n      }\n    }\n\n    .login-footer {\n      padding: var(--spacing-lg) var(--spacing-xl) var(--spacing-xl);\n      text-align: center;\n      background: var(--secondary-50);\n      border-top: 1px solid var(--secondary-100);\n    }\n\n    .footer-text {\n      font-size: var(--text-sm);\n      color: var(--secondary-600);\n      margin: 0;\n    }\n\n    .footer-link {\n      color: var(--primary-600);\n      text-decoration: none;\n      font-weight: 500;\n      transition: color 0.2s ease;\n\n      &:hover {\n        color: var(--primary-700);\n        text-decoration: underline;\n      }\n\n      &:focus {\n        outline: none;\n        color: var(--primary-700);\n        text-decoration: underline;\n      }\n    }\n\n    /* Responsive Design */\n    @media (max-width: 480px) {\n      .login-container {\n        padding: var(--spacing-md);\n      }\n\n      .login-card {\n        max-width: 100%;\n      }\n\n      .login-header {\n        padding: var(--spacing-xl) var(--spacing-lg) var(--spacing-lg);\n      }\n\n      .login-form {\n        padding: var(--spacing-lg);\n      }\n\n      .login-footer {\n        padding: var(--spacing-md) var(--spacing-lg) var(--spacing-lg);\n      }\n\n      .form-options {\n        flex-direction: column;\n        align-items: flex-start;\n        gap: var(--spacing-sm);\n      }\n\n      .forgot-password {\n        align-self: flex-end;\n      }\n    }\n\n    /* High Contrast Mode Support */\n    @media (prefers-contrast: high) {\n      .login-card {\n        border-width: 2px;\n      }\n\n      .checkbox-input {\n        border-width: 2px;\n      }\n    }\n\n    /* Reduced Motion Support */\n    @media (prefers-reduced-motion: reduce) {\n      .checkbox-input,\n      .forgot-link,\n      .footer-link {\n        transition: none;\n      }\n    }\n  "], "mappings": ";AACI;AACE,WAAA;AACA,cAAA;;AAGF,CAAA;AACE,cAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,WAAA,IAAA;AACA;IAAA;MAAA,MAAA;MAAA,IAAA,cAAA,EAAA;MAAA,IAAA,eAAA;;AAOF,CAAA;AACE,SAAA;AACA,aAAA;AACA,cAAA;AACA,iBAAA,IAAA;AACA,cAAA,IAAA;AACA,UAAA,IAAA,MAAA,IAAA;AACA,YAAA;;AAGF,CAAA;AACE,WAAA,IAAA,eAAA,IAAA,cAAA,IAAA;AACA,cAAA;AACA;IAAA;MAAA,MAAA;MAAA,IAAA,cAAA,EAAA;MAAA,MAAA;AAKA,iBAAA,IAAA,MAAA,IAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,mBAAA;AACA,OAAA,IAAA;AACA,iBAAA,IAAA;;AAGF,CAAA;AACE,SAAA;AACA,UAAA;AACA,SAAA,IAAA;AACA,gBAAA;;AAGF,CAAA;AACE,aAAA,IAAA;AACA,eAAA;AACA,SAAA,IAAA;AACA,UAAA;;AAGF,CAAA;AACE,aAAA,IAAA;AACA,eAAA;AACA,SAAA,IAAA;AACA,UAAA,EAAA,EAAA,IAAA,cAAA;AACA,eAAA,IAAA;;AAGF,CAAA;AACE,aAAA,IAAA;AACA,SAAA,IAAA;AACA,UAAA;AACA,eAAA,IAAA;;AAGF,CAAA;AACE,UAAA,IAAA,cAAA,IAAA,cAAA;AACA,WAAA,IAAA;AACA,cAAA,IAAA;AACA,UAAA,IAAA,MAAA,IAAA;AACA,iBAAA,IAAA;AACA,WAAA;AACA,eAAA;AACA,OAAA,IAAA;AACA,aAAA,IAAA;AACA,SAAA,IAAA;;AAGF,CAAA;AACE,SAAA;AACA,UAAA;AACA,SAAA,IAAA;AACA,eAAA;AACA,gBAAA;;AAGF,CAAA;AACE,WAAA,IAAA;AACA,WAAA;AACA,kBAAA;AACA,OAAA,IAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,mBAAA;AACA,OAAA,IAAA;;AAGF,CAAA;AACE,WAAA;AACA,eAAA;AACA,OAAA,IAAA;;AAGF,CAAA;AACE,SAAA;AACA,UAAA;AACA,UAAA,IAAA,MAAA,IAAA;AACA,iBAAA,IAAA;AACA,cAAA;AACA,UAAA;AACA,cAAA,IAAA,KAAA;;AAEA,CATF,cASE;AACE,oBAAA,IAAA;AACA,gBAAA,IAAA;;AAGF,CAdF,cAcE;AACE,WAAA;AACA,cAAA,EAAA,EAAA,EAAA,IAAA,IAAA;;AAGF,CAnBF,cAmBE;AACE,WAAA;AACA,UAAA;;AAIJ,CAAA;AACE,aAAA,IAAA;AACA,SAAA,IAAA;AACA,UAAA;AACA,uBAAA;AAAA,eAAA;;AAGF,CAAA;AACE,eAAA;;AAGF,CAAA;AACE,aAAA,IAAA;AACA,SAAA,IAAA;AACA,mBAAA;AACA,eAAA;AACA,cAAA,MAAA,KAAA;;AAEA,CAPF,WAOE;AACE,SAAA,IAAA;AACA,mBAAA;;AAGF,CAZF,WAYE;AACE,WAAA;AACA,SAAA,IAAA;AACA,mBAAA;;AAIJ,CAAA;AACE,WAAA,IAAA,cAAA,IAAA,cAAA,IAAA;AACA,cAAA;AACA,cAAA,IAAA;AACA,cAAA,IAAA,MAAA,IAAA;;AAGF,CAAA;AACE,aAAA,IAAA;AACA,SAAA,IAAA;AACA,UAAA;;AAGF,CAAA;AACE,SAAA,IAAA;AACA,mBAAA;AACA,eAAA;AACA,cAAA,MAAA,KAAA;;AAEA,CANF,WAME;AACE,SAAA,IAAA;AACA,mBAAA;;AAGF,CAXF,WAWE;AACE,WAAA;AACA,SAAA,IAAA;AACA,mBAAA;;AAKJ,OAAA,CAAA,SAAA,EAAA;AACE,GAxMF;AAyMI,aAAA,IAAA;;AAGF,GA/LF;AAgMI,eAAA;;AAGF,GAzLF;AA0LI,aAAA,IAAA,cAAA,IAAA,cAAA,IAAA;;AAGF,GAxHF;AAyHI,aAAA,IAAA;;AAGF,GAjDF;AAkDI,aAAA,IAAA,cAAA,IAAA,cAAA,IAAA;;AAGF,GAzHF;AA0HI,oBAAA;AACA,iBAAA;AACA,SAAA,IAAA;;AAGF,GAlFF;AAmFI,gBAAA;;;AAKJ,OAAA,CAAA,gBAAA,EAAA;AACE,GA5NF;AA6NI,kBAAA;;AAGF,GA7HF;AA8HI,kBAAA;;;AAKJ,OAAA,CAAA,sBAAA,EAAA;AACE,GApIF;EAoIE,CAhGF;EAgGE,CAhEF;AAmEI,gBAAA;;;", "names": []}