{"version": 3, "sources": ["src/app/shared/components/slide-modal/slide-modal.component.ts"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, OnChanges, Inject, PLATFORM_ID } from '@angular/core';\nimport { CommonModule, isPlatformBrowser, DOCUMENT } from '@angular/common';\nimport { ButtonComponent } from '../button/button.component';\n\n@Component({\n  selector: 'app-slide-modal',\n  standalone: true,\n  imports: [CommonModule, ButtonComponent],\n  template: `\n    <!-- Modal Backdrop -->\n    <div\n      class=\"modal-backdrop\"\n      [class.show]=\"isVisible && isAnimating\"\n      (click)=\"onBackdropClick()\"\n      *ngIf=\"isVisible\"\n    ></div>\n\n    <!-- Modal Panel -->\n    <div\n      class=\"modal-panel\"\n      [class.show]=\"isVisible && isAnimating\"\n      *ngIf=\"isVisible\"\n    >\n      <!-- Modal Header -->\n      <div class=\"modal-header\">\n        <div class=\"modal-title-section\">\n          <h2 class=\"modal-title\">{{ title }}</h2>\n          <p class=\"modal-subtitle\" *ngIf=\"subtitle\">{{ subtitle }}</p>\n        </div>\n        <button\n          class=\"close-button\"\n          (click)=\"close()\"\n          type=\"button\"\n          aria-label=\"Close modal\"\n        >\n          <svg viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n            <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\"></line>\n            <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\"></line>\n          </svg>\n        </button>\n      </div>\n\n      <!-- Modal Content -->\n      <div class=\"modal-content\">\n        <ng-content></ng-content>\n      </div>\n\n      <!-- Modal Footer -->\n      <div class=\"modal-footer\" *ngIf=\"showFooter\">\n        <div class=\"footer-actions\">\n          <app-button\n            variant=\"ghost\"\n            (clicked)=\"cancel()\"\n            [disabled]=\"loading\"\n          >\n            {{ cancelText }}\n          </app-button>\n          <app-button\n            variant=\"primary\"\n            (clicked)=\"confirm()\"\n            [loading]=\"loading\"\n            [disabled]=\"!canConfirm\"\n          >\n            {{ confirmText }}\n          </app-button>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    /* Modal Backdrop */\n    .modal-backdrop {\n      position: fixed;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background: rgba(0, 0, 0, 0.5);\n      z-index: 9998;\n      opacity: 0;\n      transition: opacity 0.3s ease;\n\n      &.show {\n        opacity: 1;\n      }\n    }\n\n    /* Modal Panel */\n    .modal-panel {\n      position: fixed;\n      top: 0;\n      right: 0;\n      bottom: 0;\n      width: 600px;\n      max-width: 90vw;\n      background: white;\n      box-shadow: var(--shadow-xl);\n      z-index: 9999;\n      display: flex;\n      flex-direction: column;\n      transform: translateX(100%);\n      transition: transform 0.3s ease;\n\n      &.show {\n        transform: translateX(0);\n      }\n    }\n\n    /* Modal Header */\n    .modal-header {\n      display: flex;\n      align-items: flex-start;\n      justify-content: space-between;\n      padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg);\n      border-bottom: 1px solid var(--secondary-200);\n      flex-shrink: 0;\n    }\n\n    .modal-title-section {\n      flex: 1;\n      margin-right: var(--spacing-md);\n    }\n\n    .modal-title {\n      margin: 0 0 var(--spacing-xs) 0;\n      font-size: var(--text-xl);\n      font-weight: 600;\n      color: var(--secondary-900);\n      line-height: var(--leading-tight);\n    }\n\n    .modal-subtitle {\n      margin: 0;\n      font-size: var(--text-sm);\n      color: var(--secondary-600);\n      line-height: var(--leading-relaxed);\n    }\n\n    .close-button {\n      background: none;\n      border: none;\n      color: var(--secondary-400);\n      cursor: pointer;\n      padding: var(--spacing-sm);\n      border-radius: var(--radius-md);\n      transition: all 0.2s ease;\n      flex-shrink: 0;\n\n      &:hover {\n        color: var(--secondary-600);\n        background: var(--secondary-100);\n      }\n\n      svg {\n        width: 20px;\n        height: 20px;\n      }\n    }\n\n    /* Modal Content */\n    .modal-content {\n      flex: 1;\n      overflow-y: auto;\n      padding: var(--spacing-lg) var(--spacing-xl);\n    }\n\n    /* Modal Footer */\n    .modal-footer {\n      padding: var(--spacing-lg) var(--spacing-xl);\n      border-top: 1px solid var(--secondary-200);\n      flex-shrink: 0;\n    }\n\n    .footer-actions {\n      display: flex;\n      justify-content: flex-end;\n      gap: var(--spacing-md);\n    }\n\n    /* Responsive */\n    @media (max-width: 768px) {\n      .modal-panel {\n        width: 100vw;\n        max-width: none;\n      }\n\n      .modal-header {\n        padding: var(--spacing-lg);\n      }\n\n      .modal-content {\n        padding: var(--spacing-md) var(--spacing-lg);\n      }\n\n      .modal-footer {\n        padding: var(--spacing-lg);\n      }\n\n      .footer-actions {\n        flex-direction: column-reverse;\n        gap: var(--spacing-sm);\n      }\n\n      .footer-actions app-button {\n        width: 100%;\n      }\n    }\n  `]\n})\nexport class SlideModalComponent implements OnInit, OnDestroy, OnChanges {\n  @Input() isOpen = false;\n  @Input() title = '';\n  @Input() subtitle = '';\n  @Input() showFooter = true;\n  @Input() confirmText = 'Save';\n  @Input() cancelText = 'Cancel';\n  @Input() loading = false;\n  @Input() canConfirm = true;\n  @Input() closeOnBackdrop = true;\n\n  @Output() opened = new EventEmitter<void>();\n  @Output() closed = new EventEmitter<void>();\n  @Output() confirmed = new EventEmitter<void>();\n  @Output() cancelled = new EventEmitter<void>();\n\n  isVisible = false;\n  isAnimating = false;\n  private animationTimeout?: number;\n\n  constructor(\n    @Inject(PLATFORM_ID) private platformId: Object,\n    @Inject(DOCUMENT) private document: Document\n  ) {}\n\n  ngOnInit(): void {\n    if (this.isOpen) {\n      this.handleOpen();\n    }\n  }\n\n  ngOnDestroy(): void {\n    this.handleClose();\n    if (isPlatformBrowser(this.platformId) && this.animationTimeout) {\n      clearTimeout(this.animationTimeout);\n    }\n  }\n\n  ngOnChanges(): void {\n    if (this.isOpen && !this.isVisible) {\n      this.handleOpen();\n    } else if (!this.isOpen && this.isVisible) {\n      this.handleClose();\n    }\n  }\n\n  private handleOpen(): void {\n    this.isVisible = true;\n\n    // Only manipulate DOM in browser environment\n    if (isPlatformBrowser(this.platformId)) {\n      this.document.body.style.overflow = 'hidden';\n\n      // Trigger animation after DOM update\n      setTimeout(() => {\n        this.isAnimating = true;\n      }, 10);\n    }\n\n    this.opened.emit();\n  }\n\n  private handleClose(): void {\n    this.isAnimating = false;\n\n    // Only use timers and DOM manipulation in browser environment\n    if (isPlatformBrowser(this.platformId)) {\n      // Wait for animation to complete before hiding\n      this.animationTimeout = window.setTimeout(() => {\n        this.isVisible = false;\n        this.document.body.style.overflow = '';\n        this.closed.emit();\n      }, 300); // Match CSS transition duration\n    } else {\n      // In SSR, immediately close without animation\n      this.isVisible = false;\n      this.closed.emit();\n    }\n  }\n\n  onBackdropClick(): void {\n    if (this.closeOnBackdrop) {\n      this.close();\n    }\n  }\n\n  close(): void {\n    this.closed.emit();\n  }\n\n  confirm(): void {\n    this.confirmed.emit();\n  }\n\n  cancel(): void {\n    this.cancelled.emit();\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUI,IAAA,yBAAA,GAAA,OAAA,CAAA;AAGE,IAAA,qBAAA,SAAA,SAAA,0DAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,gBAAA,CAAiB;IAAA,CAAA;AAE3B,IAAA,uBAAA;;;;AAHC,IAAA,sBAAA,QAAA,OAAA,aAAA,OAAA,WAAA;;;;;AAeI,IAAA,yBAAA,GAAA,KAAA,EAAA;AAA2C,IAAA,iBAAA,CAAA;AAAc,IAAA,uBAAA;;;;AAAd,IAAA,oBAAA;AAAA,IAAA,4BAAA,OAAA,QAAA;;;;;;AAqB/C,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA6C,GAAA,OAAA,EAAA,EACf,GAAA,cAAA,EAAA;AAGxB,IAAA,qBAAA,WAAA,SAAA,0EAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAW,OAAA,OAAA,CAAQ;IAAA,CAAA;AAGnB,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,cAAA,EAAA;AAEE,IAAA,qBAAA,WAAA,SAAA,0EAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAW,OAAA,QAAA,CAAS;IAAA,CAAA;AAIpB,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA,EAAa,EACT;;;;AAZF,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,YAAA,OAAA,OAAA;AAEA,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,YAAA,GAAA;AAKA,IAAA,oBAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,OAAA,EAAmB,YAAA,CAAA,OAAA,UAAA;AAGnB,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,aAAA,GAAA;;;;;;AA7CR,IAAA,yBAAA,GAAA,OAAA,CAAA,EAIC,GAAA,OAAA,CAAA,EAE2B,GAAA,OAAA,CAAA,EACS,GAAA,MAAA,CAAA;AACP,IAAA,iBAAA,CAAA;AAAW,IAAA,uBAAA;AACnC,IAAA,qBAAA,GAAA,wCAAA,GAAA,GAAA,KAAA,CAAA;AACF,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,UAAA,CAAA;AAEE,IAAA,qBAAA,SAAA,SAAA,6DAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,MAAA,CAAO;IAAA,CAAA;;AAIhB,IAAA,yBAAA,GAAA,OAAA,CAAA;AACE,IAAA,oBAAA,GAAA,QAAA,EAAA,EAA2C,GAAA,QAAA,EAAA;AAE7C,IAAA,uBAAA,EAAM,EACC;;AAIX,IAAA,yBAAA,IAAA,OAAA,EAAA;AACE,IAAA,uBAAA,EAAA;AACF,IAAA,uBAAA;AAGA,IAAA,qBAAA,IAAA,2CAAA,GAAA,GAAA,OAAA,EAAA;AAmBF,IAAA,uBAAA;;;;AA/CE,IAAA,sBAAA,QAAA,OAAA,aAAA,OAAA,WAAA;AAM4B,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,KAAA;AACG,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,QAAA;AAqBJ,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,UAAA;;;AAiK3B,IAAO,sBAAP,MAAO,qBAAmB;EAqBC;EACH;EArBnB,SAAS;EACT,QAAQ;EACR,WAAW;EACX,aAAa;EACb,cAAc;EACd,aAAa;EACb,UAAU;EACV,aAAa;EACb,kBAAkB;EAEjB,SAAS,IAAI,aAAY;EACzB,SAAS,IAAI,aAAY;EACzB,YAAY,IAAI,aAAY;EAC5B,YAAY,IAAI,aAAY;EAEtC,YAAY;EACZ,cAAc;EACN;EAER,YAC+B,YACH,UAAkB;AADf,SAAA,aAAA;AACH,SAAA,WAAA;EACzB;EAEH,WAAQ;AACN,QAAI,KAAK,QAAQ;AACf,WAAK,WAAU;IACjB;EACF;EAEA,cAAW;AACT,SAAK,YAAW;AAChB,QAAI,kBAAkB,KAAK,UAAU,KAAK,KAAK,kBAAkB;AAC/D,mBAAa,KAAK,gBAAgB;IACpC;EACF;EAEA,cAAW;AACT,QAAI,KAAK,UAAU,CAAC,KAAK,WAAW;AAClC,WAAK,WAAU;IACjB,WAAW,CAAC,KAAK,UAAU,KAAK,WAAW;AACzC,WAAK,YAAW;IAClB;EACF;EAEQ,aAAU;AAChB,SAAK,YAAY;AAGjB,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,WAAK,SAAS,KAAK,MAAM,WAAW;AAGpC,iBAAW,MAAK;AACd,aAAK,cAAc;MACrB,GAAG,EAAE;IACP;AAEA,SAAK,OAAO,KAAI;EAClB;EAEQ,cAAW;AACjB,SAAK,cAAc;AAGnB,QAAI,kBAAkB,KAAK,UAAU,GAAG;AAEtC,WAAK,mBAAmB,OAAO,WAAW,MAAK;AAC7C,aAAK,YAAY;AACjB,aAAK,SAAS,KAAK,MAAM,WAAW;AACpC,aAAK,OAAO,KAAI;MAClB,GAAG,GAAG;IACR,OAAO;AAEL,WAAK,YAAY;AACjB,WAAK,OAAO,KAAI;IAClB;EACF;EAEA,kBAAe;AACb,QAAI,KAAK,iBAAiB;AACxB,WAAK,MAAK;IACZ;EACF;EAEA,QAAK;AACH,SAAK,OAAO,KAAI;EAClB;EAEA,UAAO;AACL,SAAK,UAAU,KAAI;EACrB;EAEA,SAAM;AACJ,SAAK,UAAU,KAAI;EACrB;;qCAhGW,sBAAmB,4BAqBpB,WAAW,GAAA,4BACX,QAAQ,CAAA;EAAA;yEAtBP,sBAAmB,WAAA,CAAA,CAAA,iBAAA,CAAA,GAAA,QAAA,EAAA,QAAA,UAAA,OAAA,SAAA,UAAA,YAAA,YAAA,cAAA,aAAA,eAAA,YAAA,cAAA,SAAA,WAAA,YAAA,cAAA,iBAAA,kBAAA,GAAA,SAAA,EAAA,QAAA,UAAA,QAAA,UAAA,WAAA,aAAA,WAAA,YAAA,GAAA,UAAA,CAAA,8BAAA,GAAA,oBAAA,KAAA,OAAA,GAAA,MAAA,GAAA,QAAA,CAAA,CAAA,SAAA,kBAAA,GAAA,QAAA,SAAA,GAAA,MAAA,GAAA,CAAA,SAAA,eAAA,GAAA,QAAA,GAAA,MAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,SAAA,kBAAA,GAAA,MAAA,GAAA,CAAA,QAAA,UAAA,cAAA,eAAA,GAAA,gBAAA,GAAA,OAAA,GAAA,CAAA,WAAA,aAAA,QAAA,QAAA,UAAA,cAAA,GAAA,CAAA,MAAA,MAAA,MAAA,KAAA,MAAA,KAAA,MAAA,IAAA,GAAA,CAAA,MAAA,KAAA,MAAA,KAAA,MAAA,MAAA,MAAA,IAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,SAAA,gBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,WAAA,SAAA,GAAA,WAAA,UAAA,GAAA,CAAA,WAAA,WAAA,GAAA,WAAA,WAAA,UAAA,CAAA,GAAA,UAAA,SAAA,6BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;;AAvM5B,MAAA,qBAAA,GAAA,oCAAA,GAAA,GAAA,OAAA,CAAA,EAKC,GAAA,oCAAA,IAAA,GAAA,OAAA,CAAA;;;AADE,MAAA,qBAAA,QAAA,IAAA,SAAA;AAOA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,SAAA;;oBAdK,cAAY,MAAE,eAAe,GAAA,QAAA,CAAA,0xFAAA,EAAA,CAAA;;;sEA0M5B,qBAAmB,CAAA;UA7M/B;uBACW,mBAAiB,YACf,MAAI,SACP,CAAC,cAAc,eAAe,GAAC,UAC9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA4DT,QAAA,CAAA,4lFAAA,EAAA,CAAA;;UAkKE;WAAO,WAAW;;UAClB;WAAO,QAAQ;WArBT,QAAM,CAAA;UAAd;MACQ,OAAK,CAAA;UAAb;MACQ,UAAQ,CAAA;UAAhB;MACQ,YAAU,CAAA;UAAlB;MACQ,aAAW,CAAA;UAAnB;MACQ,YAAU,CAAA;UAAlB;MACQ,SAAO,CAAA;UAAf;MACQ,YAAU,CAAA;UAAlB;MACQ,iBAAe,CAAA;UAAvB;MAES,QAAM,CAAA;UAAf;MACS,QAAM,CAAA;UAAf;MACS,WAAS,CAAA;UAAlB;MACS,WAAS,CAAA;UAAlB;;;;6EAdU,qBAAmB,EAAA,WAAA,uBAAA,UAAA,kEAAA,YAAA,IAAA,CAAA;AAAA,GAAA;", "names": []}