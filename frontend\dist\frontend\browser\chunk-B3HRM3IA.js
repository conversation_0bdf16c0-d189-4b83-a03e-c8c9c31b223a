import {
  BaseApiService
} from "./chunk-BUYEQKW2.js";
import {
  Router
} from "./chunk-IKU57TF7.js";
import {
  BehaviorSubject,
  Inject,
  Injectable,
  PLATFORM_ID,
  catchError,
  delay,
  isPlatformBrowser,
  map,
  of,
  setClassMetadata,
  tap,
  ɵɵdefineInjectable,
  ɵɵinject
} from "./chunk-3JQLQ36P.js";

// src/app/modules/auth/services/auth.service.ts
var AuthService = class _AuthService {
  router;
  baseApi;
  platformId;
  currentUserSubject = new BehaviorSubject(null);
  currentUser$ = this.currentUserSubject.asObservable();
  isLoadingSubject = new BehaviorSubject(false);
  isLoading$ = this.isLoadingSubject.asObservable();
  constructor(router, baseApi, platformId) {
    this.router = router;
    this.baseApi = baseApi;
    this.platformId = platformId;
    this.loadUserFromStorage();
  }
  loadUserFromStorage() {
    if (!isPlatformBrowser(this.platformId))
      return;
    const userStr = localStorage.getItem("user") || sessionStorage.getItem("user");
    if (userStr) {
      try {
        const user = JSON.parse(userStr);
        this.currentUserSubject.next(user);
      } catch (e) {
        this.logout();
      }
    }
  }
  login(credentials) {
    this.isLoadingSubject.next(true);
    const mockResponse = {
      user: {
        id: "1",
        email: credentials.email,
        name: "John Doe",
        role: "admin"
      },
      token: "mock-jwt-token"
    };
    return of(mockResponse).pipe(delay(1e3), map((response) => {
      const storage = credentials.rememberMe ? localStorage : sessionStorage;
      if (isPlatformBrowser(this.platformId)) {
        storage.setItem("auth_token", response.token);
        storage.setItem("user", JSON.stringify(response.user));
      }
      this.currentUserSubject.next(response.user);
      return response.user;
    }), tap(() => {
      this.isLoadingSubject.next(false);
      this.router.navigate(["/dashboard"]);
    }), catchError((error) => {
      this.isLoadingSubject.next(false);
      throw error;
    }));
  }
  logout() {
    if (isPlatformBrowser(this.platformId)) {
      localStorage.removeItem("auth_token");
      localStorage.removeItem("user");
      sessionStorage.removeItem("auth_token");
      sessionStorage.removeItem("user");
    }
    this.currentUserSubject.next(null);
    this.router.navigate(["/auth/login"]);
  }
  isAuthenticated() {
    return !!this.currentUserSubject.value;
  }
  static \u0275fac = function AuthService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _AuthService)(\u0275\u0275inject(Router), \u0275\u0275inject(BaseApiService), \u0275\u0275inject(PLATFORM_ID));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _AuthService, factory: _AuthService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AuthService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: Router }, { type: BaseApiService }, { type: Object, decorators: [{
    type: Inject,
    args: [PLATFORM_ID]
  }] }], null);
})();

export {
  AuthService
};
//# sourceMappingURL=chunk-B3HRM3IA.js.map
