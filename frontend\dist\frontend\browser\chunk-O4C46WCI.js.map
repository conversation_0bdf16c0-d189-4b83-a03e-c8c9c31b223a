{"version": 3, "sources": ["src/app/modules/dependencies/dependency-health/dependency-health.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { CardComponent } from '../../../shared/components/card/card.component';\n\n@Component({\n  selector: 'app-dependency-health',\n  standalone: true,\n  imports: [CommonModule, CardComponent],\n  template: `\n    <div class=\"dependency-health-page\">\n      <h1>Dependency Health</h1>\n      <app-card title=\"Health Check\" subtitle=\"Monitor dependency health status\">\n        <div class=\"placeholder\">\n          <p>Dependency health monitoring coming soon...</p>\n        </div>\n      </app-card>\n    </div>\n  `,\n  styles: [`\n    .dependency-health-page {\n      min-height: 100%;\n    }\n    .placeholder {\n      padding: var(--spacing-xl);\n      text-align: center;\n      color: var(--secondary-600);\n    }\n  `]\n})\nexport class DependencyHealthComponent {}\n"], "mappings": ";;;;;;;;;;;;;;;;AA6BM,IAAO,4BAAP,MAAO,2BAAyB;;qCAAzB,4BAAyB;EAAA;yEAAzB,4BAAyB,WAAA,CAAA,CAAA,uBAAA,CAAA,GAAA,OAAA,GAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,wBAAA,GAAA,CAAA,SAAA,gBAAA,YAAA,kCAAA,GAAA,CAAA,GAAA,aAAA,CAAA,GAAA,UAAA,SAAA,mCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AApBlC,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAoC,GAAA,IAAA;AAC9B,MAAA,iBAAA,GAAA,mBAAA;AAAiB,MAAA,uBAAA;AACrB,MAAA,yBAAA,GAAA,YAAA,CAAA,EAA2E,GAAA,OAAA,CAAA,EAChD,GAAA,GAAA;AACpB,MAAA,iBAAA,GAAA,6CAAA;AAA2C,MAAA,uBAAA,EAAI,EAC9C,EACG;;oBARL,cAAc,aAAa,GAAA,QAAA,CAAA,mQAAA,EAAA,CAAA;;;sEAsB1B,2BAAyB,CAAA;UAzBrC;uBACW,yBAAuB,YACrB,MAAI,SACP,CAAC,cAAc,aAAa,GAAC,UAC5B;;;;;;;;;KAST,QAAA,CAAA,odAAA,EAAA,CAAA;;;;6EAYU,2BAAyB,EAAA,WAAA,6BAAA,UAAA,iFAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}