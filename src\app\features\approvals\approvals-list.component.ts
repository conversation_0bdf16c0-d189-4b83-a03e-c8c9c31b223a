import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ApprovalService, ApprovalRequest } from '../../core/services/approval.service';
import { ApplicationService } from '../applications/services/application.service';
import { Router } from '@angular/router';

@Component({
  selector: 'app-approvals-list',
  templateUrl: './approvals-list.component.html',
  styleUrls: ['./approvals-list.component.scss'],
  standalone: true,
  imports: [CommonModule]
})
export class ApprovalsListComponent implements OnInit {
  approvalRequests: ApprovalRequest[] = [];
  loading = true;

  constructor(
    private approvalService: ApprovalService,
    private applicationService: ApplicationService,
    private router: Router
  ) { }

  ngOnInit(): void {
    this.loadApprovalRequests();
  }

  loadApprovalRequests(): void {
    this.loading = true;
    this.approvalService.getApprovalRequests().subscribe({
      next: (requests) => {
        this.approvalRequests = requests;
        this.loading = false;
      },
      error: (error) => {
        console.error('Failed to load approval requests:', error);
        this.loading = false;
      }
    });
  }

  approve(request: ApprovalRequest): void {
    this.approvalService.approveRequest(
      request.id,
      'current-user', // In real app, get from auth service
      'Approved'
    ).subscribe({
      next: (updatedRequest) => {
        // Apply the approved changes to the application
        this.applicationService.applyApprovedChanges(updatedRequest).subscribe({
          next: (application) => {
            console.log('Application updated successfully:', application);
            this.loadApprovalRequests(); // Refresh the list
          },
          error: (error) => {
            console.error('Failed to apply approved changes:', error);
          }
        });
      },
      error: (error) => {
        console.error('Failed to approve request:', error);
      }
    });
  }

  reject(request: ApprovalRequest): void {
    this.approvalService.rejectRequest(
      request.id,
      'current-user', // In real app, get from auth service
      'Rejected'
    ).subscribe({
      next: () => {
        console.log('Request rejected successfully');
        this.loadApprovalRequests(); // Refresh the list
      },
      error: (error) => {
        console.error('Failed to reject request:', error);
      }
    });
  }

  viewApplication(applicationId: string): void {
    this.router.navigate(['/applications', applicationId]);
  }
}