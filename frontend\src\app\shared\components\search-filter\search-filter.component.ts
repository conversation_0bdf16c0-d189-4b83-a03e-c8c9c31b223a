import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { FormInputComponent } from '../form-input/form-input.component';
import { ButtonComponent } from '../button/button.component';

export interface FilterOption {
  value: string;
  label: string;
  count?: number;
}

export interface FilterConfig {
  key: string;
  label: string;
  type: 'select' | 'multiselect' | 'text' | 'date';
  options?: FilterOption[];
  placeholder?: string;
}

export interface SearchFilterState {
  search: string;
  filters: { [key: string]: any };
}

@Component({
  selector: 'app-search-filter',
  standalone: true,
  imports: [CommonModule, FormsModule, FormInputComponent, ButtonComponent],
  template: `
    <div class="search-filter">
      <!-- Search Bar -->
      <div class="search-section">
        <div class="search-input">
          <app-form-input
            type="text"
            placeholder="Search applications..."
            [(ngModel)]="searchTerm"
            (ngModelChange)="onSearchChange()"
            leftIcon="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
          ></app-form-input>
        </div>
        <app-button
          variant="outline"
          leftIcon="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z"
          (click)="toggleFilters()"
          [class.active]="showFilters"
        >
          Filters
          <span class="filter-count" *ngIf="activeFilterCount > 0">{{ activeFilterCount }}</span>
        </app-button>
      </div>

      <!-- Filters Panel -->
      <div class="filters-panel" [class.show]="showFilters" *ngIf="filterConfigs.length > 0">
        <div class="filters-grid">
          <div class="filter-group" *ngFor="let config of filterConfigs">
            <label class="filter-label">{{ config.label }}</label>

            <!-- Single Select -->
            <select
              *ngIf="config.type === 'select'"
              class="filter-select"
              [(ngModel)]="filters[config.key]"
              (ngModelChange)="onFilterChange()"
            >
              <option value="">All {{ config.label }}</option>
              <option *ngFor="let option of config.options" [value]="option.value">
                {{ option.label }}
                <span *ngIf="option.count !== undefined">({{ option.count }})</span>
              </option>
            </select>

            <!-- Multi Select -->
            <div *ngIf="config.type === 'multiselect'" class="multiselect">
              <div class="multiselect-options">
                <label class="checkbox-option" *ngFor="let option of config.options">
                  <input
                    type="checkbox"
                    [value]="option.value"
                    [checked]="isOptionSelected(config.key, option.value)"
                    (change)="onMultiSelectChange(config.key, option.value, $event)"
                  >
                  <span class="checkbox-label">
                    {{ option.label }}
                    <span class="option-count" *ngIf="option.count !== undefined">({{ option.count }})</span>
                  </span>
                </label>
              </div>
            </div>

            <!-- Text Input -->
            <app-form-input
              *ngIf="config.type === 'text'"
              type="text"
              [placeholder]="config.placeholder || 'Enter ' + config.label.toLowerCase()"
              [(ngModel)]="filters[config.key]"
              (ngModelChange)="onFilterChange()"
            ></app-form-input>

            <!-- Date Input -->
            <app-form-input
              *ngIf="config.type === 'date'"
              type="date"
              [(ngModel)]="filters[config.key]"
              (ngModelChange)="onFilterChange()"
            ></app-form-input>
          </div>
        </div>

        <div class="filters-actions">
          <app-button variant="ghost" size="sm" (click)="clearFilters()">
            Clear All
          </app-button>
          <app-button variant="outline" size="sm" (click)="toggleFilters()">
            Close
          </app-button>
        </div>
      </div>

      <!-- Active Filters Display -->
      <div class="active-filters" *ngIf="activeFilterCount > 0">
        <div class="active-filter-tags">
          <span class="filter-tag" *ngFor="let filter of getActiveFilters()">
            {{ filter.label }}: {{ filter.value }}
            <button class="remove-filter" (click)="removeFilter(filter.key)" aria-label="Remove filter">
              <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </span>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .search-filter {
      background: white;
      border: 1px solid var(--secondary-200);
      border-radius: var(--radius-lg);
      overflow: hidden;
    }

    .search-section {
      display: flex;
      gap: var(--spacing-md);
      padding: var(--spacing-lg);
      align-items: flex-start;
    }

    .search-input {
      flex: 1;
    }

    .filter-count {
      background: var(--primary-500);
      color: white;
      border-radius: 50%;
      width: 18px;
      height: 18px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 10px;
      font-weight: 600;
      margin-left: var(--spacing-xs);
    }

    .filters-panel {
      border-top: 1px solid var(--secondary-200);
      padding: var(--spacing-lg);
      background: var(--neutral-50);
      display: none;

      &.show {
        display: block;
      }
    }

    .filters-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--spacing-lg);
      margin-bottom: var(--spacing-lg);
    }

    .filter-group {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-sm);
    }

    .filter-label {
      font-size: var(--text-sm);
      font-weight: 500;
      color: var(--secondary-700);
    }

    .filter-select {
      padding: var(--spacing-sm) var(--spacing-md);
      border: 1px solid var(--secondary-300);
      border-radius: var(--radius-md);
      background: white;
      font-size: var(--text-sm);
      color: var(--secondary-900);

      &:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }
    }

    .multiselect-options {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-xs);
      max-height: 150px;
      overflow-y: auto;
      padding: var(--spacing-sm);
      border: 1px solid var(--secondary-300);
      border-radius: var(--radius-md);
      background: white;
    }

    .checkbox-option {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      cursor: pointer;
      padding: var(--spacing-xs);
      border-radius: var(--radius-sm);
      transition: background-color 0.2s ease;

      &:hover {
        background: var(--neutral-100);
      }
    }

    .checkbox-option input[type="checkbox"] {
      margin: 0;
    }

    .checkbox-label {
      font-size: var(--text-sm);
      color: var(--secondary-700);
      flex: 1;
    }

    .option-count {
      color: var(--secondary-500);
      font-size: var(--text-xs);
    }

    .filters-actions {
      display: flex;
      justify-content: flex-end;
      gap: var(--spacing-sm);
      padding-top: var(--spacing-md);
      border-top: 1px solid var(--secondary-200);
    }

    .active-filters {
      padding: var(--spacing-md) var(--spacing-lg);
      border-top: 1px solid var(--secondary-200);
      background: var(--primary-50);
    }

    .active-filter-tags {
      display: flex;
      flex-wrap: wrap;
      gap: var(--spacing-sm);
    }

    .filter-tag {
      display: inline-flex;
      align-items: center;
      gap: var(--spacing-xs);
      padding: var(--spacing-xs) var(--spacing-sm);
      background: var(--primary-100);
      color: var(--primary-700);
      border-radius: var(--radius-sm);
      font-size: var(--text-xs);
      font-weight: 500;
    }

    .remove-filter {
      background: none;
      border: none;
      color: var(--primary-600);
      cursor: pointer;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      width: 16px;
      height: 16px;
      transition: all 0.2s ease;

      &:hover {
        background: var(--primary-200);
        color: var(--primary-800);
      }
    }

    .active {
      background: var(--primary-100);
      color: var(--primary-700);
      border-color: var(--primary-300);
    }

    @media (max-width: 768px) {
      .search-section {
        flex-direction: column;
        gap: var(--spacing-md);
      }

      .filters-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
      }

      .filters-actions {
        justify-content: stretch;
      }

      .filters-actions app-button {
        flex: 1;
      }
    }
  `]
})
export class SearchFilterComponent implements OnInit {
  @Input() filterConfigs: FilterConfig[] = [];
  @Input() initialState: SearchFilterState = { search: '', filters: {} };
  @Output() stateChange = new EventEmitter<SearchFilterState>();

  searchTerm = '';
  filters: { [key: string]: any } = {};
  showFilters = false;

  ngOnInit(): void {
    this.searchTerm = this.initialState.search;
    this.filters = { ...this.initialState.filters };
  }

  onSearchChange(): void {
    this.emitStateChange();
  }

  onFilterChange(): void {
    this.emitStateChange();
  }

  onMultiSelectChange(key: string, value: string, event: any): void {
    if (!this.filters[key]) {
      this.filters[key] = [];
    }

    if (event.target.checked) {
      this.filters[key] = [...this.filters[key], value];
    } else {
      this.filters[key] = this.filters[key].filter((v: string) => v !== value);
    }

    this.onFilterChange();
  }

  isOptionSelected(key: string, value: string): boolean {
    return this.filters[key] && this.filters[key].includes(value);
  }

  toggleFilters(): void {
    this.showFilters = !this.showFilters;
  }

  clearFilters(): void {
    this.searchTerm = '';
    this.filters = {};
    this.emitStateChange();
  }

  removeFilter(key: string): void {
    delete this.filters[key];
    this.emitStateChange();
  }

  get activeFilterCount(): number {
    return Object.keys(this.filters).filter(key => {
      const value = this.filters[key];
      return value && (Array.isArray(value) ? value.length > 0 : value !== '');
    }).length;
  }

  getActiveFilters(): Array<{ key: string; label: string; value: string }> {
    const activeFilters: Array<{ key: string; label: string; value: string }> = [];

    Object.keys(this.filters).forEach(key => {
      const value = this.filters[key];
      if (value && (Array.isArray(value) ? value.length > 0 : value !== '')) {
        const config = this.filterConfigs.find(c => c.key === key);
        if (config) {
          if (Array.isArray(value)) {
            value.forEach(v => {
              const option = config.options?.find(o => o.value === v);
              activeFilters.push({
                key,
                label: config.label,
                value: option?.label || v
              });
            });
          } else {
            const option = config.options?.find(o => o.value === value);
            activeFilters.push({
              key,
              label: config.label,
              value: option?.label || value
            });
          }
        }
      }
    });

    return activeFilters;
  }

  private emitStateChange(): void {
    this.stateChange.emit({
      search: this.searchTerm,
      filters: { ...this.filters }
    });
  }
}
