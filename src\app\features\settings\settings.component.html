<div class="settings-container">
  <h2>Application Settings</h2>

  <section class="custom-fields-section">
    <h3>Custom Fields</h3>
    <div class="field-list">
      <div class="field-item" *ngFor="let field of customFields; let i = index">
        <div class="field-header">
          <span class="field-name">{{ field.name }}</span>
          <span class="field-type">{{ field.type }}</span>
          <span class="field-required" *ngIf="field.required">Required</span>
          <button class="remove-button" (click)="removeCustomField(i)">Remove</button>
        </div>
        <div class="field-options" *ngIf="field.options">
          Options: {{ field.options.join(', ') }}
        </div>
      </div>
    </div>

    <form [formGroup]="newFieldForm" (ngSubmit)="addCustomField()" class="add-form">
      <div class="form-group">
        <input type="text" formControlName="name" placeholder="Field Name" class="form-control">
      </div>
      <div class="form-group">
        <select formControlName="type" class="form-control">
          <option value="text">Text</option>
          <option value="number">Number</option>
          <option value="select">Select</option>
        </select>
      </div>
      <div class="form-group" *ngIf="newFieldForm.get('type')?.value === 'select'">
        <input type="text" formControlName="options" placeholder="Options (comma-separated)" class="form-control">
      </div>
      <div class="form-group checkbox">
        <label>
          <input type="checkbox" formControlName="required"> Required
        </label>
      </div>
      <button type="submit" class="add-button" [disabled]="!newFieldForm.valid">Add Custom Field</button>
    </form>
  </section>

  <section class="validation-rules-section">
    <h3>Validation Rules</h3>
    <div class="rules-list">
      <div class="rule-item" *ngFor="let rule of validationRules; let i = index">
        <div class="rule-header">
          <span class="rule-field">{{ rule.field }}</span>
          <button class="remove-button" (click)="removeValidationRule(i)">Remove</button>
        </div>
        <div class="rule-pattern">Pattern: {{ rule.rule }}</div>
        <div class="rule-message">{{ rule.message }}</div>
      </div>
    </div>

    <form [formGroup]="newRuleForm" (ngSubmit)="addValidationRule()" class="add-form">
      <div class="form-group">
        <input type="text" formControlName="field" placeholder="Field Name" class="form-control">
      </div>
      <div class="form-group">
        <input type="text" formControlName="rule" placeholder="Validation Pattern" class="form-control">
      </div>
      <div class="form-group">
        <input type="text" formControlName="message" placeholder="Error Message" class="form-control">
      </div>
      <button type="submit" class="add-button" [disabled]="!newRuleForm.valid">Add Validation Rule</button>
    </form>
  </section>

  <div class="actions">
    <button class="save-button" (click)="saveSettings()">Save Settings</button>
  </div>
</div>