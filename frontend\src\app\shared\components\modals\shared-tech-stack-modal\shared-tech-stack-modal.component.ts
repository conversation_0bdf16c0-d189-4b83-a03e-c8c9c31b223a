import { Component, Input, Output, EventEmitter, OnInit, OnChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { SlideModalComponent } from '../../slide-modal/slide-modal.component';
import { FormInputComponent } from '../../form-input/form-input.component';
import { ApplicationSelectionService, ApplicationOption } from '../../../services/application-selection.service';
import { Observable } from 'rxjs';

export interface SharedTechStackFormData {
  applicationId?: number;
  name: string;
  category: string;
  version: string;
  description: string;
  purpose: string;
  status: string;
  maintainer: string;
  licenseType: string;
  repository?: string;
  documentation?: string;
}

@Component({
  selector: 'app-shared-tech-stack-modal',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, SlideModalComponent, FormInputComponent],
  template: `
    <app-slide-modal
      [isOpen]="isOpen"
      [title]="editMode ? 'Edit Technology' : 'Add Technology'"
      [subtitle]="showApplicationSelection ? 'Configure technology stack details and select application' : 'Configure technology stack details'"
      [loading]="loading"
      [canConfirm]="techStackForm.valid"
      confirmText="Save Technology"
      (closed)="onClose()"
      (confirmed)="onSave()"
      (cancelled)="onClose()"
    >
      <form [formGroup]="techStackForm" class="tech-stack-form">
        <!-- Application Selection (only when not in applications module) -->
        <div class="form-group" *ngIf="showApplicationSelection">
          <label class="form-label">Application *</label>
          <select formControlName="applicationId" class="form-select">
            <option value="">Select an application</option>
            <option *ngFor="let app of applicationOptions$ | async" [value]="app.id">
              {{ app.name }} - {{ app.department }}
            </option>
          </select>
          <div class="field-error" *ngIf="getFieldError('applicationId')">
            {{ getFieldError('applicationId') }}
          </div>
        </div>

        <div class="form-grid">
          <div class="form-group">
            <label class="form-label">Category</label>
            <select formControlName="category" class="form-select">
              <option value="frontend">Frontend</option>
              <option value="backend">Backend</option>
              <option value="database">Database</option>
              <option value="infrastructure">Infrastructure</option>
              <option value="devops">DevOps</option>
              <option value="testing">Testing</option>
              <option value="monitoring">Monitoring</option>
              <option value="security">Security</option>
              <option value="analytics">Analytics</option>
              <option value="communication">Communication</option>
            </select>
          </div>

          <app-form-input
            label="Technology Name"
            placeholder="e.g., React, Node.js, PostgreSQL"
            formControlName="name"
            [required]="true"
            [errorMessage]="getFieldError('name')"
          ></app-form-input>

          <app-form-input
            label="Version"
            placeholder="e.g., 18.2.0, 16.x, latest"
            formControlName="version"
            [required]="true"
            [errorMessage]="getFieldError('version')"
          ></app-form-input>

          <div class="form-group">
            <label class="form-label">Purpose</label>
            <select formControlName="purpose" class="form-select">
              <option value="core">Core Technology</option>
              <option value="development">Development Tool</option>
              <option value="testing">Testing Framework</option>
              <option value="deployment">Deployment Tool</option>
              <option value="monitoring">Monitoring Solution</option>
              <option value="security">Security Tool</option>
              <option value="performance">Performance Optimization</option>
              <option value="integration">Integration</option>
              <option value="other">Other</option>
            </select>
          </div>

          <div class="form-group">
            <label class="form-label">Status</label>
            <select formControlName="status" class="form-select">
              <option value="active">Active</option>
              <option value="planned">Planned</option>
              <option value="deprecated">Deprecated</option>
              <option value="evaluating">Evaluating</option>
              <option value="migrating">Migrating</option>
            </select>
          </div>

          <app-form-input
            label="Maintainer/Vendor"
            placeholder="e.g., Facebook, Google, Internal Team"
            formControlName="maintainer"
            [required]="true"
            [errorMessage]="getFieldError('maintainer')"
          ></app-form-input>

          <div class="form-group">
            <label class="form-label">License Type</label>
            <select formControlName="licenseType" class="form-select">
              <option value="MIT">MIT</option>
              <option value="Apache-2.0">Apache 2.0</option>
              <option value="GPL-3.0">GPL 3.0</option>
              <option value="BSD-3-Clause">BSD 3-Clause</option>
              <option value="ISC">ISC</option>
              <option value="MPL-2.0">Mozilla Public License 2.0</option>
              <option value="proprietary">Proprietary</option>
              <option value="commercial">Commercial</option>
              <option value="other">Other</option>
            </select>
          </div>

          <app-form-input
            label="Repository URL"
            placeholder="https://github.com/..."
            formControlName="repository"
            [errorMessage]="getFieldError('repository')"
          ></app-form-input>

          <app-form-input
            label="Documentation URL"
            placeholder="https://docs.example.com/..."
            formControlName="documentation"
            [errorMessage]="getFieldError('documentation')"
          ></app-form-input>

          <div class="form-group full-width">
            <label class="form-label">Description</label>
            <textarea 
              formControlName="description"
              class="form-textarea"
              placeholder="Describe how this technology is used in the application..."
              rows="3"
            ></textarea>
          </div>
        </div>
      </form>
    </app-slide-modal>
  `,
  styles: [`
    .tech-stack-form {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-lg);
    }

    .form-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-lg);
    }

    .form-group {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-sm);
    }

    .form-group.full-width {
      grid-column: 1 / -1;
    }

    .form-label {
      font-size: var(--text-sm);
      font-weight: 500;
      color: var(--secondary-700);
    }

    .form-select,
    .form-textarea {
      height: 40px;
      padding: 0 var(--spacing-md);
      border: 1px solid var(--secondary-300);
      border-radius: var(--radius-md);
      background: white;
      font-size: var(--text-sm);
      color: var(--secondary-800);
      transition: all 0.2s ease;

      &:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px var(--primary-100);
      }
    }

    .form-textarea {
      height: auto;
      padding: var(--spacing-sm) var(--spacing-md);
      resize: vertical;
      min-height: 80px;
    }

    .field-error {
      font-size: var(--text-sm);
      color: var(--error-600);
    }

    @media (max-width: 768px) {
      .form-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
      }
    }
  `]
})
export class SharedTechStackModalComponent implements OnInit, OnChanges {
  @Input() isOpen = false;
  @Input() editMode = false;
  @Input() initialData: SharedTechStackFormData | null = null;
  @Input() loading = false;
  @Input() showApplicationSelection = true; // Hide when used within applications module

  @Output() closed = new EventEmitter<void>();
  @Output() saved = new EventEmitter<SharedTechStackFormData>();

  techStackForm!: FormGroup;
  applicationOptions$: Observable<ApplicationOption[]>;

  constructor(
    private fb: FormBuilder,
    private applicationSelectionService: ApplicationSelectionService
  ) {
    this.applicationOptions$ = this.applicationSelectionService.getApplicationOptions();
  }

  ngOnInit(): void {
    this.initializeForm();
  }

  ngOnChanges(): void {
    if (this.techStackForm && this.initialData) {
      this.techStackForm.patchValue(this.initialData);
    }
  }

  private initializeForm(): void {
    const formConfig: any = {
      category: ['frontend', [Validators.required]],
      name: ['', [Validators.required, Validators.minLength(2)]],
      version: ['', [Validators.required]],
      description: [''],
      purpose: ['core', [Validators.required]],
      status: ['active', [Validators.required]],
      maintainer: ['', [Validators.required]],
      licenseType: ['MIT', [Validators.required]],
      repository: [''],
      documentation: ['']
    };

    if (this.showApplicationSelection) {
      formConfig.applicationId = ['', [Validators.required]];
    }

    this.techStackForm = this.fb.group(formConfig);

    if (this.initialData) {
      this.techStackForm.patchValue(this.initialData);
    }
  }

  getFieldError(fieldName: string): string {
    const field = this.techStackForm.get(fieldName);
    if (field && field.invalid && field.touched) {
      if (field.errors?.['required']) {
        return `${fieldName} is required`;
      }
      if (field.errors?.['minlength']) {
        return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;
      }
      if (field.errors?.['url']) {
        return `${fieldName} must be a valid URL`;
      }
    }
    return '';
  }

  onSave(): void {
    if (this.techStackForm.valid) {
      const formData: SharedTechStackFormData = this.techStackForm.value;
      this.saved.emit(formData);
    }
  }

  onClose(): void {
    this.closed.emit();
  }
}
