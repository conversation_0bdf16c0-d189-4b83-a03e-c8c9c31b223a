{"version": 3, "sources": ["src/app/shared/components/tabs/tabs.component.ts", "src/app/modules/applications/components/dependency-modal/dependency-modal.component.ts", "src/app/modules/applications/components/tech-stack-modal/tech-stack-modal.component.ts", "src/app/modules/applications/components/documentation-modal/documentation-modal.component.ts", "src/app/modules/applications/components/vulnerability-modal/vulnerability-modal.component.ts", "src/app/modules/applications/application-form/application-form.component.ts"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, ContentChildren, QueryList, AfterContentInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\nexport interface TabItem {\n  id: string;\n  label: string;\n  icon?: string;\n  disabled?: boolean;\n  badge?: string | number;\n  hasError?: boolean;\n}\n\n@Component({\n  selector: 'app-tab',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <div class=\"tab-content\" [class.active]=\"active\">\n      <ng-content></ng-content>\n    </div>\n  `,\n  styles: [`\n    .tab-content {\n      display: none;\n      \n      &.active {\n        display: block;\n      }\n    }\n  `]\n})\nexport class TabComponent {\n  @Input() id = '';\n  @Input() label = '';\n  @Input() icon = '';\n  @Input() disabled = false;\n  @Input() badge: string | number = '';\n  @Input() hasError = false;\n  @Input() active = false;\n}\n\n@Component({\n  selector: 'app-tabs',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <div class=\"tabs-container\" [class]=\"containerClasses\">\n      <!-- Tab Navigation -->\n      <div class=\"tabs-nav\" [class.tabs-nav-vertical]=\"orientation === 'vertical'\">\n        <button\n          *ngFor=\"let tab of tabs; trackBy: trackByTab\"\n          class=\"tab-button\"\n          [class.active]=\"tab.id === activeTabId\"\n          [class.disabled]=\"tab.disabled\"\n          [class.has-error]=\"tab.hasError\"\n          [disabled]=\"tab.disabled\"\n          (click)=\"selectTab(tab.id)\"\n          [attr.aria-selected]=\"tab.id === activeTabId\"\n          [attr.aria-controls]=\"'tab-panel-' + tab.id\"\n          role=\"tab\"\n        >\n          <!-- Icon -->\n          <svg *ngIf=\"tab.icon\" class=\"tab-icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" [attr.d]=\"tab.icon\" />\n          </svg>\n\n          <!-- Label -->\n          <span class=\"tab-label\">{{ tab.label }}</span>\n\n          <!-- Badge -->\n          <span *ngIf=\"tab.badge\" class=\"tab-badge\" [class.error-badge]=\"tab.hasError\">\n            {{ tab.badge }}\n          </span>\n\n          <!-- Error Indicator -->\n          <svg *ngIf=\"tab.hasError && !tab.badge\" class=\"tab-error-icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n            <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n            <line x1=\"15\" y1=\"9\" x2=\"9\" y2=\"15\"></line>\n            <line x1=\"9\" y1=\"9\" x2=\"15\" y2=\"15\"></line>\n          </svg>\n        </button>\n      </div>\n\n      <!-- Tab Content -->\n      <div class=\"tabs-content\">\n        <ng-content></ng-content>\n      </div>\n    </div>\n  `,\n  styleUrls: ['./tabs.component.scss']\n})\nexport class TabsComponent implements AfterContentInit {\n  @Input() orientation: 'horizontal' | 'vertical' = 'horizontal';\n  @Input() size: 'sm' | 'md' | 'lg' = 'md';\n  @Input() variant: 'default' | 'pills' | 'underline' = 'default';\n  @Input() activeTabId = '';\n  @Input() tabs: TabItem[] = [];\n\n  @Output() tabChange = new EventEmitter<string>();\n\n  @ContentChildren(TabComponent) tabComponents!: QueryList<TabComponent>;\n\n  ngAfterContentInit(): void {\n    // If no active tab is set, activate the first non-disabled tab\n    if (!this.activeTabId && this.tabs.length > 0) {\n      const firstEnabledTab = this.tabs.find(tab => !tab.disabled);\n      if (firstEnabledTab) {\n        this.activeTabId = firstEnabledTab.id;\n      }\n    }\n\n    // Update tab components\n    this.updateTabComponents();\n  }\n\n  ngOnChanges(): void {\n    if (this.tabComponents) {\n      this.updateTabComponents();\n    }\n  }\n\n  get containerClasses(): string {\n    const classes = [\n      'tabs',\n      `tabs-${this.size}`,\n      `tabs-${this.variant}`,\n      `tabs-${this.orientation}`\n    ];\n\n    return classes.join(' ');\n  }\n\n  selectTab(tabId: string): void {\n    const tab = this.tabs.find(t => t.id === tabId);\n    if (!tab || tab.disabled) return;\n\n    this.activeTabId = tabId;\n    this.updateTabComponents();\n    this.tabChange.emit(tabId);\n  }\n\n  private updateTabComponents(): void {\n    if (this.tabComponents) {\n      this.tabComponents.forEach(tabComponent => {\n        tabComponent.active = tabComponent.id === this.activeTabId;\n      });\n    }\n  }\n\n  trackByTab(index: number, tab: TabItem): string {\n    return tab.id;\n  }\n\n  // Public methods for external control\n  public setActiveTab(tabId: string): void {\n    this.selectTab(tabId);\n  }\n\n  public getActiveTab(): string {\n    return this.activeTabId;\n  }\n\n  public setTabError(tabId: string, hasError: boolean): void {\n    const tab = this.tabs.find(t => t.id === tabId);\n    if (tab) {\n      tab.hasError = hasError;\n    }\n  }\n\n  public setTabBadge(tabId: string, badge: string | number): void {\n    const tab = this.tabs.find(t => t.id === tabId);\n    if (tab) {\n      tab.badge = badge;\n    }\n  }\n\n  public disableTab(tabId: string, disabled: boolean = true): void {\n    const tab = this.tabs.find(t => t.id === tabId);\n    if (tab) {\n      tab.disabled = disabled;\n      \n      // If disabling the active tab, switch to the next available tab\n      if (disabled && this.activeTabId === tabId) {\n        const nextTab = this.tabs.find(t => t.id !== tabId && !t.disabled);\n        if (nextTab) {\n          this.selectTab(nextTab.id);\n        }\n      }\n    }\n  }\n}\n", "import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';\nimport { SlideModalComponent } from '../../../../shared/components/slide-modal/slide-modal.component';\nimport { FormInputComponent } from '../../../../shared/components/form-input/form-input.component';\n\nexport interface DependencyFormData {\n  name: string;\n  type: string;\n  version: string;\n  criticality: string;\n  isInternal: boolean;\n  description: string;\n}\n\n@Component({\n  selector: 'app-dependency-modal',\n  standalone: true,\n  imports: [CommonModule, ReactiveFormsModule, SlideModalComponent, FormInputComponent],\n  template: `\n    <app-slide-modal\n      [isOpen]=\"isOpen\"\n      [title]=\"editMode ? 'Edit Dependency' : 'Add Dependency'\"\n      subtitle=\"Configure application dependency details\"\n      [loading]=\"loading\"\n      [canConfirm]=\"dependencyForm.valid\"\n      confirmText=\"Save Dependency\"\n      (closed)=\"onClose()\"\n      (confirmed)=\"onSave()\"\n      (cancelled)=\"onClose()\"\n    >\n      <form [formGroup]=\"dependencyForm\" class=\"dependency-form\">\n        <div class=\"form-grid\">\n          <app-form-input\n            label=\"Dependency Name\"\n            placeholder=\"e.g., Angular, Express.js\"\n            formControlName=\"name\"\n            [required]=\"true\"\n            [errorMessage]=\"getFieldError('name')\"\n          ></app-form-input>\n\n          <div class=\"form-group\">\n            <label class=\"form-label\">Type</label>\n            <select formControlName=\"type\" class=\"form-select\">\n              <option value=\"library\">Library</option>\n              <option value=\"framework\">Framework</option>\n              <option value=\"service\">Service</option>\n              <option value=\"database\">Database</option>\n              <option value=\"api\">API</option>\n              <option value=\"tool\">Tool</option>\n              <option value=\"infrastructure\">Infrastructure</option>\n            </select>\n          </div>\n\n          <app-form-input\n            label=\"Version\"\n            placeholder=\"e.g., 16.2.0, ^4.18.2\"\n            formControlName=\"version\"\n            [required]=\"true\"\n            [errorMessage]=\"getFieldError('version')\"\n          ></app-form-input>\n\n          <div class=\"form-group\">\n            <label class=\"form-label\">Criticality</label>\n            <select formControlName=\"criticality\" class=\"form-select\">\n              <option value=\"low\">Low</option>\n              <option value=\"medium\">Medium</option>\n              <option value=\"high\">High</option>\n              <option value=\"critical\">Critical</option>\n            </select>\n          </div>\n\n          <div class=\"form-group full-width\">\n            <label class=\"checkbox-label\">\n              <input\n                type=\"checkbox\"\n                formControlName=\"isInternal\"\n                class=\"checkbox-input\"\n              >\n              <span class=\"checkbox-text\">Internal Dependency</span>\n              <span class=\"checkbox-description\">This is an internal company service or library</span>\n            </label>\n          </div>\n\n          <div class=\"form-group full-width\">\n            <label class=\"form-label\">Description</label>\n            <textarea\n              formControlName=\"description\"\n              class=\"form-textarea\"\n              placeholder=\"Describe the purpose and usage of this dependency...\"\n              rows=\"3\"\n            ></textarea>\n          </div>\n        </div>\n      </form>\n    </app-slide-modal>\n  `,\n  styles: [`\n    .dependency-form {\n      display: flex;\n      flex-direction: column;\n      gap: var(--spacing-lg);\n    }\n\n    .form-grid {\n      display: grid;\n      grid-template-columns: 1fr 1fr;\n      gap: var(--spacing-lg);\n    }\n\n    .form-group {\n      display: flex;\n      flex-direction: column;\n      gap: var(--spacing-sm);\n    }\n\n    .form-group.full-width {\n      grid-column: 1 / -1;\n    }\n\n    .form-label {\n      font-size: var(--text-sm);\n      font-weight: 500;\n      color: var(--secondary-700);\n    }\n\n    .form-select,\n    .form-textarea {\n      border: 1px solid var(--secondary-300);\n      border-radius: var(--radius-md);\n      background: white;\n      font-size: var(--text-sm);\n      color: var(--secondary-800);\n      transition: all 0.2s ease;\n\n      &:focus {\n        outline: none;\n        border-color: var(--primary-500);\n        box-shadow: 0 0 0 3px var(--primary-100);\n      }\n    }\n\n    .form-select {\n      height: 40px;\n      padding: 0 var(--spacing-md);\n      cursor: pointer;\n      background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e\");\n      background-position: right var(--spacing-sm) center;\n      background-repeat: no-repeat;\n      background-size: 16px;\n      padding-right: 40px;\n      appearance: none;\n    }\n\n    .form-textarea {\n      padding: var(--spacing-md);\n      resize: vertical;\n      min-height: 80px;\n      font-family: inherit;\n    }\n\n    .checkbox-label {\n      display: flex;\n      align-items: flex-start;\n      gap: var(--spacing-sm);\n      cursor: pointer;\n      padding: var(--spacing-md);\n      border: 1px solid var(--secondary-200);\n      border-radius: var(--radius-md);\n      transition: all 0.2s ease;\n\n      &:hover {\n        border-color: var(--primary-300);\n        background: var(--primary-25);\n      }\n    }\n\n    .checkbox-input {\n      margin: 0;\n      margin-top: 2px;\n    }\n\n    .checkbox-text {\n      font-size: var(--text-sm);\n      font-weight: 500;\n      color: var(--secondary-900);\n      margin-bottom: var(--spacing-xs);\n    }\n\n    .checkbox-description {\n      font-size: var(--text-xs);\n      color: var(--secondary-600);\n      display: block;\n    }\n\n    @media (max-width: 768px) {\n      .form-grid {\n        grid-template-columns: 1fr;\n        gap: var(--spacing-md);\n      }\n    }\n  `]\n})\nexport class DependencyModalComponent implements OnInit {\n  @Input() isOpen = false;\n  @Input() editMode = false;\n  @Input() initialData: DependencyFormData | null = null;\n  @Input() loading = false;\n\n  @Output() closed = new EventEmitter<void>();\n  @Output() saved = new EventEmitter<DependencyFormData>();\n\n  dependencyForm!: FormGroup;\n\n  constructor(private fb: FormBuilder) {}\n\n  ngOnInit(): void {\n    this.initializeForm();\n  }\n\n  ngOnChanges(): void {\n    if (this.dependencyForm && this.initialData) {\n      this.dependencyForm.patchValue(this.initialData);\n    }\n  }\n\n  private initializeForm(): void {\n    this.dependencyForm = this.fb.group({\n      name: ['', [Validators.required, Validators.minLength(2)]],\n      type: ['library', [Validators.required]],\n      version: ['', [Validators.required]],\n      criticality: ['medium', [Validators.required]],\n      isInternal: [false],\n      description: ['']\n    });\n\n    if (this.initialData) {\n      this.dependencyForm.patchValue(this.initialData);\n    }\n  }\n\n  getFieldError(fieldName: string): string {\n    const field = this.dependencyForm.get(fieldName);\n    if (field && field.invalid && field.touched) {\n      if (field.errors?.['required']) {\n        return `${fieldName} is required`;\n      }\n      if (field.errors?.['minlength']) {\n        return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;\n      }\n    }\n    return '';\n  }\n\n  onClose(): void {\n    this.dependencyForm.reset();\n    this.closed.emit();\n  }\n\n  onSave(): void {\n    if (this.dependencyForm.valid) {\n      this.saved.emit(this.dependencyForm.value);\n    }\n  }\n}\n", "import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';\nimport { SlideModalComponent } from '../../../../shared/components/slide-modal/slide-modal.component';\nimport { FormInputComponent } from '../../../../shared/components/form-input/form-input.component';\n\nexport interface TechStackFormData {\n  category: string;\n  technology: string;\n  version: string;\n  purpose: string;\n  isCore: boolean;\n}\n\n@Component({\n  selector: 'app-tech-stack-modal',\n  standalone: true,\n  imports: [CommonModule, ReactiveFormsModule, SlideModalComponent, FormInputComponent],\n  template: `\n    <app-slide-modal\n      [isOpen]=\"isOpen\"\n      [title]=\"editMode ? 'Edit Technology' : 'Add Technology'\"\n      subtitle=\"Configure technology stack details\"\n      [loading]=\"loading\"\n      [canConfirm]=\"techStackForm.valid\"\n      confirmText=\"Save Technology\"\n      (closed)=\"onClose()\"\n      (confirmed)=\"onSave()\"\n      (cancelled)=\"onClose()\"\n    >\n      <form [formGroup]=\"techStackForm\" class=\"tech-stack-form\">\n        <div class=\"form-grid\">\n          <div class=\"form-group\">\n            <label class=\"form-label\">Category</label>\n            <select formControlName=\"category\" class=\"form-select\">\n              <option value=\"frontend\">Frontend</option>\n              <option value=\"backend\">Backend</option>\n              <option value=\"database\">Database</option>\n              <option value=\"infrastructure\">Infrastructure</option>\n              <option value=\"monitoring\">Monitoring</option>\n              <option value=\"security\">Security</option>\n              <option value=\"testing\">Testing</option>\n              <option value=\"deployment\">Deployment</option>\n              <option value=\"communication\">Communication</option>\n            </select>\n          </div>\n\n          <app-form-input\n            label=\"Technology\"\n            placeholder=\"e.g., React, Node.js, PostgreSQL\"\n            formControlName=\"technology\"\n            [required]=\"true\"\n            [errorMessage]=\"getFieldError('technology')\"\n          ></app-form-input>\n\n          <app-form-input\n            label=\"Version\"\n            placeholder=\"e.g., 18.2.0, 16.x\"\n            formControlName=\"version\"\n            [required]=\"true\"\n            [errorMessage]=\"getFieldError('version')\"\n          ></app-form-input>\n\n          <div class=\"form-group\">\n            <label class=\"checkbox-label\">\n              <input\n                type=\"checkbox\"\n                formControlName=\"isCore\"\n                class=\"checkbox-input\"\n              >\n              <span class=\"checkbox-text\">Core Technology</span>\n              <span class=\"checkbox-description\">This is a core/critical technology for the application</span>\n            </label>\n          </div>\n\n          <div class=\"form-group full-width\">\n            <label class=\"form-label\">Purpose</label>\n            <textarea\n              formControlName=\"purpose\"\n              class=\"form-textarea\"\n              placeholder=\"Describe how this technology is used in the application...\"\n              rows=\"3\"\n            ></textarea>\n          </div>\n        </div>\n      </form>\n    </app-slide-modal>\n  `,\n  styles: [`\n    .tech-stack-form {\n      display: flex;\n      flex-direction: column;\n      gap: var(--spacing-lg);\n    }\n\n    .form-grid {\n      display: grid;\n      grid-template-columns: 1fr 1fr;\n      gap: var(--spacing-lg);\n    }\n\n    .form-group {\n      display: flex;\n      flex-direction: column;\n      gap: var(--spacing-sm);\n    }\n\n    .form-group.full-width {\n      grid-column: 1 / -1;\n    }\n\n    .form-label {\n      font-size: var(--text-sm);\n      font-weight: 500;\n      color: var(--secondary-700);\n    }\n\n    .form-select,\n    .form-textarea {\n      border: 1px solid var(--secondary-300);\n      border-radius: var(--radius-md);\n      background: white;\n      font-size: var(--text-sm);\n      color: var(--secondary-800);\n      transition: all 0.2s ease;\n\n      &:focus {\n        outline: none;\n        border-color: var(--primary-500);\n        box-shadow: 0 0 0 3px var(--primary-100);\n      }\n    }\n\n    .form-select {\n      height: 40px;\n      padding: 0 var(--spacing-md);\n      cursor: pointer;\n      background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e\");\n      background-position: right var(--spacing-sm) center;\n      background-repeat: no-repeat;\n      background-size: 16px;\n      padding-right: 40px;\n      appearance: none;\n    }\n\n    .form-textarea {\n      padding: var(--spacing-md);\n      resize: vertical;\n      min-height: 80px;\n      font-family: inherit;\n    }\n\n    .checkbox-label {\n      display: flex;\n      align-items: flex-start;\n      gap: var(--spacing-sm);\n      cursor: pointer;\n      padding: var(--spacing-md);\n      border: 1px solid var(--secondary-200);\n      border-radius: var(--radius-md);\n      transition: all 0.2s ease;\n\n      &:hover {\n        border-color: var(--primary-300);\n        background: var(--primary-25);\n      }\n    }\n\n    .checkbox-input {\n      margin: 0;\n      margin-top: 2px;\n    }\n\n    .checkbox-text {\n      font-size: var(--text-sm);\n      font-weight: 500;\n      color: var(--secondary-900);\n      margin-bottom: var(--spacing-xs);\n    }\n\n    .checkbox-description {\n      font-size: var(--text-xs);\n      color: var(--secondary-600);\n      display: block;\n    }\n\n    @media (max-width: 768px) {\n      .form-grid {\n        grid-template-columns: 1fr;\n        gap: var(--spacing-md);\n      }\n    }\n  `]\n})\nexport class TechStackModalComponent implements OnInit {\n  @Input() isOpen = false;\n  @Input() editMode = false;\n  @Input() initialData: TechStackFormData | null = null;\n  @Input() loading = false;\n\n  @Output() closed = new EventEmitter<void>();\n  @Output() saved = new EventEmitter<TechStackFormData>();\n\n  techStackForm!: FormGroup;\n\n  constructor(private fb: FormBuilder) {}\n\n  ngOnInit(): void {\n    this.initializeForm();\n  }\n\n  ngOnChanges(): void {\n    if (this.techStackForm && this.initialData) {\n      this.techStackForm.patchValue(this.initialData);\n    }\n  }\n\n  private initializeForm(): void {\n    this.techStackForm = this.fb.group({\n      category: ['frontend', [Validators.required]],\n      technology: ['', [Validators.required, Validators.minLength(2)]],\n      version: ['', [Validators.required]],\n      purpose: ['', [Validators.required]],\n      isCore: [false]\n    });\n\n    if (this.initialData) {\n      this.techStackForm.patchValue(this.initialData);\n    }\n  }\n\n  getFieldError(fieldName: string): string {\n    const field = this.techStackForm.get(fieldName);\n    if (field && field.invalid && field.touched) {\n      if (field.errors?.['required']) {\n        return `${fieldName} is required`;\n      }\n      if (field.errors?.['minlength']) {\n        return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;\n      }\n    }\n    return '';\n  }\n\n  onClose(): void {\n    this.techStackForm.reset();\n    this.closed.emit();\n  }\n\n  onSave(): void {\n    if (this.techStackForm.valid) {\n      this.saved.emit(this.techStackForm.value);\n    }\n  }\n}\n", "import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';\nimport { SlideModalComponent } from '../../../../shared/components/slide-modal/slide-modal.component';\nimport { FormInputComponent } from '../../../../shared/components/form-input/form-input.component';\n\nexport interface DocumentationFormData {\n  title: string;\n  type: string;\n  version: string;\n  url: string;\n  isPublic: boolean;\n  description: string;\n}\n\n@Component({\n  selector: 'app-documentation-modal',\n  standalone: true,\n  imports: [CommonModule, ReactiveFormsModule, SlideModalComponent, FormInputComponent],\n  template: `\n    <app-slide-modal\n      [isOpen]=\"isOpen\"\n      [title]=\"editMode ? 'Edit Documentation' : 'Add Documentation'\"\n      subtitle=\"Configure documentation details\"\n      [loading]=\"loading\"\n      [canConfirm]=\"documentationForm.valid\"\n      confirmText=\"Save Documentation\"\n      (closed)=\"onClose()\"\n      (confirmed)=\"onSave()\"\n      (cancelled)=\"onClose()\"\n    >\n      <form [formGroup]=\"documentationForm\" class=\"documentation-form\">\n        <div class=\"form-grid\">\n          <app-form-input\n            label=\"Document Title\"\n            placeholder=\"e.g., API Documentation, User Guide\"\n            formControlName=\"title\"\n            [required]=\"true\"\n            [errorMessage]=\"getFieldError('title')\"\n          ></app-form-input>\n\n          <div class=\"form-group\">\n            <label class=\"form-label\">Document Type</label>\n            <select formControlName=\"type\" class=\"form-select\">\n              <option value=\"technical_spec\">Technical Specification</option>\n              <option value=\"api_documentation\">API Documentation</option>\n              <option value=\"user_manual\">User Manual</option>\n              <option value=\"installation_guide\">Installation Guide</option>\n              <option value=\"troubleshooting\">Troubleshooting Guide</option>\n              <option value=\"architecture_diagram\">Architecture Diagram</option>\n              <option value=\"deployment_guide\">Deployment Guide</option>\n              <option value=\"security_policy\">Security Policy</option>\n              <option value=\"runbook\">Runbook</option>\n              <option value=\"other\">Other</option>\n            </select>\n          </div>\n\n          <app-form-input\n            label=\"Version\"\n            placeholder=\"e.g., 1.0, 2.1.0\"\n            formControlName=\"version\"\n            [required]=\"true\"\n            [errorMessage]=\"getFieldError('version')\"\n          ></app-form-input>\n\n          <app-form-input\n            label=\"URL or Location\"\n            placeholder=\"e.g., https://docs.company.com/api\"\n            formControlName=\"url\"\n            [required]=\"true\"\n            [errorMessage]=\"getFieldError('url')\"\n          ></app-form-input>\n\n          <div class=\"form-group full-width\">\n            <label class=\"checkbox-label\">\n              <input \n                type=\"checkbox\" \n                formControlName=\"isPublic\"\n                class=\"checkbox-input\"\n              >\n              <span class=\"checkbox-text\">Public Documentation</span>\n              <span class=\"checkbox-description\">This documentation is publicly accessible</span>\n            </label>\n          </div>\n\n          <div class=\"form-group full-width\">\n            <label class=\"form-label\">Description</label>\n            <textarea \n              formControlName=\"description\"\n              class=\"form-textarea\"\n              placeholder=\"Describe the content and purpose of this documentation...\"\n              rows=\"3\"\n            ></textarea>\n          </div>\n        </div>\n      </form>\n    </app-slide-modal>\n  `,\n  styles: [`\n    .documentation-form {\n      display: flex;\n      flex-direction: column;\n      gap: var(--spacing-lg);\n    }\n\n    .form-grid {\n      display: grid;\n      grid-template-columns: 1fr 1fr;\n      gap: var(--spacing-lg);\n    }\n\n    .form-group {\n      display: flex;\n      flex-direction: column;\n      gap: var(--spacing-sm);\n    }\n\n    .form-group.full-width {\n      grid-column: 1 / -1;\n    }\n\n    .form-label {\n      font-size: var(--text-sm);\n      font-weight: 500;\n      color: var(--secondary-700);\n    }\n\n    .form-select,\n    .form-textarea {\n      padding: var(--spacing-md);\n      border: 1px solid var(--secondary-300);\n      border-radius: var(--radius-md);\n      background: white;\n      font-size: var(--text-sm);\n      color: var(--secondary-800);\n      transition: all 0.2s ease;\n\n      &:focus {\n        outline: none;\n        border-color: var(--primary-500);\n        box-shadow: 0 0 0 3px var(--primary-100);\n      }\n    }\n\n    .form-textarea {\n      resize: vertical;\n      min-height: 80px;\n      font-family: inherit;\n    }\n\n    .checkbox-label {\n      display: flex;\n      align-items: flex-start;\n      gap: var(--spacing-sm);\n      cursor: pointer;\n      padding: var(--spacing-md);\n      border: 1px solid var(--secondary-200);\n      border-radius: var(--radius-md);\n      transition: all 0.2s ease;\n\n      &:hover {\n        border-color: var(--primary-300);\n        background: var(--primary-25);\n      }\n    }\n\n    .checkbox-input {\n      margin: 0;\n      margin-top: 2px;\n    }\n\n    .checkbox-text {\n      font-size: var(--text-sm);\n      font-weight: 500;\n      color: var(--secondary-900);\n      margin-bottom: var(--spacing-xs);\n    }\n\n    .checkbox-description {\n      font-size: var(--text-xs);\n      color: var(--secondary-600);\n      display: block;\n    }\n\n    @media (max-width: 768px) {\n      .form-grid {\n        grid-template-columns: 1fr;\n        gap: var(--spacing-md);\n      }\n    }\n  `]\n})\nexport class DocumentationModalComponent implements OnInit {\n  @Input() isOpen = false;\n  @Input() editMode = false;\n  @Input() initialData: DocumentationFormData | null = null;\n  @Input() loading = false;\n\n  @Output() closed = new EventEmitter<void>();\n  @Output() saved = new EventEmitter<DocumentationFormData>();\n\n  documentationForm!: FormGroup;\n\n  constructor(private fb: FormBuilder) {}\n\n  ngOnInit(): void {\n    this.initializeForm();\n  }\n\n  ngOnChanges(): void {\n    if (this.documentationForm && this.initialData) {\n      this.documentationForm.patchValue(this.initialData);\n    }\n  }\n\n  private initializeForm(): void {\n    this.documentationForm = this.fb.group({\n      title: ['', [Validators.required, Validators.minLength(2)]],\n      type: ['technical_spec', [Validators.required]],\n      version: ['1.0', [Validators.required]],\n      url: ['', [Validators.required]],\n      isPublic: [false],\n      description: ['']\n    });\n\n    if (this.initialData) {\n      this.documentationForm.patchValue(this.initialData);\n    }\n  }\n\n  getFieldError(fieldName: string): string {\n    const field = this.documentationForm.get(fieldName);\n    if (field && field.invalid && field.touched) {\n      if (field.errors?.['required']) {\n        return `${fieldName} is required`;\n      }\n      if (field.errors?.['minlength']) {\n        return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;\n      }\n    }\n    return '';\n  }\n\n  onClose(): void {\n    this.documentationForm.reset();\n    this.closed.emit();\n  }\n\n  onSave(): void {\n    if (this.documentationForm.valid) {\n      this.saved.emit(this.documentationForm.value);\n    }\n  }\n}\n", "import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';\nimport { SlideModalComponent } from '../../../../shared/components/slide-modal/slide-modal.component';\nimport { FormInputComponent } from '../../../../shared/components/form-input/form-input.component';\n\nexport interface VulnerabilityFormData {\n  title: string;\n  severity: string;\n  cveId: string;\n  cvssScore: number;\n  status: string;\n  description: string;\n}\n\n@Component({\n  selector: 'app-vulnerability-modal',\n  standalone: true,\n  imports: [CommonModule, ReactiveFormsModule, SlideModalComponent, FormInputComponent],\n  template: `\n    <app-slide-modal\n      [isOpen]=\"isOpen\"\n      [title]=\"editMode ? 'Edit Vulnerability' : 'Add Vulnerability'\"\n      subtitle=\"Configure security vulnerability details\"\n      [loading]=\"loading\"\n      [canConfirm]=\"vulnerabilityForm.valid\"\n      confirmText=\"Save Vulnerability\"\n      (closed)=\"onClose()\"\n      (confirmed)=\"onSave()\"\n      (cancelled)=\"onClose()\"\n    >\n      <form [formGroup]=\"vulnerabilityForm\" class=\"vulnerability-form\">\n        <div class=\"form-grid\">\n          <app-form-input\n            label=\"Vulnerability Title\"\n            placeholder=\"e.g., SQL Injection in User Search\"\n            formControlName=\"title\"\n            [required]=\"true\"\n            [errorMessage]=\"getFieldError('title')\"\n          ></app-form-input>\n\n          <div class=\"form-group\">\n            <label class=\"form-label\">Severity</label>\n            <select formControlName=\"severity\" class=\"form-select\">\n              <option value=\"low\">Low</option>\n              <option value=\"medium\">Medium</option>\n              <option value=\"high\">High</option>\n              <option value=\"critical\">Critical</option>\n            </select>\n          </div>\n\n          <app-form-input\n            label=\"CVE ID (Optional)\"\n            placeholder=\"e.g., CVE-2024-0001\"\n            formControlName=\"cveId\"\n            [errorMessage]=\"getFieldError('cveId')\"\n          ></app-form-input>\n\n          <app-form-input\n            label=\"CVSS Score\"\n            placeholder=\"e.g., 7.5\"\n            formControlName=\"cvssScore\"\n            type=\"number\"\n            [errorMessage]=\"getFieldError('cvssScore')\"\n          ></app-form-input>\n\n          <div class=\"form-group\">\n            <label class=\"form-label\">Status</label>\n            <select formControlName=\"status\" class=\"form-select\">\n              <option value=\"open\">Open</option>\n              <option value=\"in_progress\">In Progress</option>\n              <option value=\"resolved\">Resolved</option>\n              <option value=\"false_positive\">False Positive</option>\n              <option value=\"accepted_risk\">Accepted Risk</option>\n            </select>\n          </div>\n\n          <div class=\"form-group full-width\">\n            <label class=\"form-label\">Description</label>\n            <textarea \n              formControlName=\"description\"\n              class=\"form-textarea\"\n              placeholder=\"Describe the vulnerability, its impact, and potential remediation steps...\"\n              rows=\"4\"\n            ></textarea>\n          </div>\n        </div>\n      </form>\n    </app-slide-modal>\n  `,\n  styles: [`\n    .vulnerability-form {\n      display: flex;\n      flex-direction: column;\n      gap: var(--spacing-lg);\n    }\n\n    .form-grid {\n      display: grid;\n      grid-template-columns: 1fr 1fr;\n      gap: var(--spacing-lg);\n    }\n\n    .form-group {\n      display: flex;\n      flex-direction: column;\n      gap: var(--spacing-sm);\n    }\n\n    .form-group.full-width {\n      grid-column: 1 / -1;\n    }\n\n    .form-label {\n      font-size: var(--text-sm);\n      font-weight: 500;\n      color: var(--secondary-700);\n    }\n\n    .form-select,\n    .form-textarea {\n      padding: var(--spacing-md);\n      border: 1px solid var(--secondary-300);\n      border-radius: var(--radius-md);\n      background: white;\n      font-size: var(--text-sm);\n      color: var(--secondary-800);\n      transition: all 0.2s ease;\n\n      &:focus {\n        outline: none;\n        border-color: var(--primary-500);\n        box-shadow: 0 0 0 3px var(--primary-100);\n      }\n    }\n\n    .form-textarea {\n      resize: vertical;\n      min-height: 100px;\n      font-family: inherit;\n    }\n\n    @media (max-width: 768px) {\n      .form-grid {\n        grid-template-columns: 1fr;\n        gap: var(--spacing-md);\n      }\n    }\n  `]\n})\nexport class VulnerabilityModalComponent implements OnInit {\n  @Input() isOpen = false;\n  @Input() editMode = false;\n  @Input() initialData: VulnerabilityFormData | null = null;\n  @Input() loading = false;\n\n  @Output() closed = new EventEmitter<void>();\n  @Output() saved = new EventEmitter<VulnerabilityFormData>();\n\n  vulnerabilityForm!: FormGroup;\n\n  constructor(private fb: FormBuilder) {}\n\n  ngOnInit(): void {\n    this.initializeForm();\n  }\n\n  ngOnChanges(): void {\n    if (this.vulnerabilityForm && this.initialData) {\n      this.vulnerabilityForm.patchValue(this.initialData);\n    }\n  }\n\n  private initializeForm(): void {\n    this.vulnerabilityForm = this.fb.group({\n      title: ['', [Validators.required, Validators.minLength(5)]],\n      severity: ['medium', [Validators.required]],\n      cveId: [''],\n      cvssScore: [0, [Validators.min(0), Validators.max(10)]],\n      status: ['open', [Validators.required]],\n      description: ['', [Validators.required, Validators.minLength(10)]]\n    });\n\n    if (this.initialData) {\n      this.vulnerabilityForm.patchValue(this.initialData);\n    }\n  }\n\n  getFieldError(fieldName: string): string {\n    const field = this.vulnerabilityForm.get(fieldName);\n    if (field && field.invalid && field.touched) {\n      if (field.errors?.['required']) {\n        return `${fieldName} is required`;\n      }\n      if (field.errors?.['minlength']) {\n        return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;\n      }\n      if (field.errors?.['min']) {\n        return `${fieldName} must be at least ${field.errors['min'].min}`;\n      }\n      if (field.errors?.['max']) {\n        return `${fieldName} must be at most ${field.errors['max'].max}`;\n      }\n    }\n    return '';\n  }\n\n  onClose(): void {\n    this.vulnerabilityForm.reset();\n    this.closed.emit();\n  }\n\n  onSave(): void {\n    if (this.vulnerabilityForm.valid) {\n      this.saved.emit(this.vulnerabilityForm.value);\n    }\n  }\n}\n", "import { Component, OnInit, ViewChild } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule, ActivatedRoute } from '@angular/router';\nimport { FormBuilder, FormGroup, Validators, ReactiveFormsModule, FormsModule } from '@angular/forms';\nimport { CardComponent } from '../../../shared/components/card/card.component';\nimport { ButtonComponent } from '../../../shared/components/button/button.component';\nimport { TabsComponent, TabComponent, TabItem } from '../../../shared/components/tabs/tabs.component';\nimport { FormInputComponent } from '../../../shared/components/form-input/form-input.component';\nimport { DependencyModalComponent, DependencyFormData } from '../components/dependency-modal/dependency-modal.component';\nimport { TechStackModalComponent, TechStackFormData } from '../components/tech-stack-modal/tech-stack-modal.component';\nimport { StakeholderModalComponent, StakeholderFormData } from '../components/stakeholder-modal/stakeholder-modal.component';\nimport { DocumentationModalComponent, DocumentationFormData } from '../components/documentation-modal/documentation-modal.component';\nimport { VulnerabilityModalComponent, VulnerabilityFormData } from '../components/vulnerability-modal/vulnerability-modal.component';\nimport { Application, LifecycleStatus, BusinessCriticality } from '../../../shared/models/application.model';\n\n@Component({\n  selector: 'app-application-form',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    ReactiveFormsModule,\n    FormsModule,\n    ButtonComponent,\n    TabsComponent,\n    TabComponent,\n    FormInputComponent,\n    DependencyModalComponent,\n    TechStackModalComponent,\n    StakeholderModalComponent,\n    DocumentationModalComponent,\n    VulnerabilityModalComponent\n  ],\n  template: `\n    <div class=\"application-form-page\">\n      <div class=\"page-header\">\n        <div class=\"header-content\">\n          <div class=\"header-info\">\n            <h1 class=\"page-title\">{{ isEditMode ? 'Edit Application' : 'New Application' }}</h1>\n            <p class=\"page-subtitle\">\n              {{ isEditMode ? 'Update application details and configuration.' : 'Register a new application in the catalog.' }}\n            </p>\n          </div>\n          <div class=\"header-actions\">\n            <app-button variant=\"outline\" routerLink=\"/applications\">\n              Cancel\n            </app-button>\n            <app-button\n              variant=\"primary\"\n              [loading]=\"isSaving\"\n              (clicked)=\"saveApplication()\"\n            >\n              {{ isEditMode ? 'Update' : 'Create' }} Application\n            </app-button>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"form-content\">\n        <form [formGroup]=\"applicationForm\" (ngSubmit)=\"saveApplication()\">\n          <app-tabs\n            [tabs]=\"tabItems\"\n            [activeTabId]=\"activeTabId\"\n            (tabChange)=\"onTabChange($event)\"\n            #tabsComponent\n          >\n            <!-- Basic Information Tab -->\n            <app-tab id=\"basic\" label=\"Basic Information\">\n              <div class=\"form-section\">\n                <div class=\"form-grid\">\n                  <app-form-input\n                    label=\"Application Name\"\n                    placeholder=\"Enter application name\"\n                    formControlName=\"name\"\n                    [required]=\"true\"\n                    [errorMessage]=\"getFieldError('name')\"\n                  ></app-form-input>\n\n                  <app-form-input\n                    label=\"Owner\"\n                    placeholder=\"Enter owner name\"\n                    formControlName=\"owner\"\n                    [required]=\"true\"\n                    [errorMessage]=\"getFieldError('owner')\"\n                  ></app-form-input>\n\n                  <app-form-input\n                    type=\"select\"\n                    label=\"Lifecycle Status\"\n                    placeholder=\"Select status\"\n                    formControlName=\"lifecycleStatus\"\n                    [options]=\"lifecycleStatusOptions\"\n                    [required]=\"true\"\n                    [errorMessage]=\"getFieldError('lifecycleStatus')\"\n                  ></app-form-input>\n\n                  <app-form-input\n                    type=\"select\"\n                    label=\"Business Criticality\"\n                    placeholder=\"Select criticality\"\n                    formControlName=\"businessCriticality\"\n                    [options]=\"businessCriticalityOptions\"\n                    [required]=\"true\"\n                    [errorMessage]=\"getFieldError('businessCriticality')\"\n                  ></app-form-input>\n\n                  <app-form-input\n                    label=\"Version\"\n                    placeholder=\"e.g., 1.0.0\"\n                    formControlName=\"version\"\n                    [required]=\"true\"\n                    [errorMessage]=\"getFieldError('version')\"\n                  ></app-form-input>\n\n                  <app-form-input\n                    type=\"url\"\n                    label=\"Repository URL\"\n                    placeholder=\"https://github.com/...\"\n                    formControlName=\"repository\"\n                    [errorMessage]=\"getFieldError('repository')\"\n                  ></app-form-input>\n\n                  <app-form-input\n                    type=\"url\"\n                    label=\"Deployment URL\"\n                    placeholder=\"https://app.company.com\"\n                    formControlName=\"deploymentUrl\"\n                    [errorMessage]=\"getFieldError('deploymentUrl')\"\n                  ></app-form-input>\n                </div>\n\n                <app-form-input\n                  type=\"textarea\"\n                  label=\"Description\"\n                  placeholder=\"Describe the application's purpose and functionality\"\n                  formControlName=\"description\"\n                  [required]=\"true\"\n                  [rows]=\"4\"\n                  [maxlength]=\"500\"\n                  [showCharacterCount]=\"true\"\n                  [errorMessage]=\"getFieldError('description')\"\n                ></app-form-input>\n              </div>\n            </app-tab>\n\n            <!-- Dependencies Tab -->\n            <app-tab id=\"dependencies\" label=\"Dependencies\">\n              <div class=\"form-section\">\n                <div class=\"section-header\">\n                  <h3>Application Dependencies</h3>\n                  <app-button variant=\"outline\" size=\"sm\" (clicked)=\"openDependencyModal()\">\n                    Add Dependency\n                  </app-button>\n                </div>\n\n                <div class=\"entity-list\" *ngIf=\"dependencies.length > 0\">\n                  <div class=\"entity-item\" *ngFor=\"let dep of dependencies; let i = index\">\n                    <div class=\"entity-header\">\n                      <h4 class=\"entity-title\">{{ dep.name }}</h4>\n                      <button class=\"remove-button\" (click)=\"removeDependency(i)\" type=\"button\">\n                        <svg viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n                          <polyline points=\"3,6 5,6 21,6\"></polyline>\n                          <path d=\"m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2\"></path>\n                        </svg>\n                      </button>\n                    </div>\n                    <div class=\"entity-meta\">\n                      <span class=\"entity-badge type-{{ dep.type }}\">{{ dep.type }}</span>\n                      <span class=\"entity-badge criticality-{{ dep.criticality }}\">{{ dep.criticality }}</span>\n                      <span class=\"entity-version\">v{{ dep.version }}</span>\n                      <span class=\"entity-badge\" *ngIf=\"dep.isInternal\">Internal</span>\n                    </div>\n                    <p class=\"entity-description\" *ngIf=\"dep.description\">{{ dep.description }}</p>\n                  </div>\n                </div>\n\n                <div class=\"empty-state\" *ngIf=\"dependencies.length === 0\">\n                  <p>No dependencies added yet. Click \"Add Dependency\" to get started.</p>\n                </div>\n              </div>\n            </app-tab>\n\n            <!-- Tech Stack Tab -->\n            <app-tab id=\"techstack\" label=\"Tech Stack\">\n              <div class=\"form-section\">\n                <div class=\"section-header\">\n                  <h3>Technology Stack</h3>\n                  <app-button variant=\"outline\" size=\"sm\" (clicked)=\"openTechStackModal()\">\n                    Add Technology\n                  </app-button>\n                </div>\n\n                <div class=\"entity-list\" *ngIf=\"techStack.length > 0\">\n                  <div class=\"entity-item\" *ngFor=\"let tech of techStack; let i = index\">\n                    <div class=\"entity-header\">\n                      <h4 class=\"entity-title\">{{ tech.technology }}</h4>\n                      <button class=\"remove-button\" (click)=\"removeTechStack(i)\" type=\"button\">\n                        <svg viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n                          <polyline points=\"3,6 5,6 21,6\"></polyline>\n                          <path d=\"m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2\"></path>\n                        </svg>\n                      </button>\n                    </div>\n                    <div class=\"entity-meta\">\n                      <span class=\"entity-badge\">{{ tech.category }}</span>\n                      <span class=\"entity-version\">v{{ tech.version }}</span>\n                      <span class=\"entity-badge\" *ngIf=\"tech.isCore\">Core</span>\n                    </div>\n                    <p class=\"entity-description\" *ngIf=\"tech.purpose\">{{ tech.purpose }}</p>\n                  </div>\n                </div>\n\n                <div class=\"empty-state\" *ngIf=\"techStack.length === 0\">\n                  <p>No technologies added yet. Click \"Add Technology\" to get started.</p>\n                </div>\n              </div>\n            </app-tab>\n\n            <!-- Stakeholders Tab -->\n            <app-tab id=\"stakeholders\" label=\"Stakeholders\">\n              <div class=\"form-section\">\n                <div class=\"section-header\">\n                  <h3>Stakeholders & Teams</h3>\n                  <app-button variant=\"outline\" size=\"sm\" (clicked)=\"openStakeholderModal()\">\n                    Add Stakeholder\n                  </app-button>\n                </div>\n\n                <div class=\"entity-list\" *ngIf=\"stakeholders.length > 0\">\n                  <div class=\"entity-item\" *ngFor=\"let stakeholder of stakeholders; let i = index\">\n                    <div class=\"entity-header\">\n                      <h4 class=\"entity-title\">{{ stakeholder.name }}</h4>\n                      <button class=\"remove-button\" (click)=\"removeStakeholder(i)\" type=\"button\">\n                        <svg viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n                          <polyline points=\"3,6 5,6 21,6\"></polyline>\n                          <path d=\"m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2\"></path>\n                        </svg>\n                      </button>\n                    </div>\n                    <div class=\"entity-meta\">\n                      <span class=\"entity-badge role-{{ stakeholder.role }}\">{{ stakeholder.role.replace('_', ' ') }}</span>\n                      <span class=\"entity-badge\">{{ stakeholder.department }}</span>\n                      <span class=\"entity-badge\" *ngIf=\"stakeholder.isPrimary\">Primary Contact</span>\n                    </div>\n                    <p class=\"entity-description\" *ngIf=\"stakeholder.email\">{{ stakeholder.email }}</p>\n                    <p class=\"entity-description\" *ngIf=\"stakeholder.responsibility\">{{ stakeholder.responsibility }}</p>\n                  </div>\n                </div>\n\n                <div class=\"empty-state\" *ngIf=\"stakeholders.length === 0\">\n                  <p>No stakeholders added yet. Click \"Add Stakeholder\" to get started.</p>\n                </div>\n              </div>\n            </app-tab>\n\n            <!-- Documentation Tab -->\n            <app-tab id=\"documentation\" label=\"Documentation\">\n              <div class=\"form-section\">\n                <div class=\"section-header\">\n                  <h3>Documentation & Resources</h3>\n                  <app-button variant=\"outline\" size=\"sm\" (clicked)=\"openDocumentationModal()\">\n                    Add Document\n                  </app-button>\n                </div>\n\n                <div class=\"entity-list\" *ngIf=\"documentation.length > 0\">\n                  <div class=\"entity-item\" *ngFor=\"let doc of documentation; let i = index\">\n                    <div class=\"entity-header\">\n                      <h4 class=\"entity-title\">{{ doc.title }}</h4>\n                      <button class=\"remove-button\" (click)=\"removeDocumentation(i)\" type=\"button\">\n                        <svg viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n                          <polyline points=\"3,6 5,6 21,6\"></polyline>\n                          <path d=\"m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2\"></path>\n                        </svg>\n                      </button>\n                    </div>\n                    <div class=\"entity-meta\">\n                      <span class=\"entity-badge\">{{ doc.type.replace('_', ' ') }}</span>\n                      <span class=\"entity-version\">v{{ doc.version }}</span>\n                      <span class=\"entity-badge\" *ngIf=\"doc.isPublic\">Public</span>\n                      <a [href]=\"doc.url\" target=\"_blank\" class=\"entity-link\" *ngIf=\"doc.url\">View Document</a>\n                    </div>\n                    <p class=\"entity-description\" *ngIf=\"doc.description\">{{ doc.description }}</p>\n                  </div>\n                </div>\n\n                <div class=\"empty-state\" *ngIf=\"documentation.length === 0\">\n                  <p>No documentation added yet. Click \"Add Document\" to get started.</p>\n                </div>\n              </div>\n            </app-tab>\n\n            <!-- Security Tab -->\n            <app-tab id=\"security\" label=\"Security\">\n              <div class=\"form-section\">\n                <div class=\"section-header\">\n                  <h3>Security & Vulnerabilities</h3>\n                  <app-button variant=\"outline\" size=\"sm\" (clicked)=\"openVulnerabilityModal()\">\n                    Add Vulnerability\n                  </app-button>\n                </div>\n\n                <div class=\"entity-list\" *ngIf=\"vulnerabilities.length > 0\">\n                  <div class=\"entity-item\" *ngFor=\"let vuln of vulnerabilities; let i = index\">\n                    <div class=\"entity-header\">\n                      <h4 class=\"entity-title\">{{ vuln.title }}</h4>\n                      <button class=\"remove-button\" (click)=\"removeVulnerability(i)\" type=\"button\">\n                        <svg viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n                          <polyline points=\"3,6 5,6 21,6\"></polyline>\n                          <path d=\"m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2\"></path>\n                        </svg>\n                      </button>\n                    </div>\n                    <div class=\"entity-meta\">\n                      <span class=\"entity-badge criticality-{{ vuln.severity }}\">{{ vuln.severity }}</span>\n                      <span class=\"entity-badge\">{{ vuln.status.replace('_', ' ') }}</span>\n                      <span class=\"entity-version\" *ngIf=\"vuln.cvssScore > 0\">CVSS: {{ vuln.cvssScore }}</span>\n                      <span class=\"entity-badge\" *ngIf=\"vuln.cveId\">{{ vuln.cveId }}</span>\n                    </div>\n                    <p class=\"entity-description\" *ngIf=\"vuln.description\">{{ vuln.description }}</p>\n                  </div>\n                </div>\n\n                <div class=\"empty-state\" *ngIf=\"vulnerabilities.length === 0\">\n                  <p>No vulnerabilities added yet. Click \"Add Vulnerability\" to get started.</p>\n                </div>\n              </div>\n            </app-tab>\n          </app-tabs>\n        </form>\n      </div>\n\n      <!-- Modals -->\n      <app-dependency-modal\n        [isOpen]=\"showDependencyModal\"\n        [editMode]=\"dependencyEditMode\"\n        [initialData]=\"dependencyEditData\"\n        [loading]=\"modalLoading\"\n        (closed)=\"closeDependencyModal()\"\n        (saved)=\"saveDependency($event)\"\n      ></app-dependency-modal>\n\n      <app-tech-stack-modal\n        [isOpen]=\"showTechStackModal\"\n        [editMode]=\"techStackEditMode\"\n        [initialData]=\"techStackEditData\"\n        [loading]=\"modalLoading\"\n        (closed)=\"closeTechStackModal()\"\n        (saved)=\"saveTechStack($event)\"\n      ></app-tech-stack-modal>\n\n      <app-stakeholder-modal\n        [isOpen]=\"showStakeholderModal\"\n        [editMode]=\"stakeholderEditMode\"\n        [initialData]=\"stakeholderEditData\"\n        [loading]=\"modalLoading\"\n        (closed)=\"closeStakeholderModal()\"\n        (saved)=\"saveStakeholder($event)\"\n      ></app-stakeholder-modal>\n\n      <app-documentation-modal\n        [isOpen]=\"showDocumentationModal\"\n        [editMode]=\"documentationEditMode\"\n        [initialData]=\"documentationEditData\"\n        [loading]=\"modalLoading\"\n        (closed)=\"closeDocumentationModal()\"\n        (saved)=\"saveDocumentation($event)\"\n      ></app-documentation-modal>\n\n      <app-vulnerability-modal\n        [isOpen]=\"showVulnerabilityModal\"\n        [editMode]=\"vulnerabilityEditMode\"\n        [initialData]=\"vulnerabilityEditData\"\n        [loading]=\"modalLoading\"\n        (closed)=\"closeVulnerabilityModal()\"\n        (saved)=\"saveVulnerability($event)\"\n      ></app-vulnerability-modal>\n    </div>\n  `,\n  styles: [`\n    .application-form-page {\n      min-height: 100%;\n    }\n\n    .page-header {\n      margin-bottom: var(--spacing-xl);\n    }\n\n    .header-content {\n      display: flex;\n      align-items: flex-start;\n      justify-content: space-between;\n      gap: var(--spacing-lg);\n    }\n\n    .header-info {\n      flex: 1;\n    }\n\n    .page-title {\n      margin: 0 0 var(--spacing-xs) 0;\n      font-size: var(--text-3xl);\n      font-weight: 700;\n      color: var(--secondary-900);\n    }\n\n    .page-subtitle {\n      margin: 0;\n      font-size: var(--text-base);\n      color: var(--secondary-600);\n      line-height: var(--leading-relaxed);\n    }\n\n    .header-actions {\n      display: flex;\n      gap: var(--spacing-sm);\n      flex-shrink: 0;\n    }\n\n    .form-placeholder {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      justify-content: center;\n      padding: var(--spacing-3xl);\n      text-align: center;\n      color: var(--secondary-600);\n    }\n\n    .placeholder-icon {\n      width: 64px;\n      height: 64px;\n      margin-bottom: var(--spacing-lg);\n      color: var(--secondary-400);\n    }\n\n    .form-placeholder h3 {\n      margin-bottom: var(--spacing-md);\n      color: var(--secondary-800);\n    }\n\n    .form-placeholder p {\n      margin-bottom: var(--spacing-md);\n      max-width: 500px;\n    }\n\n    .form-placeholder ul {\n      text-align: left;\n      margin-bottom: var(--spacing-lg);\n    }\n\n    .form-placeholder .note {\n      font-style: italic;\n      color: var(--secondary-500);\n    }\n\n    /* Form Styles */\n    .form-section {\n      padding: var(--spacing-xl);\n    }\n\n    .form-grid {\n      display: grid;\n      grid-template-columns: 1fr 1fr;\n      gap: var(--spacing-xl);\n    }\n\n    .form-group {\n      display: flex;\n      flex-direction: column;\n      gap: var(--spacing-sm);\n    }\n\n    .form-group.full-width {\n      grid-column: 1 / -1;\n    }\n\n    .form-label {\n      font-size: var(--text-sm);\n      font-weight: 500;\n      color: var(--secondary-700);\n      margin-bottom: var(--spacing-sm);\n    }\n\n    .form-select {\n      padding: var(--spacing-md);\n      border: 1px solid var(--secondary-300);\n      border-radius: var(--radius-md);\n      background: white;\n      font-size: var(--text-sm);\n      color: var(--secondary-800);\n      transition: all 0.2s ease;\n\n      &:focus {\n        outline: none;\n        border-color: var(--primary-500);\n        box-shadow: 0 0 0 3px var(--primary-100);\n      }\n    }\n\n    .error-message {\n      font-size: var(--text-xs);\n      color: var(--error-600);\n      margin-top: var(--spacing-xs);\n    }\n\n    /* Section Headers */\n    .section-header {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      margin-bottom: var(--spacing-xl);\n      padding-bottom: var(--spacing-md);\n      border-bottom: 1px solid var(--secondary-200);\n    }\n\n    .section-header h3 {\n      margin: 0;\n      font-size: var(--text-lg);\n      font-weight: 600;\n      color: var(--secondary-900);\n    }\n\n    /* Entity Lists */\n    .entity-list {\n      display: flex;\n      flex-direction: column;\n      gap: var(--spacing-lg);\n    }\n\n    .entity-item {\n      padding: var(--spacing-lg);\n      border: 1px solid var(--secondary-200);\n      border-radius: var(--radius-lg);\n      background: white;\n      position: relative;\n    }\n\n    .entity-header {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      margin-bottom: var(--spacing-md);\n    }\n\n    .entity-title {\n      font-size: var(--text-base);\n      font-weight: 600;\n      color: var(--secondary-900);\n      margin: 0;\n    }\n\n    .entity-meta {\n      display: flex;\n      align-items: center;\n      gap: var(--spacing-md);\n      flex-wrap: wrap;\n    }\n\n    .entity-badge {\n      padding: var(--spacing-xs) var(--spacing-sm);\n      border-radius: var(--radius-sm);\n      font-size: var(--text-xs);\n      font-weight: 500;\n      text-transform: capitalize;\n\n      &.type-library { background: var(--blue-100); color: var(--blue-700); }\n      &.type-framework { background: var(--purple-100); color: var(--purple-700); }\n      &.type-service { background: var(--green-100); color: var(--green-700); }\n      &.type-database { background: var(--orange-100); color: var(--orange-700); }\n\n      &.criticality-critical { background: var(--error-100); color: var(--error-700); }\n      &.criticality-high { background: var(--warning-100); color: var(--warning-700); }\n      &.criticality-medium { background: var(--primary-100); color: var(--primary-700); }\n      &.criticality-low { background: var(--success-100); color: var(--success-700); }\n\n      &.role-product_owner { background: var(--purple-100); color: var(--purple-700); }\n      &.role-developer { background: var(--blue-100); color: var(--blue-700); }\n      &.role-tech_lead { background: var(--indigo-100); color: var(--indigo-700); }\n    }\n\n    .entity-description {\n      margin: var(--spacing-sm) 0 0 0;\n      font-size: var(--text-sm);\n      color: var(--secondary-600);\n      line-height: var(--leading-relaxed);\n    }\n\n    .entity-version {\n      font-size: var(--text-xs);\n      color: var(--secondary-600);\n      font-weight: 500;\n    }\n\n    .entity-link {\n      font-size: var(--text-xs);\n      color: var(--primary-600);\n      text-decoration: none;\n      font-weight: 500;\n\n      &:hover {\n        color: var(--primary-700);\n        text-decoration: underline;\n      }\n    }\n\n    .remove-button {\n      background: none;\n      border: none;\n      color: var(--error-500);\n      cursor: pointer;\n      padding: var(--spacing-xs);\n      border-radius: var(--radius-sm);\n      transition: all 0.2s ease;\n\n      &:hover {\n        background: var(--error-50);\n        color: var(--error-600);\n      }\n\n      svg {\n        width: 16px;\n        height: 16px;\n      }\n    }\n\n    .empty-state {\n      text-align: center;\n      padding: var(--spacing-3xl);\n      color: var(--secondary-500);\n      background: var(--neutral-50);\n      border-radius: var(--radius-lg);\n      border: 2px dashed var(--secondary-200);\n    }\n\n    .empty-state p {\n      margin: 0;\n      font-size: var(--text-sm);\n    }\n\n    @media (max-width: 768px) {\n      .header-content {\n        flex-direction: column;\n        align-items: stretch;\n        gap: var(--spacing-md);\n      }\n\n      .header-actions {\n        justify-content: flex-end;\n      }\n\n      .form-grid {\n        grid-template-columns: 1fr;\n        gap: var(--spacing-lg);\n      }\n\n      .form-section {\n        padding: var(--spacing-lg);\n      }\n\n      .entity-meta {\n        flex-direction: column;\n        align-items: flex-start;\n        gap: var(--spacing-sm);\n      }\n    }\n  `]\n})\nexport class ApplicationFormComponent implements OnInit {\n  @ViewChild('tabsComponent') tabsComponent!: TabsComponent;\n\n  isEditMode = false;\n  isSaving = false;\n  applicationId: number | null = null;\n\n  // Form and data\n  applicationForm!: FormGroup;\n  activeTabId = 'basic';\n\n  // Tab configuration\n  tabItems: TabItem[] = [\n    { id: 'basic', label: 'Basic Information', icon: 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z' },\n    { id: 'dependencies', label: 'Dependencies', icon: 'M8 16l2.879-2.879m0 0a3 3 0 104.243-4.242 3 3 0 00-4.243 4.242zM21 12a9 9 0 11-18 0 9 9 0 0118 0z' },\n    { id: 'techstack', label: 'Tech Stack', icon: 'M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z' },\n    { id: 'stakeholders', label: 'Stakeholders', icon: 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a4 4 0 11-8 0 4 4 0 018 0z' },\n    { id: 'documentation', label: 'Documentation', icon: 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z' },\n    { id: 'security', label: 'Security', icon: 'M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z' }\n  ];\n\n  // Dynamic arrays for related entities\n  dependencies: any[] = [];\n  techStack: any[] = [];\n  stakeholders: any[] = [];\n  documentation: any[] = [];\n  vulnerabilities: any[] = [];\n\n  // Modal state\n  showDependencyModal = false;\n  dependencyEditMode = false;\n  dependencyEditData: DependencyFormData | null = null;\n  dependencyEditIndex = -1;\n\n  showTechStackModal = false;\n  techStackEditMode = false;\n  techStackEditData: TechStackFormData | null = null;\n  techStackEditIndex = -1;\n\n  showStakeholderModal = false;\n  stakeholderEditMode = false;\n  stakeholderEditData: StakeholderFormData | null = null;\n  stakeholderEditIndex = -1;\n\n  showDocumentationModal = false;\n  documentationEditMode = false;\n  documentationEditData: DocumentationFormData | null = null;\n  documentationEditIndex = -1;\n\n  showVulnerabilityModal = false;\n  vulnerabilityEditMode = false;\n  vulnerabilityEditData: VulnerabilityFormData | null = null;\n  vulnerabilityEditIndex = -1;\n\n  modalLoading = false;\n\n  // Dropdown options\n  lifecycleStatusOptions = [\n    { value: 'planning', label: 'Planning' },\n    { value: 'development', label: 'Development' },\n    { value: 'testing', label: 'Testing' },\n    { value: 'staging', label: 'Staging' },\n    { value: 'production', label: 'Production' },\n    { value: 'maintenance', label: 'Maintenance' },\n    { value: 'deprecated', label: 'Deprecated' },\n    { value: 'retired', label: 'Retired' }\n  ];\n\n  businessCriticalityOptions = [\n    { value: 'low', label: 'Low' },\n    { value: 'medium', label: 'Medium' },\n    { value: 'high', label: 'High' },\n    { value: 'critical', label: 'Critical' }\n  ];\n\n  dependencyTypeOptions = [\n    { value: 'library', label: 'Library' },\n    { value: 'framework', label: 'Framework' },\n    { value: 'service', label: 'Service' },\n    { value: 'database', label: 'Database' },\n    { value: 'api', label: 'API' },\n    { value: 'tool', label: 'Tool' },\n    { value: 'infrastructure', label: 'Infrastructure' }\n  ];\n\n  dependencyCriticalityOptions = [\n    { value: 'low', label: 'Low' },\n    { value: 'medium', label: 'Medium' },\n    { value: 'high', label: 'High' },\n    { value: 'critical', label: 'Critical' }\n  ];\n\n  techStackCategoryOptions = [\n    { value: 'frontend', label: 'Frontend' },\n    { value: 'backend', label: 'Backend' },\n    { value: 'database', label: 'Database' },\n    { value: 'infrastructure', label: 'Infrastructure' },\n    { value: 'monitoring', label: 'Monitoring' },\n    { value: 'security', label: 'Security' },\n    { value: 'testing', label: 'Testing' },\n    { value: 'deployment', label: 'Deployment' },\n    { value: 'communication', label: 'Communication' }\n  ];\n\n  stakeholderRoleOptions = [\n    { value: 'product_owner', label: 'Product Owner' },\n    { value: 'tech_lead', label: 'Tech Lead' },\n    { value: 'developer', label: 'Developer' },\n    { value: 'devops_engineer', label: 'DevOps Engineer' },\n    { value: 'security_officer', label: 'Security Officer' },\n    { value: 'business_analyst', label: 'Business Analyst' },\n    { value: 'project_manager', label: 'Project Manager' },\n    { value: 'architect', label: 'Architect' },\n    { value: 'qa_engineer', label: 'QA Engineer' },\n    { value: 'support_engineer', label: 'Support Engineer' }\n  ];\n\n  documentationTypeOptions = [\n    { value: 'technical_spec', label: 'Technical Specification' },\n    { value: 'api_documentation', label: 'API Documentation' },\n    { value: 'user_manual', label: 'User Manual' },\n    { value: 'deployment_guide', label: 'Deployment Guide' },\n    { value: 'architecture_diagram', label: 'Architecture Diagram' },\n    { value: 'security_policy', label: 'Security Policy' },\n    { value: 'compliance_report', label: 'Compliance Report' },\n    { value: 'runbook', label: 'Runbook' },\n    { value: 'troubleshooting', label: 'Troubleshooting' },\n    { value: 'changelog', label: 'Changelog' }\n  ];\n\n  vulnerabilitySeverityOptions = [\n    { value: 'critical', label: 'Critical' },\n    { value: 'high', label: 'High' },\n    { value: 'medium', label: 'Medium' },\n    { value: 'low', label: 'Low' },\n    { value: 'info', label: 'Info' }\n  ];\n\n  vulnerabilityStatusOptions = [\n    { value: 'open', label: 'Open' },\n    { value: 'in_progress', label: 'In Progress' },\n    { value: 'resolved', label: 'Resolved' },\n    { value: 'accepted_risk', label: 'Accepted Risk' },\n    { value: 'false_positive', label: 'False Positive' }\n  ];\n\n  constructor(\n    private fb: FormBuilder,\n    private route: ActivatedRoute\n  ) {}\n\n  ngOnInit(): void {\n    this.initializeForm();\n    this.checkEditMode();\n  }\n\n  private initializeForm(): void {\n    this.applicationForm = this.fb.group({\n      name: ['', [Validators.required, Validators.minLength(2)]],\n      description: ['', [Validators.required, Validators.minLength(10)]],\n      owner: ['', [Validators.required]],\n      lifecycleStatus: ['', [Validators.required]],\n      businessCriticality: ['', [Validators.required]],\n      version: ['', [Validators.required]],\n      repository: [''],\n      deploymentUrl: ['']\n    });\n  }\n\n  private checkEditMode(): void {\n    this.route.params.subscribe(params => {\n      if (params['id']) {\n        this.isEditMode = true;\n        this.applicationId = +params['id'];\n        this.loadApplication(this.applicationId);\n      }\n    });\n  }\n\n  private loadApplication(id: number): void {\n    // In a real application, this would load data from a service\n    // For now, we'll populate with mock data\n    this.applicationForm.patchValue({\n      name: 'E-commerce Platform',\n      description: 'Main customer-facing e-commerce application with shopping cart, payment processing, and order management.',\n      owner: 'John Doe',\n      lifecycleStatus: 'production',\n      businessCriticality: 'critical',\n      version: '2.1.0',\n      repository: 'https://github.com/company/ecommerce',\n      deploymentUrl: 'https://shop.company.com'\n    });\n\n    // Load related entities\n    this.dependencies = [\n      { name: 'Angular', type: 'framework', version: '16.2.0', criticality: 'high', isInternal: false, description: 'Frontend framework' }\n    ];\n  }\n\n  onTabChange(tabId: string): void {\n    this.activeTabId = tabId;\n  }\n\n  getFieldError(fieldName: string): string {\n    const field = this.applicationForm.get(fieldName);\n    if (field && field.invalid && field.touched) {\n      if (field.errors?.['required']) {\n        return `${fieldName} is required`;\n      }\n      if (field.errors?.['minlength']) {\n        return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;\n      }\n      if (field.errors?.['email']) {\n        return 'Please enter a valid email address';\n      }\n      if (field.errors?.['url']) {\n        return 'Please enter a valid URL';\n      }\n    }\n    return '';\n  }\n\n  // Modal methods for dependencies\n  openDependencyModal(): void {\n    this.dependencyEditMode = false;\n    this.dependencyEditData = null;\n    this.dependencyEditIndex = -1;\n    this.showDependencyModal = true;\n  }\n\n  closeDependencyModal(): void {\n    this.showDependencyModal = false;\n    this.dependencyEditMode = false;\n    this.dependencyEditData = null;\n    this.dependencyEditIndex = -1;\n  }\n\n  saveDependency(data: DependencyFormData): void {\n    if (this.dependencyEditMode && this.dependencyEditIndex >= 0) {\n      this.dependencies[this.dependencyEditIndex] = data;\n    } else {\n      this.dependencies.push(data);\n    }\n    this.closeDependencyModal();\n  }\n\n  removeDependency(index: number): void {\n    this.dependencies.splice(index, 1);\n  }\n\n  // Modal methods for tech stack\n  openTechStackModal(): void {\n    this.techStackEditMode = false;\n    this.techStackEditData = null;\n    this.techStackEditIndex = -1;\n    this.showTechStackModal = true;\n  }\n\n  closeTechStackModal(): void {\n    this.showTechStackModal = false;\n    this.techStackEditMode = false;\n    this.techStackEditData = null;\n    this.techStackEditIndex = -1;\n  }\n\n  saveTechStack(data: TechStackFormData): void {\n    if (this.techStackEditMode && this.techStackEditIndex >= 0) {\n      this.techStack[this.techStackEditIndex] = data;\n    } else {\n      this.techStack.push(data);\n    }\n    this.closeTechStackModal();\n  }\n\n  removeTechStack(index: number): void {\n    this.techStack.splice(index, 1);\n  }\n\n  // Modal methods for stakeholders\n  openStakeholderModal(): void {\n    this.stakeholderEditMode = false;\n    this.stakeholderEditData = null;\n    this.stakeholderEditIndex = -1;\n    this.showStakeholderModal = true;\n  }\n\n  closeStakeholderModal(): void {\n    this.showStakeholderModal = false;\n    this.stakeholderEditMode = false;\n    this.stakeholderEditData = null;\n    this.stakeholderEditIndex = -1;\n  }\n\n  saveStakeholder(data: StakeholderFormData): void {\n    if (this.stakeholderEditMode && this.stakeholderEditIndex >= 0) {\n      this.stakeholders[this.stakeholderEditIndex] = data;\n    } else {\n      this.stakeholders.push(data);\n    }\n    this.closeStakeholderModal();\n  }\n\n  removeStakeholder(index: number): void {\n    this.stakeholders.splice(index, 1);\n  }\n\n  // Modal methods for documentation\n  openDocumentationModal(): void {\n    this.documentationEditMode = false;\n    this.documentationEditData = null;\n    this.documentationEditIndex = -1;\n    this.showDocumentationModal = true;\n  }\n\n  closeDocumentationModal(): void {\n    this.showDocumentationModal = false;\n    this.documentationEditMode = false;\n    this.documentationEditData = null;\n    this.documentationEditIndex = -1;\n  }\n\n  saveDocumentation(data: DocumentationFormData): void {\n    if (this.documentationEditMode && this.documentationEditIndex >= 0) {\n      this.documentation[this.documentationEditIndex] = data;\n    } else {\n      this.documentation.push(data);\n    }\n    this.closeDocumentationModal();\n  }\n\n  removeDocumentation(index: number): void {\n    this.documentation.splice(index, 1);\n  }\n\n  // Modal methods for vulnerabilities\n  openVulnerabilityModal(): void {\n    this.vulnerabilityEditMode = false;\n    this.vulnerabilityEditData = null;\n    this.vulnerabilityEditIndex = -1;\n    this.showVulnerabilityModal = true;\n  }\n\n  closeVulnerabilityModal(): void {\n    this.showVulnerabilityModal = false;\n    this.vulnerabilityEditMode = false;\n    this.vulnerabilityEditData = null;\n    this.vulnerabilityEditIndex = -1;\n  }\n\n  saveVulnerability(data: VulnerabilityFormData): void {\n    if (this.vulnerabilityEditMode && this.vulnerabilityEditIndex >= 0) {\n      this.vulnerabilities[this.vulnerabilityEditIndex] = data;\n    } else {\n      this.vulnerabilities.push(data);\n    }\n    this.closeVulnerabilityModal();\n  }\n\n  removeVulnerability(index: number): void {\n    this.vulnerabilities.splice(index, 1);\n  }\n\n  saveApplication(): void {\n    if (this.applicationForm.valid) {\n      this.isSaving = true;\n\n      // Prepare the complete application data\n      const applicationData = {\n        ...this.applicationForm.value,\n        dependencies: this.dependencies,\n        techStack: this.techStack,\n        stakeholders: this.stakeholders,\n        documentation: this.documentation,\n        vulnerabilities: this.vulnerabilities\n      };\n\n      // In a real application, this would call a service to save the data\n      console.log('Saving application:', applicationData);\n\n      // Simulate API call\n      setTimeout(() => {\n        this.isSaving = false;\n        // Navigate back to applications list or show success message\n        console.log('Application saved successfully!');\n      }, 2000);\n    } else {\n      // Mark all fields as touched to show validation errors\n      this.markFormGroupTouched(this.applicationForm);\n\n      // Find the first tab with errors and switch to it\n      this.switchToFirstErrorTab();\n    }\n  }\n\n  private markFormGroupTouched(formGroup: FormGroup): void {\n    Object.keys(formGroup.controls).forEach(key => {\n      const control = formGroup.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  private switchToFirstErrorTab(): void {\n    // Check basic information tab\n    if (this.applicationForm.invalid) {\n      this.activeTabId = 'basic';\n      this.tabsComponent?.setActiveTab('basic');\n      return;\n    }\n\n    // Add logic to check other tabs for errors if needed\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8DU,IAAA,yBAAA,GAAA,OAAA,CAAA;AACE,IAAA,oBAAA,GAAA,QAAA,EAAA;AACF,IAAA,uBAAA;;;;AADwE,IAAA,oBAAA;;;;;;AAOxE,IAAA,yBAAA,GAAA,QAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AAF0C,IAAA,sBAAA,eAAA,OAAA,QAAA;AACxC,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,OAAA,GAAA;;;;;;AAIF,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,UAAA,EAAA,EAAwC,GAAA,QAAA,EAAA,EACG,GAAA,QAAA,EAAA;AAE7C,IAAA,uBAAA;;;;;;AA9BF,IAAA,yBAAA,GAAA,UAAA,CAAA;AAOE,IAAA,qBAAA,SAAA,SAAA,0DAAA;AAAA,YAAA,SAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,UAAA,OAAA,EAAA,CAAiB;IAAA,CAAA;AAM1B,IAAA,qBAAA,GAAA,4CAAA,GAAA,GAAA,OAAA,CAAA;AAKA,IAAA,yBAAA,GAAA,QAAA,CAAA;AAAwB,IAAA,iBAAA,CAAA;AAAe,IAAA,uBAAA;AAGvC,IAAA,qBAAA,GAAA,wCAAA,GAAA,GAAA,QAAA,CAAA,EAA6E,GAAA,4CAAA,GAAA,GAAA,OAAA,CAAA;AAU/E,IAAA,uBAAA;;;;;AA5BE,IAAA,sBAAA,UAAA,OAAA,OAAA,OAAA,WAAA,EAAuC,YAAA,OAAA,QAAA,EACR,aAAA,OAAA,QAAA;AAE/B,IAAA,qBAAA,YAAA,OAAA,QAAA;;AAOM,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,IAAA;AAKkB,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,KAAA;AAGjB,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,KAAA;AAKD,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,YAAA,CAAA,OAAA,KAAA;;;AA5CV,IAAO,eAAP,MAAO,cAAY;EACd,KAAK;EACL,QAAQ;EACR,OAAO;EACP,WAAW;EACX,QAAyB;EACzB,WAAW;EACX,SAAS;;qCAPP,eAAY;EAAA;yEAAZ,eAAY,WAAA,CAAA,CAAA,SAAA,CAAA,GAAA,QAAA,EAAA,IAAA,MAAA,OAAA,SAAA,MAAA,QAAA,UAAA,YAAA,OAAA,SAAA,UAAA,YAAA,QAAA,SAAA,GAAA,oBAAA,KAAA,OAAA,GAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,aAAA,CAAA,GAAA,UAAA,SAAA,sBAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;;AAdrB,MAAA,yBAAA,GAAA,OAAA,CAAA;AACE,MAAA,uBAAA,CAAA;AACF,MAAA,uBAAA;;;AAFyB,MAAA,sBAAA,UAAA,IAAA,MAAA;;oBAFjB,YAAY,GAAA,QAAA,CAAA,4KAAA,EAAA,CAAA;;;sEAgBX,cAAY,CAAA;UAnBxB;uBACW,WAAS,YACP,MAAI,SACP,CAAC,YAAY,GAAC,UACb;;;;KAIT,QAAA,CAAA,gWAAA,EAAA,CAAA;cAYQ,IAAE,CAAA;UAAV;MACQ,OAAK,CAAA;UAAb;MACQ,MAAI,CAAA;UAAZ;MACQ,UAAQ,CAAA;UAAhB;MACQ,OAAK,CAAA;UAAb;MACQ,UAAQ,CAAA;UAAhB;MACQ,QAAM,CAAA;UAAd;;;;6EAPU,cAAY,EAAA,WAAA,gBAAA,UAAA,oDAAA,YAAA,GAAA,CAAA;AAAA,GAAA;AA4DnB,IAAO,gBAAP,MAAO,eAAa;EACf,cAAyC;EACzC,OAA2B;EAC3B,UAA6C;EAC7C,cAAc;EACd,OAAkB,CAAA;EAEjB,YAAY,IAAI,aAAY;EAEP;EAE/B,qBAAkB;AAEhB,QAAI,CAAC,KAAK,eAAe,KAAK,KAAK,SAAS,GAAG;AAC7C,YAAM,kBAAkB,KAAK,KAAK,KAAK,SAAO,CAAC,IAAI,QAAQ;AAC3D,UAAI,iBAAiB;AACnB,aAAK,cAAc,gBAAgB;MACrC;IACF;AAGA,SAAK,oBAAmB;EAC1B;EAEA,cAAW;AACT,QAAI,KAAK,eAAe;AACtB,WAAK,oBAAmB;IAC1B;EACF;EAEA,IAAI,mBAAgB;AAClB,UAAM,UAAU;MACd;MACA,QAAQ,KAAK,IAAI;MACjB,QAAQ,KAAK,OAAO;MACpB,QAAQ,KAAK,WAAW;;AAG1B,WAAO,QAAQ,KAAK,GAAG;EACzB;EAEA,UAAU,OAAa;AACrB,UAAM,MAAM,KAAK,KAAK,KAAK,OAAK,EAAE,OAAO,KAAK;AAC9C,QAAI,CAAC,OAAO,IAAI;AAAU;AAE1B,SAAK,cAAc;AACnB,SAAK,oBAAmB;AACxB,SAAK,UAAU,KAAK,KAAK;EAC3B;EAEQ,sBAAmB;AACzB,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,QAAQ,kBAAe;AACxC,qBAAa,SAAS,aAAa,OAAO,KAAK;MACjD,CAAC;IACH;EACF;EAEA,WAAW,OAAe,KAAY;AACpC,WAAO,IAAI;EACb;;EAGO,aAAa,OAAa;AAC/B,SAAK,UAAU,KAAK;EACtB;EAEO,eAAY;AACjB,WAAO,KAAK;EACd;EAEO,YAAY,OAAe,UAAiB;AACjD,UAAM,MAAM,KAAK,KAAK,KAAK,OAAK,EAAE,OAAO,KAAK;AAC9C,QAAI,KAAK;AACP,UAAI,WAAW;IACjB;EACF;EAEO,YAAY,OAAe,OAAsB;AACtD,UAAM,MAAM,KAAK,KAAK,KAAK,OAAK,EAAE,OAAO,KAAK;AAC9C,QAAI,KAAK;AACP,UAAI,QAAQ;IACd;EACF;EAEO,WAAW,OAAe,WAAoB,MAAI;AACvD,UAAM,MAAM,KAAK,KAAK,KAAK,OAAK,EAAE,OAAO,KAAK;AAC9C,QAAI,KAAK;AACP,UAAI,WAAW;AAGf,UAAI,YAAY,KAAK,gBAAgB,OAAO;AAC1C,cAAM,UAAU,KAAK,KAAK,KAAK,OAAK,EAAE,OAAO,SAAS,CAAC,EAAE,QAAQ;AACjE,YAAI,SAAS;AACX,eAAK,UAAU,QAAQ,EAAE;QAC3B;MACF;IACF;EACF;;qCAlGW,gBAAa;EAAA;yEAAb,gBAAa,WAAA,CAAA,CAAA,UAAA,CAAA,GAAA,gBAAA,SAAA,6BAAA,IAAA,KAAA,UAAA;AAAA,QAAA,KAAA,GAAA;yCASP,cAAY,CAAA;;;;;;;;;AAtD3B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAuD,GAAA,OAAA,CAAA;AAGnD,MAAA,qBAAA,GAAA,iCAAA,GAAA,IAAA,UAAA,CAAA;AAgCF,MAAA,uBAAA;AAGA,MAAA,yBAAA,GAAA,OAAA,CAAA;AACE,MAAA,uBAAA,CAAA;AACF,MAAA,uBAAA,EAAM;;;AAxCoB,MAAA,qBAAA,IAAA,gBAAA;AAEJ,MAAA,oBAAA;AAAA,MAAA,sBAAA,qBAAA,IAAA,gBAAA,UAAA;AAEF,MAAA,oBAAA;AAAA,MAAA,qBAAA,WAAA,IAAA,IAAA,EAAS,gBAAA,IAAA,UAAA;;oBANvB,cAAY,SAAA,IAAA,GAAA,QAAA,CAAA,08LAAA,EAAA,CAAA;;;sEA+CX,eAAa,CAAA;UAlDzB;uBACW,YAAU,YACR,MAAI,SACP,CAAC,YAAY,GAAC,UACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA2CT,QAAA,CAAA,iuJAAA,EAAA,CAAA;cAIQ,aAAW,CAAA;UAAnB;MACQ,MAAI,CAAA;UAAZ;MACQ,SAAO,CAAA;UAAf;MACQ,aAAW,CAAA;UAAnB;MACQ,MAAI,CAAA;UAAZ;MAES,WAAS,CAAA;UAAlB;MAE8B,eAAa,CAAA;UAA3C;WAAgB,YAAY;;;;6EATlB,eAAa,EAAA,WAAA,iBAAA,UAAA,oDAAA,YAAA,GAAA,CAAA;AAAA,GAAA;;;ACgHpB,IAAO,2BAAP,MAAO,0BAAwB;EAWf;EAVX,SAAS;EACT,WAAW;EACX,cAAyC;EACzC,UAAU;EAET,SAAS,IAAI,aAAY;EACzB,QAAQ,IAAI,aAAY;EAElC;EAEA,YAAoB,IAAe;AAAf,SAAA,KAAA;EAAkB;EAEtC,WAAQ;AACN,SAAK,eAAc;EACrB;EAEA,cAAW;AACT,QAAI,KAAK,kBAAkB,KAAK,aAAa;AAC3C,WAAK,eAAe,WAAW,KAAK,WAAW;IACjD;EACF;EAEQ,iBAAc;AACpB,SAAK,iBAAiB,KAAK,GAAG,MAAM;MAClC,MAAM,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,UAAU,CAAC,CAAC,CAAC;MACzD,MAAM,CAAC,WAAW,CAAC,WAAW,QAAQ,CAAC;MACvC,SAAS,CAAC,IAAI,CAAC,WAAW,QAAQ,CAAC;MACnC,aAAa,CAAC,UAAU,CAAC,WAAW,QAAQ,CAAC;MAC7C,YAAY,CAAC,KAAK;MAClB,aAAa,CAAC,EAAE;KACjB;AAED,QAAI,KAAK,aAAa;AACpB,WAAK,eAAe,WAAW,KAAK,WAAW;IACjD;EACF;EAEA,cAAc,WAAiB;AAC7B,UAAM,QAAQ,KAAK,eAAe,IAAI,SAAS;AAC/C,QAAI,SAAS,MAAM,WAAW,MAAM,SAAS;AAC3C,UAAI,MAAM,SAAS,UAAU,GAAG;AAC9B,eAAO,GAAG,SAAS;MACrB;AACA,UAAI,MAAM,SAAS,WAAW,GAAG;AAC/B,eAAO,GAAG,SAAS,qBAAqB,MAAM,OAAO,WAAW,EAAE,cAAc;MAClF;IACF;AACA,WAAO;EACT;EAEA,UAAO;AACL,SAAK,eAAe,MAAK;AACzB,SAAK,OAAO,KAAI;EAClB;EAEA,SAAM;AACJ,QAAI,KAAK,eAAe,OAAO;AAC7B,WAAK,MAAM,KAAK,KAAK,eAAe,KAAK;IAC3C;EACF;;qCA5DW,2BAAwB,4BAAA,WAAA,CAAA;EAAA;yEAAxB,2BAAwB,WAAA,CAAA,CAAA,sBAAA,CAAA,GAAA,QAAA,EAAA,QAAA,UAAA,UAAA,YAAA,aAAA,eAAA,SAAA,UAAA,GAAA,SAAA,EAAA,QAAA,UAAA,OAAA,QAAA,GAAA,UAAA,CAAA,8BAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,YAAA,4CAAA,eAAA,mBAAA,GAAA,UAAA,aAAA,aAAA,UAAA,SAAA,WAAA,YAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,WAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,SAAA,mBAAA,eAAA,6BAAA,mBAAA,QAAA,GAAA,YAAA,cAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,mBAAA,QAAA,GAAA,aAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,SAAA,WAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,SAAA,UAAA,GAAA,CAAA,SAAA,KAAA,GAAA,CAAA,SAAA,MAAA,GAAA,CAAA,SAAA,gBAAA,GAAA,CAAA,SAAA,WAAA,eAAA,yBAAA,mBAAA,WAAA,GAAA,YAAA,cAAA,GAAA,CAAA,mBAAA,eAAA,GAAA,aAAA,GAAA,CAAA,SAAA,KAAA,GAAA,CAAA,SAAA,QAAA,GAAA,CAAA,SAAA,MAAA,GAAA,CAAA,SAAA,UAAA,GAAA,CAAA,GAAA,cAAA,YAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,QAAA,YAAA,mBAAA,cAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,sBAAA,GAAA,CAAA,mBAAA,eAAA,eAAA,wDAAA,QAAA,KAAA,GAAA,eAAA,CAAA,GAAA,UAAA,SAAA,kCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAvLjC,MAAA,yBAAA,GAAA,mBAAA,CAAA;AAOE,MAAA,qBAAA,UAAA,SAAA,sEAAA;AAAA,eAAU,IAAA,QAAA;MAAS,CAAA,EAAC,aAAA,SAAA,yEAAA;AAAA,eACP,IAAA,OAAA;MAAQ,CAAA,EAAC,aAAA,SAAA,yEAAA;AAAA,eACT,IAAA,QAAA;MAAS,CAAA;AAEtB,MAAA,yBAAA,GAAA,QAAA,CAAA,EAA2D,GAAA,OAAA,CAAA;AAEvD,MAAA,oBAAA,GAAA,kBAAA,CAAA;AAQA,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAwB,GAAA,SAAA,CAAA;AACI,MAAA,iBAAA,GAAA,MAAA;AAAI,MAAA,uBAAA;AAC9B,MAAA,yBAAA,GAAA,UAAA,CAAA,EAAmD,GAAA,UAAA,CAAA;AACzB,MAAA,iBAAA,GAAA,SAAA;AAAO,MAAA,uBAAA;AAC/B,MAAA,yBAAA,IAAA,UAAA,CAAA;AAA0B,MAAA,iBAAA,IAAA,WAAA;AAAS,MAAA,uBAAA;AACnC,MAAA,yBAAA,IAAA,UAAA,CAAA;AAAwB,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA;AAC/B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAyB,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA;AACjC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAoB,MAAA,iBAAA,IAAA,KAAA;AAAG,MAAA,uBAAA;AACvB,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAqB,MAAA,iBAAA,IAAA,MAAA;AAAI,MAAA,uBAAA;AACzB,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA+B,MAAA,iBAAA,IAAA,gBAAA;AAAc,MAAA,uBAAA,EAAS,EAC/C;AAGX,MAAA,oBAAA,IAAA,kBAAA,EAAA;AAQA,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,SAAA,CAAA;AACI,MAAA,iBAAA,IAAA,aAAA;AAAW,MAAA,uBAAA;AACrC,MAAA,yBAAA,IAAA,UAAA,EAAA,EAA0D,IAAA,UAAA,EAAA;AACpC,MAAA,iBAAA,IAAA,KAAA;AAAG,MAAA,uBAAA;AACvB,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAuB,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA;AAC7B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAqB,MAAA,iBAAA,IAAA,MAAA;AAAI,MAAA,uBAAA;AACzB,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAyB,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA,EAAS,EACnC;AAGX,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAmC,IAAA,SAAA,EAAA;AAE/B,MAAA,oBAAA,IAAA,SAAA,EAAA;AAKA,MAAA,yBAAA,IAAA,QAAA,EAAA;AAA4B,MAAA,iBAAA,IAAA,qBAAA;AAAmB,MAAA,uBAAA;AAC/C,MAAA,yBAAA,IAAA,QAAA,EAAA;AAAmC,MAAA,iBAAA,IAAA,gDAAA;AAA8C,MAAA,uBAAA,EAAO,EAClF;AAGV,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAmC,IAAA,SAAA,CAAA;AACP,MAAA,iBAAA,IAAA,aAAA;AAAW,MAAA,uBAAA;AACrC,MAAA,oBAAA,IAAA,YAAA,EAAA;AAMF,MAAA,uBAAA,EAAM,EACF,EACD;;;AAzEP,MAAA,qBAAA,UAAA,IAAA,MAAA,EAAiB,SAAA,IAAA,WAAA,oBAAA,gBAAA,EACwC,WAAA,IAAA,OAAA,EAEtC,cAAA,IAAA,eAAA,KAAA;AAOb,MAAA,oBAAA;AAAA,MAAA,qBAAA,aAAA,IAAA,cAAA;AAMA,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,YAAA,IAAA,EAAiB,gBAAA,IAAA,cAAA,MAAA,CAAA;AAqBjB,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,YAAA,IAAA,EAAiB,gBAAA,IAAA,cAAA,SAAA,CAAA;;oBAxCjB,cAAc,qBAAmB,oBAAA,gBAAA,8BAAA,sBAAA,8BAAA,4BAAA,iBAAA,sBAAA,mBAAA,oBAAA,iBAAE,qBAAqB,kBAAkB,GAAA,QAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2DAAA,EAAA,CAAA;;;sEAyLzE,0BAAwB,CAAA;UA5LpC;uBACW,wBAAsB,YACpB,MAAI,SACP,CAAC,cAAc,qBAAqB,qBAAqB,kBAAkB,GAAC,UAC3E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA6ET,QAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAAA,EAAA,CAAA;uCA4GQ,QAAM,CAAA;UAAd;MACQ,UAAQ,CAAA;UAAhB;MACQ,aAAW,CAAA;UAAnB;MACQ,SAAO,CAAA;UAAf;MAES,QAAM,CAAA;UAAf;MACS,OAAK,CAAA;UAAd;;;;6EAPU,0BAAwB,EAAA,WAAA,4BAAA,UAAA,0FAAA,YAAA,IAAA,CAAA;AAAA,GAAA;;;ACT/B,IAAO,0BAAP,MAAO,yBAAuB;EAWd;EAVX,SAAS;EACT,WAAW;EACX,cAAwC;EACxC,UAAU;EAET,SAAS,IAAI,aAAY;EACzB,QAAQ,IAAI,aAAY;EAElC;EAEA,YAAoB,IAAe;AAAf,SAAA,KAAA;EAAkB;EAEtC,WAAQ;AACN,SAAK,eAAc;EACrB;EAEA,cAAW;AACT,QAAI,KAAK,iBAAiB,KAAK,aAAa;AAC1C,WAAK,cAAc,WAAW,KAAK,WAAW;IAChD;EACF;EAEQ,iBAAc;AACpB,SAAK,gBAAgB,KAAK,GAAG,MAAM;MACjC,UAAU,CAAC,YAAY,CAAC,WAAW,QAAQ,CAAC;MAC5C,YAAY,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,UAAU,CAAC,CAAC,CAAC;MAC/D,SAAS,CAAC,IAAI,CAAC,WAAW,QAAQ,CAAC;MACnC,SAAS,CAAC,IAAI,CAAC,WAAW,QAAQ,CAAC;MACnC,QAAQ,CAAC,KAAK;KACf;AAED,QAAI,KAAK,aAAa;AACpB,WAAK,cAAc,WAAW,KAAK,WAAW;IAChD;EACF;EAEA,cAAc,WAAiB;AAC7B,UAAM,QAAQ,KAAK,cAAc,IAAI,SAAS;AAC9C,QAAI,SAAS,MAAM,WAAW,MAAM,SAAS;AAC3C,UAAI,MAAM,SAAS,UAAU,GAAG;AAC9B,eAAO,GAAG,SAAS;MACrB;AACA,UAAI,MAAM,SAAS,WAAW,GAAG;AAC/B,eAAO,GAAG,SAAS,qBAAqB,MAAM,OAAO,WAAW,EAAE,cAAc;MAClF;IACF;AACA,WAAO;EACT;EAEA,UAAO;AACL,SAAK,cAAc,MAAK;AACxB,SAAK,OAAO,KAAI;EAClB;EAEA,SAAM;AACJ,QAAI,KAAK,cAAc,OAAO;AAC5B,WAAK,MAAM,KAAK,KAAK,cAAc,KAAK;IAC1C;EACF;;qCA3DW,0BAAuB,4BAAA,WAAA,CAAA;EAAA;yEAAvB,0BAAuB,WAAA,CAAA,CAAA,sBAAA,CAAA,GAAA,QAAA,EAAA,QAAA,UAAA,UAAA,YAAA,aAAA,eAAA,SAAA,UAAA,GAAA,SAAA,EAAA,QAAA,UAAA,OAAA,QAAA,GAAA,UAAA,CAAA,8BAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,YAAA,sCAAA,eAAA,mBAAA,GAAA,UAAA,aAAA,aAAA,UAAA,SAAA,WAAA,YAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,WAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,mBAAA,YAAA,GAAA,aAAA,GAAA,CAAA,SAAA,UAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,SAAA,UAAA,GAAA,CAAA,SAAA,gBAAA,GAAA,CAAA,SAAA,YAAA,GAAA,CAAA,SAAA,UAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,SAAA,YAAA,GAAA,CAAA,SAAA,eAAA,GAAA,CAAA,SAAA,cAAA,eAAA,oCAAA,mBAAA,cAAA,GAAA,YAAA,cAAA,GAAA,CAAA,SAAA,WAAA,eAAA,sBAAA,mBAAA,WAAA,GAAA,YAAA,cAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,QAAA,YAAA,mBAAA,UAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,sBAAA,GAAA,CAAA,GAAA,cAAA,YAAA,GAAA,CAAA,mBAAA,WAAA,eAAA,8DAAA,QAAA,KAAA,GAAA,eAAA,CAAA,GAAA,UAAA,SAAA,iCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AA/KhC,MAAA,yBAAA,GAAA,mBAAA,CAAA;AAOE,MAAA,qBAAA,UAAA,SAAA,qEAAA;AAAA,eAAU,IAAA,QAAA;MAAS,CAAA,EAAC,aAAA,SAAA,wEAAA;AAAA,eACP,IAAA,OAAA;MAAQ,CAAA,EAAC,aAAA,SAAA,wEAAA;AAAA,eACT,IAAA,QAAA;MAAS,CAAA;AAEtB,MAAA,yBAAA,GAAA,QAAA,CAAA,EAA0D,GAAA,OAAA,CAAA,EACjC,GAAA,OAAA,CAAA,EACG,GAAA,SAAA,CAAA;AACI,MAAA,iBAAA,GAAA,UAAA;AAAQ,MAAA,uBAAA;AAClC,MAAA,yBAAA,GAAA,UAAA,CAAA,EAAuD,GAAA,UAAA,CAAA;AAC5B,MAAA,iBAAA,GAAA,UAAA;AAAQ,MAAA,uBAAA;AACjC,MAAA,yBAAA,GAAA,UAAA,CAAA;AAAwB,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA;AAC/B,MAAA,yBAAA,IAAA,UAAA,CAAA;AAAyB,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA;AACjC,MAAA,yBAAA,IAAA,UAAA,CAAA;AAA+B,MAAA,iBAAA,IAAA,gBAAA;AAAc,MAAA,uBAAA;AAC7C,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA2B,MAAA,iBAAA,IAAA,YAAA;AAAU,MAAA,uBAAA;AACrC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAyB,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA;AACjC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAwB,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA;AAC/B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA2B,MAAA,iBAAA,IAAA,YAAA;AAAU,MAAA,uBAAA;AACrC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA8B,MAAA,iBAAA,IAAA,eAAA;AAAa,MAAA,uBAAA,EAAS,EAC7C;AAGX,MAAA,oBAAA,IAAA,kBAAA,EAAA,EAMkB,IAAA,kBAAA,EAAA;AAUlB,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,SAAA,EAAA;AAEpB,MAAA,oBAAA,IAAA,SAAA,EAAA;AAKA,MAAA,yBAAA,IAAA,QAAA,EAAA;AAA4B,MAAA,iBAAA,IAAA,iBAAA;AAAe,MAAA,uBAAA;AAC3C,MAAA,yBAAA,IAAA,QAAA,EAAA;AAAmC,MAAA,iBAAA,IAAA,wDAAA;AAAsD,MAAA,uBAAA,EAAO,EAC1F;AAGV,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAmC,IAAA,SAAA,CAAA;AACP,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA;AACjC,MAAA,oBAAA,IAAA,YAAA,EAAA;AAMF,MAAA,uBAAA,EAAM,EACF,EACD;;;AAjEP,MAAA,qBAAA,UAAA,IAAA,MAAA,EAAiB,SAAA,IAAA,WAAA,oBAAA,gBAAA,EACwC,WAAA,IAAA,OAAA,EAEtC,cAAA,IAAA,cAAA,KAAA;AAOb,MAAA,oBAAA;AAAA,MAAA,qBAAA,aAAA,IAAA,aAAA;AAqBA,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,YAAA,IAAA,EAAiB,gBAAA,IAAA,cAAA,YAAA,CAAA;AAQjB,MAAA,oBAAA;AAAA,MAAA,qBAAA,YAAA,IAAA,EAAiB,gBAAA,IAAA,cAAA,SAAA,CAAA;;oBA1CjB,cAAc,qBAAmB,oBAAA,gBAAA,8BAAA,sBAAA,8BAAA,4BAAA,iBAAA,sBAAA,mBAAA,oBAAA,iBAAE,qBAAqB,kBAAkB,GAAA,QAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2DAAA,EAAA,CAAA;;;sEAiLzE,yBAAuB,CAAA;UApLnC;uBACW,wBAAsB,YACpB,MAAI,SACP,CAAC,cAAc,qBAAqB,qBAAqB,kBAAkB,GAAC,UAC3E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAqET,QAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAAA,EAAA,CAAA;uCA4GQ,QAAM,CAAA;UAAd;MACQ,UAAQ,CAAA;UAAhB;MACQ,aAAW,CAAA;UAAnB;MACQ,SAAO,CAAA;UAAf;MAES,QAAM,CAAA;UAAf;MACS,OAAK,CAAA;UAAd;;;;6EAPU,yBAAuB,EAAA,WAAA,2BAAA,UAAA,0FAAA,YAAA,IAAA,CAAA;AAAA,GAAA;;;ACF9B,IAAO,8BAAP,MAAO,6BAA2B;EAWlB;EAVX,SAAS;EACT,WAAW;EACX,cAA4C;EAC5C,UAAU;EAET,SAAS,IAAI,aAAY;EACzB,QAAQ,IAAI,aAAY;EAElC;EAEA,YAAoB,IAAe;AAAf,SAAA,KAAA;EAAkB;EAEtC,WAAQ;AACN,SAAK,eAAc;EACrB;EAEA,cAAW;AACT,QAAI,KAAK,qBAAqB,KAAK,aAAa;AAC9C,WAAK,kBAAkB,WAAW,KAAK,WAAW;IACpD;EACF;EAEQ,iBAAc;AACpB,SAAK,oBAAoB,KAAK,GAAG,MAAM;MACrC,OAAO,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,UAAU,CAAC,CAAC,CAAC;MAC1D,MAAM,CAAC,kBAAkB,CAAC,WAAW,QAAQ,CAAC;MAC9C,SAAS,CAAC,OAAO,CAAC,WAAW,QAAQ,CAAC;MACtC,KAAK,CAAC,IAAI,CAAC,WAAW,QAAQ,CAAC;MAC/B,UAAU,CAAC,KAAK;MAChB,aAAa,CAAC,EAAE;KACjB;AAED,QAAI,KAAK,aAAa;AACpB,WAAK,kBAAkB,WAAW,KAAK,WAAW;IACpD;EACF;EAEA,cAAc,WAAiB;AAC7B,UAAM,QAAQ,KAAK,kBAAkB,IAAI,SAAS;AAClD,QAAI,SAAS,MAAM,WAAW,MAAM,SAAS;AAC3C,UAAI,MAAM,SAAS,UAAU,GAAG;AAC9B,eAAO,GAAG,SAAS;MACrB;AACA,UAAI,MAAM,SAAS,WAAW,GAAG;AAC/B,eAAO,GAAG,SAAS,qBAAqB,MAAM,OAAO,WAAW,EAAE,cAAc;MAClF;IACF;AACA,WAAO;EACT;EAEA,UAAO;AACL,SAAK,kBAAkB,MAAK;AAC5B,SAAK,OAAO,KAAI;EAClB;EAEA,SAAM;AACJ,QAAI,KAAK,kBAAkB,OAAO;AAChC,WAAK,MAAM,KAAK,KAAK,kBAAkB,KAAK;IAC9C;EACF;;qCA5DW,8BAA2B,4BAAA,WAAA,CAAA;EAAA;yEAA3B,8BAA2B,WAAA,CAAA,CAAA,yBAAA,CAAA,GAAA,QAAA,EAAA,QAAA,UAAA,UAAA,YAAA,aAAA,eAAA,SAAA,UAAA,GAAA,SAAA,EAAA,QAAA,UAAA,OAAA,QAAA,GAAA,UAAA,CAAA,8BAAA,GAAA,OAAA,IAAA,MAAA,IAAA,QAAA,CAAA,CAAA,YAAA,mCAAA,eAAA,sBAAA,GAAA,UAAA,aAAA,aAAA,UAAA,SAAA,WAAA,YAAA,GAAA,CAAA,GAAA,sBAAA,GAAA,WAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,SAAA,kBAAA,eAAA,uCAAA,mBAAA,SAAA,GAAA,YAAA,cAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,mBAAA,QAAA,GAAA,aAAA,GAAA,CAAA,SAAA,gBAAA,GAAA,CAAA,SAAA,mBAAA,GAAA,CAAA,SAAA,aAAA,GAAA,CAAA,SAAA,oBAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,CAAA,SAAA,sBAAA,GAAA,CAAA,SAAA,kBAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,SAAA,OAAA,GAAA,CAAA,SAAA,WAAA,eAAA,oBAAA,mBAAA,WAAA,GAAA,YAAA,cAAA,GAAA,CAAA,SAAA,mBAAA,eAAA,sCAAA,mBAAA,OAAA,GAAA,YAAA,cAAA,GAAA,CAAA,GAAA,cAAA,YAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,QAAA,YAAA,mBAAA,YAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,sBAAA,GAAA,CAAA,mBAAA,eAAA,eAAA,6DAAA,QAAA,KAAA,GAAA,eAAA,CAAA,GAAA,UAAA,SAAA,qCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AA5KpC,MAAA,yBAAA,GAAA,mBAAA,CAAA;AAOE,MAAA,qBAAA,UAAA,SAAA,yEAAA;AAAA,eAAU,IAAA,QAAA;MAAS,CAAA,EAAC,aAAA,SAAA,4EAAA;AAAA,eACP,IAAA,OAAA;MAAQ,CAAA,EAAC,aAAA,SAAA,4EAAA;AAAA,eACT,IAAA,QAAA;MAAS,CAAA;AAEtB,MAAA,yBAAA,GAAA,QAAA,CAAA,EAAiE,GAAA,OAAA,CAAA;AAE7D,MAAA,oBAAA,GAAA,kBAAA,CAAA;AAQA,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAwB,GAAA,SAAA,CAAA;AACI,MAAA,iBAAA,GAAA,eAAA;AAAa,MAAA,uBAAA;AACvC,MAAA,yBAAA,GAAA,UAAA,CAAA,EAAmD,GAAA,UAAA,CAAA;AAClB,MAAA,iBAAA,GAAA,yBAAA;AAAuB,MAAA,uBAAA;AACtD,MAAA,yBAAA,IAAA,UAAA,CAAA;AAAkC,MAAA,iBAAA,IAAA,mBAAA;AAAiB,MAAA,uBAAA;AACnD,MAAA,yBAAA,IAAA,UAAA,CAAA;AAA4B,MAAA,iBAAA,IAAA,aAAA;AAAW,MAAA,uBAAA;AACvC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAmC,MAAA,iBAAA,IAAA,oBAAA;AAAkB,MAAA,uBAAA;AACrD,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAgC,MAAA,iBAAA,IAAA,uBAAA;AAAqB,MAAA,uBAAA;AACrD,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAqC,MAAA,iBAAA,IAAA,sBAAA;AAAoB,MAAA,uBAAA;AACzD,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAiC,MAAA,iBAAA,IAAA,kBAAA;AAAgB,MAAA,uBAAA;AACjD,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAgC,MAAA,iBAAA,IAAA,iBAAA;AAAe,MAAA,uBAAA;AAC/C,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAwB,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA;AAC/B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAsB,MAAA,iBAAA,IAAA,OAAA;AAAK,MAAA,uBAAA,EAAS,EAC7B;AAGX,MAAA,oBAAA,IAAA,kBAAA,EAAA,EAMkB,IAAA,kBAAA,EAAA;AAUlB,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAmC,IAAA,SAAA,EAAA;AAE/B,MAAA,oBAAA,IAAA,SAAA,EAAA;AAKA,MAAA,yBAAA,IAAA,QAAA,EAAA;AAA4B,MAAA,iBAAA,IAAA,sBAAA;AAAoB,MAAA,uBAAA;AAChD,MAAA,yBAAA,IAAA,QAAA,EAAA;AAAmC,MAAA,iBAAA,IAAA,2CAAA;AAAyC,MAAA,uBAAA,EAAO,EAC7E;AAGV,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAmC,IAAA,SAAA,CAAA;AACP,MAAA,iBAAA,IAAA,aAAA;AAAW,MAAA,uBAAA;AACrC,MAAA,oBAAA,IAAA,YAAA,EAAA;AAMF,MAAA,uBAAA,EAAM,EACF,EACD;;;AA1EP,MAAA,qBAAA,UAAA,IAAA,MAAA,EAAiB,SAAA,IAAA,WAAA,uBAAA,mBAAA,EAC8C,WAAA,IAAA,OAAA,EAE5C,cAAA,IAAA,kBAAA,KAAA;AAOb,MAAA,oBAAA;AAAA,MAAA,qBAAA,aAAA,IAAA,iBAAA;AAMA,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,YAAA,IAAA,EAAiB,gBAAA,IAAA,cAAA,OAAA,CAAA;AAwBjB,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,YAAA,IAAA,EAAiB,gBAAA,IAAA,cAAA,SAAA,CAAA;AAQjB,MAAA,oBAAA;AAAA,MAAA,qBAAA,YAAA,IAAA,EAAiB,gBAAA,IAAA,cAAA,KAAA,CAAA;;oBAnDjB,cAAc,qBAAmB,oBAAA,gBAAA,8BAAA,sBAAA,8BAAA,4BAAA,iBAAA,sBAAA,mBAAA,oBAAA,iBAAE,qBAAqB,kBAAkB,GAAA,QAAA,CAAA,iiEAAA,EAAA,CAAA;;;sEA8KzE,6BAA2B,CAAA;UAjLvC;uBACW,2BAAyB,YACvB,MAAI,SACP,CAAC,cAAc,qBAAqB,qBAAqB,kBAAkB,GAAC,UAC3E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA8ET,QAAA,CAAA,q/DAAA,EAAA,CAAA;uCAgGQ,QAAM,CAAA;UAAd;MACQ,UAAQ,CAAA;UAAhB;MACQ,aAAW,CAAA;UAAnB;MACQ,SAAO,CAAA;UAAf;MAES,QAAM,CAAA;UAAf;MACS,OAAK,CAAA;UAAd;;;;6EAPU,6BAA2B,EAAA,WAAA,+BAAA,UAAA,gGAAA,YAAA,IAAA,CAAA;AAAA,GAAA;;;AC1ClC,IAAO,8BAAP,MAAO,6BAA2B;EAWlB;EAVX,SAAS;EACT,WAAW;EACX,cAA4C;EAC5C,UAAU;EAET,SAAS,IAAI,aAAY;EACzB,QAAQ,IAAI,aAAY;EAElC;EAEA,YAAoB,IAAe;AAAf,SAAA,KAAA;EAAkB;EAEtC,WAAQ;AACN,SAAK,eAAc;EACrB;EAEA,cAAW;AACT,QAAI,KAAK,qBAAqB,KAAK,aAAa;AAC9C,WAAK,kBAAkB,WAAW,KAAK,WAAW;IACpD;EACF;EAEQ,iBAAc;AACpB,SAAK,oBAAoB,KAAK,GAAG,MAAM;MACrC,OAAO,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,UAAU,CAAC,CAAC,CAAC;MAC1D,UAAU,CAAC,UAAU,CAAC,WAAW,QAAQ,CAAC;MAC1C,OAAO,CAAC,EAAE;MACV,WAAW,CAAC,GAAG,CAAC,WAAW,IAAI,CAAC,GAAG,WAAW,IAAI,EAAE,CAAC,CAAC;MACtD,QAAQ,CAAC,QAAQ,CAAC,WAAW,QAAQ,CAAC;MACtC,aAAa,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,UAAU,EAAE,CAAC,CAAC;KAClE;AAED,QAAI,KAAK,aAAa;AACpB,WAAK,kBAAkB,WAAW,KAAK,WAAW;IACpD;EACF;EAEA,cAAc,WAAiB;AAC7B,UAAM,QAAQ,KAAK,kBAAkB,IAAI,SAAS;AAClD,QAAI,SAAS,MAAM,WAAW,MAAM,SAAS;AAC3C,UAAI,MAAM,SAAS,UAAU,GAAG;AAC9B,eAAO,GAAG,SAAS;MACrB;AACA,UAAI,MAAM,SAAS,WAAW,GAAG;AAC/B,eAAO,GAAG,SAAS,qBAAqB,MAAM,OAAO,WAAW,EAAE,cAAc;MAClF;AACA,UAAI,MAAM,SAAS,KAAK,GAAG;AACzB,eAAO,GAAG,SAAS,qBAAqB,MAAM,OAAO,KAAK,EAAE,GAAG;MACjE;AACA,UAAI,MAAM,SAAS,KAAK,GAAG;AACzB,eAAO,GAAG,SAAS,oBAAoB,MAAM,OAAO,KAAK,EAAE,GAAG;MAChE;IACF;AACA,WAAO;EACT;EAEA,UAAO;AACL,SAAK,kBAAkB,MAAK;AAC5B,SAAK,OAAO,KAAI;EAClB;EAEA,SAAM;AACJ,QAAI,KAAK,kBAAkB,OAAO;AAChC,WAAK,MAAM,KAAK,KAAK,kBAAkB,KAAK;IAC9C;EACF;;qCAlEW,8BAA2B,4BAAA,WAAA,CAAA;EAAA;yEAA3B,8BAA2B,WAAA,CAAA,CAAA,yBAAA,CAAA,GAAA,QAAA,EAAA,QAAA,UAAA,UAAA,YAAA,aAAA,eAAA,SAAA,UAAA,GAAA,SAAA,EAAA,QAAA,UAAA,OAAA,QAAA,GAAA,UAAA,CAAA,8BAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,YAAA,4CAAA,eAAA,sBAAA,GAAA,UAAA,aAAA,aAAA,UAAA,SAAA,WAAA,YAAA,GAAA,CAAA,GAAA,sBAAA,GAAA,WAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,SAAA,uBAAA,eAAA,sCAAA,mBAAA,SAAA,GAAA,YAAA,cAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,mBAAA,YAAA,GAAA,aAAA,GAAA,CAAA,SAAA,KAAA,GAAA,CAAA,SAAA,QAAA,GAAA,CAAA,SAAA,MAAA,GAAA,CAAA,SAAA,UAAA,GAAA,CAAA,SAAA,qBAAA,eAAA,uBAAA,mBAAA,SAAA,GAAA,cAAA,GAAA,CAAA,SAAA,cAAA,eAAA,aAAA,mBAAA,aAAA,QAAA,UAAA,GAAA,cAAA,GAAA,CAAA,mBAAA,UAAA,GAAA,aAAA,GAAA,CAAA,SAAA,MAAA,GAAA,CAAA,SAAA,aAAA,GAAA,CAAA,SAAA,UAAA,GAAA,CAAA,SAAA,gBAAA,GAAA,CAAA,SAAA,eAAA,GAAA,CAAA,GAAA,cAAA,YAAA,GAAA,CAAA,mBAAA,eAAA,eAAA,8EAAA,QAAA,KAAA,GAAA,eAAA,CAAA,GAAA,UAAA,SAAA,qCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAlIpC,MAAA,yBAAA,GAAA,mBAAA,CAAA;AAOE,MAAA,qBAAA,UAAA,SAAA,yEAAA;AAAA,eAAU,IAAA,QAAA;MAAS,CAAA,EAAC,aAAA,SAAA,4EAAA;AAAA,eACP,IAAA,OAAA;MAAQ,CAAA,EAAC,aAAA,SAAA,4EAAA;AAAA,eACT,IAAA,QAAA;MAAS,CAAA;AAEtB,MAAA,yBAAA,GAAA,QAAA,CAAA,EAAiE,GAAA,OAAA,CAAA;AAE7D,MAAA,oBAAA,GAAA,kBAAA,CAAA;AAQA,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAwB,GAAA,SAAA,CAAA;AACI,MAAA,iBAAA,GAAA,UAAA;AAAQ,MAAA,uBAAA;AAClC,MAAA,yBAAA,GAAA,UAAA,CAAA,EAAuD,GAAA,UAAA,CAAA;AACjC,MAAA,iBAAA,GAAA,KAAA;AAAG,MAAA,uBAAA;AACvB,MAAA,yBAAA,IAAA,UAAA,CAAA;AAAuB,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA;AAC7B,MAAA,yBAAA,IAAA,UAAA,CAAA;AAAqB,MAAA,iBAAA,IAAA,MAAA;AAAI,MAAA,uBAAA;AACzB,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAyB,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA,EAAS,EACnC;AAGX,MAAA,oBAAA,IAAA,kBAAA,EAAA,EAKkB,IAAA,kBAAA,EAAA;AAUlB,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,SAAA,CAAA;AACI,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA;AAChC,MAAA,yBAAA,IAAA,UAAA,EAAA,EAAqD,IAAA,UAAA,EAAA;AAC9B,MAAA,iBAAA,IAAA,MAAA;AAAI,MAAA,uBAAA;AACzB,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA4B,MAAA,iBAAA,IAAA,aAAA;AAAW,MAAA,uBAAA;AACvC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAyB,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA;AACjC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA+B,MAAA,iBAAA,IAAA,gBAAA;AAAc,MAAA,uBAAA;AAC7C,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA8B,MAAA,iBAAA,IAAA,eAAA;AAAa,MAAA,uBAAA,EAAS,EAC7C;AAGX,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAmC,IAAA,SAAA,CAAA;AACP,MAAA,iBAAA,IAAA,aAAA;AAAW,MAAA,uBAAA;AACrC,MAAA,oBAAA,IAAA,YAAA,EAAA;AAMF,MAAA,uBAAA,EAAM,EACF,EACD;;;AAlEP,MAAA,qBAAA,UAAA,IAAA,MAAA,EAAiB,SAAA,IAAA,WAAA,uBAAA,mBAAA,EAC8C,WAAA,IAAA,OAAA,EAE5C,cAAA,IAAA,kBAAA,KAAA;AAOb,MAAA,oBAAA;AAAA,MAAA,qBAAA,aAAA,IAAA,iBAAA;AAMA,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,YAAA,IAAA,EAAiB,gBAAA,IAAA,cAAA,OAAA,CAAA;AAkBjB,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,gBAAA,IAAA,cAAA,OAAA,CAAA;AAQA,MAAA,oBAAA;AAAA,MAAA,qBAAA,gBAAA,IAAA,cAAA,WAAA,CAAA;;oBA7CA,cAAc,qBAAmB,oBAAA,gBAAA,8BAAA,sBAAA,4BAAA,iBAAA,sBAAA,mBAAA,oBAAA,iBAAE,qBAAqB,kBAAkB,GAAA,QAAA,CAAA,+yCAAA,EAAA,CAAA;;;sEAoIzE,6BAA2B,CAAA;UAvIvC;uBACW,2BAAyB,YACvB,MAAI,SACP,CAAC,cAAc,qBAAqB,qBAAqB,kBAAkB,GAAC,UAC3E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAsET,QAAA,CAAA,k2CAAA,EAAA,CAAA;uCA8DQ,QAAM,CAAA;UAAd;MACQ,UAAQ,CAAA;UAAhB;MACQ,aAAW,CAAA;UAAnB;MACQ,SAAO,CAAA;UAAf;MAES,QAAM,CAAA;UAAf;MACS,OAAK,CAAA;UAAd;;;;6EAPU,6BAA2B,EAAA,WAAA,+BAAA,UAAA,gGAAA,YAAA,IAAA,CAAA;AAAA,GAAA;;;;;;ACoBlB,IAAA,yBAAA,GAAA,QAAA,EAAA;AAAkD,IAAA,iBAAA,GAAA,UAAA;AAAQ,IAAA,uBAAA;;;;;AAE5D,IAAA,yBAAA,GAAA,KAAA,EAAA;AAAsD,IAAA,iBAAA,CAAA;AAAqB,IAAA,uBAAA;;;;AAArB,IAAA,oBAAA;AAAA,IAAA,4BAAA,OAAA,WAAA;;;;;;AAhBxD,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAyE,GAAA,OAAA,EAAA,EAC5C,GAAA,MAAA,EAAA;AACA,IAAA,iBAAA,CAAA;AAAc,IAAA,uBAAA;AACvC,IAAA,yBAAA,GAAA,UAAA,EAAA;AAA8B,IAAA,qBAAA,SAAA,SAAA,yEAAA;AAAA,YAAA,OAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,iBAAA,IAAA,CAAmB;IAAA,CAAA;;AACxD,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,YAAA,EAAA,EAA2C,GAAA,QAAA,EAAA;AAE7C,IAAA,uBAAA,EAAM,EACC;;AAEX,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAyB,GAAA,MAAA;AACwB,IAAA,iBAAA,EAAA;AAAc,IAAA,uBAAA;AAC7D,IAAA,yBAAA,IAAA,MAAA;AAA6D,IAAA,iBAAA,EAAA;AAAqB,IAAA,uBAAA;AAClF,IAAA,yBAAA,IAAA,QAAA,EAAA;AAA6B,IAAA,iBAAA,EAAA;AAAkB,IAAA,uBAAA;AAC/C,IAAA,qBAAA,IAAA,wDAAA,GAAA,GAAA,QAAA,EAAA;AACF,IAAA,uBAAA;AACA,IAAA,qBAAA,IAAA,qDAAA,GAAA,GAAA,KAAA,EAAA;AACF,IAAA,uBAAA;;;;AAf6B,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,IAAA;AASnB,IAAA,oBAAA,CAAA;AAAA,IAAA,iCAAA,sBAAA,OAAA,MAAA,EAAA;AAAyC,IAAA,oBAAA;AAAA,IAAA,4BAAA,OAAA,IAAA;AACzC,IAAA,oBAAA;AAAA,IAAA,iCAAA,6BAAA,OAAA,aAAA,EAAA;AAAuD,IAAA,oBAAA;AAAA,IAAA,4BAAA,OAAA,WAAA;AAChC,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,SAAA,EAAA;AACD,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,UAAA;AAEC,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,WAAA;;;;;AAjBnC,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,gDAAA,IAAA,IAAA,OAAA,EAAA;AAkBF,IAAA,uBAAA;;;;AAlB2C,IAAA,oBAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,YAAA;;;;;AAoB3C,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA2D,GAAA,GAAA;AACtD,IAAA,iBAAA,GAAA,mEAAA;AAAiE,IAAA,uBAAA,EAAI;;;;;AA6BpE,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA+C,IAAA,iBAAA,GAAA,MAAA;AAAI,IAAA,uBAAA;;;;;AAErD,IAAA,yBAAA,GAAA,KAAA,EAAA;AAAmD,IAAA,iBAAA,CAAA;AAAkB,IAAA,uBAAA;;;;AAAlB,IAAA,oBAAA;AAAA,IAAA,4BAAA,QAAA,OAAA;;;;;;AAfrD,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAuE,GAAA,OAAA,EAAA,EAC1C,GAAA,MAAA,EAAA;AACA,IAAA,iBAAA,CAAA;AAAqB,IAAA,uBAAA;AAC9C,IAAA,yBAAA,GAAA,UAAA,EAAA;AAA8B,IAAA,qBAAA,SAAA,SAAA,yEAAA;AAAA,YAAA,OAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,gBAAA,IAAA,CAAkB;IAAA,CAAA;;AACvD,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,YAAA,EAAA,EAA2C,GAAA,QAAA,EAAA;AAE7C,IAAA,uBAAA,EAAM,EACC;;AAEX,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAyB,GAAA,QAAA,EAAA;AACI,IAAA,iBAAA,EAAA;AAAmB,IAAA,uBAAA;AAC9C,IAAA,yBAAA,IAAA,QAAA,EAAA;AAA6B,IAAA,iBAAA,EAAA;AAAmB,IAAA,uBAAA;AAChD,IAAA,qBAAA,IAAA,wDAAA,GAAA,GAAA,QAAA,EAAA;AACF,IAAA,uBAAA;AACA,IAAA,qBAAA,IAAA,qDAAA,GAAA,GAAA,KAAA,EAAA;AACF,IAAA,uBAAA;;;;AAd6B,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,QAAA,UAAA;AASE,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,QAAA,QAAA;AACE,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,QAAA,SAAA,EAAA;AACD,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,QAAA,MAAA;AAEC,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,QAAA,OAAA;;;;;AAhBnC,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,gDAAA,IAAA,GAAA,OAAA,EAAA;AAiBF,IAAA,uBAAA;;;;AAjB4C,IAAA,oBAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,SAAA;;;;;AAmB5C,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAwD,GAAA,GAAA;AACnD,IAAA,iBAAA,GAAA,mEAAA;AAAiE,IAAA,uBAAA,EAAI;;;;;AA6BpE,IAAA,yBAAA,GAAA,QAAA,EAAA;AAAyD,IAAA,iBAAA,GAAA,iBAAA;AAAe,IAAA,uBAAA;;;;;AAE1E,IAAA,yBAAA,GAAA,KAAA,EAAA;AAAwD,IAAA,iBAAA,CAAA;AAAuB,IAAA,uBAAA;;;;AAAvB,IAAA,oBAAA;AAAA,IAAA,4BAAA,gBAAA,KAAA;;;;;AACxD,IAAA,yBAAA,GAAA,KAAA,EAAA;AAAiE,IAAA,iBAAA,CAAA;AAAgC,IAAA,uBAAA;;;;AAAhC,IAAA,oBAAA;AAAA,IAAA,4BAAA,gBAAA,cAAA;;;;;;AAhBnE,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAiF,GAAA,OAAA,EAAA,EACpD,GAAA,MAAA,EAAA;AACA,IAAA,iBAAA,CAAA;AAAsB,IAAA,uBAAA;AAC/C,IAAA,yBAAA,GAAA,UAAA,EAAA;AAA8B,IAAA,qBAAA,SAAA,SAAA,yEAAA;AAAA,YAAA,QAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,kBAAA,KAAA,CAAoB;IAAA,CAAA;;AACzD,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,YAAA,EAAA,EAA2C,GAAA,QAAA,EAAA;AAE7C,IAAA,uBAAA,EAAM,EACC;;AAEX,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAyB,GAAA,MAAA;AACgC,IAAA,iBAAA,EAAA;AAAwC,IAAA,uBAAA;AAC/F,IAAA,yBAAA,IAAA,QAAA,EAAA;AAA2B,IAAA,iBAAA,EAAA;AAA4B,IAAA,uBAAA;AACvD,IAAA,qBAAA,IAAA,wDAAA,GAAA,GAAA,QAAA,EAAA;AACF,IAAA,uBAAA;AACA,IAAA,qBAAA,IAAA,qDAAA,GAAA,GAAA,KAAA,EAAA,EAAwD,IAAA,qDAAA,GAAA,GAAA,KAAA,EAAA;AAE1D,IAAA,uBAAA;;;;AAf6B,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,gBAAA,IAAA;AASnB,IAAA,oBAAA,CAAA;AAAA,IAAA,iCAAA,sBAAA,gBAAA,MAAA,EAAA;AAAiD,IAAA,oBAAA;AAAA,IAAA,4BAAA,gBAAA,KAAA,QAAA,KAAA,GAAA,CAAA;AAC5B,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,gBAAA,UAAA;AACC,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,gBAAA,SAAA;AAEC,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,gBAAA,KAAA;AACA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,gBAAA,cAAA;;;;;AAjBnC,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,gDAAA,IAAA,GAAA,OAAA,EAAA;AAkBF,IAAA,uBAAA;;;;AAlBmD,IAAA,oBAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,YAAA;;;;;AAoBnD,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA2D,GAAA,GAAA;AACtD,IAAA,iBAAA,GAAA,oEAAA;AAAkE,IAAA,uBAAA,EAAI;;;;;AA6BrE,IAAA,yBAAA,GAAA,QAAA,EAAA;AAAgD,IAAA,iBAAA,GAAA,QAAA;AAAM,IAAA,uBAAA;;;;;AACtD,IAAA,yBAAA,GAAA,KAAA,EAAA;AAAwE,IAAA,iBAAA,GAAA,eAAA;AAAa,IAAA,uBAAA;;;;AAAlF,IAAA,qBAAA,QAAA,QAAA,KAAA,uBAAA;;;;;AAEL,IAAA,yBAAA,GAAA,KAAA,EAAA;AAAsD,IAAA,iBAAA,CAAA;AAAqB,IAAA,uBAAA;;;;AAArB,IAAA,oBAAA;AAAA,IAAA,4BAAA,QAAA,WAAA;;;;;;AAhBxD,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA0E,GAAA,OAAA,EAAA,EAC7C,GAAA,MAAA,EAAA;AACA,IAAA,iBAAA,CAAA;AAAe,IAAA,uBAAA;AACxC,IAAA,yBAAA,GAAA,UAAA,EAAA;AAA8B,IAAA,qBAAA,SAAA,SAAA,yEAAA;AAAA,YAAA,QAAA,wBAAA,IAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,oBAAA,KAAA,CAAsB;IAAA,CAAA;;AAC3D,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,YAAA,EAAA,EAA2C,GAAA,QAAA,EAAA;AAE7C,IAAA,uBAAA,EAAM,EACC;;AAEX,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAyB,GAAA,QAAA,EAAA;AACI,IAAA,iBAAA,EAAA;AAAgC,IAAA,uBAAA;AAC3D,IAAA,yBAAA,IAAA,QAAA,EAAA;AAA6B,IAAA,iBAAA,EAAA;AAAkB,IAAA,uBAAA;AAC/C,IAAA,qBAAA,IAAA,wDAAA,GAAA,GAAA,QAAA,EAAA,EAAgD,IAAA,qDAAA,GAAA,GAAA,KAAA,EAAA;AAElD,IAAA,uBAAA;AACA,IAAA,qBAAA,IAAA,qDAAA,GAAA,GAAA,KAAA,EAAA;AACF,IAAA,uBAAA;;;;AAf6B,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,QAAA,KAAA;AASE,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,QAAA,KAAA,QAAA,KAAA,GAAA,CAAA;AACE,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,KAAA,QAAA,SAAA,EAAA;AACD,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,QAAA,QAAA;AAC6B,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,QAAA,GAAA;AAE5B,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,QAAA,WAAA;;;;;AAjBnC,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,gDAAA,IAAA,GAAA,OAAA,EAAA;AAkBF,IAAA,uBAAA;;;;AAlB2C,IAAA,oBAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,aAAA;;;;;AAoB3C,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA4D,GAAA,GAAA;AACvD,IAAA,iBAAA,GAAA,kEAAA;AAAgE,IAAA,uBAAA,EAAI;;;;;AA6BnE,IAAA,yBAAA,GAAA,QAAA,EAAA;AAAwD,IAAA,iBAAA,CAAA;AAA0B,IAAA,uBAAA;;;;AAA1B,IAAA,oBAAA;AAAA,IAAA,6BAAA,UAAA,SAAA,WAAA,EAAA;;;;;AACxD,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA8C,IAAA,iBAAA,CAAA;AAAgB,IAAA,uBAAA;;;;AAAhB,IAAA,oBAAA;AAAA,IAAA,4BAAA,SAAA,KAAA;;;;;AAEhD,IAAA,yBAAA,GAAA,KAAA,EAAA;AAAuD,IAAA,iBAAA,CAAA;AAAsB,IAAA,uBAAA;;;;AAAtB,IAAA,oBAAA;AAAA,IAAA,4BAAA,SAAA,WAAA;;;;;;AAhBzD,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA6E,GAAA,OAAA,EAAA,EAChD,GAAA,MAAA,EAAA;AACA,IAAA,iBAAA,CAAA;AAAgB,IAAA,uBAAA;AACzC,IAAA,yBAAA,GAAA,UAAA,EAAA;AAA8B,IAAA,qBAAA,SAAA,SAAA,yEAAA;AAAA,YAAA,QAAA,wBAAA,IAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,oBAAA,KAAA,CAAsB;IAAA,CAAA;;AAC3D,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,YAAA,EAAA,EAA2C,GAAA,QAAA,EAAA;AAE7C,IAAA,uBAAA,EAAM,EACC;;AAEX,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAyB,GAAA,MAAA;AACoC,IAAA,iBAAA,EAAA;AAAmB,IAAA,uBAAA;AAC9E,IAAA,yBAAA,IAAA,QAAA,EAAA;AAA2B,IAAA,iBAAA,EAAA;AAAmC,IAAA,uBAAA;AAC9D,IAAA,qBAAA,IAAA,wDAAA,GAAA,GAAA,QAAA,EAAA,EAAwD,IAAA,wDAAA,GAAA,GAAA,QAAA,EAAA;AAE1D,IAAA,uBAAA;AACA,IAAA,qBAAA,IAAA,qDAAA,GAAA,GAAA,KAAA,EAAA;AACF,IAAA,uBAAA;;;;AAf6B,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,SAAA,KAAA;AASnB,IAAA,oBAAA,CAAA;AAAA,IAAA,iCAAA,6BAAA,SAAA,UAAA,EAAA;AAAqD,IAAA,oBAAA;AAAA,IAAA,4BAAA,SAAA,QAAA;AAChC,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,SAAA,OAAA,QAAA,KAAA,GAAA,CAAA;AACG,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,SAAA,YAAA,CAAA;AACF,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,SAAA,KAAA;AAEC,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,SAAA,WAAA;;;;;AAjBnC,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,gDAAA,IAAA,GAAA,OAAA,EAAA;AAkBF,IAAA,uBAAA;;;;AAlB4C,IAAA,oBAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,eAAA;;;;;AAoB5C,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA8D,GAAA,GAAA;AACzD,IAAA,iBAAA,GAAA,yEAAA;AAAuE,IAAA,uBAAA,EAAI;;;AAwV1F,IAAO,2BAAP,MAAO,0BAAwB;EAmJzB;EACA;EAnJkB;EAE5B,aAAa;EACb,WAAW;EACX,gBAA+B;;EAG/B;EACA,cAAc;;EAGd,WAAsB;IACpB,EAAE,IAAI,SAAS,OAAO,qBAAqB,MAAM,4DAA2D;IAC5G,EAAE,IAAI,gBAAgB,OAAO,gBAAgB,MAAM,oGAAmG;IACtJ,EAAE,IAAI,aAAa,OAAO,cAAc,MAAM,wQAAuQ;IACrT,EAAE,IAAI,gBAAgB,OAAO,gBAAgB,MAAM,kHAAiH;IACpK,EAAE,IAAI,iBAAiB,OAAO,iBAAiB,MAAM,uHAAsH;IAC3K,EAAE,IAAI,YAAY,OAAO,YAAY,MAAM,iMAAgM;;;EAI7O,eAAsB,CAAA;EACtB,YAAmB,CAAA;EACnB,eAAsB,CAAA;EACtB,gBAAuB,CAAA;EACvB,kBAAyB,CAAA;;EAGzB,sBAAsB;EACtB,qBAAqB;EACrB,qBAAgD;EAChD,sBAAsB;EAEtB,qBAAqB;EACrB,oBAAoB;EACpB,oBAA8C;EAC9C,qBAAqB;EAErB,uBAAuB;EACvB,sBAAsB;EACtB,sBAAkD;EAClD,uBAAuB;EAEvB,yBAAyB;EACzB,wBAAwB;EACxB,wBAAsD;EACtD,yBAAyB;EAEzB,yBAAyB;EACzB,wBAAwB;EACxB,wBAAsD;EACtD,yBAAyB;EAEzB,eAAe;;EAGf,yBAAyB;IACvB,EAAE,OAAO,YAAY,OAAO,WAAU;IACtC,EAAE,OAAO,eAAe,OAAO,cAAa;IAC5C,EAAE,OAAO,WAAW,OAAO,UAAS;IACpC,EAAE,OAAO,WAAW,OAAO,UAAS;IACpC,EAAE,OAAO,cAAc,OAAO,aAAY;IAC1C,EAAE,OAAO,eAAe,OAAO,cAAa;IAC5C,EAAE,OAAO,cAAc,OAAO,aAAY;IAC1C,EAAE,OAAO,WAAW,OAAO,UAAS;;EAGtC,6BAA6B;IAC3B,EAAE,OAAO,OAAO,OAAO,MAAK;IAC5B,EAAE,OAAO,UAAU,OAAO,SAAQ;IAClC,EAAE,OAAO,QAAQ,OAAO,OAAM;IAC9B,EAAE,OAAO,YAAY,OAAO,WAAU;;EAGxC,wBAAwB;IACtB,EAAE,OAAO,WAAW,OAAO,UAAS;IACpC,EAAE,OAAO,aAAa,OAAO,YAAW;IACxC,EAAE,OAAO,WAAW,OAAO,UAAS;IACpC,EAAE,OAAO,YAAY,OAAO,WAAU;IACtC,EAAE,OAAO,OAAO,OAAO,MAAK;IAC5B,EAAE,OAAO,QAAQ,OAAO,OAAM;IAC9B,EAAE,OAAO,kBAAkB,OAAO,iBAAgB;;EAGpD,+BAA+B;IAC7B,EAAE,OAAO,OAAO,OAAO,MAAK;IAC5B,EAAE,OAAO,UAAU,OAAO,SAAQ;IAClC,EAAE,OAAO,QAAQ,OAAO,OAAM;IAC9B,EAAE,OAAO,YAAY,OAAO,WAAU;;EAGxC,2BAA2B;IACzB,EAAE,OAAO,YAAY,OAAO,WAAU;IACtC,EAAE,OAAO,WAAW,OAAO,UAAS;IACpC,EAAE,OAAO,YAAY,OAAO,WAAU;IACtC,EAAE,OAAO,kBAAkB,OAAO,iBAAgB;IAClD,EAAE,OAAO,cAAc,OAAO,aAAY;IAC1C,EAAE,OAAO,YAAY,OAAO,WAAU;IACtC,EAAE,OAAO,WAAW,OAAO,UAAS;IACpC,EAAE,OAAO,cAAc,OAAO,aAAY;IAC1C,EAAE,OAAO,iBAAiB,OAAO,gBAAe;;EAGlD,yBAAyB;IACvB,EAAE,OAAO,iBAAiB,OAAO,gBAAe;IAChD,EAAE,OAAO,aAAa,OAAO,YAAW;IACxC,EAAE,OAAO,aAAa,OAAO,YAAW;IACxC,EAAE,OAAO,mBAAmB,OAAO,kBAAiB;IACpD,EAAE,OAAO,oBAAoB,OAAO,mBAAkB;IACtD,EAAE,OAAO,oBAAoB,OAAO,mBAAkB;IACtD,EAAE,OAAO,mBAAmB,OAAO,kBAAiB;IACpD,EAAE,OAAO,aAAa,OAAO,YAAW;IACxC,EAAE,OAAO,eAAe,OAAO,cAAa;IAC5C,EAAE,OAAO,oBAAoB,OAAO,mBAAkB;;EAGxD,2BAA2B;IACzB,EAAE,OAAO,kBAAkB,OAAO,0BAAyB;IAC3D,EAAE,OAAO,qBAAqB,OAAO,oBAAmB;IACxD,EAAE,OAAO,eAAe,OAAO,cAAa;IAC5C,EAAE,OAAO,oBAAoB,OAAO,mBAAkB;IACtD,EAAE,OAAO,wBAAwB,OAAO,uBAAsB;IAC9D,EAAE,OAAO,mBAAmB,OAAO,kBAAiB;IACpD,EAAE,OAAO,qBAAqB,OAAO,oBAAmB;IACxD,EAAE,OAAO,WAAW,OAAO,UAAS;IACpC,EAAE,OAAO,mBAAmB,OAAO,kBAAiB;IACpD,EAAE,OAAO,aAAa,OAAO,YAAW;;EAG1C,+BAA+B;IAC7B,EAAE,OAAO,YAAY,OAAO,WAAU;IACtC,EAAE,OAAO,QAAQ,OAAO,OAAM;IAC9B,EAAE,OAAO,UAAU,OAAO,SAAQ;IAClC,EAAE,OAAO,OAAO,OAAO,MAAK;IAC5B,EAAE,OAAO,QAAQ,OAAO,OAAM;;EAGhC,6BAA6B;IAC3B,EAAE,OAAO,QAAQ,OAAO,OAAM;IAC9B,EAAE,OAAO,eAAe,OAAO,cAAa;IAC5C,EAAE,OAAO,YAAY,OAAO,WAAU;IACtC,EAAE,OAAO,iBAAiB,OAAO,gBAAe;IAChD,EAAE,OAAO,kBAAkB,OAAO,iBAAgB;;EAGpD,YACU,IACA,OAAqB;AADrB,SAAA,KAAA;AACA,SAAA,QAAA;EACP;EAEH,WAAQ;AACN,SAAK,eAAc;AACnB,SAAK,cAAa;EACpB;EAEQ,iBAAc;AACpB,SAAK,kBAAkB,KAAK,GAAG,MAAM;MACnC,MAAM,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,UAAU,CAAC,CAAC,CAAC;MACzD,aAAa,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,UAAU,EAAE,CAAC,CAAC;MACjE,OAAO,CAAC,IAAI,CAAC,WAAW,QAAQ,CAAC;MACjC,iBAAiB,CAAC,IAAI,CAAC,WAAW,QAAQ,CAAC;MAC3C,qBAAqB,CAAC,IAAI,CAAC,WAAW,QAAQ,CAAC;MAC/C,SAAS,CAAC,IAAI,CAAC,WAAW,QAAQ,CAAC;MACnC,YAAY,CAAC,EAAE;MACf,eAAe,CAAC,EAAE;KACnB;EACH;EAEQ,gBAAa;AACnB,SAAK,MAAM,OAAO,UAAU,YAAS;AACnC,UAAI,OAAO,IAAI,GAAG;AAChB,aAAK,aAAa;AAClB,aAAK,gBAAgB,CAAC,OAAO,IAAI;AACjC,aAAK,gBAAgB,KAAK,aAAa;MACzC;IACF,CAAC;EACH;EAEQ,gBAAgB,IAAU;AAGhC,SAAK,gBAAgB,WAAW;MAC9B,MAAM;MACN,aAAa;MACb,OAAO;MACP,iBAAiB;MACjB,qBAAqB;MACrB,SAAS;MACT,YAAY;MACZ,eAAe;KAChB;AAGD,SAAK,eAAe;MAClB,EAAE,MAAM,WAAW,MAAM,aAAa,SAAS,UAAU,aAAa,QAAQ,YAAY,OAAO,aAAa,qBAAoB;;EAEtI;EAEA,YAAY,OAAa;AACvB,SAAK,cAAc;EACrB;EAEA,cAAc,WAAiB;AAC7B,UAAM,QAAQ,KAAK,gBAAgB,IAAI,SAAS;AAChD,QAAI,SAAS,MAAM,WAAW,MAAM,SAAS;AAC3C,UAAI,MAAM,SAAS,UAAU,GAAG;AAC9B,eAAO,GAAG,SAAS;MACrB;AACA,UAAI,MAAM,SAAS,WAAW,GAAG;AAC/B,eAAO,GAAG,SAAS,qBAAqB,MAAM,OAAO,WAAW,EAAE,cAAc;MAClF;AACA,UAAI,MAAM,SAAS,OAAO,GAAG;AAC3B,eAAO;MACT;AACA,UAAI,MAAM,SAAS,KAAK,GAAG;AACzB,eAAO;MACT;IACF;AACA,WAAO;EACT;;EAGA,sBAAmB;AACjB,SAAK,qBAAqB;AAC1B,SAAK,qBAAqB;AAC1B,SAAK,sBAAsB;AAC3B,SAAK,sBAAsB;EAC7B;EAEA,uBAAoB;AAClB,SAAK,sBAAsB;AAC3B,SAAK,qBAAqB;AAC1B,SAAK,qBAAqB;AAC1B,SAAK,sBAAsB;EAC7B;EAEA,eAAe,MAAwB;AACrC,QAAI,KAAK,sBAAsB,KAAK,uBAAuB,GAAG;AAC5D,WAAK,aAAa,KAAK,mBAAmB,IAAI;IAChD,OAAO;AACL,WAAK,aAAa,KAAK,IAAI;IAC7B;AACA,SAAK,qBAAoB;EAC3B;EAEA,iBAAiB,OAAa;AAC5B,SAAK,aAAa,OAAO,OAAO,CAAC;EACnC;;EAGA,qBAAkB;AAChB,SAAK,oBAAoB;AACzB,SAAK,oBAAoB;AACzB,SAAK,qBAAqB;AAC1B,SAAK,qBAAqB;EAC5B;EAEA,sBAAmB;AACjB,SAAK,qBAAqB;AAC1B,SAAK,oBAAoB;AACzB,SAAK,oBAAoB;AACzB,SAAK,qBAAqB;EAC5B;EAEA,cAAc,MAAuB;AACnC,QAAI,KAAK,qBAAqB,KAAK,sBAAsB,GAAG;AAC1D,WAAK,UAAU,KAAK,kBAAkB,IAAI;IAC5C,OAAO;AACL,WAAK,UAAU,KAAK,IAAI;IAC1B;AACA,SAAK,oBAAmB;EAC1B;EAEA,gBAAgB,OAAa;AAC3B,SAAK,UAAU,OAAO,OAAO,CAAC;EAChC;;EAGA,uBAAoB;AAClB,SAAK,sBAAsB;AAC3B,SAAK,sBAAsB;AAC3B,SAAK,uBAAuB;AAC5B,SAAK,uBAAuB;EAC9B;EAEA,wBAAqB;AACnB,SAAK,uBAAuB;AAC5B,SAAK,sBAAsB;AAC3B,SAAK,sBAAsB;AAC3B,SAAK,uBAAuB;EAC9B;EAEA,gBAAgB,MAAyB;AACvC,QAAI,KAAK,uBAAuB,KAAK,wBAAwB,GAAG;AAC9D,WAAK,aAAa,KAAK,oBAAoB,IAAI;IACjD,OAAO;AACL,WAAK,aAAa,KAAK,IAAI;IAC7B;AACA,SAAK,sBAAqB;EAC5B;EAEA,kBAAkB,OAAa;AAC7B,SAAK,aAAa,OAAO,OAAO,CAAC;EACnC;;EAGA,yBAAsB;AACpB,SAAK,wBAAwB;AAC7B,SAAK,wBAAwB;AAC7B,SAAK,yBAAyB;AAC9B,SAAK,yBAAyB;EAChC;EAEA,0BAAuB;AACrB,SAAK,yBAAyB;AAC9B,SAAK,wBAAwB;AAC7B,SAAK,wBAAwB;AAC7B,SAAK,yBAAyB;EAChC;EAEA,kBAAkB,MAA2B;AAC3C,QAAI,KAAK,yBAAyB,KAAK,0BAA0B,GAAG;AAClE,WAAK,cAAc,KAAK,sBAAsB,IAAI;IACpD,OAAO;AACL,WAAK,cAAc,KAAK,IAAI;IAC9B;AACA,SAAK,wBAAuB;EAC9B;EAEA,oBAAoB,OAAa;AAC/B,SAAK,cAAc,OAAO,OAAO,CAAC;EACpC;;EAGA,yBAAsB;AACpB,SAAK,wBAAwB;AAC7B,SAAK,wBAAwB;AAC7B,SAAK,yBAAyB;AAC9B,SAAK,yBAAyB;EAChC;EAEA,0BAAuB;AACrB,SAAK,yBAAyB;AAC9B,SAAK,wBAAwB;AAC7B,SAAK,wBAAwB;AAC7B,SAAK,yBAAyB;EAChC;EAEA,kBAAkB,MAA2B;AAC3C,QAAI,KAAK,yBAAyB,KAAK,0BAA0B,GAAG;AAClE,WAAK,gBAAgB,KAAK,sBAAsB,IAAI;IACtD,OAAO;AACL,WAAK,gBAAgB,KAAK,IAAI;IAChC;AACA,SAAK,wBAAuB;EAC9B;EAEA,oBAAoB,OAAa;AAC/B,SAAK,gBAAgB,OAAO,OAAO,CAAC;EACtC;EAEA,kBAAe;AACb,QAAI,KAAK,gBAAgB,OAAO;AAC9B,WAAK,WAAW;AAGhB,YAAM,kBAAkB,iCACnB,KAAK,gBAAgB,QADF;QAEtB,cAAc,KAAK;QACnB,WAAW,KAAK;QAChB,cAAc,KAAK;QACnB,eAAe,KAAK;QACpB,iBAAiB,KAAK;;AAIxB,cAAQ,IAAI,uBAAuB,eAAe;AAGlD,iBAAW,MAAK;AACd,aAAK,WAAW;AAEhB,gBAAQ,IAAI,iCAAiC;MAC/C,GAAG,GAAI;IACT,OAAO;AAEL,WAAK,qBAAqB,KAAK,eAAe;AAG9C,WAAK,sBAAqB;IAC5B;EACF;EAEQ,qBAAqB,WAAoB;AAC/C,WAAO,KAAK,UAAU,QAAQ,EAAE,QAAQ,SAAM;AAC5C,YAAM,UAAU,UAAU,IAAI,GAAG;AACjC,eAAS,cAAa;IACxB,CAAC;EACH;EAEQ,wBAAqB;AAE3B,QAAI,KAAK,gBAAgB,SAAS;AAChC,WAAK,cAAc;AACnB,WAAK,eAAe,aAAa,OAAO;AACxC;IACF;EAGF;;qCA1ZW,2BAAwB,4BAAA,WAAA,GAAA,4BAAA,cAAA,CAAA;EAAA;yEAAxB,2BAAwB,WAAA,CAAA,CAAA,sBAAA,CAAA,GAAA,WAAA,SAAA,+BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;;;;;;;;;;AA1nBjC,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAmC,GAAA,OAAA,CAAA,EACR,GAAA,OAAA,CAAA,EACK,GAAA,OAAA,CAAA,EACD,GAAA,MAAA,CAAA;AACA,MAAA,iBAAA,CAAA;AAAyD,MAAA,uBAAA;AAChF,MAAA,yBAAA,GAAA,KAAA,CAAA;AACE,MAAA,iBAAA,CAAA;AACF,MAAA,uBAAA,EAAI;AAEN,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA4B,GAAA,cAAA,CAAA;AAExB,MAAA,iBAAA,IAAA,UAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,cAAA,CAAA;AAGE,MAAA,qBAAA,WAAA,SAAA,mEAAA;AAAA,QAAA,wBAAA,GAAA;AAAA,eAAA,sBAAW,IAAA,gBAAA,CAAiB;MAAA,CAAA;AAE5B,MAAA,iBAAA,EAAA;AACF,MAAA,uBAAA,EAAa,EACT,EACF;AAGR,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA0B,IAAA,QAAA,EAAA;AACY,MAAA,qBAAA,YAAA,SAAA,8DAAA;AAAA,QAAA,wBAAA,GAAA;AAAA,eAAA,sBAAY,IAAA,gBAAA,CAAiB;MAAA,CAAA;AAC/D,MAAA,yBAAA,IAAA,YAAA,IAAA,CAAA;AAGE,MAAA,qBAAA,aAAA,SAAA,iEAAA,QAAA;AAAA,QAAA,wBAAA,GAAA;AAAA,eAAA,sBAAa,IAAA,YAAA,MAAA,CAAmB;MAAA,CAAA;AAIhC,MAAA,yBAAA,IAAA,WAAA,EAAA,EAA8C,IAAA,OAAA,EAAA,EAClB,IAAA,OAAA,EAAA;AAEtB,MAAA,oBAAA,IAAA,kBAAA,EAAA,EAMkB,IAAA,kBAAA,EAAA,EAQA,IAAA,kBAAA,EAAA,EAUA,IAAA,kBAAA,EAAA,EAUA,IAAA,kBAAA,EAAA,EAQA,IAAA,kBAAA,EAAA,EAQA,IAAA,kBAAA,EAAA;AASpB,MAAA,uBAAA;AAEA,MAAA,oBAAA,IAAA,kBAAA,EAAA;AAWF,MAAA,uBAAA,EAAM;AAIR,MAAA,yBAAA,IAAA,WAAA,EAAA,EAAgD,IAAA,OAAA,EAAA,EACpB,IAAA,OAAA,EAAA,EACI,IAAA,IAAA;AACtB,MAAA,iBAAA,IAAA,0BAAA;AAAwB,MAAA,uBAAA;AAC5B,MAAA,yBAAA,IAAA,cAAA,EAAA;AAAwC,MAAA,qBAAA,WAAA,SAAA,mEAAA;AAAA,QAAA,wBAAA,GAAA;AAAA,eAAA,sBAAW,IAAA,oBAAA,CAAqB;MAAA,CAAA;AACtE,MAAA,iBAAA,IAAA,kBAAA;AACF,MAAA,uBAAA,EAAa;AAGf,MAAA,qBAAA,IAAA,0CAAA,GAAA,GAAA,OAAA,EAAA,EAAyD,IAAA,0CAAA,GAAA,GAAA,OAAA,EAAA;AAwB3D,MAAA,uBAAA,EAAM;AAIR,MAAA,yBAAA,IAAA,WAAA,EAAA,EAA2C,IAAA,OAAA,EAAA,EACf,IAAA,OAAA,EAAA,EACI,IAAA,IAAA;AACtB,MAAA,iBAAA,IAAA,kBAAA;AAAgB,MAAA,uBAAA;AACpB,MAAA,yBAAA,IAAA,cAAA,EAAA;AAAwC,MAAA,qBAAA,WAAA,SAAA,mEAAA;AAAA,QAAA,wBAAA,GAAA;AAAA,eAAA,sBAAW,IAAA,mBAAA,CAAoB;MAAA,CAAA;AACrE,MAAA,iBAAA,IAAA,kBAAA;AACF,MAAA,uBAAA,EAAa;AAGf,MAAA,qBAAA,IAAA,0CAAA,GAAA,GAAA,OAAA,EAAA,EAAsD,IAAA,0CAAA,GAAA,GAAA,OAAA,EAAA;AAuBxD,MAAA,uBAAA,EAAM;AAIR,MAAA,yBAAA,IAAA,WAAA,EAAA,EAAgD,IAAA,OAAA,EAAA,EACpB,IAAA,OAAA,EAAA,EACI,IAAA,IAAA;AACtB,MAAA,iBAAA,IAAA,sBAAA;AAAoB,MAAA,uBAAA;AACxB,MAAA,yBAAA,IAAA,cAAA,EAAA;AAAwC,MAAA,qBAAA,WAAA,SAAA,mEAAA;AAAA,QAAA,wBAAA,GAAA;AAAA,eAAA,sBAAW,IAAA,qBAAA,CAAsB;MAAA,CAAA;AACvE,MAAA,iBAAA,IAAA,mBAAA;AACF,MAAA,uBAAA,EAAa;AAGf,MAAA,qBAAA,IAAA,0CAAA,GAAA,GAAA,OAAA,EAAA,EAAyD,IAAA,0CAAA,GAAA,GAAA,OAAA,EAAA;AAwB3D,MAAA,uBAAA,EAAM;AAIR,MAAA,yBAAA,IAAA,WAAA,EAAA,EAAkD,IAAA,OAAA,EAAA,EACtB,IAAA,OAAA,EAAA,EACI,IAAA,IAAA;AACtB,MAAA,iBAAA,IAAA,2BAAA;AAAyB,MAAA,uBAAA;AAC7B,MAAA,yBAAA,IAAA,cAAA,EAAA;AAAwC,MAAA,qBAAA,WAAA,SAAA,mEAAA;AAAA,QAAA,wBAAA,GAAA;AAAA,eAAA,sBAAW,IAAA,uBAAA,CAAwB;MAAA,CAAA;AACzE,MAAA,iBAAA,IAAA,gBAAA;AACF,MAAA,uBAAA,EAAa;AAGf,MAAA,qBAAA,IAAA,0CAAA,GAAA,GAAA,OAAA,EAAA,EAA0D,IAAA,0CAAA,GAAA,GAAA,OAAA,EAAA;AAwB5D,MAAA,uBAAA,EAAM;AAIR,MAAA,yBAAA,IAAA,WAAA,EAAA,EAAwC,IAAA,OAAA,EAAA,EACZ,IAAA,OAAA,EAAA,EACI,IAAA,IAAA;AACtB,MAAA,iBAAA,IAAA,4BAAA;AAA0B,MAAA,uBAAA;AAC9B,MAAA,yBAAA,IAAA,cAAA,EAAA;AAAwC,MAAA,qBAAA,WAAA,SAAA,mEAAA;AAAA,QAAA,wBAAA,GAAA;AAAA,eAAA,sBAAW,IAAA,uBAAA,CAAwB;MAAA,CAAA;AACzE,MAAA,iBAAA,IAAA,qBAAA;AACF,MAAA,uBAAA,EAAa;AAGf,MAAA,qBAAA,IAAA,0CAAA,GAAA,GAAA,OAAA,EAAA,EAA4D,IAAA,0CAAA,GAAA,GAAA,OAAA,EAAA;AAwB9D,MAAA,uBAAA,EAAM,EACE,EACD,EACN;AAIT,MAAA,yBAAA,IAAA,wBAAA,EAAA;AAKE,MAAA,qBAAA,UAAA,SAAA,4EAAA;AAAA,QAAA,wBAAA,GAAA;AAAA,eAAA,sBAAU,IAAA,qBAAA,CAAsB;MAAA,CAAA,EAAC,SAAA,SAAA,yEAAA,QAAA;AAAA,QAAA,wBAAA,GAAA;AAAA,eAAA,sBACxB,IAAA,eAAA,MAAA,CAAsB;MAAA,CAAA;AAChC,MAAA,uBAAA;AAED,MAAA,yBAAA,IAAA,wBAAA,EAAA;AAKE,MAAA,qBAAA,UAAA,SAAA,4EAAA;AAAA,QAAA,wBAAA,GAAA;AAAA,eAAA,sBAAU,IAAA,oBAAA,CAAqB;MAAA,CAAA,EAAC,SAAA,SAAA,yEAAA,QAAA;AAAA,QAAA,wBAAA,GAAA;AAAA,eAAA,sBACvB,IAAA,cAAA,MAAA,CAAqB;MAAA,CAAA;AAC/B,MAAA,uBAAA;AAED,MAAA,yBAAA,IAAA,yBAAA,EAAA;AAKE,MAAA,qBAAA,UAAA,SAAA,6EAAA;AAAA,QAAA,wBAAA,GAAA;AAAA,eAAA,sBAAU,IAAA,sBAAA,CAAuB;MAAA,CAAA,EAAC,SAAA,SAAA,0EAAA,QAAA;AAAA,QAAA,wBAAA,GAAA;AAAA,eAAA,sBACzB,IAAA,gBAAA,MAAA,CAAuB;MAAA,CAAA;AACjC,MAAA,uBAAA;AAED,MAAA,yBAAA,IAAA,2BAAA,EAAA;AAKE,MAAA,qBAAA,UAAA,SAAA,+EAAA;AAAA,QAAA,wBAAA,GAAA;AAAA,eAAA,sBAAU,IAAA,wBAAA,CAAyB;MAAA,CAAA,EAAC,SAAA,SAAA,4EAAA,QAAA;AAAA,QAAA,wBAAA,GAAA;AAAA,eAAA,sBAC3B,IAAA,kBAAA,MAAA,CAAyB;MAAA,CAAA;AACnC,MAAA,uBAAA;AAED,MAAA,yBAAA,IAAA,2BAAA,EAAA;AAKE,MAAA,qBAAA,UAAA,SAAA,+EAAA;AAAA,QAAA,wBAAA,GAAA;AAAA,eAAA,sBAAU,IAAA,wBAAA,CAAyB;MAAA,CAAA,EAAC,SAAA,SAAA,4EAAA,QAAA;AAAA,QAAA,wBAAA,GAAA;AAAA,eAAA,sBAC3B,IAAA,kBAAA,MAAA,CAAyB;MAAA,CAAA;AACnC,MAAA,uBAAA,EAA0B;;;AAlVE,MAAA,oBAAA,CAAA;AAAA,MAAA,4BAAA,IAAA,aAAA,qBAAA,iBAAA;AAErB,MAAA,oBAAA,CAAA;AAAA,MAAA,6BAAA,KAAA,IAAA,aAAA,kDAAA,8CAAA,GAAA;AASA,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,WAAA,IAAA,QAAA;AAGA,MAAA,oBAAA;AAAA,MAAA,6BAAA,KAAA,IAAA,aAAA,WAAA,UAAA,eAAA;AAOA,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,aAAA,IAAA,eAAA;AAEF,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,QAAA,EAAiB,eAAA,IAAA,WAAA;AAaT,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,YAAA,IAAA,EAAiB,gBAAA,IAAA,cAAA,MAAA,CAAA;AAQjB,MAAA,oBAAA;AAAA,MAAA,qBAAA,YAAA,IAAA,EAAiB,gBAAA,IAAA,cAAA,OAAA,CAAA;AASjB,MAAA,oBAAA;AAAA,MAAA,qBAAA,WAAA,IAAA,sBAAA,EAAkC,YAAA,IAAA,EACjB,gBAAA,IAAA,cAAA,iBAAA,CAAA;AASjB,MAAA,oBAAA;AAAA,MAAA,qBAAA,WAAA,IAAA,0BAAA,EAAsC,YAAA,IAAA,EACrB,gBAAA,IAAA,cAAA,qBAAA,CAAA;AAQjB,MAAA,oBAAA;AAAA,MAAA,qBAAA,YAAA,IAAA,EAAiB,gBAAA,IAAA,cAAA,SAAA,CAAA;AASjB,MAAA,oBAAA;AAAA,MAAA,qBAAA,gBAAA,IAAA,cAAA,YAAA,CAAA;AAQA,MAAA,oBAAA;AAAA,MAAA,qBAAA,gBAAA,IAAA,cAAA,eAAA,CAAA;AASF,MAAA,oBAAA;AAAA,MAAA,qBAAA,YAAA,IAAA,EAAiB,QAAA,CAAA,EACP,aAAA,GAAA,EACO,sBAAA,IAAA,EACU,gBAAA,IAAA,cAAA,aAAA,CAAA;AAgBH,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,aAAA,SAAA,CAAA;AAqBA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,aAAA,WAAA,CAAA;AAgBA,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,UAAA,SAAA,CAAA;AAoBA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,UAAA,WAAA,CAAA;AAgBA,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,aAAA,SAAA,CAAA;AAqBA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,aAAA,WAAA,CAAA;AAgBA,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,cAAA,SAAA,CAAA;AAqBA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,cAAA,WAAA,CAAA;AAgBA,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,gBAAA,SAAA,CAAA;AAqBA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,gBAAA,WAAA,CAAA;AAWlC,MAAA,oBAAA;AAAA,MAAA,qBAAA,UAAA,IAAA,mBAAA,EAA8B,YAAA,IAAA,kBAAA,EACC,eAAA,IAAA,kBAAA,EACG,WAAA,IAAA,YAAA;AAOlC,MAAA,oBAAA;AAAA,MAAA,qBAAA,UAAA,IAAA,kBAAA,EAA6B,YAAA,IAAA,iBAAA,EACC,eAAA,IAAA,iBAAA,EACG,WAAA,IAAA,YAAA;AAOjC,MAAA,oBAAA;AAAA,MAAA,qBAAA,UAAA,IAAA,oBAAA,EAA+B,YAAA,IAAA,mBAAA,EACC,eAAA,IAAA,mBAAA,EACG,WAAA,IAAA,YAAA;AAOnC,MAAA,oBAAA;AAAA,MAAA,qBAAA,UAAA,IAAA,sBAAA,EAAiC,YAAA,IAAA,qBAAA,EACC,eAAA,IAAA,qBAAA,EACG,WAAA,IAAA,YAAA;AAOrC,MAAA,oBAAA;AAAA,MAAA,qBAAA,UAAA,IAAA,sBAAA,EAAiC,YAAA,IAAA,qBAAA,EACC,eAAA,IAAA,qBAAA,EACG,WAAA,IAAA,YAAA;;;IAjWzC;IAAY;IAAA;IACZ;IAAY;IACZ;IAAmB;IAAA;IAAA;IAAA;IAAA;IAAA;IAAA;IACnB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAA2B,GAAA,QAAA,CAAA,smOAAA,EAAA,CAAA;;;sEA6nBlB,0BAAwB,CAAA;UA7oBpC;uBACW,wBAAsB,YACpB,MAAI,SACP;MACP;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;OACD,UACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAyVT,QAAA,CAAA,ssMAAA,EAAA,CAAA;iEAmS2B,eAAa,CAAA;UAAxC;WAAU,eAAe;;;;6EADf,0BAAwB,EAAA,WAAA,4BAAA,UAAA,+EAAA,YAAA,IAAA,CAAA;AAAA,GAAA;", "names": []}