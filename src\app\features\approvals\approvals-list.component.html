<div class="approvals-list-container">
  <h2>Approvals Workflow</h2>
  <div class="alert alert-info mb-4">
    <i class="bi bi-info-circle me-2"></i>
    Review and manage application creation and update requests.
  </div>

  <div *ngIf="loading" class="loading-spinner">Loading approvals...</div>

  <table class="table table-striped" *ngIf="!loading && approvalRequests.length">
    <thead>
      <tr>
        <th>Application</th>
        <th>Request Type</th>
        <th>Requested By</th>
        <th>Status</th>
        <th>Submitted At</th>
        <th>Reviewed By</th>
        <th>Reviewed At</th>
        <th>Comments</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let request of approvalRequests">
        <td>
          <a href="javascript:void(0)" (click)="viewApplication(request.applicationId)">
            {{ request.applicationName }}
          </a>
        </td>
        <td>
          <span class="badge" [ngClass]="{
            'bg-primary': request.requestType === 'CREATE',
            'bg-info': request.requestType === 'UPDATE'
          }">{{ request.requestType }}</span>
        </td>
        <td>{{ request.requestedBy }}</td>
        <td>
          <span [ngClass]="{
            'badge bg-warning': request.status === 'PENDING',
            'badge bg-success': request.status === 'APPROVED',
            'badge bg-danger': request.status === 'REJECTED'
          }">{{ request.status }}</span>
        </td>
        <td>{{ request.requestedAt | date:'short' }}</td>
        <td>{{ request.reviewedBy || '-' }}</td>
        <td>{{ request.reviewedAt ? (request.reviewedAt | date:'short') : '-' }}</td>
        <td>{{ request.comments || '-' }}</td>
        <td>
          <div class="btn-group">
            <button 
              class="btn btn-success btn-sm" 
              (click)="approve(request)" 
              [disabled]="request.status !== 'PENDING'">
              <i class="bi bi-check-circle me-1"></i>
              Approve
            </button>
            <button 
              class="btn btn-danger btn-sm" 
              (click)="reject(request)" 
              [disabled]="request.status !== 'PENDING'">
              <i class="bi bi-x-circle me-1"></i>
              Reject
            </button>
          </div>
        </td>
      </tr>
    </tbody>
  </table>

  <div *ngIf="!loading && !approvalRequests.length" class="no-approvals">
    <i class="bi bi-inbox me-2"></i>
    No approval requests found.
  </div>
</div>