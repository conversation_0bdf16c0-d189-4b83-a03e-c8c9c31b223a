import { Component, Input, Output, EventEmitter, forwardRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ControlValueAccessor, NG_VALUE_ACCESSOR, FormsModule } from '@angular/forms';

@Component({
  selector: 'app-form-input',
  standalone: true,
  imports: [CommonModule, FormsModule],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => FormInputComponent),
      multi: true
    }
  ],
  template: `
    <div class="form-group">
      <label *ngIf="label" [for]="inputId" class="block text-sm font-medium text-gray-700 mb-1">
        {{ label }}
        <span class="text-red-500" *ngIf="required">*</span>
      </label>
      <div class="relative rounded-md shadow-sm">
        <input
          [id]="inputId"
          [type]="type"
          [placeholder]="placeholder"
          [disabled]="disabled"
          [readonly]="readonly"
          [required]="required"
          [class.error]="!!errorMessage"
          class="block w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
          [class.border-red-300]="!!errorMessage"
          [class.text-red-900]="!!errorMessage"
          [class.placeholder-red-300]="!!errorMessage"
          [(ngModel)]="value"
          (input)="onInput($event)"
          (blur)="onBlur()"
          (focus)="onFocus()"
          (keyup.enter)="onEnter()"
        />

        <!-- Textarea -->
        <textarea
          *ngIf="type === 'textarea'"
          [id]="inputId"
          [placeholder]="placeholder"
          [disabled]="disabled"
          [readonly]="readonly"
          [required]="required"
          [attr.maxlength]="maxlength"
          [rows]="rows"
          [class]="inputClasses"
          [(ngModel)]="value"
          (input)="onInput($event)"
          (blur)="onBlur()"
          (focus)="onFocus()"
        ></textarea>

        <!-- Select -->
        <select
          *ngIf="type === 'select'"
          [id]="inputId"
          [disabled]="disabled"
          [required]="required"
          [class]="inputClasses"
          [(ngModel)]="value"
          (change)="onInput($event)"
          (blur)="onBlur()"
          (focus)="onFocus()"
        >
          <option value="" *ngIf="placeholder" disabled>{{ placeholder }}</option>
          <option *ngFor="let option of options" [value]="option.value">
            {{ option.label }}
          </option>
        </select>

        <!-- Right Icon / Clear Button -->
        <div class="input-icon-right" *ngIf="rightIcon || (clearable && value)">
          <!-- Clear Button -->
          <button
            *ngIf="clearable && value && !disabled"
            type="button"
            class="clear-button"
            (click)="clearValue()"
            [attr.aria-label]="'Clear ' + (label || 'input')"
          >
            <svg class="clear-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>

          <!-- Right Icon -->
          <svg *ngIf="rightIcon" class="input-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" [attr.d]="rightIcon" />
          </svg>
        </div>
      </div>

      <!-- Help Text -->
      <div class="form-help" *ngIf="helpText && !errorMessage">
        {{ helpText }}
      </div>

      <!-- Error Message -->
      <div class="form-error" *ngIf="errorMessage">
        <svg class="error-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <circle cx="12" cy="12" r="10"></circle>
          <line x1="15" y1="9" x2="9" y2="15"></line>
          <line x1="9" y1="9" x2="15" y2="15"></line>
        </svg>
        {{ errorMessage }}
      </div>

      <!-- Character Count -->
      <div class="character-count" *ngIf="maxlength && showCharacterCount">
        {{ (value || '').length }}/{{ maxlength }}
      </div>
    </div>
  `,
  styleUrls: ['./form-input.component.scss']
})
export class FormInputComponent implements ControlValueAccessor {
  @Input() type: string = 'text';
  @Input() label = '';
  @Input() placeholder = '';
  @Input() helpText = '';
  @Input() errorMessage = '';
  @Input() leftIcon = '';
  @Input() rightIcon = '';
  @Input() disabled = false;
  @Input() readonly = false;
  @Input() required = false;
  @Input() clearable = false;
  @Input() showCharacterCount = false;

  // Input-specific attributes
  @Input() min?: number;
  @Input() max?: number;
  @Input() step?: number;
  @Input() maxlength?: number;
  @Input() pattern?: string;
  @Input() autocomplete?: string;
  @Input() rows = 4; // For textarea
  @Input() options: { value: any; label: string }[] = []; // For select

  @Output() inputChange = new EventEmitter<any>();
  @Output() inputFocus = new EventEmitter<void>();
  @Output() inputBlur = new EventEmitter<void>();
  @Output() enterPressed = new EventEmitter<void>();

  value: any = '';
  inputId = `input-${Math.random().toString(36).substr(2, 9)}`;

  // ControlValueAccessor implementation
  private onChange = (value: any) => {};
  private onTouched = () => {};

  writeValue(value: any): void {
    this.value = value;
  }

  registerOnChange(fn: (value: any) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  get containerClasses(): string {
    const classes = [
      'form-input',
    ];

    if (this.errorMessage) classes.push('form-input-error');
    if (this.disabled) classes.push('form-input-disabled');
    if (this.readonly) classes.push('form-input-readonly');

    return classes.join(' ');
  }

  get inputClasses(): string {
    const classes = ['input-field'];

    if (this.leftIcon) classes.push('has-left-icon');
    if (this.rightIcon || this.clearable) classes.push('has-right-icon');

    return classes.join(' ');
  }

  onInput(event: any): void {
    const target = event.target;
    this.value = target.value;
    this.onChange(this.value);
    this.inputChange.emit(this.value);
  }

  onFocus(): void {
    this.inputFocus.emit();
  }

  onBlur(): void {
    this.onTouched();
    this.inputBlur.emit();
  }

  onEnter(): void {
    this.enterPressed.emit();
  }

  clearValue(): void {
    this.value = '';
    this.onChange(this.value);
    this.inputChange.emit(this.value);
  }
}
