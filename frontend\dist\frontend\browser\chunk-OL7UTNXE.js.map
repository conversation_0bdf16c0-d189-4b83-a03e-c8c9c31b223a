{"version": 3, "sources": ["src/app/shared/components/modals/shared-tech-stack-modal/shared-tech-stack-modal.component.ts", "src/app/modules/tech-stack/tech-stack.component.ts"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, OnChanges } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';\nimport { SlideModalComponent } from '../../slide-modal/slide-modal.component';\nimport { FormInputComponent } from '../../form-input/form-input.component';\nimport { ApplicationSelectionService, ApplicationOption } from '../../../services/application-selection.service';\nimport { Observable } from 'rxjs';\n\nexport interface SharedTechStackFormData {\n  applicationId?: number;\n  name: string;\n  category: string;\n  version: string;\n  description: string;\n  purpose: string;\n  status: string;\n  maintainer: string;\n  licenseType: string;\n  repository?: string;\n  documentation?: string;\n}\n\n@Component({\n  selector: 'app-shared-tech-stack-modal',\n  standalone: true,\n  imports: [CommonModule, ReactiveFormsModule, SlideModalComponent, FormInputComponent],\n  template: `\n    <app-slide-modal\n      [isOpen]=\"isOpen\"\n      [title]=\"editMode ? 'Edit Technology' : 'Add Technology'\"\n      [subtitle]=\"showApplicationSelection ? 'Configure technology stack details and select application' : 'Configure technology stack details'\"\n      [loading]=\"loading\"\n      [canConfirm]=\"techStackForm.valid\"\n      confirmText=\"Save Technology\"\n      (closed)=\"onClose()\"\n      (confirmed)=\"onSave()\"\n      (cancelled)=\"onClose()\"\n    >\n      <form [formGroup]=\"techStackForm\" class=\"tech-stack-form\">\n        <!-- Application Selection (only when not in applications module) -->\n        <div class=\"form-group\" *ngIf=\"showApplicationSelection\">\n          <label class=\"form-label\">Application *</label>\n          <select formControlName=\"applicationId\" class=\"form-select\">\n            <option value=\"\">Select an application</option>\n            <option *ngFor=\"let app of applicationOptions$ | async\" [value]=\"app.id\">\n              {{ app.name }} - {{ app.department }}\n            </option>\n          </select>\n          <div class=\"field-error\" *ngIf=\"getFieldError('applicationId')\">\n            {{ getFieldError('applicationId') }}\n          </div>\n        </div>\n\n        <div class=\"form-grid\">\n          <div class=\"form-group\">\n            <label class=\"form-label\">Category</label>\n            <select formControlName=\"category\" class=\"form-select\">\n              <option value=\"frontend\">Frontend</option>\n              <option value=\"backend\">Backend</option>\n              <option value=\"database\">Database</option>\n              <option value=\"infrastructure\">Infrastructure</option>\n              <option value=\"devops\">DevOps</option>\n              <option value=\"testing\">Testing</option>\n              <option value=\"monitoring\">Monitoring</option>\n              <option value=\"security\">Security</option>\n              <option value=\"analytics\">Analytics</option>\n              <option value=\"communication\">Communication</option>\n            </select>\n          </div>\n\n          <app-form-input\n            label=\"Technology Name\"\n            placeholder=\"e.g., React, Node.js, PostgreSQL\"\n            formControlName=\"name\"\n            [required]=\"true\"\n            [errorMessage]=\"getFieldError('name')\"\n          ></app-form-input>\n\n          <app-form-input\n            label=\"Version\"\n            placeholder=\"e.g., 18.2.0, 16.x, latest\"\n            formControlName=\"version\"\n            [required]=\"true\"\n            [errorMessage]=\"getFieldError('version')\"\n          ></app-form-input>\n\n          <div class=\"form-group\">\n            <label class=\"form-label\">Purpose</label>\n            <select formControlName=\"purpose\" class=\"form-select\">\n              <option value=\"core\">Core Technology</option>\n              <option value=\"development\">Development Tool</option>\n              <option value=\"testing\">Testing Framework</option>\n              <option value=\"deployment\">Deployment Tool</option>\n              <option value=\"monitoring\">Monitoring Solution</option>\n              <option value=\"security\">Security Tool</option>\n              <option value=\"performance\">Performance Optimization</option>\n              <option value=\"integration\">Integration</option>\n              <option value=\"other\">Other</option>\n            </select>\n          </div>\n\n          <div class=\"form-group\">\n            <label class=\"form-label\">Status</label>\n            <select formControlName=\"status\" class=\"form-select\">\n              <option value=\"active\">Active</option>\n              <option value=\"planned\">Planned</option>\n              <option value=\"deprecated\">Deprecated</option>\n              <option value=\"evaluating\">Evaluating</option>\n              <option value=\"migrating\">Migrating</option>\n            </select>\n          </div>\n\n          <app-form-input\n            label=\"Maintainer/Vendor\"\n            placeholder=\"e.g., Facebook, Google, Internal Team\"\n            formControlName=\"maintainer\"\n            [required]=\"true\"\n            [errorMessage]=\"getFieldError('maintainer')\"\n          ></app-form-input>\n\n          <div class=\"form-group\">\n            <label class=\"form-label\">License Type</label>\n            <select formControlName=\"licenseType\" class=\"form-select\">\n              <option value=\"MIT\">MIT</option>\n              <option value=\"Apache-2.0\">Apache 2.0</option>\n              <option value=\"GPL-3.0\">GPL 3.0</option>\n              <option value=\"BSD-3-Clause\">BSD 3-Clause</option>\n              <option value=\"ISC\">ISC</option>\n              <option value=\"MPL-2.0\">Mozilla Public License 2.0</option>\n              <option value=\"proprietary\">Proprietary</option>\n              <option value=\"commercial\">Commercial</option>\n              <option value=\"other\">Other</option>\n            </select>\n          </div>\n\n          <app-form-input\n            label=\"Repository URL\"\n            placeholder=\"https://github.com/...\"\n            formControlName=\"repository\"\n            [errorMessage]=\"getFieldError('repository')\"\n          ></app-form-input>\n\n          <app-form-input\n            label=\"Documentation URL\"\n            placeholder=\"https://docs.example.com/...\"\n            formControlName=\"documentation\"\n            [errorMessage]=\"getFieldError('documentation')\"\n          ></app-form-input>\n\n          <div class=\"form-group full-width\">\n            <label class=\"form-label\">Description</label>\n            <textarea \n              formControlName=\"description\"\n              class=\"form-textarea\"\n              placeholder=\"Describe how this technology is used in the application...\"\n              rows=\"3\"\n            ></textarea>\n          </div>\n        </div>\n      </form>\n    </app-slide-modal>\n  `,\n  styles: [`\n    .tech-stack-form {\n      display: flex;\n      flex-direction: column;\n      gap: var(--spacing-lg);\n    }\n\n    .form-grid {\n      display: grid;\n      grid-template-columns: 1fr 1fr;\n      gap: var(--spacing-lg);\n    }\n\n    .form-group {\n      display: flex;\n      flex-direction: column;\n      gap: var(--spacing-sm);\n    }\n\n    .form-group.full-width {\n      grid-column: 1 / -1;\n    }\n\n    .form-label {\n      font-size: var(--text-sm);\n      font-weight: 500;\n      color: var(--secondary-700);\n    }\n\n    .form-select,\n    .form-textarea {\n      height: 40px;\n      padding: 0 var(--spacing-md);\n      border: 1px solid var(--secondary-300);\n      border-radius: var(--radius-md);\n      background: white;\n      font-size: var(--text-sm);\n      color: var(--secondary-800);\n      transition: all 0.2s ease;\n\n      &:focus {\n        outline: none;\n        border-color: var(--primary-500);\n        box-shadow: 0 0 0 3px var(--primary-100);\n      }\n    }\n\n    .form-textarea {\n      height: auto;\n      padding: var(--spacing-sm) var(--spacing-md);\n      resize: vertical;\n      min-height: 80px;\n    }\n\n    .field-error {\n      font-size: var(--text-sm);\n      color: var(--error-600);\n    }\n\n    @media (max-width: 768px) {\n      .form-grid {\n        grid-template-columns: 1fr;\n        gap: var(--spacing-md);\n      }\n    }\n  `]\n})\nexport class SharedTechStackModalComponent implements OnInit, OnChanges {\n  @Input() isOpen = false;\n  @Input() editMode = false;\n  @Input() initialData: SharedTechStackFormData | null = null;\n  @Input() loading = false;\n  @Input() showApplicationSelection = true; // Hide when used within applications module\n\n  @Output() closed = new EventEmitter<void>();\n  @Output() saved = new EventEmitter<SharedTechStackFormData>();\n\n  techStackForm!: FormGroup;\n  applicationOptions$: Observable<ApplicationOption[]>;\n\n  constructor(\n    private fb: FormBuilder,\n    private applicationSelectionService: ApplicationSelectionService\n  ) {\n    this.applicationOptions$ = this.applicationSelectionService.getApplicationOptions();\n  }\n\n  ngOnInit(): void {\n    this.initializeForm();\n  }\n\n  ngOnChanges(): void {\n    if (this.techStackForm && this.initialData) {\n      this.techStackForm.patchValue(this.initialData);\n    }\n  }\n\n  private initializeForm(): void {\n    const formConfig: any = {\n      category: ['frontend', [Validators.required]],\n      name: ['', [Validators.required, Validators.minLength(2)]],\n      version: ['', [Validators.required]],\n      description: [''],\n      purpose: ['core', [Validators.required]],\n      status: ['active', [Validators.required]],\n      maintainer: ['', [Validators.required]],\n      licenseType: ['MIT', [Validators.required]],\n      repository: [''],\n      documentation: ['']\n    };\n\n    if (this.showApplicationSelection) {\n      formConfig.applicationId = ['', [Validators.required]];\n    }\n\n    this.techStackForm = this.fb.group(formConfig);\n\n    if (this.initialData) {\n      this.techStackForm.patchValue(this.initialData);\n    }\n  }\n\n  getFieldError(fieldName: string): string {\n    const field = this.techStackForm.get(fieldName);\n    if (field && field.invalid && field.touched) {\n      if (field.errors?.['required']) {\n        return `${fieldName} is required`;\n      }\n      if (field.errors?.['minlength']) {\n        return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;\n      }\n      if (field.errors?.['url']) {\n        return `${fieldName} must be a valid URL`;\n      }\n    }\n    return '';\n  }\n\n  onSave(): void {\n    if (this.techStackForm.valid) {\n      const formData: SharedTechStackFormData = this.techStackForm.value;\n      this.saved.emit(formData);\n    }\n  }\n\n  onClose(): void {\n    this.closed.emit();\n  }\n}\n", "import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { ReactiveFormsModule, FormBuilder } from '@angular/forms';\nimport { CardComponent } from '../../shared/components/card/card.component';\nimport { ButtonComponent } from '../../shared/components/button/button.component';\nimport { TableComponent, TableColumn, TableAction } from '../../shared/components/table/table.component';\nimport { ChartComponent } from '../../shared/components/chart/chart.component';\nimport { SharedTechStackModalComponent, SharedTechStackFormData } from '../../shared/components/modals/shared-tech-stack-modal/shared-tech-stack-modal.component';\n\ninterface TechStackItem {\n  id: number;\n  category: string;\n  technology: string;\n  version: string;\n  purpose: string;\n  isCore: boolean;\n  applications: number;\n  supportLevel: string;\n  endOfLife?: Date;\n  licenseType?: string;\n}\n\n@Component({\n  selector: 'app-tech-stack',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    ReactiveFormsModule,\n    CardComponent,\n    ButtonComponent,\n    TableComponent,\n    ChartComponent,\n    SharedTechStackModalComponent\n  ],\n  template: `\n    <div class=\"tech-stack-page\">\n      <div class=\"page-content\">\n        <div class=\"page-header\">\n          <div class=\"header-content\">\n            <h1>Technology Stack</h1>\n            <p class=\"page-subtitle\">Manage and monitor technology stack across applications</p>\n          </div>\n          <div class=\"header-actions\">\n            <app-button\n              variant=\"primary\"\n              leftIcon=\"M12 4v16m8-8H4\"\n              (clicked)=\"openAddModal()\"\n            >\n              Add Technology\n            </app-button>\n          </div>\n        </div>\n\n      <!-- Statistics Cards -->\n      <div class=\"stats-grid\">\n        <app-card title=\"Total Technologies\">\n          <div class=\"stat-content\">\n            <div class=\"stat-number\">{{ stats.total }}</div>\n            <div class=\"stat-change\">Across {{ stats.categories }} categories</div>\n          </div>\n        </app-card>\n\n        <app-card title=\"Core Technologies\">\n          <div class=\"stat-content\">\n            <div class=\"stat-number core\">{{ stats.core }}</div>\n            <div class=\"stat-change\">{{ stats.corePercentage }}% of total</div>\n          </div>\n        </app-card>\n\n        <app-card title=\"End of Life Soon\">\n          <div class=\"stat-content\">\n            <div class=\"stat-number warning\">{{ stats.endOfLife }}</div>\n            <div class=\"stat-change\">Need replacement</div>\n          </div>\n        </app-card>\n\n        <app-card title=\"Applications Using\">\n          <div class=\"stat-content\">\n            <div class=\"stat-number\">{{ stats.totalApplications }}</div>\n            <div class=\"stat-change\">Total usage</div>\n          </div>\n        </app-card>\n      </div>\n\n      <!-- Charts Section -->\n      <div class=\"charts-section\">\n        <app-card title=\"Technologies by Category\" subtitle=\"Distribution across categories\">\n          <app-chart\n            type=\"doughnut\"\n            [data]=\"categoryChartData\"\n            [options]=\"chartOptions\"\n            height=\"300px\"\n          ></app-chart>\n        </app-card>\n\n        <app-card title=\"Support Level Distribution\" subtitle=\"Technologies by support level\">\n          <app-chart\n            type=\"bar\"\n            [data]=\"supportChartData\"\n            [options]=\"chartOptions\"\n            height=\"300px\"\n          ></app-chart>\n        </app-card>\n      </div>\n\n      <!-- Technology Stack Table -->\n      <app-card title=\"Technology Stack\" subtitle=\"All technologies in use\">\n        <div class=\"table-controls\">\n          <div class=\"search-controls\">\n            <input\n              type=\"text\"\n              placeholder=\"Search technologies...\"\n              class=\"search-input\"\n              (input)=\"onSearchInput($event)\"\n            >\n            <select class=\"filter-select\" (change)=\"onFilterChanged('category', $event)\">\n              <option value=\"\">All Categories</option>\n              <option value=\"frontend\">Frontend</option>\n              <option value=\"backend\">Backend</option>\n              <option value=\"database\">Database</option>\n              <option value=\"infrastructure\">Infrastructure</option>\n              <option value=\"devops\">DevOps</option>\n              <option value=\"testing\">Testing</option>\n              <option value=\"monitoring\">Monitoring</option>\n            </select>\n            <select class=\"filter-select\" (change)=\"onFilterChanged('supportLevel', $event)\">\n              <option value=\"\">All Support Levels</option>\n              <option value=\"active\">Active</option>\n              <option value=\"maintenance\">Maintenance</option>\n              <option value=\"deprecated\">Deprecated</option>\n              <option value=\"end_of_life\">End of Life</option>\n            </select>\n          </div>\n        </div>\n\n        <app-table\n          [columns]=\"tableColumns\"\n          [data]=\"filteredTechStack\"\n          [actions]=\"tableActions\"\n          [loading]=\"loading\"\n          [sortable]=\"true\"\n          (sortChanged)=\"onSortChanged($event)\"\n        ></app-table>\n      </app-card>\n\n      <!-- Add/Edit Modal -->\n      <app-shared-tech-stack-modal\n        [isOpen]=\"showModal\"\n        [editMode]=\"editMode\"\n        [initialData]=\"editData\"\n        [loading]=\"modalLoading\"\n        [showApplicationSelection]=\"true\"\n        (closed)=\"closeModal()\"\n        (saved)=\"onTechStackSaved($event)\"\n      ></app-shared-tech-stack-modal>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .tech-stack-page {\n      min-height: 100%;\n    }\n\n    .page-content {\n      padding: var(--spacing-xl);\n    }\n\n    .page-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: flex-start;\n      margin-bottom: var(--spacing-xl);\n    }\n\n    .header-content h1 {\n      margin: 0 0 var(--spacing-sm) 0;\n      font-size: var(--text-3xl);\n      font-weight: 700;\n      color: var(--secondary-900);\n    }\n\n    .page-subtitle {\n      margin: 0;\n      font-size: var(--text-lg);\n      color: var(--secondary-600);\n    }\n\n    .stats-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n      gap: var(--spacing-lg);\n      margin-bottom: var(--spacing-xl);\n    }\n\n    .stat-content {\n      display: flex;\n      flex-direction: column;\n      gap: var(--spacing-sm);\n      padding: var(--spacing-lg);\n    }\n\n    .stat-number {\n      font-size: var(--text-3xl);\n      font-weight: 700;\n      color: var(--secondary-900);\n\n      &.core {\n        color: var(--primary-600);\n      }\n\n      &.warning {\n        color: var(--warning-600);\n      }\n    }\n\n    .stat-change {\n      font-size: var(--text-sm);\n      color: var(--secondary-600);\n    }\n\n    .charts-section {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n      gap: var(--spacing-lg);\n      margin-bottom: var(--spacing-xl);\n    }\n\n    .table-controls {\n      margin-bottom: var(--spacing-lg);\n    }\n\n    .search-controls {\n      display: flex;\n      gap: var(--spacing-md);\n      align-items: center;\n    }\n\n    .search-input,\n    .filter-select {\n      height: 40px;\n      padding: 0 var(--spacing-md);\n      border: 1px solid var(--secondary-300);\n      border-radius: var(--radius-md);\n      background: white;\n      font-size: var(--text-sm);\n      color: var(--secondary-800);\n      transition: all 0.2s ease;\n\n      &:focus {\n        outline: none;\n        border-color: var(--primary-500);\n        box-shadow: 0 0 0 3px var(--primary-100);\n      }\n    }\n\n    .search-input {\n      flex: 1;\n      min-width: 200px;\n    }\n\n    .filter-select {\n      min-width: 150px;\n      cursor: pointer;\n      background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e\");\n      background-position: right var(--spacing-sm) center;\n      background-repeat: no-repeat;\n      background-size: 16px;\n      padding-right: 40px;\n      appearance: none;\n    }\n\n    @media (max-width: 768px) {\n      .page-header {\n        flex-direction: column;\n        gap: var(--spacing-md);\n        align-items: stretch;\n      }\n\n      .stats-grid {\n        grid-template-columns: 1fr;\n        gap: var(--spacing-md);\n      }\n\n      .charts-section {\n        grid-template-columns: 1fr;\n        gap: var(--spacing-md);\n      }\n\n      .search-controls {\n        flex-direction: column;\n        align-items: stretch;\n      }\n\n      .search-input,\n      .filter-select {\n        min-width: auto;\n      }\n    }\n  `]\n})\nexport class TechStackComponent implements OnInit {\n  techStack: TechStackItem[] = [];\n  filteredTechStack: TechStackItem[] = [];\n  loading = false;\n\n  // Modal state\n  showModal = false;\n  editMode = false;\n  editData: SharedTechStackFormData | null = null;\n  modalLoading = false;\n\n  // Statistics\n  stats = {\n    total: 0,\n    core: 0,\n    endOfLife: 0,\n    categories: 0,\n    totalApplications: 0,\n    corePercentage: 0\n  };\n\n  // Chart data\n  categoryChartData: any = null;\n  supportChartData: any = null;\n  chartOptions = {\n    responsive: true,\n    maintainAspectRatio: false\n  };\n\n  // Table configuration\n  tableColumns: TableColumn[] = [\n    { key: 'technology', label: 'Technology', sortable: true },\n    { key: 'category', label: 'Category', type: 'badge', sortable: true },\n    { key: 'version', label: 'Version', sortable: true },\n    { key: 'purpose', label: 'Purpose', sortable: true },\n    { key: 'supportLevel', label: 'Support', type: 'badge', sortable: true },\n    { key: 'applications', label: 'Apps', type: 'number', sortable: true },\n    { key: 'isCore', label: 'Core', type: 'boolean', sortable: true },\n    { key: 'licenseType', label: 'License', sortable: true }\n  ];\n\n  tableActions: TableAction[] = [\n    {\n      label: 'Edit',\n      icon: 'M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7',\n      variant: 'ghost',\n      action: (item) => this.editTechStack(item)\n    },\n    {\n      label: 'Delete',\n      icon: 'M3 6h18m-2 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2',\n      variant: 'error',\n      action: (item) => this.deleteTechStack(item)\n    }\n  ];\n\n  constructor(private fb: FormBuilder) {}\n\n  ngOnInit(): void {\n    this.loadTechStack();\n  }\n\n  private loadTechStack(): void {\n    this.loading = true;\n\n    // Mock data - replace with actual API call\n    setTimeout(() => {\n      this.techStack = this.generateMockTechStack();\n      this.filteredTechStack = [...this.techStack];\n      this.updateStats();\n      this.updateChartData();\n      this.loading = false;\n    }, 1000);\n  }\n\n  private generateMockTechStack(): TechStackItem[] {\n    const categories = ['frontend', 'backend', 'database', 'infrastructure', 'devops', 'testing', 'monitoring'];\n    const supportLevels = ['active', 'maintenance', 'deprecated', 'end_of_life'];\n    const licenses = ['MIT', 'Apache 2.0', 'GPL', 'BSD', 'Commercial', 'Open Source'];\n\n    return Array.from({ length: 30 }, (_, i) => ({\n      id: i + 1,\n      category: categories[Math.floor(Math.random() * categories.length)],\n      technology: `Technology ${i + 1}`,\n      version: `${Math.floor(Math.random() * 5) + 1}.${Math.floor(Math.random() * 10)}.${Math.floor(Math.random() * 10)}`,\n      purpose: `Purpose for technology ${i + 1}`,\n      isCore: Math.random() > 0.7,\n      applications: Math.floor(Math.random() * 15) + 1,\n      supportLevel: supportLevels[Math.floor(Math.random() * supportLevels.length)],\n      endOfLife: Math.random() > 0.8 ? new Date(Date.now() + Math.random() * 365 * 24 * 60 * 60 * 1000) : undefined,\n      licenseType: licenses[Math.floor(Math.random() * licenses.length)]\n    }));\n  }\n\n  private updateStats(): void {\n    this.stats.total = this.techStack.length;\n    this.stats.core = this.techStack.filter(t => t.isCore).length;\n    this.stats.endOfLife = this.techStack.filter(t => t.supportLevel === 'end_of_life' || t.supportLevel === 'deprecated').length;\n    this.stats.categories = new Set(this.techStack.map(t => t.category)).size;\n    this.stats.totalApplications = this.techStack.reduce((sum, t) => sum + t.applications, 0);\n    this.stats.corePercentage = Math.round((this.stats.core / this.stats.total) * 100);\n  }\n\n  private updateChartData(): void {\n    // Category distribution chart\n    const categoryCount = this.techStack.reduce((acc, tech) => {\n      acc[tech.category] = (acc[tech.category] || 0) + 1;\n      return acc;\n    }, {} as any);\n\n    this.categoryChartData = {\n      labels: Object.keys(categoryCount),\n      datasets: [{\n        data: Object.values(categoryCount),\n        backgroundColor: [\n          '#0ea5e9', '#22c55e', '#f59e0b', '#ef4444', '#8b5cf6',\n          '#06b6d4', '#84cc16'\n        ]\n      }]\n    };\n\n    // Support level distribution chart\n    const supportCount = this.techStack.reduce((acc, tech) => {\n      acc[tech.supportLevel] = (acc[tech.supportLevel] || 0) + 1;\n      return acc;\n    }, {} as any);\n\n    this.supportChartData = {\n      labels: Object.keys(supportCount),\n      datasets: [{\n        label: 'Technologies',\n        data: Object.values(supportCount),\n        backgroundColor: ['#22c55e', '#f59e0b', '#f97316', '#ef4444']\n      }]\n    };\n  }\n\n  openAddModal(): void {\n    this.editMode = false;\n    this.editData = null;\n    this.showModal = true;\n  }\n\n  editTechStack(techStack: TechStackItem): void {\n    this.editMode = true;\n    this.editData = {\n      name: techStack.technology,\n      category: techStack.category,\n      version: techStack.version,\n      description: '',\n      purpose: techStack.purpose,\n      status: 'active',\n      maintainer: 'Unknown',\n      licenseType: techStack.licenseType || 'MIT'\n    };\n    this.showModal = true;\n  }\n\n  deleteTechStack(techStack: TechStackItem): void {\n    if (confirm(`Are you sure you want to delete ${techStack.technology}?`)) {\n      this.techStack = this.techStack.filter(t => t.id !== techStack.id);\n      this.filteredTechStack = this.filteredTechStack.filter(t => t.id !== techStack.id);\n      this.updateStats();\n      this.updateChartData();\n    }\n  }\n\n  closeModal(): void {\n    this.showModal = false;\n    this.editMode = false;\n    this.editData = null;\n  }\n\n  onTechStackSaved(techStackData: SharedTechStackFormData): void {\n    this.modalLoading = true;\n\n    setTimeout(() => {\n      console.log('Tech stack saved:', techStackData);\n\n      // In a real app, you would make an API call here and refresh the data\n      this.modalLoading = false;\n      this.closeModal();\n      this.loadTechStack(); // Refresh the list\n    }, 1000);\n  }\n\n  onSearchInput(event: Event): void {\n    const target = event.target as HTMLInputElement;\n    this.onSearchChanged(target.value);\n  }\n\n  onSearchChanged(searchTerm: string): void {\n    this.applyFilters();\n  }\n\n  onFilterChanged(filterType: string, event: Event): void {\n    const target = event.target as HTMLSelectElement;\n    // Apply filter logic here\n    this.applyFilters();\n  }\n\n  onSortChanged(sort: any): void {\n    // Implement sorting logic\n    console.log('Sort changed:', sort);\n  }\n\n  private applyFilters(): void {\n    // Implement filtering logic\n    this.filteredTechStack = [...this.techStack];\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CY,IAAA,yBAAA,GAAA,UAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AAFwD,IAAA,qBAAA,SAAA,OAAA,EAAA;AACtD,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,MAAA,OAAA,OAAA,YAAA,GAAA;;;;;AAGJ,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,cAAA,eAAA,GAAA,GAAA;;;;;AATJ,IAAA,yBAAA,GAAA,OAAA,CAAA,EAAyD,GAAA,SAAA,CAAA;AAC7B,IAAA,iBAAA,GAAA,eAAA;AAAa,IAAA,uBAAA;AACvC,IAAA,yBAAA,GAAA,UAAA,EAAA,EAA4D,GAAA,UAAA,EAAA;AACzC,IAAA,iBAAA,GAAA,uBAAA;AAAqB,IAAA,uBAAA;AACtC,IAAA,qBAAA,GAAA,uDAAA,GAAA,GAAA,UAAA,EAAA;;AAGF,IAAA,uBAAA;AACA,IAAA,qBAAA,GAAA,oDAAA,GAAA,GAAA,OAAA,EAAA;AAGF,IAAA,uBAAA;;;;AAP4B,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,sBAAA,GAAA,GAAA,OAAA,mBAAA,CAAA;AAIA,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,cAAA,eAAA,CAAA;;;AAqL9B,IAAO,gCAAP,MAAO,+BAA6B;EAc9B;EACA;EAdD,SAAS;EACT,WAAW;EACX,cAA8C;EAC9C,UAAU;EACV,2BAA2B;;EAE1B,SAAS,IAAI,aAAY;EACzB,QAAQ,IAAI,aAAY;EAElC;EACA;EAEA,YACU,IACA,6BAAwD;AADxD,SAAA,KAAA;AACA,SAAA,8BAAA;AAER,SAAK,sBAAsB,KAAK,4BAA4B,sBAAqB;EACnF;EAEA,WAAQ;AACN,SAAK,eAAc;EACrB;EAEA,cAAW;AACT,QAAI,KAAK,iBAAiB,KAAK,aAAa;AAC1C,WAAK,cAAc,WAAW,KAAK,WAAW;IAChD;EACF;EAEQ,iBAAc;AACpB,UAAM,aAAkB;MACtB,UAAU,CAAC,YAAY,CAAC,WAAW,QAAQ,CAAC;MAC5C,MAAM,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,UAAU,CAAC,CAAC,CAAC;MACzD,SAAS,CAAC,IAAI,CAAC,WAAW,QAAQ,CAAC;MACnC,aAAa,CAAC,EAAE;MAChB,SAAS,CAAC,QAAQ,CAAC,WAAW,QAAQ,CAAC;MACvC,QAAQ,CAAC,UAAU,CAAC,WAAW,QAAQ,CAAC;MACxC,YAAY,CAAC,IAAI,CAAC,WAAW,QAAQ,CAAC;MACtC,aAAa,CAAC,OAAO,CAAC,WAAW,QAAQ,CAAC;MAC1C,YAAY,CAAC,EAAE;MACf,eAAe,CAAC,EAAE;;AAGpB,QAAI,KAAK,0BAA0B;AACjC,iBAAW,gBAAgB,CAAC,IAAI,CAAC,WAAW,QAAQ,CAAC;IACvD;AAEA,SAAK,gBAAgB,KAAK,GAAG,MAAM,UAAU;AAE7C,QAAI,KAAK,aAAa;AACpB,WAAK,cAAc,WAAW,KAAK,WAAW;IAChD;EACF;EAEA,cAAc,WAAiB;AAC7B,UAAM,QAAQ,KAAK,cAAc,IAAI,SAAS;AAC9C,QAAI,SAAS,MAAM,WAAW,MAAM,SAAS;AAC3C,UAAI,MAAM,SAAS,UAAU,GAAG;AAC9B,eAAO,GAAG,SAAS;MACrB;AACA,UAAI,MAAM,SAAS,WAAW,GAAG;AAC/B,eAAO,GAAG,SAAS,qBAAqB,MAAM,OAAO,WAAW,EAAE,cAAc;MAClF;AACA,UAAI,MAAM,SAAS,KAAK,GAAG;AACzB,eAAO,GAAG,SAAS;MACrB;IACF;AACA,WAAO;EACT;EAEA,SAAM;AACJ,QAAI,KAAK,cAAc,OAAO;AAC5B,YAAM,WAAoC,KAAK,cAAc;AAC7D,WAAK,MAAM,KAAK,QAAQ;IAC1B;EACF;EAEA,UAAO;AACL,SAAK,OAAO,KAAI;EAClB;;qCAhFW,gCAA6B,4BAAA,WAAA,GAAA,4BAAA,2BAAA,CAAA;EAAA;yEAA7B,gCAA6B,WAAA,CAAA,CAAA,6BAAA,CAAA,GAAA,QAAA,EAAA,QAAA,UAAA,UAAA,YAAA,aAAA,eAAA,SAAA,WAAA,0BAAA,2BAAA,GAAA,SAAA,EAAA,QAAA,UAAA,OAAA,QAAA,GAAA,UAAA,CAAA,8BAAA,GAAA,OAAA,IAAA,MAAA,IAAA,QAAA,CAAA,CAAA,eAAA,mBAAA,GAAA,UAAA,aAAA,aAAA,UAAA,SAAA,YAAA,WAAA,YAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,WAAA,GAAA,CAAA,SAAA,cAAA,GAAA,MAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,mBAAA,YAAA,GAAA,aAAA,GAAA,CAAA,SAAA,UAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,SAAA,UAAA,GAAA,CAAA,SAAA,gBAAA,GAAA,CAAA,SAAA,QAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,SAAA,YAAA,GAAA,CAAA,SAAA,UAAA,GAAA,CAAA,SAAA,WAAA,GAAA,CAAA,SAAA,eAAA,GAAA,CAAA,SAAA,mBAAA,eAAA,oCAAA,mBAAA,QAAA,GAAA,YAAA,cAAA,GAAA,CAAA,SAAA,WAAA,eAAA,8BAAA,mBAAA,WAAA,GAAA,YAAA,cAAA,GAAA,CAAA,mBAAA,WAAA,GAAA,aAAA,GAAA,CAAA,SAAA,MAAA,GAAA,CAAA,SAAA,aAAA,GAAA,CAAA,SAAA,YAAA,GAAA,CAAA,SAAA,aAAA,GAAA,CAAA,SAAA,aAAA,GAAA,CAAA,SAAA,OAAA,GAAA,CAAA,mBAAA,UAAA,GAAA,aAAA,GAAA,CAAA,SAAA,QAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,SAAA,YAAA,GAAA,CAAA,SAAA,YAAA,GAAA,CAAA,SAAA,WAAA,GAAA,CAAA,SAAA,qBAAA,eAAA,yCAAA,mBAAA,cAAA,GAAA,YAAA,cAAA,GAAA,CAAA,mBAAA,eAAA,GAAA,aAAA,GAAA,CAAA,SAAA,KAAA,GAAA,CAAA,SAAA,YAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,SAAA,cAAA,GAAA,CAAA,SAAA,KAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,SAAA,aAAA,GAAA,CAAA,SAAA,YAAA,GAAA,CAAA,SAAA,kBAAA,eAAA,0BAAA,mBAAA,cAAA,GAAA,cAAA,GAAA,CAAA,SAAA,qBAAA,eAAA,gCAAA,mBAAA,iBAAA,GAAA,cAAA,GAAA,CAAA,GAAA,cAAA,YAAA,GAAA,CAAA,mBAAA,eAAA,eAAA,8DAAA,QAAA,KAAA,GAAA,eAAA,GAAA,CAAA,mBAAA,iBAAA,GAAA,aAAA,GAAA,CAAA,SAAA,EAAA,GAAA,CAAA,GAAA,SAAA,GAAA,SAAA,SAAA,GAAA,CAAA,SAAA,eAAA,GAAA,MAAA,GAAA,CAAA,GAAA,OAAA,GAAA,CAAA,GAAA,aAAA,CAAA,GAAA,UAAA,SAAA,uCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AA1MtC,MAAA,yBAAA,GAAA,mBAAA,CAAA;AAOE,MAAA,qBAAA,UAAA,SAAA,2EAAA;AAAA,eAAU,IAAA,QAAA;MAAS,CAAA,EAAC,aAAA,SAAA,8EAAA;AAAA,eACP,IAAA,OAAA;MAAQ,CAAA,EAAC,aAAA,SAAA,8EAAA;AAAA,eACT,IAAA,QAAA;MAAS,CAAA;AAEtB,MAAA,yBAAA,GAAA,QAAA,CAAA;AAEE,MAAA,qBAAA,GAAA,8CAAA,GAAA,GAAA,OAAA,CAAA;AAaA,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAuB,GAAA,OAAA,CAAA,EACG,GAAA,SAAA,CAAA;AACI,MAAA,iBAAA,GAAA,UAAA;AAAQ,MAAA,uBAAA;AAClC,MAAA,yBAAA,GAAA,UAAA,CAAA,EAAuD,GAAA,UAAA,CAAA;AAC5B,MAAA,iBAAA,GAAA,UAAA;AAAQ,MAAA,uBAAA;AACjC,MAAA,yBAAA,IAAA,UAAA,CAAA;AAAwB,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA;AAC/B,MAAA,yBAAA,IAAA,UAAA,CAAA;AAAyB,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA;AACjC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA+B,MAAA,iBAAA,IAAA,gBAAA;AAAc,MAAA,uBAAA;AAC7C,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAuB,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA;AAC7B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAwB,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA;AAC/B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA2B,MAAA,iBAAA,IAAA,YAAA;AAAU,MAAA,uBAAA;AACrC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAyB,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA;AACjC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA0B,MAAA,iBAAA,IAAA,WAAA;AAAS,MAAA,uBAAA;AACnC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA8B,MAAA,iBAAA,IAAA,eAAA;AAAa,MAAA,uBAAA,EAAS,EAC7C;AAGX,MAAA,oBAAA,IAAA,kBAAA,EAAA,EAMkB,IAAA,kBAAA,EAAA;AAUlB,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,SAAA,CAAA;AACI,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA;AACjC,MAAA,yBAAA,IAAA,UAAA,EAAA,EAAsD,IAAA,UAAA,EAAA;AAC/B,MAAA,iBAAA,IAAA,iBAAA;AAAe,MAAA,uBAAA;AACpC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA4B,MAAA,iBAAA,IAAA,kBAAA;AAAgB,MAAA,uBAAA;AAC5C,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAwB,MAAA,iBAAA,IAAA,mBAAA;AAAiB,MAAA,uBAAA;AACzC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA2B,MAAA,iBAAA,IAAA,iBAAA;AAAe,MAAA,uBAAA;AAC1C,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA2B,MAAA,iBAAA,IAAA,qBAAA;AAAmB,MAAA,uBAAA;AAC9C,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAyB,MAAA,iBAAA,IAAA,eAAA;AAAa,MAAA,uBAAA;AACtC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA4B,MAAA,iBAAA,IAAA,0BAAA;AAAwB,MAAA,uBAAA;AACpD,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA4B,MAAA,iBAAA,IAAA,aAAA;AAAW,MAAA,uBAAA;AACvC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAsB,MAAA,iBAAA,IAAA,OAAA;AAAK,MAAA,uBAAA,EAAS,EAC7B;AAGX,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,SAAA,CAAA;AACI,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA;AAChC,MAAA,yBAAA,IAAA,UAAA,EAAA,EAAqD,IAAA,UAAA,EAAA;AAC5B,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA;AAC7B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAwB,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA;AAC/B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA2B,MAAA,iBAAA,IAAA,YAAA;AAAU,MAAA,uBAAA;AACrC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA2B,MAAA,iBAAA,IAAA,YAAA;AAAU,MAAA,uBAAA;AACrC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA0B,MAAA,iBAAA,IAAA,WAAA;AAAS,MAAA,uBAAA,EAAS,EACrC;AAGX,MAAA,oBAAA,IAAA,kBAAA,EAAA;AAQA,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,SAAA,CAAA;AACI,MAAA,iBAAA,IAAA,cAAA;AAAY,MAAA,uBAAA;AACtC,MAAA,yBAAA,IAAA,UAAA,EAAA,EAA0D,IAAA,UAAA,EAAA;AACpC,MAAA,iBAAA,IAAA,KAAA;AAAG,MAAA,uBAAA;AACvB,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA2B,MAAA,iBAAA,IAAA,YAAA;AAAU,MAAA,uBAAA;AACrC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAwB,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA;AAC/B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA6B,MAAA,iBAAA,IAAA,cAAA;AAAY,MAAA,uBAAA;AACzC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAoB,MAAA,iBAAA,IAAA,KAAA;AAAG,MAAA,uBAAA;AACvB,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAwB,MAAA,iBAAA,IAAA,4BAAA;AAA0B,MAAA,uBAAA;AAClD,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA4B,MAAA,iBAAA,IAAA,aAAA;AAAW,MAAA,uBAAA;AACvC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA2B,MAAA,iBAAA,IAAA,YAAA;AAAU,MAAA,uBAAA;AACrC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAsB,MAAA,iBAAA,IAAA,OAAA;AAAK,MAAA,uBAAA,EAAS,EAC7B;AAGX,MAAA,oBAAA,IAAA,kBAAA,EAAA,EAKkB,IAAA,kBAAA,EAAA;AASlB,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAmC,IAAA,SAAA,CAAA;AACP,MAAA,iBAAA,IAAA,aAAA;AAAW,MAAA,uBAAA;AACrC,MAAA,oBAAA,IAAA,YAAA,EAAA;AAMF,MAAA,uBAAA,EAAM,EACF,EACD;;;AAnIP,MAAA,qBAAA,UAAA,IAAA,MAAA,EAAiB,SAAA,IAAA,WAAA,oBAAA,gBAAA,EACwC,YAAA,IAAA,2BAAA,8DAAA,oCAAA,EACiF,WAAA,IAAA,OAAA,EACvH,cAAA,IAAA,cAAA,KAAA;AAOb,MAAA,oBAAA;AAAA,MAAA,qBAAA,aAAA,IAAA,aAAA;AAEqB,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,wBAAA;AAkCrB,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,YAAA,IAAA,EAAiB,gBAAA,IAAA,cAAA,MAAA,CAAA;AAQjB,MAAA,oBAAA;AAAA,MAAA,qBAAA,YAAA,IAAA,EAAiB,gBAAA,IAAA,cAAA,SAAA,CAAA;AAkCjB,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,YAAA,IAAA,EAAiB,gBAAA,IAAA,cAAA,YAAA,CAAA;AAuBjB,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,gBAAA,IAAA,cAAA,YAAA,CAAA;AAOA,MAAA,oBAAA;AAAA,MAAA,qBAAA,gBAAA,IAAA,cAAA,eAAA,CAAA;;oBAzHA,cAAY,SAAA,MAAA,WAAE,qBAAmB,oBAAA,gBAAA,8BAAA,sBAAA,4BAAA,iBAAA,sBAAA,mBAAA,oBAAA,iBAAE,qBAAqB,kBAAkB,GAAA,QAAA,CAAA,28CAAA,EAAA,CAAA;;;sEA4MzE,+BAA6B,CAAA;UA/MzC;uBACW,+BAA6B,YAC3B,MAAI,SACP,CAAC,cAAc,qBAAqB,qBAAqB,kBAAkB,GAAC,UAC3E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAuIT,QAAA,CAAA,4+CAAA,EAAA,CAAA;8EAqEQ,QAAM,CAAA;UAAd;MACQ,UAAQ,CAAA;UAAhB;MACQ,aAAW,CAAA;UAAnB;MACQ,SAAO,CAAA;UAAf;MACQ,0BAAwB,CAAA;UAAhC;MAES,QAAM,CAAA;UAAf;MACS,OAAK,CAAA;UAAd;;;;6EARU,+BAA6B,EAAA,WAAA,iCAAA,UAAA,iGAAA,YAAA,IAAA,CAAA;AAAA,GAAA;;;ACyEpC,IAAO,qBAAP,MAAO,oBAAkB;EAwDT;EAvDpB,YAA6B,CAAA;EAC7B,oBAAqC,CAAA;EACrC,UAAU;;EAGV,YAAY;EACZ,WAAW;EACX,WAA2C;EAC3C,eAAe;;EAGf,QAAQ;IACN,OAAO;IACP,MAAM;IACN,WAAW;IACX,YAAY;IACZ,mBAAmB;IACnB,gBAAgB;;;EAIlB,oBAAyB;EACzB,mBAAwB;EACxB,eAAe;IACb,YAAY;IACZ,qBAAqB;;;EAIvB,eAA8B;IAC5B,EAAE,KAAK,cAAc,OAAO,cAAc,UAAU,KAAI;IACxD,EAAE,KAAK,YAAY,OAAO,YAAY,MAAM,SAAS,UAAU,KAAI;IACnE,EAAE,KAAK,WAAW,OAAO,WAAW,UAAU,KAAI;IAClD,EAAE,KAAK,WAAW,OAAO,WAAW,UAAU,KAAI;IAClD,EAAE,KAAK,gBAAgB,OAAO,WAAW,MAAM,SAAS,UAAU,KAAI;IACtE,EAAE,KAAK,gBAAgB,OAAO,QAAQ,MAAM,UAAU,UAAU,KAAI;IACpE,EAAE,KAAK,UAAU,OAAO,QAAQ,MAAM,WAAW,UAAU,KAAI;IAC/D,EAAE,KAAK,eAAe,OAAO,WAAW,UAAU,KAAI;;EAGxD,eAA8B;IAC5B;MACE,OAAO;MACP,MAAM;MACN,SAAS;MACT,QAAQ,CAAC,SAAS,KAAK,cAAc,IAAI;;IAE3C;MACE,OAAO;MACP,MAAM;MACN,SAAS;MACT,QAAQ,CAAC,SAAS,KAAK,gBAAgB,IAAI;;;EAI/C,YAAoB,IAAe;AAAf,SAAA,KAAA;EAAkB;EAEtC,WAAQ;AACN,SAAK,cAAa;EACpB;EAEQ,gBAAa;AACnB,SAAK,UAAU;AAGf,eAAW,MAAK;AACd,WAAK,YAAY,KAAK,sBAAqB;AAC3C,WAAK,oBAAoB,CAAC,GAAG,KAAK,SAAS;AAC3C,WAAK,YAAW;AAChB,WAAK,gBAAe;AACpB,WAAK,UAAU;IACjB,GAAG,GAAI;EACT;EAEQ,wBAAqB;AAC3B,UAAM,aAAa,CAAC,YAAY,WAAW,YAAY,kBAAkB,UAAU,WAAW,YAAY;AAC1G,UAAM,gBAAgB,CAAC,UAAU,eAAe,cAAc,aAAa;AAC3E,UAAM,WAAW,CAAC,OAAO,cAAc,OAAO,OAAO,cAAc,aAAa;AAEhF,WAAO,MAAM,KAAK,EAAE,QAAQ,GAAE,GAAI,CAAC,GAAG,OAAO;MAC3C,IAAI,IAAI;MACR,UAAU,WAAW,KAAK,MAAM,KAAK,OAAM,IAAK,WAAW,MAAM,CAAC;MAClE,YAAY,cAAc,IAAI,CAAC;MAC/B,SAAS,GAAG,KAAK,MAAM,KAAK,OAAM,IAAK,CAAC,IAAI,CAAC,IAAI,KAAK,MAAM,KAAK,OAAM,IAAK,EAAE,CAAC,IAAI,KAAK,MAAM,KAAK,OAAM,IAAK,EAAE,CAAC;MACjH,SAAS,0BAA0B,IAAI,CAAC;MACxC,QAAQ,KAAK,OAAM,IAAK;MACxB,cAAc,KAAK,MAAM,KAAK,OAAM,IAAK,EAAE,IAAI;MAC/C,cAAc,cAAc,KAAK,MAAM,KAAK,OAAM,IAAK,cAAc,MAAM,CAAC;MAC5E,WAAW,KAAK,OAAM,IAAK,MAAM,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,OAAM,IAAK,MAAM,KAAK,KAAK,KAAK,GAAI,IAAI;MACpG,aAAa,SAAS,KAAK,MAAM,KAAK,OAAM,IAAK,SAAS,MAAM,CAAC;MACjE;EACJ;EAEQ,cAAW;AACjB,SAAK,MAAM,QAAQ,KAAK,UAAU;AAClC,SAAK,MAAM,OAAO,KAAK,UAAU,OAAO,OAAK,EAAE,MAAM,EAAE;AACvD,SAAK,MAAM,YAAY,KAAK,UAAU,OAAO,OAAK,EAAE,iBAAiB,iBAAiB,EAAE,iBAAiB,YAAY,EAAE;AACvH,SAAK,MAAM,aAAa,IAAI,IAAI,KAAK,UAAU,IAAI,OAAK,EAAE,QAAQ,CAAC,EAAE;AACrE,SAAK,MAAM,oBAAoB,KAAK,UAAU,OAAO,CAAC,KAAK,MAAM,MAAM,EAAE,cAAc,CAAC;AACxF,SAAK,MAAM,iBAAiB,KAAK,MAAO,KAAK,MAAM,OAAO,KAAK,MAAM,QAAS,GAAG;EACnF;EAEQ,kBAAe;AAErB,UAAM,gBAAgB,KAAK,UAAU,OAAO,CAAC,KAAK,SAAQ;AACxD,UAAI,KAAK,QAAQ,KAAK,IAAI,KAAK,QAAQ,KAAK,KAAK;AACjD,aAAO;IACT,GAAG,CAAA,CAAS;AAEZ,SAAK,oBAAoB;MACvB,QAAQ,OAAO,KAAK,aAAa;MACjC,UAAU,CAAC;QACT,MAAM,OAAO,OAAO,aAAa;QACjC,iBAAiB;UACf;UAAW;UAAW;UAAW;UAAW;UAC5C;UAAW;;OAEd;;AAIH,UAAM,eAAe,KAAK,UAAU,OAAO,CAAC,KAAK,SAAQ;AACvD,UAAI,KAAK,YAAY,KAAK,IAAI,KAAK,YAAY,KAAK,KAAK;AACzD,aAAO;IACT,GAAG,CAAA,CAAS;AAEZ,SAAK,mBAAmB;MACtB,QAAQ,OAAO,KAAK,YAAY;MAChC,UAAU,CAAC;QACT,OAAO;QACP,MAAM,OAAO,OAAO,YAAY;QAChC,iBAAiB,CAAC,WAAW,WAAW,WAAW,SAAS;OAC7D;;EAEL;EAEA,eAAY;AACV,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,YAAY;EACnB;EAEA,cAAc,WAAwB;AACpC,SAAK,WAAW;AAChB,SAAK,WAAW;MACd,MAAM,UAAU;MAChB,UAAU,UAAU;MACpB,SAAS,UAAU;MACnB,aAAa;MACb,SAAS,UAAU;MACnB,QAAQ;MACR,YAAY;MACZ,aAAa,UAAU,eAAe;;AAExC,SAAK,YAAY;EACnB;EAEA,gBAAgB,WAAwB;AACtC,QAAI,QAAQ,mCAAmC,UAAU,UAAU,GAAG,GAAG;AACvE,WAAK,YAAY,KAAK,UAAU,OAAO,OAAK,EAAE,OAAO,UAAU,EAAE;AACjE,WAAK,oBAAoB,KAAK,kBAAkB,OAAO,OAAK,EAAE,OAAO,UAAU,EAAE;AACjF,WAAK,YAAW;AAChB,WAAK,gBAAe;IACtB;EACF;EAEA,aAAU;AACR,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,WAAW;EAClB;EAEA,iBAAiB,eAAsC;AACrD,SAAK,eAAe;AAEpB,eAAW,MAAK;AACd,cAAQ,IAAI,qBAAqB,aAAa;AAG9C,WAAK,eAAe;AACpB,WAAK,WAAU;AACf,WAAK,cAAa;IACpB,GAAG,GAAI;EACT;EAEA,cAAc,OAAY;AACxB,UAAM,SAAS,MAAM;AACrB,SAAK,gBAAgB,OAAO,KAAK;EACnC;EAEA,gBAAgB,YAAkB;AAChC,SAAK,aAAY;EACnB;EAEA,gBAAgB,YAAoB,OAAY;AAC9C,UAAM,SAAS,MAAM;AAErB,SAAK,aAAY;EACnB;EAEA,cAAc,MAAS;AAErB,YAAQ,IAAI,iBAAiB,IAAI;EACnC;EAEQ,eAAY;AAElB,SAAK,oBAAoB,CAAC,GAAG,KAAK,SAAS;EAC7C;;qCAjNW,qBAAkB,4BAAA,WAAA,CAAA;EAAA;yEAAlB,qBAAkB,WAAA,CAAA,CAAA,gBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,IAAA,QAAA,CAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,WAAA,WAAA,YAAA,kBAAA,GAAA,SAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,SAAA,oBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,SAAA,mBAAA,GAAA,CAAA,GAAA,eAAA,MAAA,GAAA,CAAA,SAAA,kBAAA,GAAA,CAAA,GAAA,eAAA,SAAA,GAAA,CAAA,SAAA,oBAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,SAAA,4BAAA,YAAA,gCAAA,GAAA,CAAA,QAAA,YAAA,UAAA,SAAA,GAAA,QAAA,SAAA,GAAA,CAAA,SAAA,8BAAA,YAAA,+BAAA,GAAA,CAAA,QAAA,OAAA,UAAA,SAAA,GAAA,QAAA,SAAA,GAAA,CAAA,SAAA,oBAAA,YAAA,yBAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,QAAA,QAAA,eAAA,0BAAA,GAAA,gBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,QAAA,GAAA,CAAA,SAAA,EAAA,GAAA,CAAA,SAAA,UAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,SAAA,UAAA,GAAA,CAAA,SAAA,gBAAA,GAAA,CAAA,SAAA,QAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,SAAA,YAAA,GAAA,CAAA,SAAA,QAAA,GAAA,CAAA,SAAA,aAAA,GAAA,CAAA,SAAA,YAAA,GAAA,CAAA,SAAA,aAAA,GAAA,CAAA,GAAA,eAAA,WAAA,QAAA,WAAA,WAAA,UAAA,GAAA,CAAA,GAAA,UAAA,SAAA,UAAA,YAAA,eAAA,WAAA,0BAAA,CAAA,GAAA,UAAA,SAAA,4BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAzQ3B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA6B,GAAA,OAAA,CAAA,EACD,GAAA,OAAA,CAAA,EACC,GAAA,OAAA,CAAA,EACK,GAAA,IAAA;AACtB,MAAA,iBAAA,GAAA,kBAAA;AAAgB,MAAA,uBAAA;AACpB,MAAA,yBAAA,GAAA,KAAA,CAAA;AAAyB,MAAA,iBAAA,GAAA,yDAAA;AAAuD,MAAA,uBAAA,EAAI;AAEtF,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA4B,GAAA,cAAA,CAAA;AAIxB,MAAA,qBAAA,WAAA,SAAA,4DAAA;AAAA,eAAW,IAAA,aAAA;MAAc,CAAA;AAEzB,MAAA,iBAAA,IAAA,kBAAA;AACF,MAAA,uBAAA,EAAa,EACT;AAIV,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,YAAA,CAAA,EACe,IAAA,OAAA,CAAA,EACT,IAAA,OAAA,EAAA;AACC,MAAA,iBAAA,EAAA;AAAiB,MAAA,uBAAA;AAC1C,MAAA,yBAAA,IAAA,OAAA,EAAA;AAAyB,MAAA,iBAAA,EAAA;AAAwC,MAAA,uBAAA,EAAM,EACnE;AAGR,MAAA,yBAAA,IAAA,YAAA,EAAA,EAAoC,IAAA,OAAA,CAAA,EACR,IAAA,OAAA,EAAA;AACM,MAAA,iBAAA,EAAA;AAAgB,MAAA,uBAAA;AAC9C,MAAA,yBAAA,IAAA,OAAA,EAAA;AAAyB,MAAA,iBAAA,EAAA;AAAoC,MAAA,uBAAA,EAAM,EAC/D;AAGR,MAAA,yBAAA,IAAA,YAAA,EAAA,EAAmC,IAAA,OAAA,CAAA,EACP,IAAA,OAAA,EAAA;AACS,MAAA,iBAAA,EAAA;AAAqB,MAAA,uBAAA;AACtD,MAAA,yBAAA,IAAA,OAAA,EAAA;AAAyB,MAAA,iBAAA,IAAA,kBAAA;AAAgB,MAAA,uBAAA,EAAM,EAC3C;AAGR,MAAA,yBAAA,IAAA,YAAA,EAAA,EAAqC,IAAA,OAAA,CAAA,EACT,IAAA,OAAA,EAAA;AACC,MAAA,iBAAA,EAAA;AAA6B,MAAA,uBAAA;AACtD,MAAA,yBAAA,IAAA,OAAA,EAAA;AAAyB,MAAA,iBAAA,IAAA,aAAA;AAAW,MAAA,uBAAA,EAAM,EACtC,EACG;AAIb,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA4B,IAAA,YAAA,EAAA;AAExB,MAAA,oBAAA,IAAA,aAAA,EAAA;AAMF,MAAA,uBAAA;AAEA,MAAA,yBAAA,IAAA,YAAA,EAAA;AACE,MAAA,oBAAA,IAAA,aAAA,EAAA;AAMF,MAAA,uBAAA,EAAW;AAIb,MAAA,yBAAA,IAAA,YAAA,EAAA,EAAsE,IAAA,OAAA,EAAA,EACxC,IAAA,OAAA,EAAA,EACG,IAAA,SAAA,EAAA;AAKzB,MAAA,qBAAA,SAAA,SAAA,oDAAA,QAAA;AAAA,eAAS,IAAA,cAAA,MAAA;MAAqB,CAAA;AAJhC,MAAA,uBAAA;AAMA,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA8B,MAAA,qBAAA,UAAA,SAAA,sDAAA,QAAA;AAAA,eAAU,IAAA,gBAAgB,YAAU,MAAA;MAAS,CAAA;AACzE,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAiB,MAAA,iBAAA,IAAA,gBAAA;AAAc,MAAA,uBAAA;AAC/B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAyB,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA;AACjC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAwB,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA;AAC/B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAyB,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA;AACjC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA+B,MAAA,iBAAA,IAAA,gBAAA;AAAc,MAAA,uBAAA;AAC7C,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAuB,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA;AAC7B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAwB,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA;AAC/B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA2B,MAAA,iBAAA,IAAA,YAAA;AAAU,MAAA,uBAAA,EAAS;AAEhD,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA8B,MAAA,qBAAA,UAAA,SAAA,sDAAA,QAAA;AAAA,eAAU,IAAA,gBAAgB,gBAAc,MAAA;MAAS,CAAA;AAC7E,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAiB,MAAA,iBAAA,IAAA,oBAAA;AAAkB,MAAA,uBAAA;AACnC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAuB,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA;AAC7B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA4B,MAAA,iBAAA,IAAA,aAAA;AAAW,MAAA,uBAAA;AACvC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA2B,MAAA,iBAAA,IAAA,YAAA;AAAU,MAAA,uBAAA;AACrC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA4B,MAAA,iBAAA,IAAA,aAAA;AAAW,MAAA,uBAAA,EAAS,EACzC,EACL;AAGR,MAAA,yBAAA,IAAA,aAAA,EAAA;AAME,MAAA,qBAAA,eAAA,SAAA,8DAAA,QAAA;AAAA,eAAe,IAAA,cAAA,MAAA;MAAqB,CAAA;AACrC,MAAA,uBAAA,EAAY;AAIf,MAAA,yBAAA,IAAA,+BAAA,EAAA;AAME,MAAA,qBAAA,UAAA,SAAA,6EAAA;AAAA,eAAU,IAAA,WAAA;MAAY,CAAA,EAAC,SAAA,SAAA,0EAAA,QAAA;AAAA,eACd,IAAA,iBAAA,MAAA;MAAwB,CAAA;AAClC,MAAA,uBAAA,EAA8B,EACzB;;;AAlGyB,MAAA,oBAAA,EAAA;AAAA,MAAA,4BAAA,IAAA,MAAA,KAAA;AACA,MAAA,oBAAA,CAAA;AAAA,MAAA,6BAAA,WAAA,IAAA,MAAA,YAAA,aAAA;AAMK,MAAA,oBAAA,CAAA;AAAA,MAAA,4BAAA,IAAA,MAAA,IAAA;AACL,MAAA,oBAAA,CAAA;AAAA,MAAA,6BAAA,IAAA,IAAA,MAAA,gBAAA,YAAA;AAMQ,MAAA,oBAAA,CAAA;AAAA,MAAA,4BAAA,IAAA,MAAA,SAAA;AAOR,MAAA,oBAAA,CAAA;AAAA,MAAA,4BAAA,IAAA,MAAA,iBAAA;AAWzB,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,iBAAA,EAA0B,WAAA,IAAA,YAAA;AAS1B,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,gBAAA,EAAyB,WAAA,IAAA,YAAA;AAsC3B,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,WAAA,IAAA,YAAA,EAAwB,QAAA,IAAA,iBAAA,EACE,WAAA,IAAA,YAAA,EACF,WAAA,IAAA,OAAA,EACL,YAAA,IAAA;AAQrB,MAAA,oBAAA;AAAA,MAAA,qBAAA,UAAA,IAAA,SAAA,EAAoB,YAAA,IAAA,QAAA,EACC,eAAA,IAAA,QAAA,EACG,WAAA,IAAA,YAAA,EACA,4BAAA,IAAA;;;IA7H5B;IACA;IACA;IAAmB;IAAA;IACnB;IACA;IACA;IACA;IACA;EAA6B,GAAA,QAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;qDAAA,EAAA,CAAA;;;sEA4QpB,oBAAkB,CAAA;UAvR9B;uBACW,kBAAgB,YACd,MAAI,SACP;MACP;MACA;MACA;MACA;MACA;MACA;MACA;MACA;OACD,UACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA2HT,QAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAAA,EAAA,CAAA;;;;6EA+IU,oBAAkB,EAAA,WAAA,sBAAA,UAAA,sDAAA,YAAA,IAAA,CAAA;AAAA,GAAA;", "names": []}