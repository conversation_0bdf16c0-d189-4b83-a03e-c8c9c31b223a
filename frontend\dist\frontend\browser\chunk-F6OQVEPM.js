import {
  ApplicationsService
} from "./chunk-DZCIXRES.js";
import {
  CardComponent
} from "./chunk-SLWZQAXJ.js";
import {
  ButtonComponent
} from "./chunk-QMVBAXS7.js";
import {
  ActivatedRoute,
  RouterLink,
  RouterModule
} from "./chunk-IKU57TF7.js";
import {
  CommonModule,
  Component,
  DatePipe,
  DecimalPipe,
  NgForOf,
  NgIf,
  Subject,
  setClassMetadata,
  takeUntil,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassMap,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnamespaceHTML,
  ɵɵnamespaceSVG,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind1,
  ɵɵpipeBind2,
  ɵɵproperty,
  ɵɵpureFunction1,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵsanitizeUrl,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1
} from "./chunk-3JQLQ36P.js";
import "./chunk-WDMUDEB6.js";

// src/app/modules/applications/application-detail/application-detail.component.ts
var _c0 = (a0) => ["/applications", a0, "edit"];
function ApplicationDetailComponent_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 4)(1, "div", 5);
    \u0275\u0275element(2, "div", 6);
    \u0275\u0275elementStart(3, "p");
    \u0275\u0275text(4, "Loading application details...");
    \u0275\u0275elementEnd()()();
  }
}
function ApplicationDetailComponent_div_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 7)(1, "div", 8);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(2, "svg", 9);
    \u0275\u0275element(3, "circle", 10)(4, "line", 11)(5, "line", 12);
    \u0275\u0275elementEnd();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(6, "h3");
    \u0275\u0275text(7, "Failed to load application");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(8, "p");
    \u0275\u0275text(9, "There was an error loading the application details. Please try again.");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "app-button", 13);
    \u0275\u0275listener("click", function ApplicationDetailComponent_div_2_Template_app_button_click_10_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.loadApplication());
    });
    \u0275\u0275text(11, " Retry ");
    \u0275\u0275elementEnd()()();
  }
}
function ApplicationDetailComponent_div_3_a_94_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "a", 89);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(1, "svg", 90);
    \u0275\u0275element(2, "path", 91);
    \u0275\u0275elementEnd();
    \u0275\u0275text(3, " View Repository ");
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275property("href", ctx_r1.application.repository, \u0275\u0275sanitizeUrl);
  }
}
function ApplicationDetailComponent_div_3_span_95_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 92);
    \u0275\u0275text(1, "Not specified");
    \u0275\u0275elementEnd();
  }
}
function ApplicationDetailComponent_div_3_a_99_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "a", 89);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(1, "svg", 90);
    \u0275\u0275element(2, "path", 93)(3, "polyline", 94)(4, "line", 95);
    \u0275\u0275elementEnd();
    \u0275\u0275text(5, " Open Application ");
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275property("href", ctx_r1.application.url, \u0275\u0275sanitizeUrl);
  }
}
function ApplicationDetailComponent_div_3_span_100_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 92);
    \u0275\u0275text(1, "Not deployed");
    \u0275\u0275elementEnd();
  }
}
function ApplicationDetailComponent_div_3_a_104_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "a", 89);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(1, "svg", 90);
    \u0275\u0275element(2, "path", 53)(3, "polyline", 54);
    \u0275\u0275elementEnd();
    \u0275\u0275text(4, " View Documentation ");
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275property("href", ctx_r1.application.documentation, \u0275\u0275sanitizeUrl);
  }
}
function ApplicationDetailComponent_div_3_span_105_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 92);
    \u0275\u0275text(1, "Not available");
    \u0275\u0275elementEnd();
  }
}
function ApplicationDetailComponent_div_3_app_card_193_div_2_span_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 109);
    \u0275\u0275text(1, "Internal");
    \u0275\u0275elementEnd();
  }
}
function ApplicationDetailComponent_div_3_app_card_193_div_2_p_14_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p", 110);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const dep_r3 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(dep_r3.description);
  }
}
function ApplicationDetailComponent_div_3_app_card_193_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 99)(1, "div", 100)(2, "h4", 101);
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "span", 102);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "span", 103);
    \u0275\u0275text(7);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(8, "div", 104)(9, "span", 105);
    \u0275\u0275text(10);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(11, "span", 106);
    \u0275\u0275text(12);
    \u0275\u0275elementEnd();
    \u0275\u0275template(13, ApplicationDetailComponent_div_3_app_card_193_div_2_span_13_Template, 2, 0, "span", 107);
    \u0275\u0275elementEnd();
    \u0275\u0275template(14, ApplicationDetailComponent_div_3_app_card_193_div_2_p_14_Template, 2, 1, "p", 108);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const dep_r3 = ctx.$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(dep_r3.name);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(dep_r3.type);
    \u0275\u0275advance();
    \u0275\u0275classMap("criticality-" + dep_r3.criticality);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(dep_r3.criticality);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1("v", dep_r3.version, "");
    \u0275\u0275advance();
    \u0275\u0275classMap("status-" + dep_r3.status);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(dep_r3.status);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", dep_r3.isInternal);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", dep_r3.description);
  }
}
function ApplicationDetailComponent_div_3_app_card_193_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "app-card", 96)(1, "div", 97);
    \u0275\u0275template(2, ApplicationDetailComponent_div_3_app_card_193_div_2_Template, 15, 11, "div", 98);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngForOf", ctx_r1.application.dependencies);
  }
}
function ApplicationDetailComponent_div_3_app_card_194_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 114)(1, "div", 115)(2, "h4", 116);
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "span", 117);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(6, "div", 118)(7, "span", 119);
    \u0275\u0275text(8);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const tech_r4 = ctx.$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(tech_r4.name);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(tech_r4.category);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate1("v", tech_r4.version, "");
  }
}
function ApplicationDetailComponent_div_3_app_card_194_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "app-card", 111)(1, "div", 112);
    \u0275\u0275template(2, ApplicationDetailComponent_div_3_app_card_194_div_2_Template, 9, 3, "div", 113);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngForOf", ctx_r1.application.techStack);
  }
}
function ApplicationDetailComponent_div_3_app_card_195_div_2_span_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 132);
    \u0275\u0275text(1, "Primary");
    \u0275\u0275elementEnd();
  }
}
function ApplicationDetailComponent_div_3_app_card_195_div_2_p_12_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p", 133);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const stakeholder_r5 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(stakeholder_r5.responsibility);
  }
}
function ApplicationDetailComponent_div_3_app_card_195_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 123)(1, "div", 124)(2, "h4", 125);
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "span", 126);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd();
    \u0275\u0275template(6, ApplicationDetailComponent_div_3_app_card_195_div_2_span_6_Template, 2, 0, "span", 127);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "div", 128)(8, "span", 129);
    \u0275\u0275text(9);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "span", 130);
    \u0275\u0275text(11);
    \u0275\u0275elementEnd()();
    \u0275\u0275template(12, ApplicationDetailComponent_div_3_app_card_195_div_2_p_12_Template, 2, 1, "p", 131);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const stakeholder_r5 = ctx.$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(stakeholder_r5.name);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(stakeholder_r5.role);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", stakeholder_r5.isPrimary);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(stakeholder_r5.email);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(stakeholder_r5.department);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", stakeholder_r5.responsibility);
  }
}
function ApplicationDetailComponent_div_3_app_card_195_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "app-card", 120)(1, "div", 121);
    \u0275\u0275template(2, ApplicationDetailComponent_div_3_app_card_195_div_2_Template, 13, 6, "div", 122);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngForOf", ctx_r1.application.stakeholders);
  }
}
function ApplicationDetailComponent_div_3_app_card_196_div_2_p_8_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p", 146);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const doc_r6 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(doc_r6.description);
  }
}
function ApplicationDetailComponent_div_3_app_card_196_div_2_span_10_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 147);
    \u0275\u0275text(1);
    \u0275\u0275pipe(2, "date");
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const doc_r6 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1("Uploaded ", \u0275\u0275pipeBind2(2, 1, doc_r6.uploadedAt, "short"), "");
  }
}
function ApplicationDetailComponent_div_3_app_card_196_div_2_span_11_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 148);
    \u0275\u0275text(1);
    \u0275\u0275pipe(2, "number");
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const doc_r6 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1("", \u0275\u0275pipeBind1(2, 1, doc_r6.fileSize), " bytes");
  }
}
function ApplicationDetailComponent_div_3_app_card_196_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 137)(1, "div", 138)(2, "h4", 139);
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "span", 140);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "span", 141);
    \u0275\u0275text(7);
    \u0275\u0275elementEnd()();
    \u0275\u0275template(8, ApplicationDetailComponent_div_3_app_card_196_div_2_p_8_Template, 2, 1, "p", 142);
    \u0275\u0275elementStart(9, "div", 143);
    \u0275\u0275template(10, ApplicationDetailComponent_div_3_app_card_196_div_2_span_10_Template, 3, 4, "span", 144)(11, ApplicationDetailComponent_div_3_app_card_196_div_2_span_11_Template, 3, 3, "span", 145);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const doc_r6 = ctx.$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(doc_r6.title);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(doc_r6.type);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1("v", doc_r6.version, "");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", doc_r6.description);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", doc_r6.uploadedAt);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", doc_r6.fileSize);
  }
}
function ApplicationDetailComponent_div_3_app_card_196_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "app-card", 134)(1, "div", 135);
    \u0275\u0275template(2, ApplicationDetailComponent_div_3_app_card_196_div_2_Template, 12, 6, "div", 136);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngForOf", ctx_r1.application.documents);
  }
}
function ApplicationDetailComponent_div_3_app_card_197_div_2_span_11_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 162);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const vuln_r7 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(vuln_r7.cveId);
  }
}
function ApplicationDetailComponent_div_3_app_card_197_div_2_span_12_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 163);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const vuln_r7 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1("CVSS: ", vuln_r7.cvssScore, "");
  }
}
function ApplicationDetailComponent_div_3_app_card_197_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 152)(1, "div", 153)(2, "h4", 154);
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "span", 155);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "span", 156);
    \u0275\u0275text(7);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(8, "p", 157);
    \u0275\u0275text(9);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "div", 158);
    \u0275\u0275template(11, ApplicationDetailComponent_div_3_app_card_197_div_2_span_11_Template, 2, 1, "span", 159)(12, ApplicationDetailComponent_div_3_app_card_197_div_2_span_12_Template, 2, 1, "span", 160);
    \u0275\u0275elementStart(13, "span", 161);
    \u0275\u0275text(14);
    \u0275\u0275pipe(15, "date");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const vuln_r7 = ctx.$implicit;
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(vuln_r7.title);
    \u0275\u0275advance();
    \u0275\u0275classMap("severity-" + vuln_r7.severity);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(vuln_r7.severity);
    \u0275\u0275advance();
    \u0275\u0275classMap("status-" + vuln_r7.status);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(vuln_r7.status);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(vuln_r7.description);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", vuln_r7.cveId);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", vuln_r7.cvssScore);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1("Discovered ", \u0275\u0275pipeBind2(15, 11, vuln_r7.discoveredAt, "short"), "");
  }
}
function ApplicationDetailComponent_div_3_app_card_197_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "app-card", 149)(1, "div", 150);
    \u0275\u0275template(2, ApplicationDetailComponent_div_3_app_card_197_div_2_Template, 16, 14, "div", 151);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngForOf", ctx_r1.application.vulnerabilities);
  }
}
function ApplicationDetailComponent_div_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 14)(1, "div", 15);
    \u0275\u0275element(2, "div", 16);
    \u0275\u0275elementStart(3, "div", 17)(4, "div", 18)(5, "div", 19);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(6, "svg", 20);
    \u0275\u0275element(7, "rect", 21)(8, "circle", 22)(9, "path", 23)(10, "path", 24);
    \u0275\u0275elementEnd()();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(11, "div", 25)(12, "h1", 26);
    \u0275\u0275text(13);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(14, "p", 27);
    \u0275\u0275text(15);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(16, "div", 28)(17, "span", 29);
    \u0275\u0275text(18);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(19, "span", 30);
    \u0275\u0275text(20);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(21, "span", 31);
    \u0275\u0275text(22);
    \u0275\u0275elementEnd()()()();
    \u0275\u0275elementStart(23, "div", 32)(24, "app-button", 33);
    \u0275\u0275text(25, " Back to List ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(26, "app-button", 34);
    \u0275\u0275text(27, " Edit Application ");
    \u0275\u0275elementEnd()()()();
    \u0275\u0275elementStart(28, "div", 35)(29, "div", 36)(30, "div", 37)(31, "div", 38);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(32, "svg", 20);
    \u0275\u0275element(33, "path", 39);
    \u0275\u0275elementEnd()();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(34, "div", 40)(35, "div", 41);
    \u0275\u0275text(36);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(37, "div", 42);
    \u0275\u0275text(38, "Health Score");
    \u0275\u0275elementEnd()();
    \u0275\u0275element(39, "div", 43);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(40, "div", 37)(41, "div", 44);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(42, "svg", 20);
    \u0275\u0275element(43, "path", 45);
    \u0275\u0275elementEnd()();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(44, "div", 40)(45, "div", 41);
    \u0275\u0275text(46);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(47, "div", 42);
    \u0275\u0275text(48, "Security Score");
    \u0275\u0275elementEnd()();
    \u0275\u0275element(49, "div", 43);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(50, "div", 37)(51, "div", 46);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(52, "svg", 20);
    \u0275\u0275element(53, "circle", 10)(54, "polyline", 47);
    \u0275\u0275elementEnd()();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(55, "div", 40)(56, "div", 41);
    \u0275\u0275text(57);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(58, "div", 42);
    \u0275\u0275text(59, "Performance Score");
    \u0275\u0275elementEnd()();
    \u0275\u0275element(60, "div", 43);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(61, "div", 48)(62, "div", 49)(63, "div", 50)(64, "div", 51)(65, "div", 52);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(66, "svg", 20);
    \u0275\u0275element(67, "path", 53)(68, "polyline", 54)(69, "line", 55)(70, "line", 56)(71, "polyline", 57);
    \u0275\u0275elementEnd()();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(72, "h3", 58);
    \u0275\u0275text(73, "Application Details");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(74, "div", 59)(75, "div", 60)(76, "div", 61)(77, "span", 62);
    \u0275\u0275text(78, "Owner");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(79, "span", 63);
    \u0275\u0275text(80);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(81, "div", 61)(82, "span", 62);
    \u0275\u0275text(83, "Department");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(84, "span", 63);
    \u0275\u0275text(85);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(86, "div", 61)(87, "span", 62);
    \u0275\u0275text(88, "Version");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(89, "span", 64);
    \u0275\u0275text(90);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(91, "div", 61)(92, "span", 62);
    \u0275\u0275text(93, "Repository");
    \u0275\u0275elementEnd();
    \u0275\u0275template(94, ApplicationDetailComponent_div_3_a_94_Template, 4, 1, "a", 65)(95, ApplicationDetailComponent_div_3_span_95_Template, 2, 0, "span", 66);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(96, "div", 61)(97, "span", 62);
    \u0275\u0275text(98, "Deployment");
    \u0275\u0275elementEnd();
    \u0275\u0275template(99, ApplicationDetailComponent_div_3_a_99_Template, 6, 1, "a", 65)(100, ApplicationDetailComponent_div_3_span_100_Template, 2, 0, "span", 66);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(101, "div", 61)(102, "span", 62);
    \u0275\u0275text(103, "Documentation");
    \u0275\u0275elementEnd();
    \u0275\u0275template(104, ApplicationDetailComponent_div_3_a_104_Template, 5, 1, "a", 65)(105, ApplicationDetailComponent_div_3_span_105_Template, 2, 0, "span", 66);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(106, "div", 61)(107, "span", 62);
    \u0275\u0275text(108, "Created");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(109, "span", 63);
    \u0275\u0275text(110);
    \u0275\u0275pipe(111, "date");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(112, "div", 61)(113, "span", 62);
    \u0275\u0275text(114, "Last Updated");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(115, "span", 63);
    \u0275\u0275text(116);
    \u0275\u0275pipe(117, "date");
    \u0275\u0275elementEnd()()()()();
    \u0275\u0275elementStart(118, "app-card", 67)(119, "div", 68)(120, "div", 69)(121, "div", 70);
    \u0275\u0275text(122);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(123, "div", 71);
    \u0275\u0275text(124, "Dependencies");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(125, "div", 69)(126, "div", 70);
    \u0275\u0275text(127);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(128, "div", 71);
    \u0275\u0275text(129, "Tech Stack Items");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(130, "div", 69)(131, "div", 70);
    \u0275\u0275text(132);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(133, "div", 71);
    \u0275\u0275text(134, "Stakeholders");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(135, "div", 69)(136, "div", 70);
    \u0275\u0275text(137);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(138, "div", 71);
    \u0275\u0275text(139, "Documents");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(140, "div", 69)(141, "div", 70);
    \u0275\u0275text(142);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(143, "div", 71);
    \u0275\u0275text(144, "Vulnerabilities");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(145, "div", 69)(146, "div", 70);
    \u0275\u0275text(147);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(148, "div", 71);
    \u0275\u0275text(149, "Security Assessments");
    \u0275\u0275elementEnd()()()();
    \u0275\u0275elementStart(150, "app-card", 72)(151, "div", 36)(152, "div", 73)(153, "div", 74)(154, "div", 41);
    \u0275\u0275text(155);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(156, "div", 42);
    \u0275\u0275text(157, "Health");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(158, "div", 73)(159, "div", 74)(160, "div", 41);
    \u0275\u0275text(161);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(162, "div", 42);
    \u0275\u0275text(163, "Security");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(164, "div", 73)(165, "div", 74)(166, "div", 41);
    \u0275\u0275text(167);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(168, "div", 42);
    \u0275\u0275text(169, "Performance");
    \u0275\u0275elementEnd()()()()();
    \u0275\u0275elementStart(170, "app-card", 75)(171, "div", 76)(172, "div", 77)(173, "div", 78)(174, "span", 79);
    \u0275\u0275text(175);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(176, "span", 80);
    \u0275\u0275text(177, "Critical");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(178, "div", 78)(179, "span", 81);
    \u0275\u0275text(180);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(181, "span", 80);
    \u0275\u0275text(182, "High");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(183, "div", 78)(184, "span", 82);
    \u0275\u0275text(185);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(186, "span", 80);
    \u0275\u0275text(187, "Medium");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(188, "div", 78)(189, "span", 83);
    \u0275\u0275text(190);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(191, "span", 80);
    \u0275\u0275text(192, "Low");
    \u0275\u0275elementEnd()()()()();
    \u0275\u0275template(193, ApplicationDetailComponent_div_3_app_card_193_Template, 3, 1, "app-card", 84)(194, ApplicationDetailComponent_div_3_app_card_194_Template, 3, 1, "app-card", 85)(195, ApplicationDetailComponent_div_3_app_card_195_Template, 3, 1, "app-card", 86)(196, ApplicationDetailComponent_div_3_app_card_196_Template, 3, 1, "app-card", 87)(197, ApplicationDetailComponent_div_3_app_card_197_Template, 3, 1, "app-card", 88);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(13);
    \u0275\u0275textInterpolate(ctx_r1.application.name);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(ctx_r1.application.description);
    \u0275\u0275advance(2);
    \u0275\u0275classMap("status-" + ctx_r1.application.status.toLowerCase());
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r1.application.status, " ");
    \u0275\u0275advance();
    \u0275\u0275classMap("criticality-" + ctx_r1.application.criticality.toLowerCase());
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r1.application.criticality, " ");
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(ctx_r1.application.department);
    \u0275\u0275advance(4);
    \u0275\u0275property("routerLink", \u0275\u0275pureFunction1(60, _c0, ctx_r1.application.id));
    \u0275\u0275advance(10);
    \u0275\u0275textInterpolate(ctx_r1.application.healthScore);
    \u0275\u0275advance(3);
    \u0275\u0275classMap(ctx_r1.getScoreClass(ctx_r1.application.healthScore));
    \u0275\u0275advance(7);
    \u0275\u0275textInterpolate(ctx_r1.application.securityScore);
    \u0275\u0275advance(3);
    \u0275\u0275classMap(ctx_r1.getScoreClass(ctx_r1.application.securityScore));
    \u0275\u0275advance(8);
    \u0275\u0275textInterpolate(ctx_r1.application.performanceScore);
    \u0275\u0275advance(3);
    \u0275\u0275classMap(ctx_r1.getScoreClass(ctx_r1.application.performanceScore));
    \u0275\u0275advance(20);
    \u0275\u0275textInterpolate(ctx_r1.application.owner);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r1.application.department);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r1.application.version);
    \u0275\u0275advance(4);
    \u0275\u0275property("ngIf", ctx_r1.application.repository);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r1.application.repository);
    \u0275\u0275advance(4);
    \u0275\u0275property("ngIf", ctx_r1.application.url);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r1.application.url);
    \u0275\u0275advance(4);
    \u0275\u0275property("ngIf", ctx_r1.application.documentation);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r1.application.documentation);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(\u0275\u0275pipeBind2(111, 54, ctx_r1.application.createdDate, "medium"));
    \u0275\u0275advance(6);
    \u0275\u0275textInterpolate(\u0275\u0275pipeBind2(117, 57, ctx_r1.application.lastUpdated, "medium"));
    \u0275\u0275advance(6);
    \u0275\u0275textInterpolate((ctx_r1.application.dependencies == null ? null : ctx_r1.application.dependencies.length) || 0);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate((ctx_r1.application.techStack == null ? null : ctx_r1.application.techStack.length) || 0);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate((ctx_r1.application.stakeholders == null ? null : ctx_r1.application.stakeholders.length) || 0);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate((ctx_r1.application.documents == null ? null : ctx_r1.application.documents.length) || 0);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate((ctx_r1.application.vulnerabilities == null ? null : ctx_r1.application.vulnerabilities.length) || 0);
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate((ctx_r1.application.securityAssessments == null ? null : ctx_r1.application.securityAssessments.length) || 0);
    \u0275\u0275advance(7);
    \u0275\u0275classMap(ctx_r1.getScoreClass(ctx_r1.application.healthScore));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1("", ctx_r1.application.healthScore, "%");
    \u0275\u0275advance(5);
    \u0275\u0275classMap(ctx_r1.getScoreClass(ctx_r1.application.securityScore));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1("", ctx_r1.application.securityScore, "%");
    \u0275\u0275advance(5);
    \u0275\u0275classMap(ctx_r1.getScoreClass(ctx_r1.application.performanceScore));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1("", ctx_r1.application.performanceScore, "%");
    \u0275\u0275advance(8);
    \u0275\u0275textInterpolate(ctx_r1.getVulnerabilityCount("critical"));
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r1.getVulnerabilityCount("high"));
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r1.getVulnerabilityCount("medium"));
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r1.getVulnerabilityCount("low"));
    \u0275\u0275advance(3);
    \u0275\u0275property("ngIf", ctx_r1.application.dependencies && ctx_r1.application.dependencies.length > 0);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.application.techStack && ctx_r1.application.techStack.length > 0);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.application.stakeholders && ctx_r1.application.stakeholders.length > 0);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.application.documents && ctx_r1.application.documents.length > 0);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.application.vulnerabilities && ctx_r1.application.vulnerabilities.length > 0);
  }
}
var ApplicationDetailComponent = class _ApplicationDetailComponent {
  route;
  applicationsService;
  application = null;
  isLoading = true;
  hasError = false;
  applicationId = null;
  destroy$ = new Subject();
  constructor(route, applicationsService) {
    this.route = route;
    this.applicationsService = applicationsService;
  }
  ngOnInit() {
    this.route.params.pipe(takeUntil(this.destroy$)).subscribe((params) => {
      this.applicationId = +params["id"];
      if (this.applicationId) {
        this.loadApplication();
      }
    });
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  loadApplication() {
    if (!this.applicationId)
      return;
    this.isLoading = true;
    this.hasError = false;
    this.applicationsService.getApplication(this.applicationId).pipe(takeUntil(this.destroy$)).subscribe({
      next: (app) => {
        this.application = app;
        this.isLoading = false;
      },
      error: (error) => {
        console.error("Error loading application:", error);
        this.hasError = true;
        this.isLoading = false;
      }
    });
  }
  getScoreClass(score) {
    if (score >= 90)
      return "score-excellent";
    if (score >= 80)
      return "score-good";
    if (score >= 70)
      return "score-fair";
    return "score-poor";
  }
  getVulnerabilityCount(severity) {
    if (!this.application?.vulnerabilities)
      return 0;
    return this.application.vulnerabilities.filter((v) => v.severity.toLowerCase() === severity.toLowerCase()).length;
  }
  static \u0275fac = function ApplicationDetailComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ApplicationDetailComponent)(\u0275\u0275directiveInject(ActivatedRoute), \u0275\u0275directiveInject(ApplicationsService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _ApplicationDetailComponent, selectors: [["app-application-detail"]], decls: 4, vars: 3, consts: [[1, "application-detail-page"], ["class", "loading-state", 4, "ngIf"], ["class", "error-state", 4, "ngIf"], ["class", "application-content", 4, "ngIf"], [1, "loading-state"], [1, "loading-content"], [1, "loading-spinner"], [1, "error-state"], [1, "error-content"], ["viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", 1, "error-icon"], ["cx", "12", "cy", "12", "r", "10"], ["x1", "12", "y1", "8", "x2", "12", "y2", "12"], ["x1", "12", "y1", "16", "x2", "12.01", "y2", "16"], ["variant", "primary", 3, "click"], [1, "application-content"], [1, "hero-section"], [1, "hero-background"], [1, "hero-content"], [1, "hero-info"], [1, "app-icon"], ["viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor"], ["x", "3", "y", "3", "width", "18", "height", "18", "rx", "2", "ry", "2"], ["cx", "9", "cy", "9", "r", "2"], ["d", "m21 15-3.086-3.086a2 2 0 0 0-1.414-.586H13"], ["d", "M9 21v-6a2 2 0 0 1 2-2h4"], [1, "app-details"], [1, "app-title"], [1, "app-description"], [1, "app-badges"], [1, "status-badge"], [1, "criticality-badge"], [1, "department-badge"], [1, "hero-actions"], ["variant", "ghost", "routerLink", "/applications", "leftIcon", "M15 19l-7-7 7-7"], ["variant", "primary", "leftIcon", "M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7", 3, "routerLink"], [1, "scores-section"], [1, "scores-grid"], [1, "score-card"], [1, "score-icon", "health"], ["d", "M22 12h-4l-3 9L9 3l-3 9H2"], [1, "score-content"], [1, "score-value"], [1, "score-label"], [1, "score-indicator"], [1, "score-icon", "security"], ["d", "M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"], [1, "score-icon", "performance"], ["points", "12,6 12,12 16,14"], [1, "main-content"], [1, "content-grid"], [1, "info-card"], [1, "card-header"], [1, "card-icon"], ["d", "M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"], ["points", "14,2 14,8 20,8"], ["x1", "16", "y1", "13", "x2", "8", "y2", "13"], ["x1", "16", "y1", "17", "x2", "8", "y2", "17"], ["points", "10,9 9,9 8,9"], [1, "card-title"], [1, "card-content"], [1, "detail-grid"], [1, "detail-item"], [1, "detail-label"], [1, "detail-value"], [1, "detail-value", "version-badge"], ["target", "_blank", "class", "detail-link", 3, "href", 4, "ngIf"], ["class", "detail-value muted", 4, "ngIf"], ["title", "Quick Stats", 1, "stats-card"], [1, "stats-grid"], [1, "stat-item"], [1, "stat-value"], [1, "stat-label"], ["title", "Performance Scores", 1, "scores-card"], [1, "score-item"], [1, "score-circle"], ["title", "Security Overview", 1, "security-card"], [1, "security-content"], [1, "vulnerability-summary"], [1, "vuln-item"], [1, "vuln-count", "critical"], [1, "vuln-label"], [1, "vuln-count", "high"], [1, "vuln-count", "medium"], [1, "vuln-count", "low"], ["title", "Dependencies", "class", "dependencies-card", 4, "ngIf"], ["title", "Technology Stack", "class", "techstack-card", 4, "ngIf"], ["title", "Stakeholders", "class", "stakeholders-card", 4, "ngIf"], ["title", "Documentation", "class", "documents-card", 4, "ngIf"], ["title", "Vulnerabilities", "class", "vulnerabilities-card", 4, "ngIf"], ["target", "_blank", 1, "detail-link", 3, "href"], ["viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", 1, "link-icon"], ["d", "M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"], [1, "detail-value", "muted"], ["d", "M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"], ["points", "15,3 21,3 21,9"], ["x1", "10", "y1", "14", "x2", "21", "y2", "3"], ["title", "Dependencies", 1, "dependencies-card"], [1, "dependencies-list"], ["class", "dependency-item", 4, "ngFor", "ngForOf"], [1, "dependency-item"], [1, "dependency-header"], [1, "dependency-name"], [1, "dependency-type"], [1, "dependency-criticality"], [1, "dependency-details"], [1, "dependency-version"], [1, "dependency-status"], ["class", "dependency-internal", 4, "ngIf"], ["class", "dependency-description", 4, "ngIf"], [1, "dependency-internal"], [1, "dependency-description"], ["title", "Technology Stack", 1, "techstack-card"], [1, "techstack-list"], ["class", "techstack-item", 4, "ngFor", "ngForOf"], [1, "techstack-item"], [1, "techstack-header"], [1, "techstack-name"], [1, "techstack-category"], [1, "techstack-details"], [1, "techstack-version"], ["title", "Stakeholders", 1, "stakeholders-card"], [1, "stakeholders-list"], ["class", "stakeholder-item", 4, "ngFor", "ngForOf"], [1, "stakeholder-item"], [1, "stakeholder-header"], [1, "stakeholder-name"], [1, "stakeholder-role"], ["class", "stakeholder-primary", 4, "ngIf"], [1, "stakeholder-details"], [1, "stakeholder-email"], [1, "stakeholder-department"], ["class", "stakeholder-responsibility", 4, "ngIf"], [1, "stakeholder-primary"], [1, "stakeholder-responsibility"], ["title", "Documentation", 1, "documents-card"], [1, "documents-list"], ["class", "document-item", 4, "ngFor", "ngForOf"], [1, "document-item"], [1, "document-header"], [1, "document-title"], [1, "document-type"], [1, "document-version"], ["class", "document-description", 4, "ngIf"], [1, "document-meta"], ["class", "document-uploaded", 4, "ngIf"], ["class", "document-size", 4, "ngIf"], [1, "document-description"], [1, "document-uploaded"], [1, "document-size"], ["title", "Vulnerabilities", 1, "vulnerabilities-card"], [1, "vulnerabilities-list"], ["class", "vulnerability-item", 4, "ngFor", "ngForOf"], [1, "vulnerability-item"], [1, "vulnerability-header"], [1, "vulnerability-title"], [1, "vulnerability-severity"], [1, "vulnerability-status"], [1, "vulnerability-description"], [1, "vulnerability-meta"], ["class", "vulnerability-cve", 4, "ngIf"], ["class", "vulnerability-score", 4, "ngIf"], [1, "vulnerability-discovered"], [1, "vulnerability-cve"], [1, "vulnerability-score"]], template: function ApplicationDetailComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0);
      \u0275\u0275template(1, ApplicationDetailComponent_div_1_Template, 5, 0, "div", 1)(2, ApplicationDetailComponent_div_2_Template, 12, 0, "div", 2)(3, ApplicationDetailComponent_div_3_Template, 198, 62, "div", 3);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.isLoading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.hasError && !ctx.isLoading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.isLoading && !ctx.hasError && ctx.application);
    }
  }, dependencies: [CommonModule, NgForOf, NgIf, DecimalPipe, DatePipe, RouterModule, RouterLink, CardComponent, ButtonComponent], styles: [`

.application-detail-page[_ngcontent-%COMP%] {
  min-height: 100%;
  background: var(--neutral-50);
}
.hero-section[_ngcontent-%COMP%] {
  position: relative;
  background:
    linear-gradient(
      135deg,
      var(--primary-600) 0%,
      var(--primary-800) 100%);
  color: white;
  overflow: hidden;
  margin-bottom: var(--spacing-xl);
}
.hero-background[_ngcontent-%COMP%] {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}
.hero-content[_ngcontent-%COMP%] {
  position: relative;
  padding: var(--spacing-3xl) var(--spacing-xl);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-xl);
}
.hero-info[_ngcontent-%COMP%] {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  flex: 1;
}
.app-icon[_ngcontent-%COMP%] {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}
.app-icon[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {
  width: 40px;
  height: 40px;
  color: white;
}
.app-details[_ngcontent-%COMP%] {
  flex: 1;
}
.app-title[_ngcontent-%COMP%] {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--text-4xl);
  font-weight: 700;
  color: white;
  line-height: var(--leading-tight);
}
.app-description[_ngcontent-%COMP%] {
  margin: 0 0 var(--spacing-md) 0;
  font-size: var(--text-lg);
  color: rgba(255, 255, 255, 0.9);
  line-height: var(--leading-relaxed);
  max-width: 600px;
}
.app-badges[_ngcontent-%COMP%] {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}
.status-badge[_ngcontent-%COMP%], 
.criticality-badge[_ngcontent-%COMP%], 
.department-badge[_ngcontent-%COMP%] {
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  font-weight: 500;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}
.hero-actions[_ngcontent-%COMP%] {
  display: flex;
  gap: var(--spacing-sm);
  flex-shrink: 0;
}
.scores-section[_ngcontent-%COMP%] {
  margin-bottom: var(--spacing-xl);
}
.scores-grid[_ngcontent-%COMP%] {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-lg);
  padding: 0 var(--spacing-xl);
}
.score-card[_ngcontent-%COMP%] {
  background: white;
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--secondary-200);
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}
.score-card[_ngcontent-%COMP%]:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}
.score-icon[_ngcontent-%COMP%] {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.score-icon.health[_ngcontent-%COMP%] {
  background:
    linear-gradient(
      135deg,
      var(--success-500),
      var(--success-600));
  color: white;
}
.score-icon.security[_ngcontent-%COMP%] {
  background:
    linear-gradient(
      135deg,
      var(--primary-500),
      var(--primary-600));
  color: white;
}
.score-icon.performance[_ngcontent-%COMP%] {
  background:
    linear-gradient(
      135deg,
      var(--warning-500),
      var(--warning-600));
  color: white;
}
.score-icon[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {
  width: 28px;
  height: 28px;
}
.score-content[_ngcontent-%COMP%] {
  flex: 1;
}
.score-value[_ngcontent-%COMP%] {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--secondary-900);
  margin-bottom: var(--spacing-xs);
}
.score-label[_ngcontent-%COMP%] {
  font-size: var(--text-sm);
  color: var(--secondary-600);
  font-weight: 500;
}
.score-indicator[_ngcontent-%COMP%] {
  position: absolute;
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
}
.score-indicator.score-excellent[_ngcontent-%COMP%] {
  background: var(--success-500);
}
.score-indicator.score-good[_ngcontent-%COMP%] {
  background: var(--primary-500);
}
.score-indicator.score-fair[_ngcontent-%COMP%] {
  background: var(--warning-500);
}
.score-indicator.score-poor[_ngcontent-%COMP%] {
  background: var(--error-500);
}
.main-content[_ngcontent-%COMP%] {
  padding: 0 var(--spacing-xl) var(--spacing-xl);
}
.content-grid[_ngcontent-%COMP%] {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-xl);
}
.info-card[_ngcontent-%COMP%] {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--secondary-200);
  overflow: hidden;
  transition: all 0.3s ease;
}
.info-card[_ngcontent-%COMP%]:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}
.card-header[_ngcontent-%COMP%] {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg);
  border-bottom: 1px solid var(--secondary-100);
  background:
    linear-gradient(
      135deg,
      var(--neutral-50),
      var(--neutral-100));
}
.card-icon[_ngcontent-%COMP%] {
  width: 48px;
  height: 48px;
  background:
    linear-gradient(
      135deg,
      var(--primary-500),
      var(--primary-600));
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}
.card-icon[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {
  width: 24px;
  height: 24px;
}
.card-title[_ngcontent-%COMP%] {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--secondary-900);
}
.card-content[_ngcontent-%COMP%] {
  padding: var(--spacing-xl);
}
.detail-grid[_ngcontent-%COMP%] {
  display: grid;
  gap: var(--spacing-lg);
}
.detail-item[_ngcontent-%COMP%] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) 0;
  border-bottom: 1px solid var(--secondary-100);
}
.detail-item[_ngcontent-%COMP%]:last-child {
  border-bottom: none;
}
.detail-label[_ngcontent-%COMP%] {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--secondary-600);
  flex-shrink: 0;
  min-width: 120px;
}
.detail-value[_ngcontent-%COMP%] {
  font-size: var(--text-sm);
  color: var(--secondary-900);
  font-weight: 500;
  text-align: right;
}
.detail-value.muted[_ngcontent-%COMP%] {
  color: var(--secondary-500);
  font-style: italic;
}
.detail-value.version-badge[_ngcontent-%COMP%] {
  background: var(--primary-100);
  color: var(--primary-700);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 600;
}
.detail-link[_ngcontent-%COMP%] {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--primary-600);
  text-decoration: none;
  font-size: var(--text-sm);
  font-weight: 500;
  transition: all 0.2s ease;
}
.detail-link[_ngcontent-%COMP%]:hover {
  color: var(--primary-700);
  text-decoration: underline;
}
.detail-link[_ngcontent-%COMP%]   .link-icon[_ngcontent-%COMP%] {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}
.loading-state[_ngcontent-%COMP%] {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: var(--spacing-xl);
}
.loading-content[_ngcontent-%COMP%] {
  text-align: center;
}
.loading-spinner[_ngcontent-%COMP%] {
  width: 40px;
  height: 40px;
  border: 3px solid var(--secondary-200);
  border-top: 3px solid var(--primary-500);
  border-radius: 50%;
  animation: _ngcontent-%COMP%_spin 1s linear infinite;
  margin: 0 auto var(--spacing-md);
}
@keyframes _ngcontent-%COMP%_spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.loading-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {
  margin: 0;
  color: var(--secondary-600);
  font-size: var(--text-sm);
}
.error-state[_ngcontent-%COMP%] {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: var(--spacing-xl);
}
.error-content[_ngcontent-%COMP%] {
  text-align: center;
  max-width: 400px;
}
.error-icon[_ngcontent-%COMP%] {
  width: 64px;
  height: 64px;
  color: var(--error-500);
  margin: 0 auto var(--spacing-lg);
}
.error-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {
  margin-bottom: var(--spacing-sm);
  color: var(--secondary-900);
}
.error-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {
  margin-bottom: var(--spacing-lg);
  color: var(--secondary-600);
}
.page-header[_ngcontent-%COMP%] {
  margin-bottom: var(--spacing-xl);
}
.header-content[_ngcontent-%COMP%] {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: var(--spacing-lg);
}
.header-info[_ngcontent-%COMP%] {
  flex: 1;
}
.page-title[_ngcontent-%COMP%] {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--secondary-900);
}
.page-subtitle[_ngcontent-%COMP%] {
  margin: 0 0 var(--spacing-md) 0;
  font-size: var(--text-base);
  color: var(--secondary-600);
  line-height: var(--leading-relaxed);
}
.header-meta[_ngcontent-%COMP%] {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}
.app-status[_ngcontent-%COMP%] {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 500;
  text-transform: uppercase;
}
.app-status.status-production[_ngcontent-%COMP%] {
  background: var(--success-100);
  color: var(--success-700);
}
.app-status.status-development[_ngcontent-%COMP%] {
  background: var(--primary-100);
  color: var(--primary-700);
}
.app-status.status-testing[_ngcontent-%COMP%] {
  background: var(--warning-100);
  color: var(--warning-700);
}
.app-status.status-deprecated[_ngcontent-%COMP%] {
  background: var(--error-100);
  color: var(--error-700);
}
.app-criticality[_ngcontent-%COMP%] {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 500;
}
.app-criticality.criticality-critical[_ngcontent-%COMP%] {
  background: var(--error-100);
  color: var(--error-700);
}
.app-criticality.criticality-high[_ngcontent-%COMP%] {
  background: var(--warning-100);
  color: var(--warning-700);
}
.app-criticality.criticality-medium[_ngcontent-%COMP%] {
  background: var(--primary-100);
  color: var(--primary-700);
}
.app-criticality.criticality-low[_ngcontent-%COMP%] {
  background: var(--success-100);
  color: var(--success-700);
}
.app-department[_ngcontent-%COMP%] {
  color: var(--secondary-500);
  font-size: var(--text-sm);
  font-weight: 500;
}
.header-actions[_ngcontent-%COMP%] {
  display: flex;
  gap: var(--spacing-sm);
  flex-shrink: 0;
}
.detail-grid[_ngcontent-%COMP%] {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}
.info-grid[_ngcontent-%COMP%] {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
}
.info-item[_ngcontent-%COMP%] {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}
.info-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--secondary-600);
}
.info-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {
  font-size: var(--text-sm);
  color: var(--secondary-900);
}
.status-badge[_ngcontent-%COMP%], 
.criticality-badge[_ngcontent-%COMP%] {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 500;
  text-transform: uppercase;
  width: fit-content;
}
.status-production[_ngcontent-%COMP%] {
  background: var(--success-100);
  color: var(--success-700);
}
.criticality-critical[_ngcontent-%COMP%] {
  background: var(--error-100);
  color: var(--error-700);
}
.repo-link[_ngcontent-%COMP%], 
.deployment-link[_ngcontent-%COMP%] {
  color: var(--primary-600);
  text-decoration: none;
  font-size: var(--text-sm);
}
.stats-grid[_ngcontent-%COMP%] {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
}
.stat-item[_ngcontent-%COMP%] {
  text-align: center;
}
.stat-value[_ngcontent-%COMP%] {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--primary-600);
  margin-bottom: var(--spacing-xs);
}
.stat-label[_ngcontent-%COMP%] {
  font-size: var(--text-sm);
  color: var(--secondary-600);
}
.scores-grid[_ngcontent-%COMP%] {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
}
.score-item[_ngcontent-%COMP%] {
  text-align: center;
}
.score-circle[_ngcontent-%COMP%] {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
}
.score-circle[_ngcontent-%COMP%]   .score-value[_ngcontent-%COMP%] {
  font-size: var(--text-2xl);
  font-weight: 700;
  padding: var(--spacing-md);
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid;
}
.score-circle[_ngcontent-%COMP%]   .score-value.score-excellent[_ngcontent-%COMP%] {
  color: var(--success-700);
  border-color: var(--success-500);
  background: var(--success-50);
}
.score-circle[_ngcontent-%COMP%]   .score-value.score-good[_ngcontent-%COMP%] {
  color: var(--primary-700);
  border-color: var(--primary-500);
  background: var(--primary-50);
}
.score-circle[_ngcontent-%COMP%]   .score-value.score-fair[_ngcontent-%COMP%] {
  color: var(--warning-700);
  border-color: var(--warning-500);
  background: var(--warning-50);
}
.score-circle[_ngcontent-%COMP%]   .score-value.score-poor[_ngcontent-%COMP%] {
  color: var(--error-700);
  border-color: var(--error-500);
  background: var(--error-50);
}
.score-circle[_ngcontent-%COMP%]   .score-label[_ngcontent-%COMP%] {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--secondary-700);
}
.security-content[_ngcontent-%COMP%] {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
}
.security-score[_ngcontent-%COMP%] {
  text-align: center;
}
.score-value[_ngcontent-%COMP%] {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--success-600);
  margin-bottom: var(--spacing-xs);
}
.score-label[_ngcontent-%COMP%] {
  font-size: var(--text-sm);
  color: var(--secondary-600);
}
.vulnerability-summary[_ngcontent-%COMP%] {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-md);
}
.vuln-item[_ngcontent-%COMP%] {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}
.vuln-count[_ngcontent-%COMP%] {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-xs);
  font-weight: 600;
  color: white;
}
.vuln-count.critical[_ngcontent-%COMP%] {
  background: var(--error-500);
}
.vuln-count.high[_ngcontent-%COMP%] {
  background: var(--warning-500);
}
.vuln-count.medium[_ngcontent-%COMP%] {
  background: var(--primary-500);
}
.vuln-count.low[_ngcontent-%COMP%] {
  background: var(--success-500);
}
.vuln-label[_ngcontent-%COMP%] {
  font-size: var(--text-sm);
  color: var(--secondary-700);
}
.dependencies-list[_ngcontent-%COMP%] {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}
.dependency-item[_ngcontent-%COMP%] {
  padding: var(--spacing-md);
  border: 1px solid var(--secondary-200);
  border-radius: var(--radius-md);
  background: white;
}
.dependency-header[_ngcontent-%COMP%] {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  flex-wrap: wrap;
}
.dependency-name[_ngcontent-%COMP%] {
  margin: 0;
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--secondary-900);
}
.dependency-type[_ngcontent-%COMP%] {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--neutral-100);
  color: var(--secondary-700);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 500;
}
.dependency-criticality[_ngcontent-%COMP%] {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 500;
}
.dependency-criticality.criticality-critical[_ngcontent-%COMP%] {
  background: var(--error-100);
  color: var(--error-700);
}
.dependency-criticality.criticality-high[_ngcontent-%COMP%] {
  background: var(--warning-100);
  color: var(--warning-700);
}
.dependency-criticality.criticality-medium[_ngcontent-%COMP%] {
  background: var(--primary-100);
  color: var(--primary-700);
}
.dependency-criticality.criticality-low[_ngcontent-%COMP%] {
  background: var(--success-100);
  color: var(--success-700);
}
.dependency-details[_ngcontent-%COMP%] {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  flex-wrap: wrap;
}
.dependency-version[_ngcontent-%COMP%] {
  font-size: var(--text-sm);
  color: var(--secondary-600);
  font-weight: 500;
}
.dependency-status[_ngcontent-%COMP%] {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 500;
}
.dependency-status.status-active[_ngcontent-%COMP%] {
  background: var(--success-100);
  color: var(--success-700);
}
.dependency-status.status-deprecated[_ngcontent-%COMP%] {
  background: var(--warning-100);
  color: var(--warning-700);
}
.dependency-status.status-end_of_life[_ngcontent-%COMP%] {
  background: var(--error-100);
  color: var(--error-700);
}
.dependency-internal[_ngcontent-%COMP%] {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--primary-100);
  color: var(--primary-700);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 500;
}
.dependency-description[_ngcontent-%COMP%] {
  margin: 0;
  font-size: var(--text-sm);
  color: var(--secondary-600);
  line-height: var(--leading-relaxed);
}
.techstack-list[_ngcontent-%COMP%] {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: var(--spacing-md);
}
.techstack-item[_ngcontent-%COMP%] {
  padding: var(--spacing-md);
  border: 1px solid var(--secondary-200);
  border-radius: var(--radius-md);
  background: white;
}
.techstack-header[_ngcontent-%COMP%] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
}
.techstack-name[_ngcontent-%COMP%] {
  margin: 0;
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--secondary-900);
}
.techstack-category[_ngcontent-%COMP%] {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--neutral-100);
  color: var(--secondary-700);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 500;
}
.techstack-version[_ngcontent-%COMP%] {
  font-size: var(--text-sm);
  color: var(--secondary-600);
  font-weight: 500;
}
.stakeholders-list[_ngcontent-%COMP%] {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}
.stakeholder-item[_ngcontent-%COMP%] {
  padding: var(--spacing-md);
  border: 1px solid var(--secondary-200);
  border-radius: var(--radius-md);
  background: white;
}
.stakeholder-header[_ngcontent-%COMP%] {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  flex-wrap: wrap;
}
.stakeholder-name[_ngcontent-%COMP%] {
  margin: 0;
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--secondary-900);
}
.stakeholder-role[_ngcontent-%COMP%] {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--primary-100);
  color: var(--primary-700);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 500;
}
.stakeholder-primary[_ngcontent-%COMP%] {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--success-100);
  color: var(--success-700);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 500;
}
.stakeholder-details[_ngcontent-%COMP%] {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  flex-wrap: wrap;
}
.stakeholder-email[_ngcontent-%COMP%] {
  font-size: var(--text-sm);
  color: var(--secondary-600);
}
.stakeholder-department[_ngcontent-%COMP%] {
  font-size: var(--text-sm);
  color: var(--secondary-500);
}
.stakeholder-responsibility[_ngcontent-%COMP%] {
  margin: 0;
  font-size: var(--text-sm);
  color: var(--secondary-600);
  line-height: var(--leading-relaxed);
}
.documents-list[_ngcontent-%COMP%] {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}
.document-item[_ngcontent-%COMP%] {
  padding: var(--spacing-md);
  border: 1px solid var(--secondary-200);
  border-radius: var(--radius-md);
  background: white;
}
.document-header[_ngcontent-%COMP%] {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  flex-wrap: wrap;
}
.document-title[_ngcontent-%COMP%] {
  margin: 0;
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--secondary-900);
}
.document-type[_ngcontent-%COMP%] {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--neutral-100);
  color: var(--secondary-700);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 500;
}
.document-version[_ngcontent-%COMP%] {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--primary-100);
  color: var(--primary-700);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 500;
}
.document-description[_ngcontent-%COMP%] {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--text-sm);
  color: var(--secondary-600);
  line-height: var(--leading-relaxed);
}
.document-meta[_ngcontent-%COMP%] {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}
.document-uploaded[_ngcontent-%COMP%], 
.document-size[_ngcontent-%COMP%] {
  font-size: var(--text-xs);
  color: var(--secondary-500);
}
.vulnerabilities-list[_ngcontent-%COMP%] {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}
.vulnerability-item[_ngcontent-%COMP%] {
  padding: var(--spacing-md);
  border: 1px solid var(--secondary-200);
  border-radius: var(--radius-md);
  background: white;
}
.vulnerability-header[_ngcontent-%COMP%] {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  flex-wrap: wrap;
}
.vulnerability-title[_ngcontent-%COMP%] {
  margin: 0;
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--secondary-900);
}
.vulnerability-severity[_ngcontent-%COMP%] {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 500;
}
.vulnerability-severity.severity-critical[_ngcontent-%COMP%] {
  background: var(--error-100);
  color: var(--error-700);
}
.vulnerability-severity.severity-high[_ngcontent-%COMP%] {
  background: var(--warning-100);
  color: var(--warning-700);
}
.vulnerability-severity.severity-medium[_ngcontent-%COMP%] {
  background: var(--primary-100);
  color: var(--primary-700);
}
.vulnerability-severity.severity-low[_ngcontent-%COMP%] {
  background: var(--success-100);
  color: var(--success-700);
}
.vulnerability-status[_ngcontent-%COMP%] {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 500;
}
.vulnerability-status.status-open[_ngcontent-%COMP%] {
  background: var(--error-100);
  color: var(--error-700);
}
.vulnerability-status.status-in_progress[_ngcontent-%COMP%] {
  background: var(--warning-100);
  color: var(--warning-700);
}
.vulnerability-status.status-resolved[_ngcontent-%COMP%] {
  background: var(--success-100);
  color: var(--success-700);
}
.vulnerability-description[_ngcontent-%COMP%] {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--text-sm);
  color: var(--secondary-600);
  line-height: var(--leading-relaxed);
}
.vulnerability-meta[_ngcontent-%COMP%] {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}
.vulnerability-cve[_ngcontent-%COMP%], 
.vulnerability-score[_ngcontent-%COMP%], 
.vulnerability-discovered[_ngcontent-%COMP%] {
  font-size: var(--text-xs);
  color: var(--secondary-500);
}
@media (max-width: 768px) {
  .header-content[_ngcontent-%COMP%] {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }
  .header-actions[_ngcontent-%COMP%] {
    justify-content: flex-end;
  }
  .detail-grid[_ngcontent-%COMP%] {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  .info-grid[_ngcontent-%COMP%] {
    grid-template-columns: 1fr;
  }
  .stats-grid[_ngcontent-%COMP%] {
    grid-template-columns: repeat(2, 1fr);
  }
  .vulnerability-summary[_ngcontent-%COMP%] {
    grid-template-columns: 1fr;
  }
}
/*# sourceMappingURL=application-detail.component.css.map */`] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ApplicationDetailComponent, [{
    type: Component,
    args: [{ selector: "app-application-detail", standalone: true, imports: [CommonModule, RouterModule, CardComponent, ButtonComponent], template: `
    <div class="application-detail-page">
      <!-- Loading State -->
      <div *ngIf="isLoading" class="loading-state">
        <div class="loading-content">
          <div class="loading-spinner"></div>
          <p>Loading application details...</p>
        </div>
      </div>

      <!-- Error State -->
      <div *ngIf="hasError && !isLoading" class="error-state">
        <div class="error-content">
          <svg class="error-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="8" x2="12" y2="12"></line>
            <line x1="12" y1="16" x2="12.01" y2="16"></line>
          </svg>
          <h3>Failed to load application</h3>
          <p>There was an error loading the application details. Please try again.</p>
          <app-button variant="primary" (click)="loadApplication()">
            Retry
          </app-button>
        </div>
      </div>

      <!-- Application Content -->
      <div *ngIf="!isLoading && !hasError && application" class="application-content">
        <!-- Hero Section -->
        <div class="hero-section">
          <div class="hero-background"></div>
          <div class="hero-content">
            <div class="hero-info">
              <div class="app-icon">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                  <circle cx="9" cy="9" r="2"></circle>
                  <path d="m21 15-3.086-3.086a2 2 0 0 0-1.414-.586H13"></path>
                  <path d="M9 21v-6a2 2 0 0 1 2-2h4"></path>
                </svg>
              </div>
              <div class="app-details">
                <h1 class="app-title">{{ application.name }}</h1>
                <p class="app-description">{{ application.description }}</p>
                <div class="app-badges">
                  <span class="status-badge" [class]="'status-' + application.status.toLowerCase()">
                    {{ application.status }}
                  </span>
                  <span class="criticality-badge" [class]="'criticality-' + application.criticality.toLowerCase()">
                    {{ application.criticality }}
                  </span>
                  <span class="department-badge">{{ application.department }}</span>
                </div>
              </div>
            </div>
            <div class="hero-actions">
              <app-button variant="ghost" routerLink="/applications" leftIcon="M15 19l-7-7 7-7">
                Back to List
              </app-button>
              <app-button variant="primary" [routerLink]="['/applications', application.id, 'edit']" leftIcon="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7">
                Edit Application
              </app-button>
            </div>
          </div>
        </div>

        <!-- Performance Scores -->
        <div class="scores-section">
          <div class="scores-grid">
            <div class="score-card">
              <div class="score-icon health">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
                </svg>
              </div>
              <div class="score-content">
                <div class="score-value">{{ application.healthScore }}</div>
                <div class="score-label">Health Score</div>
              </div>
              <div class="score-indicator" [class]="getScoreClass(application.healthScore)"></div>
            </div>

            <div class="score-card">
              <div class="score-icon security">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                </svg>
              </div>
              <div class="score-content">
                <div class="score-value">{{ application.securityScore }}</div>
                <div class="score-label">Security Score</div>
              </div>
              <div class="score-indicator" [class]="getScoreClass(application.securityScore)"></div>
            </div>

            <div class="score-card">
              <div class="score-icon performance">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <circle cx="12" cy="12" r="10"></circle>
                  <polyline points="12,6 12,12 16,14"></polyline>
                </svg>
              </div>
              <div class="score-content">
                <div class="score-value">{{ application.performanceScore }}</div>
                <div class="score-label">Performance Score</div>
              </div>
              <div class="score-indicator" [class]="getScoreClass(application.performanceScore)"></div>
            </div>
          </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
          <div class="content-grid">
            <!-- Basic Information Card -->
            <div class="info-card">
              <div class="card-header">
                <div class="card-icon">
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                    <polyline points="14,2 14,8 20,8"></polyline>
                    <line x1="16" y1="13" x2="8" y2="13"></line>
                    <line x1="16" y1="17" x2="8" y2="17"></line>
                    <polyline points="10,9 9,9 8,9"></polyline>
                  </svg>
                </div>
                <h3 class="card-title">Application Details</h3>
              </div>
              <div class="card-content">
                <div class="detail-grid">
                  <div class="detail-item">
                    <span class="detail-label">Owner</span>
                    <span class="detail-value">{{ application.owner }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Department</span>
                    <span class="detail-value">{{ application.department }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Version</span>
                    <span class="detail-value version-badge">{{ application.version }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Repository</span>
                    <a [href]="application.repository" target="_blank" class="detail-link" *ngIf="application.repository">
                      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" class="link-icon">
                        <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"></path>
                      </svg>
                      View Repository
                    </a>
                    <span class="detail-value muted" *ngIf="!application.repository">Not specified</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Deployment</span>
                    <a [href]="application.url" target="_blank" class="detail-link" *ngIf="application.url">
                      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" class="link-icon">
                        <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                        <polyline points="15,3 21,3 21,9"></polyline>
                        <line x1="10" y1="14" x2="21" y2="3"></line>
                      </svg>
                      Open Application
                    </a>
                    <span class="detail-value muted" *ngIf="!application.url">Not deployed</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Documentation</span>
                    <a [href]="application.documentation" target="_blank" class="detail-link" *ngIf="application.documentation">
                      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" class="link-icon">
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                        <polyline points="14,2 14,8 20,8"></polyline>
                      </svg>
                      View Documentation
                    </a>
                    <span class="detail-value muted" *ngIf="!application.documentation">Not available</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Created</span>
                    <span class="detail-value">{{ application.createdDate | date:'medium' }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Last Updated</span>
                    <span class="detail-value">{{ application.lastUpdated | date:'medium' }}</span>
                  </div>
                </div>
              </div>
            </div>

          <!-- Quick Stats -->
          <app-card title="Quick Stats" class="stats-card">
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-value">{{ application.dependencies?.length || 0 }}</div>
                <div class="stat-label">Dependencies</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ application.techStack?.length || 0 }}</div>
                <div class="stat-label">Tech Stack Items</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ application.stakeholders?.length || 0 }}</div>
                <div class="stat-label">Stakeholders</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ application.documents?.length || 0 }}</div>
                <div class="stat-label">Documents</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ application.vulnerabilities?.length || 0 }}</div>
                <div class="stat-label">Vulnerabilities</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ application.securityAssessments?.length || 0 }}</div>
                <div class="stat-label">Security Assessments</div>
              </div>
            </div>
          </app-card>

          <!-- Performance Scores -->
          <app-card title="Performance Scores" class="scores-card">
            <div class="scores-grid">
              <div class="score-item">
                <div class="score-circle">
                  <div class="score-value" [class]="getScoreClass(application.healthScore)">{{ application.healthScore }}%</div>
                  <div class="score-label">Health</div>
                </div>
              </div>
              <div class="score-item">
                <div class="score-circle">
                  <div class="score-value" [class]="getScoreClass(application.securityScore)">{{ application.securityScore }}%</div>
                  <div class="score-label">Security</div>
                </div>
              </div>
              <div class="score-item">
                <div class="score-circle">
                  <div class="score-value" [class]="getScoreClass(application.performanceScore)">{{ application.performanceScore }}%</div>
                  <div class="score-label">Performance</div>
                </div>
              </div>
            </div>
          </app-card>

          <!-- Security Overview -->
          <app-card title="Security Overview" class="security-card">
            <div class="security-content">
              <div class="vulnerability-summary">
                <div class="vuln-item">
                  <span class="vuln-count critical">{{ getVulnerabilityCount('critical') }}</span>
                  <span class="vuln-label">Critical</span>
                </div>
                <div class="vuln-item">
                  <span class="vuln-count high">{{ getVulnerabilityCount('high') }}</span>
                  <span class="vuln-label">High</span>
                </div>
                <div class="vuln-item">
                  <span class="vuln-count medium">{{ getVulnerabilityCount('medium') }}</span>
                  <span class="vuln-label">Medium</span>
                </div>
                <div class="vuln-item">
                  <span class="vuln-count low">{{ getVulnerabilityCount('low') }}</span>
                  <span class="vuln-label">Low</span>
                </div>
              </div>
            </div>
          </app-card>

          <!-- Dependencies -->
          <app-card title="Dependencies" class="dependencies-card" *ngIf="application.dependencies && application.dependencies.length > 0">
            <div class="dependencies-list">
              <div class="dependency-item" *ngFor="let dep of application.dependencies">
                <div class="dependency-header">
                  <h4 class="dependency-name">{{ dep.name }}</h4>
                  <span class="dependency-type">{{ dep.type }}</span>
                  <span class="dependency-criticality" [class]="'criticality-' + dep.criticality">{{ dep.criticality }}</span>
                </div>
                <div class="dependency-details">
                  <span class="dependency-version">v{{ dep.version }}</span>
                  <span class="dependency-status" [class]="'status-' + dep.status">{{ dep.status }}</span>
                  <span class="dependency-internal" *ngIf="dep.isInternal">Internal</span>
                </div>
                <p class="dependency-description" *ngIf="dep.description">{{ dep.description }}</p>
              </div>
            </div>
          </app-card>

          <!-- Tech Stack -->
          <app-card title="Technology Stack" class="techstack-card" *ngIf="application.techStack && application.techStack.length > 0">
            <div class="techstack-list">
              <div class="techstack-item" *ngFor="let tech of application.techStack">
                <div class="techstack-header">
                  <h4 class="techstack-name">{{ tech.name }}</h4>
                  <span class="techstack-category">{{ tech.category }}</span>
                </div>
                <div class="techstack-details">
                  <span class="techstack-version">v{{ tech.version }}</span>
                </div>
              </div>
            </div>
          </app-card>

          <!-- Stakeholders -->
          <app-card title="Stakeholders" class="stakeholders-card" *ngIf="application.stakeholders && application.stakeholders.length > 0">
            <div class="stakeholders-list">
              <div class="stakeholder-item" *ngFor="let stakeholder of application.stakeholders">
                <div class="stakeholder-header">
                  <h4 class="stakeholder-name">{{ stakeholder.name }}</h4>
                  <span class="stakeholder-role">{{ stakeholder.role }}</span>
                  <span class="stakeholder-primary" *ngIf="stakeholder.isPrimary">Primary</span>
                </div>
                <div class="stakeholder-details">
                  <span class="stakeholder-email">{{ stakeholder.email }}</span>
                  <span class="stakeholder-department">{{ stakeholder.department }}</span>
                </div>
                <p class="stakeholder-responsibility" *ngIf="stakeholder.responsibility">{{ stakeholder.responsibility }}</p>
              </div>
            </div>
          </app-card>

          <!-- Documents -->
          <app-card title="Documentation" class="documents-card" *ngIf="application.documents && application.documents.length > 0">
            <div class="documents-list">
              <div class="document-item" *ngFor="let doc of application.documents">
                <div class="document-header">
                  <h4 class="document-title">{{ doc.title }}</h4>
                  <span class="document-type">{{ doc.type }}</span>
                  <span class="document-version">v{{ doc.version }}</span>
                </div>
                <p class="document-description" *ngIf="doc.description">{{ doc.description }}</p>
                <div class="document-meta">
                  <span class="document-uploaded" *ngIf="doc.uploadedAt">Uploaded {{ doc.uploadedAt | date:'short' }}</span>
                  <span class="document-size" *ngIf="doc.fileSize">{{ doc.fileSize | number }} bytes</span>
                </div>
              </div>
            </div>
          </app-card>

          <!-- Vulnerabilities -->
          <app-card title="Vulnerabilities" class="vulnerabilities-card" *ngIf="application.vulnerabilities && application.vulnerabilities.length > 0">
            <div class="vulnerabilities-list">
              <div class="vulnerability-item" *ngFor="let vuln of application.vulnerabilities">
                <div class="vulnerability-header">
                  <h4 class="vulnerability-title">{{ vuln.title }}</h4>
                  <span class="vulnerability-severity" [class]="'severity-' + vuln.severity">{{ vuln.severity }}</span>
                  <span class="vulnerability-status" [class]="'status-' + vuln.status">{{ vuln.status }}</span>
                </div>
                <p class="vulnerability-description">{{ vuln.description }}</p>
                <div class="vulnerability-meta">
                  <span class="vulnerability-cve" *ngIf="vuln.cveId">{{ vuln.cveId }}</span>
                  <span class="vulnerability-score" *ngIf="vuln.cvssScore">CVSS: {{ vuln.cvssScore }}</span>
                  <span class="vulnerability-discovered">Discovered {{ vuln.discoveredAt | date:'short' }}</span>
                </div>
              </div>
            </div>
          </app-card>
        </div>
      </div>
    </div>
  `, styles: [`/* angular:styles/component:scss;fb2b7598fa79b3c36fe3126d9df9e9c362c46dc17b88656ed2e89b36f155f95c;C:/Users/<USER>/Documents/VibeCoding/AppCatalog/app-catalog/frontend/src/app/modules/applications/application-detail/application-detail.component.ts */
.application-detail-page {
  min-height: 100%;
  background: var(--neutral-50);
}
.hero-section {
  position: relative;
  background:
    linear-gradient(
      135deg,
      var(--primary-600) 0%,
      var(--primary-800) 100%);
  color: white;
  overflow: hidden;
  margin-bottom: var(--spacing-xl);
}
.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}
.hero-content {
  position: relative;
  padding: var(--spacing-3xl) var(--spacing-xl);
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-xl);
}
.hero-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  flex: 1;
}
.app-icon {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}
.app-icon svg {
  width: 40px;
  height: 40px;
  color: white;
}
.app-details {
  flex: 1;
}
.app-title {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--text-4xl);
  font-weight: 700;
  color: white;
  line-height: var(--leading-tight);
}
.app-description {
  margin: 0 0 var(--spacing-md) 0;
  font-size: var(--text-lg);
  color: rgba(255, 255, 255, 0.9);
  line-height: var(--leading-relaxed);
  max-width: 600px;
}
.app-badges {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}
.status-badge,
.criticality-badge,
.department-badge {
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
  font-weight: 500;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}
.hero-actions {
  display: flex;
  gap: var(--spacing-sm);
  flex-shrink: 0;
}
.scores-section {
  margin-bottom: var(--spacing-xl);
}
.scores-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-lg);
  padding: 0 var(--spacing-xl);
}
.score-card {
  background: white;
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--secondary-200);
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}
.score-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}
.score-icon {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}
.score-icon.health {
  background:
    linear-gradient(
      135deg,
      var(--success-500),
      var(--success-600));
  color: white;
}
.score-icon.security {
  background:
    linear-gradient(
      135deg,
      var(--primary-500),
      var(--primary-600));
  color: white;
}
.score-icon.performance {
  background:
    linear-gradient(
      135deg,
      var(--warning-500),
      var(--warning-600));
  color: white;
}
.score-icon svg {
  width: 28px;
  height: 28px;
}
.score-content {
  flex: 1;
}
.score-value {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--secondary-900);
  margin-bottom: var(--spacing-xs);
}
.score-label {
  font-size: var(--text-sm);
  color: var(--secondary-600);
  font-weight: 500;
}
.score-indicator {
  position: absolute;
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
}
.score-indicator.score-excellent {
  background: var(--success-500);
}
.score-indicator.score-good {
  background: var(--primary-500);
}
.score-indicator.score-fair {
  background: var(--warning-500);
}
.score-indicator.score-poor {
  background: var(--error-500);
}
.main-content {
  padding: 0 var(--spacing-xl) var(--spacing-xl);
}
.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-xl);
}
.info-card {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--secondary-200);
  overflow: hidden;
  transition: all 0.3s ease;
}
.info-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}
.card-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg);
  border-bottom: 1px solid var(--secondary-100);
  background:
    linear-gradient(
      135deg,
      var(--neutral-50),
      var(--neutral-100));
}
.card-icon {
  width: 48px;
  height: 48px;
  background:
    linear-gradient(
      135deg,
      var(--primary-500),
      var(--primary-600));
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}
.card-icon svg {
  width: 24px;
  height: 24px;
}
.card-title {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: 600;
  color: var(--secondary-900);
}
.card-content {
  padding: var(--spacing-xl);
}
.detail-grid {
  display: grid;
  gap: var(--spacing-lg);
}
.detail-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-md) 0;
  border-bottom: 1px solid var(--secondary-100);
}
.detail-item:last-child {
  border-bottom: none;
}
.detail-label {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--secondary-600);
  flex-shrink: 0;
  min-width: 120px;
}
.detail-value {
  font-size: var(--text-sm);
  color: var(--secondary-900);
  font-weight: 500;
  text-align: right;
}
.detail-value.muted {
  color: var(--secondary-500);
  font-style: italic;
}
.detail-value.version-badge {
  background: var(--primary-100);
  color: var(--primary-700);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 600;
}
.detail-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--primary-600);
  text-decoration: none;
  font-size: var(--text-sm);
  font-weight: 500;
  transition: all 0.2s ease;
}
.detail-link:hover {
  color: var(--primary-700);
  text-decoration: underline;
}
.detail-link .link-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: var(--spacing-xl);
}
.loading-content {
  text-align: center;
}
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--secondary-200);
  border-top: 3px solid var(--primary-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto var(--spacing-md);
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.loading-content p {
  margin: 0;
  color: var(--secondary-600);
  font-size: var(--text-sm);
}
.error-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: var(--spacing-xl);
}
.error-content {
  text-align: center;
  max-width: 400px;
}
.error-icon {
  width: 64px;
  height: 64px;
  color: var(--error-500);
  margin: 0 auto var(--spacing-lg);
}
.error-content h3 {
  margin-bottom: var(--spacing-sm);
  color: var(--secondary-900);
}
.error-content p {
  margin-bottom: var(--spacing-lg);
  color: var(--secondary-600);
}
.page-header {
  margin-bottom: var(--spacing-xl);
}
.header-content {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: var(--spacing-lg);
}
.header-info {
  flex: 1;
}
.page-title {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--secondary-900);
}
.page-subtitle {
  margin: 0 0 var(--spacing-md) 0;
  font-size: var(--text-base);
  color: var(--secondary-600);
  line-height: var(--leading-relaxed);
}
.header-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}
.app-status {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 500;
  text-transform: uppercase;
}
.app-status.status-production {
  background: var(--success-100);
  color: var(--success-700);
}
.app-status.status-development {
  background: var(--primary-100);
  color: var(--primary-700);
}
.app-status.status-testing {
  background: var(--warning-100);
  color: var(--warning-700);
}
.app-status.status-deprecated {
  background: var(--error-100);
  color: var(--error-700);
}
.app-criticality {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 500;
}
.app-criticality.criticality-critical {
  background: var(--error-100);
  color: var(--error-700);
}
.app-criticality.criticality-high {
  background: var(--warning-100);
  color: var(--warning-700);
}
.app-criticality.criticality-medium {
  background: var(--primary-100);
  color: var(--primary-700);
}
.app-criticality.criticality-low {
  background: var(--success-100);
  color: var(--success-700);
}
.app-department {
  color: var(--secondary-500);
  font-size: var(--text-sm);
  font-weight: 500;
}
.header-actions {
  display: flex;
  gap: var(--spacing-sm);
  flex-shrink: 0;
}
.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
}
.info-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}
.info-item label {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--secondary-600);
}
.info-item span {
  font-size: var(--text-sm);
  color: var(--secondary-900);
}
.status-badge,
.criticality-badge {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 500;
  text-transform: uppercase;
  width: fit-content;
}
.status-production {
  background: var(--success-100);
  color: var(--success-700);
}
.criticality-critical {
  background: var(--error-100);
  color: var(--error-700);
}
.repo-link,
.deployment-link {
  color: var(--primary-600);
  text-decoration: none;
  font-size: var(--text-sm);
}
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
}
.stat-item {
  text-align: center;
}
.stat-value {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--primary-600);
  margin-bottom: var(--spacing-xs);
}
.stat-label {
  font-size: var(--text-sm);
  color: var(--secondary-600);
}
.scores-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
}
.score-item {
  text-align: center;
}
.score-circle {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
}
.score-circle .score-value {
  font-size: var(--text-2xl);
  font-weight: 700;
  padding: var(--spacing-md);
  border-radius: 50%;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid;
}
.score-circle .score-value.score-excellent {
  color: var(--success-700);
  border-color: var(--success-500);
  background: var(--success-50);
}
.score-circle .score-value.score-good {
  color: var(--primary-700);
  border-color: var(--primary-500);
  background: var(--primary-50);
}
.score-circle .score-value.score-fair {
  color: var(--warning-700);
  border-color: var(--warning-500);
  background: var(--warning-50);
}
.score-circle .score-value.score-poor {
  color: var(--error-700);
  border-color: var(--error-500);
  background: var(--error-50);
}
.score-circle .score-label {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--secondary-700);
}
.security-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
}
.security-score {
  text-align: center;
}
.score-value {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--success-600);
  margin-bottom: var(--spacing-xs);
}
.score-label {
  font-size: var(--text-sm);
  color: var(--secondary-600);
}
.vulnerability-summary {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-md);
}
.vuln-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}
.vuln-count {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-xs);
  font-weight: 600;
  color: white;
}
.vuln-count.critical {
  background: var(--error-500);
}
.vuln-count.high {
  background: var(--warning-500);
}
.vuln-count.medium {
  background: var(--primary-500);
}
.vuln-count.low {
  background: var(--success-500);
}
.vuln-label {
  font-size: var(--text-sm);
  color: var(--secondary-700);
}
.dependencies-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}
.dependency-item {
  padding: var(--spacing-md);
  border: 1px solid var(--secondary-200);
  border-radius: var(--radius-md);
  background: white;
}
.dependency-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  flex-wrap: wrap;
}
.dependency-name {
  margin: 0;
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--secondary-900);
}
.dependency-type {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--neutral-100);
  color: var(--secondary-700);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 500;
}
.dependency-criticality {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 500;
}
.dependency-criticality.criticality-critical {
  background: var(--error-100);
  color: var(--error-700);
}
.dependency-criticality.criticality-high {
  background: var(--warning-100);
  color: var(--warning-700);
}
.dependency-criticality.criticality-medium {
  background: var(--primary-100);
  color: var(--primary-700);
}
.dependency-criticality.criticality-low {
  background: var(--success-100);
  color: var(--success-700);
}
.dependency-details {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  flex-wrap: wrap;
}
.dependency-version {
  font-size: var(--text-sm);
  color: var(--secondary-600);
  font-weight: 500;
}
.dependency-status {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 500;
}
.dependency-status.status-active {
  background: var(--success-100);
  color: var(--success-700);
}
.dependency-status.status-deprecated {
  background: var(--warning-100);
  color: var(--warning-700);
}
.dependency-status.status-end_of_life {
  background: var(--error-100);
  color: var(--error-700);
}
.dependency-internal {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--primary-100);
  color: var(--primary-700);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 500;
}
.dependency-description {
  margin: 0;
  font-size: var(--text-sm);
  color: var(--secondary-600);
  line-height: var(--leading-relaxed);
}
.techstack-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: var(--spacing-md);
}
.techstack-item {
  padding: var(--spacing-md);
  border: 1px solid var(--secondary-200);
  border-radius: var(--radius-md);
  background: white;
}
.techstack-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
}
.techstack-name {
  margin: 0;
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--secondary-900);
}
.techstack-category {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--neutral-100);
  color: var(--secondary-700);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 500;
}
.techstack-version {
  font-size: var(--text-sm);
  color: var(--secondary-600);
  font-weight: 500;
}
.stakeholders-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}
.stakeholder-item {
  padding: var(--spacing-md);
  border: 1px solid var(--secondary-200);
  border-radius: var(--radius-md);
  background: white;
}
.stakeholder-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  flex-wrap: wrap;
}
.stakeholder-name {
  margin: 0;
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--secondary-900);
}
.stakeholder-role {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--primary-100);
  color: var(--primary-700);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 500;
}
.stakeholder-primary {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--success-100);
  color: var(--success-700);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 500;
}
.stakeholder-details {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  flex-wrap: wrap;
}
.stakeholder-email {
  font-size: var(--text-sm);
  color: var(--secondary-600);
}
.stakeholder-department {
  font-size: var(--text-sm);
  color: var(--secondary-500);
}
.stakeholder-responsibility {
  margin: 0;
  font-size: var(--text-sm);
  color: var(--secondary-600);
  line-height: var(--leading-relaxed);
}
.documents-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}
.document-item {
  padding: var(--spacing-md);
  border: 1px solid var(--secondary-200);
  border-radius: var(--radius-md);
  background: white;
}
.document-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  flex-wrap: wrap;
}
.document-title {
  margin: 0;
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--secondary-900);
}
.document-type {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--neutral-100);
  color: var(--secondary-700);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 500;
}
.document-version {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--primary-100);
  color: var(--primary-700);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 500;
}
.document-description {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--text-sm);
  color: var(--secondary-600);
  line-height: var(--leading-relaxed);
}
.document-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}
.document-uploaded,
.document-size {
  font-size: var(--text-xs);
  color: var(--secondary-500);
}
.vulnerabilities-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}
.vulnerability-item {
  padding: var(--spacing-md);
  border: 1px solid var(--secondary-200);
  border-radius: var(--radius-md);
  background: white;
}
.vulnerability-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
  flex-wrap: wrap;
}
.vulnerability-title {
  margin: 0;
  font-size: var(--text-sm);
  font-weight: 600;
  color: var(--secondary-900);
}
.vulnerability-severity {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 500;
}
.vulnerability-severity.severity-critical {
  background: var(--error-100);
  color: var(--error-700);
}
.vulnerability-severity.severity-high {
  background: var(--warning-100);
  color: var(--warning-700);
}
.vulnerability-severity.severity-medium {
  background: var(--primary-100);
  color: var(--primary-700);
}
.vulnerability-severity.severity-low {
  background: var(--success-100);
  color: var(--success-700);
}
.vulnerability-status {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 500;
}
.vulnerability-status.status-open {
  background: var(--error-100);
  color: var(--error-700);
}
.vulnerability-status.status-in_progress {
  background: var(--warning-100);
  color: var(--warning-700);
}
.vulnerability-status.status-resolved {
  background: var(--success-100);
  color: var(--success-700);
}
.vulnerability-description {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--text-sm);
  color: var(--secondary-600);
  line-height: var(--leading-relaxed);
}
.vulnerability-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}
.vulnerability-cve,
.vulnerability-score,
.vulnerability-discovered {
  font-size: var(--text-xs);
  color: var(--secondary-500);
}
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }
  .header-actions {
    justify-content: flex-end;
  }
  .detail-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  .info-grid {
    grid-template-columns: 1fr;
  }
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  .vulnerability-summary {
    grid-template-columns: 1fr;
  }
}
/*# sourceMappingURL=application-detail.component.css.map */
`] }]
  }], () => [{ type: ActivatedRoute }, { type: ApplicationsService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(ApplicationDetailComponent, { className: "ApplicationDetailComponent", filePath: "src/app/modules/applications/application-detail/application-detail.component.ts", lineNumber: 1502 });
})();
export {
  ApplicationDetailComponent
};
//# sourceMappingURL=chunk-F6OQVEPM.js.map
