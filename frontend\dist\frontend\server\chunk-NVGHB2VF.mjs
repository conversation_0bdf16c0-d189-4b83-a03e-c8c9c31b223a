import './polyfills.server.mjs';
import {
  CommonModule,
  Component,
  EventEmitter,
  Input,
  NgIf,
  Output,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵattribute,
  ɵɵclassMap,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵnamespaceSVG,
  ɵɵnextContext,
  ɵɵprojection,
  ɵɵprojectionDef,
  ɵɵproperty,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1
} from "./chunk-IPMSWJNG.mjs";

// src/app/shared/components/button/button.component.ts
var _c0 = ["*"];
function ButtonComponent__svg_svg_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(0, "svg", 7)(1, "circle", 8);
    \u0275\u0275element(2, "animate", 9)(3, "animate", 10);
    \u0275\u0275elementEnd()();
  }
}
function ButtonComponent__svg_svg_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(0, "svg", 11);
    \u0275\u0275element(1, "path", 12);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275attribute("d", ctx_r0.leftIcon);
  }
}
function ButtonComponent_span_5_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 13);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.loadingText, " ");
  }
}
function ButtonComponent__svg_svg_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(0, "svg", 14);
    \u0275\u0275element(1, "path", 12);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275attribute("d", ctx_r0.rightIcon);
  }
}
function ButtonComponent_span_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 15);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.badge);
  }
}
var ButtonComponent = class _ButtonComponent {
  variant = "primary";
  size = "md";
  type = "button";
  disabled = false;
  loading = false;
  loadingText = "";
  leftIcon = "";
  rightIcon = "";
  badge = "";
  fullWidth = false;
  rounded = false;
  clicked = new EventEmitter();
  get buttonClasses() {
    const classes = [
      "btn",
      `btn-${this.variant}`,
      `btn-${this.size}`
    ];
    if (this.fullWidth)
      classes.push("btn-full-width");
    if (this.rounded)
      classes.push("btn-rounded");
    if (this.loading)
      classes.push("btn-loading");
    if (this.disabled)
      classes.push("btn-disabled");
    return classes.join(" ");
  }
  onClick(event) {
    if (!this.disabled && !this.loading) {
      this.clicked.emit(event);
    }
  }
  static \u0275fac = function ButtonComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ButtonComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _ButtonComponent, selectors: [["app-button"]], inputs: { variant: "variant", size: "size", type: "type", disabled: "disabled", loading: "loading", loadingText: "loadingText", leftIcon: "leftIcon", rightIcon: "rightIcon", badge: "badge", fullWidth: "fullWidth", rounded: "rounded" }, outputs: { clicked: "clicked" }, ngContentSelectors: _c0, decls: 8, vars: 11, consts: [[3, "click", "type", "disabled"], ["class", "button-spinner", "viewBox", "0 0 24 24", "fill", "none", 4, "ngIf"], ["class", "button-icon button-icon-left", "viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", 4, "ngIf"], [1, "button-content"], ["class", "button-loading-text", 4, "ngIf"], ["class", "button-icon button-icon-right", "viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", 4, "ngIf"], ["class", "button-badge", 4, "ngIf"], ["viewBox", "0 0 24 24", "fill", "none", 1, "button-spinner"], ["cx", "12", "cy", "12", "r", "10", "stroke", "currentColor", "stroke-width", "2", "stroke-linecap", "round", "stroke-dasharray", "31.416", "stroke-dashoffset", "31.416"], ["attributeName", "stroke-dasharray", "dur", "2s", "values", "0 31.416;15.708 15.708;0 31.416;0 31.416", "repeatCount", "indefinite"], ["attributeName", "stroke-dashoffset", "dur", "2s", "values", "0;-15.708;-31.416;-31.416", "repeatCount", "indefinite"], ["viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", 1, "button-icon", "button-icon-left"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2"], [1, "button-loading-text"], ["viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", 1, "button-icon", "button-icon-right"], [1, "button-badge"]], template: function ButtonComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275projectionDef();
      \u0275\u0275elementStart(0, "button", 0);
      \u0275\u0275listener("click", function ButtonComponent_Template_button_click_0_listener($event) {
        return ctx.onClick($event);
      });
      \u0275\u0275template(1, ButtonComponent__svg_svg_1_Template, 4, 0, "svg", 1)(2, ButtonComponent__svg_svg_2_Template, 2, 1, "svg", 2);
      \u0275\u0275elementStart(3, "span", 3);
      \u0275\u0275projection(4);
      \u0275\u0275elementEnd();
      \u0275\u0275template(5, ButtonComponent_span_5_Template, 2, 1, "span", 4)(6, ButtonComponent__svg_svg_6_Template, 2, 1, "svg", 5)(7, ButtonComponent_span_7_Template, 2, 1, "span", 6);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275classMap(ctx.buttonClasses);
      \u0275\u0275property("type", ctx.type)("disabled", ctx.disabled || ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.leftIcon && !ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275classProp("sr-only", ctx.loading && ctx.loadingText);
      \u0275\u0275advance(2);
      \u0275\u0275property("ngIf", ctx.loading && ctx.loadingText);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.rightIcon && !ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.badge);
    }
  }, dependencies: [CommonModule, NgIf], styles: ["\n\n.btn[_ngcontent-%COMP%] {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  gap: var(--spacing-sm);\n  border: 1px solid transparent;\n  border-radius: var(--radius-md);\n  font-family: inherit;\n  font-weight: 500;\n  text-decoration: none;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  position: relative;\n  white-space: nowrap;\n  -webkit-user-select: none;\n  user-select: none;\n  outline: none;\n}\n.btn[_ngcontent-%COMP%]:focus-visible {\n  outline: 2px solid var(--primary-500);\n  outline-offset: 2px;\n}\n.btn[_ngcontent-%COMP%]:disabled, \n.btn.btn-disabled[_ngcontent-%COMP%] {\n  opacity: 0.6;\n  cursor: not-allowed;\n  pointer-events: none;\n}\n.btn.btn-loading[_ngcontent-%COMP%] {\n  cursor: wait;\n}\n.btn-xs[_ngcontent-%COMP%] {\n  padding: var(--spacing-xs) var(--spacing-sm);\n  font-size: var(--text-xs);\n  line-height: 1.25;\n  min-height: 24px;\n}\n.btn-sm[_ngcontent-%COMP%] {\n  padding: var(--spacing-sm) var(--spacing-md);\n  font-size: var(--text-sm);\n  line-height: 1.25;\n  min-height: 32px;\n}\n.btn-md[_ngcontent-%COMP%] {\n  padding: var(--spacing-sm) var(--spacing-lg);\n  font-size: var(--text-sm);\n  line-height: 1.5;\n  min-height: 40px;\n}\n.btn-lg[_ngcontent-%COMP%] {\n  padding: var(--spacing-md) var(--spacing-xl);\n  font-size: var(--text-base);\n  line-height: 1.5;\n  min-height: 48px;\n}\n.btn-xl[_ngcontent-%COMP%] {\n  padding: var(--spacing-lg) var(--spacing-2xl);\n  font-size: var(--text-lg);\n  line-height: 1.5;\n  min-height: 56px;\n}\n.btn-primary[_ngcontent-%COMP%] {\n  background-color: var(--primary-500);\n  border-color: var(--primary-500);\n  color: white;\n}\n.btn-primary[_ngcontent-%COMP%]:hover:not(:disabled) {\n  background-color: var(--primary-600);\n  border-color: var(--primary-600);\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-md);\n}\n.btn-primary[_ngcontent-%COMP%]:active:not(:disabled) {\n  background-color: var(--primary-700);\n  border-color: var(--primary-700);\n  transform: translateY(0);\n}\n.btn-secondary[_ngcontent-%COMP%] {\n  background-color: var(--secondary-100);\n  border-color: var(--secondary-200);\n  color: var(--secondary-800);\n}\n.btn-secondary[_ngcontent-%COMP%]:hover:not(:disabled) {\n  background-color: var(--secondary-200);\n  border-color: var(--secondary-300);\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-md);\n}\n.btn-secondary[_ngcontent-%COMP%]:active:not(:disabled) {\n  background-color: var(--secondary-300);\n  border-color: var(--secondary-400);\n  transform: translateY(0);\n}\n.btn-success[_ngcontent-%COMP%] {\n  background-color: var(--success-500);\n  border-color: var(--success-500);\n  color: white;\n}\n.btn-success[_ngcontent-%COMP%]:hover:not(:disabled) {\n  background-color: var(--success-600);\n  border-color: var(--success-600);\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-md);\n}\n.btn-success[_ngcontent-%COMP%]:active:not(:disabled) {\n  background-color: var(--success-700);\n  border-color: var(--success-700);\n  transform: translateY(0);\n}\n.btn-warning[_ngcontent-%COMP%] {\n  background-color: var(--warning-500);\n  border-color: var(--warning-500);\n  color: white;\n}\n.btn-warning[_ngcontent-%COMP%]:hover:not(:disabled) {\n  background-color: var(--warning-600);\n  border-color: var(--warning-600);\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-md);\n}\n.btn-warning[_ngcontent-%COMP%]:active:not(:disabled) {\n  background-color: var(--warning-700);\n  border-color: var(--warning-700);\n  transform: translateY(0);\n}\n.btn-error[_ngcontent-%COMP%] {\n  background-color: var(--error-500);\n  border-color: var(--error-500);\n  color: white;\n}\n.btn-error[_ngcontent-%COMP%]:hover:not(:disabled) {\n  background-color: var(--error-600);\n  border-color: var(--error-600);\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-md);\n}\n.btn-error[_ngcontent-%COMP%]:active:not(:disabled) {\n  background-color: var(--error-700);\n  border-color: var(--error-700);\n  transform: translateY(0);\n}\n.btn-ghost[_ngcontent-%COMP%] {\n  background-color: transparent;\n  border-color: transparent;\n  color: var(--secondary-700);\n}\n.btn-ghost[_ngcontent-%COMP%]:hover:not(:disabled) {\n  background-color: var(--secondary-100);\n  color: var(--secondary-900);\n}\n.btn-ghost[_ngcontent-%COMP%]:active:not(:disabled) {\n  background-color: var(--secondary-200);\n}\n.btn-outline[_ngcontent-%COMP%] {\n  background-color: transparent;\n  border-color: var(--secondary-300);\n  color: var(--secondary-700);\n}\n.btn-outline[_ngcontent-%COMP%]:hover:not(:disabled) {\n  background-color: var(--secondary-50);\n  border-color: var(--secondary-400);\n  color: var(--secondary-900);\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-sm);\n}\n.btn-outline[_ngcontent-%COMP%]:active:not(:disabled) {\n  background-color: var(--secondary-100);\n  border-color: var(--secondary-500);\n  transform: translateY(0);\n}\n.btn-full-width[_ngcontent-%COMP%] {\n  width: 100%;\n}\n.btn-rounded[_ngcontent-%COMP%] {\n  border-radius: var(--radius-2xl);\n}\n.button-icon[_ngcontent-%COMP%] {\n  flex-shrink: 0;\n}\n.btn-xs[_ngcontent-%COMP%]   .button-icon[_ngcontent-%COMP%] {\n  width: 12px;\n  height: 12px;\n}\n.btn-sm[_ngcontent-%COMP%]   .button-icon[_ngcontent-%COMP%] {\n  width: 14px;\n  height: 14px;\n}\n.btn-md[_ngcontent-%COMP%]   .button-icon[_ngcontent-%COMP%] {\n  width: 16px;\n  height: 16px;\n}\n.btn-lg[_ngcontent-%COMP%]   .button-icon[_ngcontent-%COMP%] {\n  width: 18px;\n  height: 18px;\n}\n.btn-xl[_ngcontent-%COMP%]   .button-icon[_ngcontent-%COMP%] {\n  width: 20px;\n  height: 20px;\n}\n.button-spinner[_ngcontent-%COMP%] {\n  flex-shrink: 0;\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\n}\n.btn-xs[_ngcontent-%COMP%]   .button-spinner[_ngcontent-%COMP%] {\n  width: 12px;\n  height: 12px;\n}\n.btn-sm[_ngcontent-%COMP%]   .button-spinner[_ngcontent-%COMP%] {\n  width: 14px;\n  height: 14px;\n}\n.btn-md[_ngcontent-%COMP%]   .button-spinner[_ngcontent-%COMP%] {\n  width: 16px;\n  height: 16px;\n}\n.btn-lg[_ngcontent-%COMP%]   .button-spinner[_ngcontent-%COMP%] {\n  width: 18px;\n  height: 18px;\n}\n.btn-xl[_ngcontent-%COMP%]   .button-spinner[_ngcontent-%COMP%] {\n  width: 20px;\n  height: 20px;\n}\n@keyframes _ngcontent-%COMP%_spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n.button-content[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: inherit;\n}\n.button-loading-text[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n}\n.button-badge[_ngcontent-%COMP%] {\n  position: absolute;\n  top: -6px;\n  right: -6px;\n  min-width: 18px;\n  height: 18px;\n  padding: 0 4px;\n  background: var(--error-500);\n  color: white;\n  font-size: 10px;\n  font-weight: 600;\n  border-radius: 9px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  line-height: 1;\n  border: 2px solid white;\n}\n.sr-only[_ngcontent-%COMP%] {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border: 0;\n}\n.btn[_ngcontent-%COMP%]:focus-visible {\n  outline: 2px solid var(--primary-500);\n  outline-offset: 2px;\n}\n.btn-primary[_ngcontent-%COMP%]:focus-visible {\n  outline-color: var(--primary-300);\n}\n.btn-success[_ngcontent-%COMP%]:focus-visible {\n  outline-color: var(--success-300);\n}\n.btn-warning[_ngcontent-%COMP%]:focus-visible {\n  outline-color: var(--warning-300);\n}\n.btn-error[_ngcontent-%COMP%]:focus-visible {\n  outline-color: var(--error-300);\n}\n@media (prefers-contrast: high) {\n  .btn[_ngcontent-%COMP%] {\n    border-width: 2px;\n  }\n  .btn-ghost[_ngcontent-%COMP%] {\n    border-color: currentColor;\n  }\n}\n@media (prefers-reduced-motion: reduce) {\n  .btn[_ngcontent-%COMP%] {\n    transition: none;\n  }\n  .button-spinner[_ngcontent-%COMP%] {\n    animation: none;\n  }\n  .btn[_ngcontent-%COMP%]:hover:not(:disabled) {\n    transform: none;\n  }\n}\n/*# sourceMappingURL=button.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ButtonComponent, [{
    type: Component,
    args: [{ selector: "app-button", standalone: true, imports: [CommonModule], template: `
    <button
      [type]="type"
      [disabled]="disabled || loading"
      [class]="buttonClasses"
      (click)="onClick($event)"
    >
      <!-- Loading Spinner -->
      <svg 
        *ngIf="loading" 
        class="button-spinner"
        viewBox="0 0 24 24" 
        fill="none"
      >
        <circle 
          cx="12" 
          cy="12" 
          r="10" 
          stroke="currentColor" 
          stroke-width="2" 
          stroke-linecap="round" 
          stroke-dasharray="31.416" 
          stroke-dashoffset="31.416"
        >
          <animate 
            attributeName="stroke-dasharray" 
            dur="2s" 
            values="0 31.416;15.708 15.708;0 31.416;0 31.416" 
            repeatCount="indefinite"
          />
          <animate 
            attributeName="stroke-dashoffset" 
            dur="2s" 
            values="0;-15.708;-31.416;-31.416" 
            repeatCount="indefinite"
          />
        </circle>
      </svg>

      <!-- Left Icon -->
      <svg 
        *ngIf="leftIcon && !loading" 
        class="button-icon button-icon-left"
        viewBox="0 0 24 24" 
        fill="none" 
        stroke="currentColor"
      >
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" [attr.d]="leftIcon" />
      </svg>

      <!-- Button Content -->
      <span class="button-content" [class.sr-only]="loading && loadingText">
        <ng-content></ng-content>
      </span>

      <!-- Loading Text -->
      <span *ngIf="loading && loadingText" class="button-loading-text">
        {{ loadingText }}
      </span>

      <!-- Right Icon -->
      <svg 
        *ngIf="rightIcon && !loading" 
        class="button-icon button-icon-right"
        viewBox="0 0 24 24" 
        fill="none" 
        stroke="currentColor"
      >
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" [attr.d]="rightIcon" />
      </svg>

      <!-- Badge -->
      <span *ngIf="badge" class="button-badge">{{ badge }}</span>
    </button>
  `, styles: ["/* src/app/shared/components/button/button.component.scss */\n.btn {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  gap: var(--spacing-sm);\n  border: 1px solid transparent;\n  border-radius: var(--radius-md);\n  font-family: inherit;\n  font-weight: 500;\n  text-decoration: none;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  position: relative;\n  white-space: nowrap;\n  -webkit-user-select: none;\n  user-select: none;\n  outline: none;\n}\n.btn:focus-visible {\n  outline: 2px solid var(--primary-500);\n  outline-offset: 2px;\n}\n.btn:disabled,\n.btn.btn-disabled {\n  opacity: 0.6;\n  cursor: not-allowed;\n  pointer-events: none;\n}\n.btn.btn-loading {\n  cursor: wait;\n}\n.btn-xs {\n  padding: var(--spacing-xs) var(--spacing-sm);\n  font-size: var(--text-xs);\n  line-height: 1.25;\n  min-height: 24px;\n}\n.btn-sm {\n  padding: var(--spacing-sm) var(--spacing-md);\n  font-size: var(--text-sm);\n  line-height: 1.25;\n  min-height: 32px;\n}\n.btn-md {\n  padding: var(--spacing-sm) var(--spacing-lg);\n  font-size: var(--text-sm);\n  line-height: 1.5;\n  min-height: 40px;\n}\n.btn-lg {\n  padding: var(--spacing-md) var(--spacing-xl);\n  font-size: var(--text-base);\n  line-height: 1.5;\n  min-height: 48px;\n}\n.btn-xl {\n  padding: var(--spacing-lg) var(--spacing-2xl);\n  font-size: var(--text-lg);\n  line-height: 1.5;\n  min-height: 56px;\n}\n.btn-primary {\n  background-color: var(--primary-500);\n  border-color: var(--primary-500);\n  color: white;\n}\n.btn-primary:hover:not(:disabled) {\n  background-color: var(--primary-600);\n  border-color: var(--primary-600);\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-md);\n}\n.btn-primary:active:not(:disabled) {\n  background-color: var(--primary-700);\n  border-color: var(--primary-700);\n  transform: translateY(0);\n}\n.btn-secondary {\n  background-color: var(--secondary-100);\n  border-color: var(--secondary-200);\n  color: var(--secondary-800);\n}\n.btn-secondary:hover:not(:disabled) {\n  background-color: var(--secondary-200);\n  border-color: var(--secondary-300);\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-md);\n}\n.btn-secondary:active:not(:disabled) {\n  background-color: var(--secondary-300);\n  border-color: var(--secondary-400);\n  transform: translateY(0);\n}\n.btn-success {\n  background-color: var(--success-500);\n  border-color: var(--success-500);\n  color: white;\n}\n.btn-success:hover:not(:disabled) {\n  background-color: var(--success-600);\n  border-color: var(--success-600);\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-md);\n}\n.btn-success:active:not(:disabled) {\n  background-color: var(--success-700);\n  border-color: var(--success-700);\n  transform: translateY(0);\n}\n.btn-warning {\n  background-color: var(--warning-500);\n  border-color: var(--warning-500);\n  color: white;\n}\n.btn-warning:hover:not(:disabled) {\n  background-color: var(--warning-600);\n  border-color: var(--warning-600);\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-md);\n}\n.btn-warning:active:not(:disabled) {\n  background-color: var(--warning-700);\n  border-color: var(--warning-700);\n  transform: translateY(0);\n}\n.btn-error {\n  background-color: var(--error-500);\n  border-color: var(--error-500);\n  color: white;\n}\n.btn-error:hover:not(:disabled) {\n  background-color: var(--error-600);\n  border-color: var(--error-600);\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-md);\n}\n.btn-error:active:not(:disabled) {\n  background-color: var(--error-700);\n  border-color: var(--error-700);\n  transform: translateY(0);\n}\n.btn-ghost {\n  background-color: transparent;\n  border-color: transparent;\n  color: var(--secondary-700);\n}\n.btn-ghost:hover:not(:disabled) {\n  background-color: var(--secondary-100);\n  color: var(--secondary-900);\n}\n.btn-ghost:active:not(:disabled) {\n  background-color: var(--secondary-200);\n}\n.btn-outline {\n  background-color: transparent;\n  border-color: var(--secondary-300);\n  color: var(--secondary-700);\n}\n.btn-outline:hover:not(:disabled) {\n  background-color: var(--secondary-50);\n  border-color: var(--secondary-400);\n  color: var(--secondary-900);\n  transform: translateY(-1px);\n  box-shadow: var(--shadow-sm);\n}\n.btn-outline:active:not(:disabled) {\n  background-color: var(--secondary-100);\n  border-color: var(--secondary-500);\n  transform: translateY(0);\n}\n.btn-full-width {\n  width: 100%;\n}\n.btn-rounded {\n  border-radius: var(--radius-2xl);\n}\n.button-icon {\n  flex-shrink: 0;\n}\n.btn-xs .button-icon {\n  width: 12px;\n  height: 12px;\n}\n.btn-sm .button-icon {\n  width: 14px;\n  height: 14px;\n}\n.btn-md .button-icon {\n  width: 16px;\n  height: 16px;\n}\n.btn-lg .button-icon {\n  width: 18px;\n  height: 18px;\n}\n.btn-xl .button-icon {\n  width: 20px;\n  height: 20px;\n}\n.button-spinner {\n  flex-shrink: 0;\n  animation: spin 1s linear infinite;\n}\n.btn-xs .button-spinner {\n  width: 12px;\n  height: 12px;\n}\n.btn-sm .button-spinner {\n  width: 14px;\n  height: 14px;\n}\n.btn-md .button-spinner {\n  width: 16px;\n  height: 16px;\n}\n.btn-lg .button-spinner {\n  width: 18px;\n  height: 18px;\n}\n.btn-xl .button-spinner {\n  width: 20px;\n  height: 20px;\n}\n@keyframes spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n.button-content {\n  display: flex;\n  align-items: center;\n  gap: inherit;\n}\n.button-loading-text {\n  display: flex;\n  align-items: center;\n}\n.button-badge {\n  position: absolute;\n  top: -6px;\n  right: -6px;\n  min-width: 18px;\n  height: 18px;\n  padding: 0 4px;\n  background: var(--error-500);\n  color: white;\n  font-size: 10px;\n  font-weight: 600;\n  border-radius: 9px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  line-height: 1;\n  border: 2px solid white;\n}\n.sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border: 0;\n}\n.btn:focus-visible {\n  outline: 2px solid var(--primary-500);\n  outline-offset: 2px;\n}\n.btn-primary:focus-visible {\n  outline-color: var(--primary-300);\n}\n.btn-success:focus-visible {\n  outline-color: var(--success-300);\n}\n.btn-warning:focus-visible {\n  outline-color: var(--warning-300);\n}\n.btn-error:focus-visible {\n  outline-color: var(--error-300);\n}\n@media (prefers-contrast: high) {\n  .btn {\n    border-width: 2px;\n  }\n  .btn-ghost {\n    border-color: currentColor;\n  }\n}\n@media (prefers-reduced-motion: reduce) {\n  .btn {\n    transition: none;\n  }\n  .button-spinner {\n    animation: none;\n  }\n  .btn:hover:not(:disabled) {\n    transform: none;\n  }\n}\n/*# sourceMappingURL=button.component.css.map */\n"] }]
  }], null, { variant: [{
    type: Input
  }], size: [{
    type: Input
  }], type: [{
    type: Input
  }], disabled: [{
    type: Input
  }], loading: [{
    type: Input
  }], loadingText: [{
    type: Input
  }], leftIcon: [{
    type: Input
  }], rightIcon: [{
    type: Input
  }], badge: [{
    type: Input
  }], fullWidth: [{
    type: Input
  }], rounded: [{
    type: Input
  }], clicked: [{
    type: Output
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(ButtonComponent, { className: "ButtonComponent", filePath: "src/app/shared/components/button/button.component.ts", lineNumber: 88 });
})();

export {
  ButtonComponent
};
//# sourceMappingURL=chunk-NVGHB2VF.mjs.map
