import { Component, Input, forwardRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ControlValueAccessor, NG_VALUE_ACCESSOR, FormsModule } from '@angular/forms';

@Component({
  selector: 'app-login-input',
  standalone: true,
  imports: [CommonModule, FormsModule],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => LoginInputComponent),
      multi: true
    }
  ],
  template: `
    <div class="form-group">
      <label *ngIf="label" [for]="inputId" class="form-label">
        {{ label }}
        <span class="required-indicator" *ngIf="required">*</span>
      </label>

      <div class="input-wrapper">
        <input
          [id]="inputId"
          [type]="type"
          [placeholder]="placeholder"
          [disabled]="disabled"
          [(ngModel)]="value"
          (input)="onInput($event)"
          (blur)="onBlur()"
          class="form-input"
          [class.error]="!!errorMessage"
        >
      </div>

      @if (errorMessage) {
        <p class="error-message">{{ errorMessage }}</p>
      }
    </div>
  `,
  styles: [`
    :host {
      display: block;
      width: 100%;
    }

    .form-group {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-sm);
    }

    .form-label {
      font-size: var(--text-sm);
      font-weight: 500;
      color: var(--secondary-700);
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
    }

    .required-indicator {
      color: var(--error-500);
      font-weight: 600;
    }

    .input-wrapper {
      position: relative;
    }

    .form-input {
      width: 100%;
      height: 44px;
      padding: 0 var(--spacing-md);
      border: 1px solid var(--secondary-300);
      border-radius: var(--radius-md);
      background: white;
      font-size: var(--text-sm);
      color: var(--secondary-800);
      transition: all 0.2s ease;
      outline: none;
    }

    .form-input::placeholder {
      color: var(--secondary-400);
    }

    .form-input:focus {
      border-color: var(--primary-500);
      box-shadow: 0 0 0 3px var(--primary-100);
    }

    .form-input:disabled {
      background: var(--secondary-50);
      color: var(--secondary-500);
      cursor: not-allowed;
      border-color: var(--secondary-200);
    }

    .form-input.error {
      border-color: var(--error-500);
      box-shadow: 0 0 0 3px var(--error-100);
    }

    .form-input.error:focus {
      border-color: var(--error-500);
      box-shadow: 0 0 0 3px var(--error-100);
    }

    .error-message {
      font-size: var(--text-sm);
      color: var(--error-600);
      margin: 0;
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
    }

    /* High Contrast Mode Support */
    @media (prefers-contrast: high) {
      .form-input {
        border-width: 2px;
      }
    }

    /* Reduced Motion Support */
    @media (prefers-reduced-motion: reduce) {
      .form-input {
        transition: none;
      }
    }
  `]
})
export class LoginInputComponent implements ControlValueAccessor {
  @Input() label = '';
  @Input() type = 'text';
  @Input() inputId = '';
  @Input() placeholder = '';
  @Input() errorMessage = '';
  @Input() disabled = false;
  @Input() required = false;

  value: any = '';
  private touched = false;
  private onChange = (value: any) => {};
  private onTouched = () => {};

  writeValue(value: any): void {
    this.value = value;
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  onInput(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.value = target.value;
    this.onChange(this.value);
  }

  onBlur(): void {
    if (!this.touched) {
      this.touched = true;
      this.onTouched();
    }
  }
}
