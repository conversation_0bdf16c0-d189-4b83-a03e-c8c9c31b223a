{"version": 3, "sources": ["src/app/modules/security/security.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\n\nexport const routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./security.component').then(m => m.SecurityComponent)\n  },\n  {\n    path: 'vulnerabilities',\n    loadComponent: () => import('./vulnerabilities/vulnerabilities.component').then(m => m.VulnerabilitiesComponent)\n  },\n  {\n    path: 'assessments',\n    loadComponent: () => import('./assessments/assessments.component').then(m => m.AssessmentsComponent)\n  }\n];\n"], "mappings": ";;;;;;AAEO,IAAM,SAAiB;EAC5B;IACE,MAAM;IACN,eAAe,MAAM,OAAO,sBAAsB,EAAE,KAAK,OAAK,EAAE,iBAAiB;;EAEnF;IACE,MAAM;IACN,eAAe,MAAM,OAAO,sBAA6C,EAAE,KAAK,OAAK,EAAE,wBAAwB;;EAEjH;IACE,MAAM;IACN,eAAe,MAAM,OAAO,sBAAqC,EAAE,KAAK,OAAK,EAAE,oBAAoB;;;", "names": []}