import {
  Default<PERSON><PERSON>ueAccessor,
  FormsModule,
  NgControlStatus,
  NgModel
} from "./chunk-SM75SJZE.js";
import {
  ButtonComponent
} from "./chunk-QMVBAXS7.js";
import {
  CommonModule,
  Component,
  DatePipe,
  DecimalPipe,
  EventEmitter,
  Input,
  NgForOf,
  NgIf,
  Output,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵNgOnChangesFeature,
  ɵɵadvance,
  ɵɵattribute,
  ɵɵclassMap,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵelement,
  ɵɵelementContainerEnd,
  ɵɵelementContainerStart,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnamespaceHTML,
  ɵɵnamespaceSVG,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind1,
  ɵɵpipeBind2,
  ɵɵprojection,
  ɵɵprojectionDef,
  ɵɵproperty,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵstyleProp,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtextInterpolate3,
  ɵɵtwoWayBindingSet,
  ɵɵtwoWayListener,
  ɵɵtwoWayProperty
} from "./chunk-3JQLQ36P.js";

// src/app/shared/components/table/table.component.ts
var _c0 = [[["", "slot", "actions"]]];
var _c1 = ["[slot=actions]"];
function TableComponent_div_1_h3_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "h3", 17);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.title);
  }
}
function TableComponent_div_1_p_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p", 18);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.subtitle);
  }
}
function TableComponent_div_1_div_5_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 19);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(1, "svg", 20);
    \u0275\u0275element(2, "circle", 21)(3, "path", 22);
    \u0275\u0275elementEnd();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(4, "input", 23);
    \u0275\u0275twoWayListener("ngModelChange", function TableComponent_div_1_div_5_Template_input_ngModelChange_4_listener($event) {
      \u0275\u0275restoreView(_r2);
      const ctx_r0 = \u0275\u0275nextContext(2);
      \u0275\u0275twoWayBindingSet(ctx_r0.searchTerm, $event) || (ctx_r0.searchTerm = $event);
      return \u0275\u0275resetView($event);
    });
    \u0275\u0275listener("input", function TableComponent_div_1_div_5_Template_input_input_4_listener() {
      \u0275\u0275restoreView(_r2);
      const ctx_r0 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r0.onSearch());
    });
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(4);
    \u0275\u0275twoWayProperty("ngModel", ctx_r0.searchTerm);
  }
}
function TableComponent_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 11)(1, "div", 12);
    \u0275\u0275template(2, TableComponent_div_1_h3_2_Template, 2, 1, "h3", 13)(3, TableComponent_div_1_p_3_Template, 2, 1, "p", 14);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "div", 15);
    \u0275\u0275template(5, TableComponent_div_1_div_5_Template, 5, 1, "div", 16);
    \u0275\u0275projection(6);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", ctx_r0.title);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.subtitle);
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", ctx_r0.searchable);
  }
}
function TableComponent_th_6_div_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 27);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(1, "svg", 28);
    \u0275\u0275element(2, "polyline", 29);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const column_r4 = \u0275\u0275nextContext().$implicit;
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275classProp("sort-active", (ctx_r0.sortConfig == null ? null : ctx_r0.sortConfig.column) === column_r4.key)("sort-desc", (ctx_r0.sortConfig == null ? null : ctx_r0.sortConfig.column) === column_r4.key && (ctx_r0.sortConfig == null ? null : ctx_r0.sortConfig.direction) === "desc");
  }
}
function TableComponent_th_6_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "th", 24);
    \u0275\u0275listener("click", function TableComponent_th_6_Template_th_click_0_listener() {
      const column_r4 = \u0275\u0275restoreView(_r3).$implicit;
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.onSort(column_r4));
    });
    \u0275\u0275elementStart(1, "div", 25)(2, "span");
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275template(4, TableComponent_th_6_div_4_Template, 3, 4, "div", 26);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const column_r4 = ctx.$implicit;
    \u0275\u0275styleProp("width", column_r4.width);
    \u0275\u0275classProp("sortable", column_r4.sortable);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(column_r4.label);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", column_r4.sortable);
  }
}
function TableComponent_tr_8_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "tr", 30)(1, "td", 31)(2, "div", 32);
    \u0275\u0275element(3, "div", 33);
    \u0275\u0275elementStart(4, "span");
    \u0275\u0275text(5, "Loading...");
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275attribute("colspan", ctx_r0.columns.length);
  }
}
function TableComponent_tr_9_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "tr", 34)(1, "td", 35)(2, "div", 36);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(3, "svg", 37);
    \u0275\u0275element(4, "circle", 21)(5, "path", 22);
    \u0275\u0275elementEnd();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(6, "h4");
    \u0275\u0275text(7);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(8, "p");
    \u0275\u0275text(9);
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275attribute("colspan", ctx_r0.columns.length);
    \u0275\u0275advance(6);
    \u0275\u0275textInterpolate(ctx_r0.emptyMessage || "No data available");
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(ctx_r0.emptyDescription || "There are no items to display.");
  }
}
function TableComponent_tr_10_td_1_span_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 47);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const column_r7 = \u0275\u0275nextContext().$implicit;
    const item_r6 = \u0275\u0275nextContext().$implicit;
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", column_r7.format ? column_r7.format(ctx_r0.getColumnValue(item_r6, column_r7.key)) : ctx_r0.getColumnValue(item_r6, column_r7.key), " ");
  }
}
function TableComponent_tr_10_td_1_span_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 48);
    \u0275\u0275text(1);
    \u0275\u0275pipe(2, "number");
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const column_r7 = \u0275\u0275nextContext().$implicit;
    const item_r6 = \u0275\u0275nextContext().$implicit;
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", column_r7.format ? column_r7.format(ctx_r0.getColumnValue(item_r6, column_r7.key)) : \u0275\u0275pipeBind1(2, 1, ctx_r0.getColumnValue(item_r6, column_r7.key)), " ");
  }
}
function TableComponent_tr_10_td_1_span_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 49);
    \u0275\u0275text(1);
    \u0275\u0275pipe(2, "date");
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const column_r7 = \u0275\u0275nextContext().$implicit;
    const item_r6 = \u0275\u0275nextContext().$implicit;
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", column_r7.format ? column_r7.format(ctx_r0.getColumnValue(item_r6, column_r7.key)) : \u0275\u0275pipeBind2(2, 1, ctx_r0.getColumnValue(item_r6, column_r7.key), "medium"), " ");
  }
}
function TableComponent_tr_10_td_1_span_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 50);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const column_r7 = \u0275\u0275nextContext().$implicit;
    const item_r6 = \u0275\u0275nextContext().$implicit;
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275classMap("badge-" + ctx_r0.getBadgeClass(ctx_r0.getColumnValue(item_r6, column_r7.key), column_r7.badgeConfig));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.getBadgeLabel(ctx_r0.getColumnValue(item_r6, column_r7.key), column_r7.badgeConfig), " ");
  }
}
function TableComponent_tr_10_td_1_span_5_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 51)(1, "span", 52);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const column_r7 = \u0275\u0275nextContext().$implicit;
    const item_r6 = \u0275\u0275nextContext().$implicit;
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275classProp("boolean-true", ctx_r0.getColumnValue(item_r6, column_r7.key))("boolean-false", !ctx_r0.getColumnValue(item_r6, column_r7.key));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.getColumnValue(item_r6, column_r7.key) ? "Active" : "Inactive", " ");
  }
}
function TableComponent_tr_10_td_1_div_6_ng_container_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r8 = \u0275\u0275getCurrentView();
    \u0275\u0275elementContainerStart(0);
    \u0275\u0275elementStart(1, "app-button", 55);
    \u0275\u0275listener("clicked", function TableComponent_tr_10_td_1_div_6_ng_container_1_Template_app_button_clicked_1_listener() {
      const action_r9 = \u0275\u0275restoreView(_r8).$implicit;
      const item_r6 = \u0275\u0275nextContext(3).$implicit;
      return \u0275\u0275resetView(action_r9.action(item_r6));
    });
    \u0275\u0275text(2);
    \u0275\u0275elementEnd();
    \u0275\u0275elementContainerEnd();
  }
  if (rf & 2) {
    const action_r9 = ctx.$implicit;
    \u0275\u0275advance();
    \u0275\u0275property("variant", action_r9.variant || "ghost")("leftIcon", action_r9.icon || "");
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", action_r9.label, " ");
  }
}
function TableComponent_tr_10_td_1_div_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 53);
    \u0275\u0275template(1, TableComponent_tr_10_td_1_div_6_ng_container_1_Template, 3, 3, "ng-container", 54);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const item_r6 = \u0275\u0275nextContext(2).$implicit;
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngForOf", ctx_r0.getVisibleActions(item_r6));
  }
}
function TableComponent_tr_10_td_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "td", 40);
    \u0275\u0275template(1, TableComponent_tr_10_td_1_span_1_Template, 2, 1, "span", 41)(2, TableComponent_tr_10_td_1_span_2_Template, 3, 3, "span", 42)(3, TableComponent_tr_10_td_1_span_3_Template, 3, 4, "span", 43)(4, TableComponent_tr_10_td_1_span_4_Template, 2, 3, "span", 44)(5, TableComponent_tr_10_td_1_span_5_Template, 3, 5, "span", 45)(6, TableComponent_tr_10_td_1_div_6_Template, 2, 1, "div", 46);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const column_r7 = ctx.$implicit;
    \u0275\u0275classMap("cell-" + column_r7.type);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", column_r7.type === "text" || !column_r7.type);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", column_r7.type === "number");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", column_r7.type === "date");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", column_r7.type === "badge");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", column_r7.type === "boolean");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", column_r7.type === "actions");
  }
}
function TableComponent_tr_10_Template(rf, ctx) {
  if (rf & 1) {
    const _r5 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "tr", 38);
    \u0275\u0275listener("click", function TableComponent_tr_10_Template_tr_click_0_listener() {
      const item_r6 = \u0275\u0275restoreView(_r5).$implicit;
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.onRowClick(item_r6));
    });
    \u0275\u0275template(1, TableComponent_tr_10_td_1_Template, 7, 8, "td", 39);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const item_r6 = ctx.$implicit;
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275classProp("selected", ctx_r0.isSelected(item_r6));
    \u0275\u0275advance();
    \u0275\u0275property("ngForOf", ctx_r0.columns);
  }
}
function TableComponent_div_11_div_4_button_4_Template(rf, ctx) {
  if (rf & 1) {
    const _r11 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 65);
    \u0275\u0275listener("click", function TableComponent_div_11_div_4_button_4_Template_button_click_0_listener() {
      const page_r12 = \u0275\u0275restoreView(_r11).$implicit;
      const ctx_r0 = \u0275\u0275nextContext(3);
      return \u0275\u0275resetView(ctx_r0.goToPage(page_r12));
    });
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const page_r12 = ctx.$implicit;
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275classProp("active", page_r12 === ctx_r0.currentPage);
    \u0275\u0275property("disabled", page_r12 === "...");
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", page_r12, " ");
  }
}
function TableComponent_div_11_div_4_Template(rf, ctx) {
  if (rf & 1) {
    const _r10 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 60)(1, "app-button", 61);
    \u0275\u0275listener("clicked", function TableComponent_div_11_div_4_Template_app_button_clicked_1_listener() {
      \u0275\u0275restoreView(_r10);
      const ctx_r0 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r0.goToPage(ctx_r0.currentPage - 1));
    });
    \u0275\u0275text(2, " Previous ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "div", 62);
    \u0275\u0275template(4, TableComponent_div_11_div_4_button_4_Template, 2, 4, "button", 63);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "app-button", 64);
    \u0275\u0275listener("clicked", function TableComponent_div_11_div_4_Template_app_button_clicked_5_listener() {
      \u0275\u0275restoreView(_r10);
      const ctx_r0 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r0.goToPage(ctx_r0.currentPage + 1));
    });
    \u0275\u0275text(6, " Next ");
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275property("disabled", ctx_r0.currentPage === 1);
    \u0275\u0275advance(3);
    \u0275\u0275property("ngForOf", ctx_r0.getPageNumbers());
    \u0275\u0275advance();
    \u0275\u0275property("disabled", ctx_r0.currentPage === ctx_r0.totalPages);
  }
}
function TableComponent_div_11_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 56)(1, "div", 57)(2, "span", 58);
    \u0275\u0275text(3);
    \u0275\u0275elementEnd()();
    \u0275\u0275template(4, TableComponent_div_11_div_4_Template, 7, 3, "div", 59);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate3(" Showing ", ctx_r0.startIndex + 1, "-", ctx_r0.endIndex, " of ", ctx_r0.filteredData.length, " items ");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.paginated && ctx_r0.totalPages > 1);
  }
}
var TableComponent = class _TableComponent {
  data = [];
  columns = [];
  actions = [];
  title = "";
  subtitle = "";
  loading = false;
  searchable = true;
  sortable = true;
  paginated = true;
  pageSize = 10;
  striped = true;
  hoverable = true;
  selectable = false;
  showHeader = true;
  showFooter = true;
  emptyMessage = "";
  emptyDescription = "";
  rowClick = new EventEmitter();
  sortChange = new EventEmitter();
  searchChange = new EventEmitter();
  pageChange = new EventEmitter();
  searchTerm = "";
  sortConfig = null;
  currentPage = 1;
  selectedItems = [];
  filteredData = [];
  paginatedData = [];
  ngOnInit() {
    this.updateData();
  }
  ngOnChanges() {
    this.updateData();
  }
  updateData() {
    this.filterData();
    this.sortData();
    this.paginateData();
  }
  filterData() {
    if (!this.searchTerm.trim()) {
      this.filteredData = [...this.data];
      return;
    }
    const term = this.searchTerm.toLowerCase();
    this.filteredData = this.data.filter((item) => {
      return this.columns.some((column) => {
        const value = this.getColumnValue(item, column.key);
        return value && value.toString().toLowerCase().includes(term);
      });
    });
    this.currentPage = 1;
  }
  sortData() {
    if (!this.sortConfig)
      return;
    this.filteredData.sort((a, b) => {
      const aValue = this.getColumnValue(a, this.sortConfig.column);
      const bValue = this.getColumnValue(b, this.sortConfig.column);
      let comparison = 0;
      if (aValue < bValue)
        comparison = -1;
      if (aValue > bValue)
        comparison = 1;
      return this.sortConfig.direction === "desc" ? -comparison : comparison;
    });
  }
  paginateData() {
    if (!this.paginated) {
      this.paginatedData = this.filteredData;
      return;
    }
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.paginatedData = this.filteredData.slice(startIndex, endIndex);
  }
  onSearch() {
    this.updateData();
    this.searchChange.emit(this.searchTerm);
  }
  onSort(column) {
    if (!column.sortable)
      return;
    if (this.sortConfig?.column === column.key) {
      this.sortConfig.direction = this.sortConfig.direction === "asc" ? "desc" : "asc";
    } else {
      this.sortConfig = { column: column.key, direction: "asc" };
    }
    this.updateData();
    this.sortChange.emit(this.sortConfig);
  }
  onRowClick(item) {
    if (this.selectable) {
      this.toggleSelection(item);
    }
    this.rowClick.emit(item);
  }
  goToPage(page) {
    if (typeof page === "string")
      return;
    this.currentPage = Math.max(1, Math.min(page, this.totalPages));
    this.paginateData();
    this.pageChange.emit(this.currentPage);
  }
  getColumnValue(item, key) {
    return key.split(".").reduce((obj, prop) => obj?.[prop], item);
  }
  getBadgeClass(value, config) {
    return config?.[value]?.color || "default";
  }
  getBadgeLabel(value, config) {
    return config?.[value]?.label || value;
  }
  getVisibleActions(item) {
    return this.actions.filter((action) => !action.visible || action.visible(item));
  }
  isSelected(item) {
    return this.selectedItems.includes(item);
  }
  toggleSelection(item) {
    const index = this.selectedItems.indexOf(item);
    if (index > -1) {
      this.selectedItems.splice(index, 1);
    } else {
      this.selectedItems.push(item);
    }
  }
  getPageNumbers() {
    const pages = [];
    const total = this.totalPages;
    const current = this.currentPage;
    if (total <= 7) {
      for (let i = 1; i <= total; i++) {
        pages.push(i);
      }
    } else {
      pages.push(1);
      if (current > 4) {
        pages.push("...");
      }
      const start = Math.max(2, current - 1);
      const end = Math.min(total - 1, current + 1);
      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
      if (current < total - 3) {
        pages.push("...");
      }
      pages.push(total);
    }
    return pages;
  }
  trackByFn(index, item) {
    return item.id || index;
  }
  get totalPages() {
    return Math.ceil(this.filteredData.length / this.pageSize);
  }
  get startIndex() {
    return (this.currentPage - 1) * this.pageSize;
  }
  get endIndex() {
    return Math.min(this.startIndex + this.pageSize, this.filteredData.length);
  }
  static \u0275fac = function TableComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _TableComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _TableComponent, selectors: [["app-table"]], inputs: { data: "data", columns: "columns", actions: "actions", title: "title", subtitle: "subtitle", loading: "loading", searchable: "searchable", sortable: "sortable", paginated: "paginated", pageSize: "pageSize", striped: "striped", hoverable: "hoverable", selectable: "selectable", showHeader: "showHeader", showFooter: "showFooter", emptyMessage: "emptyMessage", emptyDescription: "emptyDescription" }, outputs: { rowClick: "rowClick", sortChange: "sortChange", searchChange: "searchChange", pageChange: "pageChange" }, features: [\u0275\u0275NgOnChangesFeature], ngContentSelectors: _c1, decls: 12, vars: 11, consts: [[1, "table-container"], ["class", "table-header", 4, "ngIf"], [1, "table-wrapper"], [1, "table"], [1, "table-head"], ["class", "table-header-cell", 3, "sortable", "width", "click", 4, "ngFor", "ngForOf"], [1, "table-body"], ["class", "loading-row", 4, "ngIf"], ["class", "empty-row", 4, "ngIf"], ["class", "table-row", 3, "selected", "click", 4, "ngFor", "ngForOf", "ngForTrackBy"], ["class", "table-footer", 4, "ngIf"], [1, "table-header"], [1, "table-title-section"], ["class", "table-title", 4, "ngIf"], ["class", "table-subtitle", 4, "ngIf"], [1, "table-actions"], ["class", "search-container", 4, "ngIf"], [1, "table-title"], [1, "table-subtitle"], [1, "search-container"], ["viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", 1, "search-icon"], ["cx", "11", "cy", "11", "r", "8"], ["d", "m21 21-4.35-4.35"], ["type", "text", "placeholder", "Search...", 1, "search-input", 3, "ngModelChange", "input", "ngModel"], [1, "table-header-cell", 3, "click"], [1, "header-content"], ["class", "sort-indicator", 4, "ngIf"], [1, "sort-indicator"], ["viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", 1, "sort-icon"], ["points", "6,9 12,15 18,9"], [1, "loading-row"], [1, "loading-cell"], [1, "loading-content"], [1, "spinner"], [1, "empty-row"], [1, "empty-cell"], [1, "empty-content"], ["viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", 1, "empty-icon"], [1, "table-row", 3, "click"], ["class", "table-cell", 3, "class", 4, "ngFor", "ngForOf"], [1, "table-cell"], ["class", "cell-text", 4, "ngIf"], ["class", "cell-number", 4, "ngIf"], ["class", "cell-date", 4, "ngIf"], ["class", "cell-badge", 3, "class", 4, "ngIf"], ["class", "cell-boolean", 4, "ngIf"], ["class", "cell-actions", 4, "ngIf"], [1, "cell-text"], [1, "cell-number"], [1, "cell-date"], [1, "cell-badge"], [1, "cell-boolean"], [1, "boolean-indicator"], [1, "cell-actions"], [4, "ngFor", "ngForOf"], ["size", "sm", 3, "clicked", "variant", "leftIcon"], [1, "table-footer"], [1, "footer-info"], [1, "item-count"], ["class", "pagination", 4, "ngIf"], [1, "pagination"], ["variant", "outline", "size", "sm", "leftIcon", "M15 19l-7-7 7-7", 3, "clicked", "disabled"], [1, "page-numbers"], ["class", "page-button", 3, "active", "disabled", "click", 4, "ngFor", "ngForOf"], ["variant", "outline", "size", "sm", "rightIcon", "M9 5l7 7-7 7", 3, "clicked", "disabled"], [1, "page-button", 3, "click", "disabled"]], template: function TableComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275projectionDef(_c0);
      \u0275\u0275elementStart(0, "div", 0);
      \u0275\u0275template(1, TableComponent_div_1_Template, 7, 3, "div", 1);
      \u0275\u0275elementStart(2, "div", 2)(3, "table", 3)(4, "thead", 4)(5, "tr");
      \u0275\u0275template(6, TableComponent_th_6_Template, 5, 6, "th", 5);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(7, "tbody", 6);
      \u0275\u0275template(8, TableComponent_tr_8_Template, 6, 1, "tr", 7)(9, TableComponent_tr_9_Template, 10, 3, "tr", 8)(10, TableComponent_tr_10_Template, 2, 3, "tr", 9);
      \u0275\u0275elementEnd()()();
      \u0275\u0275template(11, TableComponent_div_11_Template, 5, 4, "div", 10);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.showHeader);
      \u0275\u0275advance(2);
      \u0275\u0275classProp("table-striped", ctx.striped)("table-hover", ctx.hoverable);
      \u0275\u0275advance(3);
      \u0275\u0275property("ngForOf", ctx.columns);
      \u0275\u0275advance(2);
      \u0275\u0275property("ngIf", ctx.loading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.loading && (!ctx.data || ctx.data.length === 0));
      \u0275\u0275advance();
      \u0275\u0275property("ngForOf", ctx.paginatedData)("ngForTrackBy", ctx.trackByFn);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.showFooter && !ctx.loading);
    }
  }, dependencies: [CommonModule, NgForOf, NgIf, DecimalPipe, DatePipe, FormsModule, DefaultValueAccessor, NgControlStatus, NgModel, ButtonComponent], styles: ["\n\n.table-container[_ngcontent-%COMP%] {\n  background: white;\n  border-radius: var(--radius-lg);\n  border: 1px solid var(--secondary-200);\n  overflow: hidden;\n}\n.table-header[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: flex-start;\n  justify-content: space-between;\n  gap: var(--spacing-lg);\n  padding: var(--spacing-lg);\n  border-bottom: 1px solid var(--secondary-200);\n  background: var(--neutral-50);\n}\n.table-title-section[_ngcontent-%COMP%] {\n  flex: 1;\n}\n.table-title[_ngcontent-%COMP%] {\n  margin: 0 0 var(--spacing-xs) 0;\n  font-size: var(--text-lg);\n  font-weight: 600;\n  color: var(--secondary-900);\n}\n.table-subtitle[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n}\n.table-actions[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-md);\n}\n.search-container[_ngcontent-%COMP%] {\n  position: relative;\n  display: flex;\n  align-items: center;\n}\n.search-icon[_ngcontent-%COMP%] {\n  position: absolute;\n  left: var(--spacing-md);\n  width: 16px;\n  height: 16px;\n  color: var(--secondary-400);\n  z-index: 1;\n}\n.search-input[_ngcontent-%COMP%] {\n  width: 250px;\n  height: 36px;\n  padding: 0 var(--spacing-md) 0 40px;\n  border: 1px solid var(--secondary-200);\n  border-radius: var(--radius-md);\n  background: white;\n  font-size: var(--text-sm);\n  color: var(--secondary-800);\n  transition: all 0.2s ease;\n}\n.search-input[_ngcontent-%COMP%]::placeholder {\n  color: var(--secondary-400);\n}\n.search-input[_ngcontent-%COMP%]:focus {\n  outline: none;\n  border-color: var(--primary-500);\n  box-shadow: 0 0 0 3px var(--primary-100);\n}\n.table-wrapper[_ngcontent-%COMP%] {\n  overflow-x: auto;\n}\n.table[_ngcontent-%COMP%] {\n  width: 100%;\n  border-collapse: collapse;\n  font-size: var(--text-sm);\n}\n.table-head[_ngcontent-%COMP%] {\n  background: var(--neutral-50);\n  border-bottom: 1px solid var(--secondary-200);\n}\n.table-header-cell[_ngcontent-%COMP%] {\n  padding: var(--spacing-md) var(--spacing-lg);\n  text-align: left;\n  font-weight: 600;\n  color: var(--secondary-700);\n  border-bottom: 1px solid var(--secondary-200);\n  white-space: nowrap;\n}\n.table-header-cell.sortable[_ngcontent-%COMP%] {\n  cursor: pointer;\n  -webkit-user-select: none;\n  user-select: none;\n  transition: background-color 0.2s ease;\n}\n.table-header-cell.sortable[_ngcontent-%COMP%]:hover {\n  background-color: var(--secondary-100);\n}\n.header-content[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n}\n.sort-indicator[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n}\n.sort-icon[_ngcontent-%COMP%] {\n  width: 14px;\n  height: 14px;\n  color: var(--secondary-400);\n  transition: all 0.2s ease;\n}\n.sort-icon.sort-active[_ngcontent-%COMP%] {\n  color: var(--primary-600);\n}\n.sort-icon.sort-desc[_ngcontent-%COMP%] {\n  transform: rotate(180deg);\n}\n.table-body[_ngcontent-%COMP%] {\n  background: white;\n}\n.table-row[_ngcontent-%COMP%] {\n  border-bottom: 1px solid var(--secondary-100);\n  transition: background-color 0.2s ease;\n}\n.table-row[_ngcontent-%COMP%]:last-child {\n  border-bottom: none;\n}\n.table-row.selected[_ngcontent-%COMP%] {\n  background-color: var(--primary-50);\n}\n.table.table-hover[_ngcontent-%COMP%]   .table-row[_ngcontent-%COMP%]:hover {\n  background-color: var(--neutral-50);\n}\n.table.table-striped[_ngcontent-%COMP%]   .table-row[_ngcontent-%COMP%]:nth-child(even) {\n  background-color: var(--neutral-25);\n}\n.table-cell[_ngcontent-%COMP%] {\n  padding: var(--spacing-md) var(--spacing-lg);\n  vertical-align: middle;\n  border-bottom: 1px solid var(--secondary-100);\n}\n.cell-text[_ngcontent-%COMP%] {\n  color: var(--secondary-800);\n}\n.cell-number[_ngcontent-%COMP%] {\n  color: var(--secondary-800);\n  font-variant-numeric: tabular-nums;\n}\n.cell-date[_ngcontent-%COMP%] {\n  color: var(--secondary-600);\n  font-size: var(--text-sm);\n}\n.cell-badge[_ngcontent-%COMP%] {\n  display: inline-flex;\n  align-items: center;\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border-radius: var(--radius-sm);\n  font-size: var(--text-xs);\n  font-weight: 500;\n  text-transform: uppercase;\n  letter-spacing: 0.025em;\n}\n.cell-badge.badge-default[_ngcontent-%COMP%] {\n  background: var(--secondary-100);\n  color: var(--secondary-700);\n}\n.cell-badge.badge-primary[_ngcontent-%COMP%] {\n  background: var(--primary-100);\n  color: var(--primary-700);\n}\n.cell-badge.badge-success[_ngcontent-%COMP%] {\n  background: var(--success-100);\n  color: var(--success-700);\n}\n.cell-badge.badge-warning[_ngcontent-%COMP%] {\n  background: var(--warning-100);\n  color: var(--warning-700);\n}\n.cell-badge.badge-error[_ngcontent-%COMP%] {\n  background: var(--error-100);\n  color: var(--error-700);\n}\n.cell-boolean[_ngcontent-%COMP%] {\n  display: inline-flex;\n  align-items: center;\n}\n.boolean-indicator[_ngcontent-%COMP%] {\n  display: inline-flex;\n  align-items: center;\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border-radius: var(--radius-sm);\n  font-size: var(--text-xs);\n  font-weight: 500;\n  text-transform: uppercase;\n  letter-spacing: 0.025em;\n}\n.boolean-indicator.boolean-true[_ngcontent-%COMP%] {\n  background: var(--success-100);\n  color: var(--success-700);\n}\n.boolean-indicator.boolean-false[_ngcontent-%COMP%] {\n  background: var(--secondary-100);\n  color: var(--secondary-700);\n}\n.cell-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: var(--spacing-xs);\n  align-items: center;\n}\n.loading-row[_ngcontent-%COMP%] {\n  background: white;\n}\n.loading-cell[_ngcontent-%COMP%] {\n  padding: var(--spacing-3xl);\n  text-align: center;\n  border: none;\n}\n.loading-content[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: var(--spacing-md);\n  color: var(--secondary-600);\n}\n.spinner[_ngcontent-%COMP%] {\n  width: 32px;\n  height: 32px;\n  border: 3px solid var(--secondary-200);\n  border-top: 3px solid var(--primary-500);\n  border-radius: 50%;\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\n}\n@keyframes _ngcontent-%COMP%_spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n.empty-row[_ngcontent-%COMP%] {\n  background: white;\n}\n.empty-cell[_ngcontent-%COMP%] {\n  padding: var(--spacing-3xl);\n  text-align: center;\n  border: none;\n}\n.empty-content[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: var(--spacing-md);\n  color: var(--secondary-500);\n}\n.empty-icon[_ngcontent-%COMP%] {\n  width: 48px;\n  height: 48px;\n  color: var(--secondary-300);\n}\n.empty-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: var(--text-lg);\n  font-weight: 600;\n  color: var(--secondary-700);\n}\n.empty-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: var(--text-sm);\n  color: var(--secondary-500);\n}\n.table-footer[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  gap: var(--spacing-lg);\n  padding: var(--spacing-lg);\n  border-top: 1px solid var(--secondary-200);\n  background: var(--neutral-50);\n}\n.footer-info[_ngcontent-%COMP%] {\n  flex: 1;\n}\n.item-count[_ngcontent-%COMP%] {\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n}\n.pagination[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n}\n.page-numbers[_ngcontent-%COMP%] {\n  display: flex;\n  gap: var(--spacing-xs);\n}\n.page-button[_ngcontent-%COMP%] {\n  min-width: 32px;\n  height: 32px;\n  padding: 0 var(--spacing-sm);\n  border: 1px solid var(--secondary-200);\n  background: white;\n  color: var(--secondary-700);\n  font-size: var(--text-sm);\n  border-radius: var(--radius-md);\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n.page-button[_ngcontent-%COMP%]:hover:not(:disabled) {\n  background: var(--secondary-50);\n  border-color: var(--secondary-300);\n}\n.page-button.active[_ngcontent-%COMP%] {\n  background: var(--primary-500);\n  border-color: var(--primary-500);\n  color: white;\n}\n.page-button[_ngcontent-%COMP%]:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n@media (max-width: 768px) {\n  .table-header[_ngcontent-%COMP%] {\n    flex-direction: column;\n    align-items: stretch;\n    gap: var(--spacing-md);\n  }\n  .table-actions[_ngcontent-%COMP%] {\n    justify-content: space-between;\n  }\n  .search-input[_ngcontent-%COMP%] {\n    width: 200px;\n  }\n  .table-footer[_ngcontent-%COMP%] {\n    flex-direction: column;\n    align-items: stretch;\n    gap: var(--spacing-md);\n  }\n  .pagination[_ngcontent-%COMP%] {\n    justify-content: center;\n  }\n  .table-wrapper[_ngcontent-%COMP%] {\n    overflow-x: scroll;\n  }\n  .table[_ngcontent-%COMP%] {\n    min-width: 600px;\n  }\n  .table-header-cell[_ngcontent-%COMP%], \n   .table-cell[_ngcontent-%COMP%] {\n    padding: var(--spacing-sm) var(--spacing-md);\n  }\n}\n@media (max-width: 480px) {\n  .search-input[_ngcontent-%COMP%] {\n    width: 150px;\n  }\n  .page-numbers[_ngcontent-%COMP%] {\n    display: none;\n  }\n}\n@media print {\n  .table-container[_ngcontent-%COMP%] {\n    border: 1px solid var(--secondary-300);\n    box-shadow: none;\n  }\n  .table-header[_ngcontent-%COMP%], \n   .table-footer[_ngcontent-%COMP%] {\n    background: white;\n  }\n  .search-container[_ngcontent-%COMP%], \n   .pagination[_ngcontent-%COMP%] {\n    display: none;\n  }\n  .cell-actions[_ngcontent-%COMP%] {\n    display: none;\n  }\n}\n/*# sourceMappingURL=table.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(TableComponent, [{
    type: Component,
    args: [{ selector: "app-table", standalone: true, imports: [CommonModule, FormsModule, ButtonComponent], template: `
    <div class="table-container">
      <!-- Table Header -->
      <div class="table-header" *ngIf="showHeader">
        <div class="table-title-section">
          <h3 class="table-title" *ngIf="title">{{ title }}</h3>
          <p class="table-subtitle" *ngIf="subtitle">{{ subtitle }}</p>
        </div>

        <div class="table-actions">
          <!-- Search -->
          <div class="search-container" *ngIf="searchable">
            <svg class="search-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="11" cy="11" r="8"></circle>
              <path d="m21 21-4.35-4.35"></path>
            </svg>
            <input
              type="text"
              class="search-input"
              placeholder="Search..."
              [(ngModel)]="searchTerm"
              (input)="onSearch()"
            >
          </div>

          <!-- Custom Actions -->
          <ng-content select="[slot=actions]"></ng-content>
        </div>
      </div>

      <!-- Table -->
      <div class="table-wrapper">
        <table class="table" [class.table-striped]="striped" [class.table-hover]="hoverable">
          <!-- Table Head -->
          <thead class="table-head">
            <tr>
              <th
                *ngFor="let column of columns"
                class="table-header-cell"
                [class.sortable]="column.sortable"
                [style.width]="column.width"
                (click)="onSort(column)"
              >
                <div class="header-content">
                  <span>{{ column.label }}</span>
                  <div class="sort-indicator" *ngIf="column.sortable">
                    <svg
                      class="sort-icon"
                      [class.sort-active]="sortConfig?.column === column.key"
                      [class.sort-desc]="sortConfig?.column === column.key && sortConfig?.direction === 'desc'"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                    >
                      <polyline points="6,9 12,15 18,9"></polyline>
                    </svg>
                  </div>
                </div>
              </th>
            </tr>
          </thead>

          <!-- Table Body -->
          <tbody class="table-body">
            <!-- Loading State -->
            <tr *ngIf="loading" class="loading-row">
              <td [attr.colspan]="columns.length" class="loading-cell">
                <div class="loading-content">
                  <div class="spinner"></div>
                  <span>Loading...</span>
                </div>
              </td>
            </tr>

            <!-- Empty State -->
            <tr *ngIf="!loading && (!data || data.length === 0)" class="empty-row">
              <td [attr.colspan]="columns.length" class="empty-cell">
                <div class="empty-content">
                  <svg class="empty-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <circle cx="11" cy="11" r="8"></circle>
                    <path d="m21 21-4.35-4.35"></path>
                  </svg>
                  <h4>{{ emptyMessage || 'No data available' }}</h4>
                  <p>{{ emptyDescription || 'There are no items to display.' }}</p>
                </div>
              </td>
            </tr>

            <!-- Data Rows -->
            <tr
              *ngFor="let item of paginatedData; let i = index; trackBy: trackByFn"
              class="table-row"
              [class.selected]="isSelected(item)"
              (click)="onRowClick(item)"
            >
              <td
                *ngFor="let column of columns"
                class="table-cell"
                [class]="'cell-' + column.type"
              >
                <!-- Text Content -->
                <span *ngIf="column.type === 'text' || !column.type" class="cell-text">
                  {{ column.format ? column.format(getColumnValue(item, column.key)) : getColumnValue(item, column.key) }}
                </span>

                <!-- Number Content -->
                <span *ngIf="column.type === 'number'" class="cell-number">
                  {{ column.format ? column.format(getColumnValue(item, column.key)) : (getColumnValue(item, column.key) | number) }}
                </span>

                <!-- Date Content -->
                <span *ngIf="column.type === 'date'" class="cell-date">
                  {{ column.format ? column.format(getColumnValue(item, column.key)) : (getColumnValue(item, column.key) | date:'medium') }}
                </span>

                <!-- Badge Content -->
                <span
                  *ngIf="column.type === 'badge'"
                  class="cell-badge"
                  [class]="'badge-' + getBadgeClass(getColumnValue(item, column.key), column.badgeConfig)"
                >
                  {{ getBadgeLabel(getColumnValue(item, column.key), column.badgeConfig) }}
                </span>

                <!-- Boolean Content -->
                <span *ngIf="column.type === 'boolean'" class="cell-boolean">
                  <span
                    class="boolean-indicator"
                    [class.boolean-true]="getColumnValue(item, column.key)"
                    [class.boolean-false]="!getColumnValue(item, column.key)"
                  >
                    {{ getColumnValue(item, column.key) ? 'Active' : 'Inactive' }}
                  </span>
                </span>

                <!-- Actions Content -->
                <div *ngIf="column.type === 'actions'" class="cell-actions">
                  <ng-container *ngFor="let action of getVisibleActions(item)">
                    <app-button
                      [variant]="action.variant || 'ghost'"
                      size="sm"
                      [leftIcon]="action.icon || ''"
                      (clicked)="action.action(item)"
                    >
                      {{ action.label }}
                    </app-button>
                  </ng-container>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Table Footer -->
      <div class="table-footer" *ngIf="showFooter && !loading">
        <div class="footer-info">
          <span class="item-count">
            Showing {{ startIndex + 1 }}-{{ endIndex }} of {{ filteredData.length }} items
          </span>
        </div>

        <!-- Pagination -->
        <div class="pagination" *ngIf="paginated && totalPages > 1">
          <app-button
            variant="outline"
            size="sm"
            leftIcon="M15 19l-7-7 7-7"
            [disabled]="currentPage === 1"
            (clicked)="goToPage(currentPage - 1)"
          >
            Previous
          </app-button>

          <div class="page-numbers">
            <button
              *ngFor="let page of getPageNumbers()"
              class="page-button"
              [class.active]="page === currentPage"
              [disabled]="page === '...'"
              (click)="goToPage(page)"
            >
              {{ page }}
            </button>
          </div>

          <app-button
            variant="outline"
            size="sm"
            rightIcon="M9 5l7 7-7 7"
            [disabled]="currentPage === totalPages"
            (clicked)="goToPage(currentPage + 1)"
          >
            Next
          </app-button>
        </div>
      </div>
    </div>
  `, styles: ["/* src/app/shared/components/table/table.component.scss */\n.table-container {\n  background: white;\n  border-radius: var(--radius-lg);\n  border: 1px solid var(--secondary-200);\n  overflow: hidden;\n}\n.table-header {\n  display: flex;\n  align-items: flex-start;\n  justify-content: space-between;\n  gap: var(--spacing-lg);\n  padding: var(--spacing-lg);\n  border-bottom: 1px solid var(--secondary-200);\n  background: var(--neutral-50);\n}\n.table-title-section {\n  flex: 1;\n}\n.table-title {\n  margin: 0 0 var(--spacing-xs) 0;\n  font-size: var(--text-lg);\n  font-weight: 600;\n  color: var(--secondary-900);\n}\n.table-subtitle {\n  margin: 0;\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n}\n.table-actions {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-md);\n}\n.search-container {\n  position: relative;\n  display: flex;\n  align-items: center;\n}\n.search-icon {\n  position: absolute;\n  left: var(--spacing-md);\n  width: 16px;\n  height: 16px;\n  color: var(--secondary-400);\n  z-index: 1;\n}\n.search-input {\n  width: 250px;\n  height: 36px;\n  padding: 0 var(--spacing-md) 0 40px;\n  border: 1px solid var(--secondary-200);\n  border-radius: var(--radius-md);\n  background: white;\n  font-size: var(--text-sm);\n  color: var(--secondary-800);\n  transition: all 0.2s ease;\n}\n.search-input::placeholder {\n  color: var(--secondary-400);\n}\n.search-input:focus {\n  outline: none;\n  border-color: var(--primary-500);\n  box-shadow: 0 0 0 3px var(--primary-100);\n}\n.table-wrapper {\n  overflow-x: auto;\n}\n.table {\n  width: 100%;\n  border-collapse: collapse;\n  font-size: var(--text-sm);\n}\n.table-head {\n  background: var(--neutral-50);\n  border-bottom: 1px solid var(--secondary-200);\n}\n.table-header-cell {\n  padding: var(--spacing-md) var(--spacing-lg);\n  text-align: left;\n  font-weight: 600;\n  color: var(--secondary-700);\n  border-bottom: 1px solid var(--secondary-200);\n  white-space: nowrap;\n}\n.table-header-cell.sortable {\n  cursor: pointer;\n  -webkit-user-select: none;\n  user-select: none;\n  transition: background-color 0.2s ease;\n}\n.table-header-cell.sortable:hover {\n  background-color: var(--secondary-100);\n}\n.header-content {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n}\n.sort-indicator {\n  display: flex;\n  align-items: center;\n}\n.sort-icon {\n  width: 14px;\n  height: 14px;\n  color: var(--secondary-400);\n  transition: all 0.2s ease;\n}\n.sort-icon.sort-active {\n  color: var(--primary-600);\n}\n.sort-icon.sort-desc {\n  transform: rotate(180deg);\n}\n.table-body {\n  background: white;\n}\n.table-row {\n  border-bottom: 1px solid var(--secondary-100);\n  transition: background-color 0.2s ease;\n}\n.table-row:last-child {\n  border-bottom: none;\n}\n.table-row.selected {\n  background-color: var(--primary-50);\n}\n.table.table-hover .table-row:hover {\n  background-color: var(--neutral-50);\n}\n.table.table-striped .table-row:nth-child(even) {\n  background-color: var(--neutral-25);\n}\n.table-cell {\n  padding: var(--spacing-md) var(--spacing-lg);\n  vertical-align: middle;\n  border-bottom: 1px solid var(--secondary-100);\n}\n.cell-text {\n  color: var(--secondary-800);\n}\n.cell-number {\n  color: var(--secondary-800);\n  font-variant-numeric: tabular-nums;\n}\n.cell-date {\n  color: var(--secondary-600);\n  font-size: var(--text-sm);\n}\n.cell-badge {\n  display: inline-flex;\n  align-items: center;\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border-radius: var(--radius-sm);\n  font-size: var(--text-xs);\n  font-weight: 500;\n  text-transform: uppercase;\n  letter-spacing: 0.025em;\n}\n.cell-badge.badge-default {\n  background: var(--secondary-100);\n  color: var(--secondary-700);\n}\n.cell-badge.badge-primary {\n  background: var(--primary-100);\n  color: var(--primary-700);\n}\n.cell-badge.badge-success {\n  background: var(--success-100);\n  color: var(--success-700);\n}\n.cell-badge.badge-warning {\n  background: var(--warning-100);\n  color: var(--warning-700);\n}\n.cell-badge.badge-error {\n  background: var(--error-100);\n  color: var(--error-700);\n}\n.cell-boolean {\n  display: inline-flex;\n  align-items: center;\n}\n.boolean-indicator {\n  display: inline-flex;\n  align-items: center;\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border-radius: var(--radius-sm);\n  font-size: var(--text-xs);\n  font-weight: 500;\n  text-transform: uppercase;\n  letter-spacing: 0.025em;\n}\n.boolean-indicator.boolean-true {\n  background: var(--success-100);\n  color: var(--success-700);\n}\n.boolean-indicator.boolean-false {\n  background: var(--secondary-100);\n  color: var(--secondary-700);\n}\n.cell-actions {\n  display: flex;\n  gap: var(--spacing-xs);\n  align-items: center;\n}\n.loading-row {\n  background: white;\n}\n.loading-cell {\n  padding: var(--spacing-3xl);\n  text-align: center;\n  border: none;\n}\n.loading-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: var(--spacing-md);\n  color: var(--secondary-600);\n}\n.spinner {\n  width: 32px;\n  height: 32px;\n  border: 3px solid var(--secondary-200);\n  border-top: 3px solid var(--primary-500);\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n@keyframes spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n.empty-row {\n  background: white;\n}\n.empty-cell {\n  padding: var(--spacing-3xl);\n  text-align: center;\n  border: none;\n}\n.empty-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: var(--spacing-md);\n  color: var(--secondary-500);\n}\n.empty-icon {\n  width: 48px;\n  height: 48px;\n  color: var(--secondary-300);\n}\n.empty-content h4 {\n  margin: 0;\n  font-size: var(--text-lg);\n  font-weight: 600;\n  color: var(--secondary-700);\n}\n.empty-content p {\n  margin: 0;\n  font-size: var(--text-sm);\n  color: var(--secondary-500);\n}\n.table-footer {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  gap: var(--spacing-lg);\n  padding: var(--spacing-lg);\n  border-top: 1px solid var(--secondary-200);\n  background: var(--neutral-50);\n}\n.footer-info {\n  flex: 1;\n}\n.item-count {\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n}\n.pagination {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n}\n.page-numbers {\n  display: flex;\n  gap: var(--spacing-xs);\n}\n.page-button {\n  min-width: 32px;\n  height: 32px;\n  padding: 0 var(--spacing-sm);\n  border: 1px solid var(--secondary-200);\n  background: white;\n  color: var(--secondary-700);\n  font-size: var(--text-sm);\n  border-radius: var(--radius-md);\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n.page-button:hover:not(:disabled) {\n  background: var(--secondary-50);\n  border-color: var(--secondary-300);\n}\n.page-button.active {\n  background: var(--primary-500);\n  border-color: var(--primary-500);\n  color: white;\n}\n.page-button:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n@media (max-width: 768px) {\n  .table-header {\n    flex-direction: column;\n    align-items: stretch;\n    gap: var(--spacing-md);\n  }\n  .table-actions {\n    justify-content: space-between;\n  }\n  .search-input {\n    width: 200px;\n  }\n  .table-footer {\n    flex-direction: column;\n    align-items: stretch;\n    gap: var(--spacing-md);\n  }\n  .pagination {\n    justify-content: center;\n  }\n  .table-wrapper {\n    overflow-x: scroll;\n  }\n  .table {\n    min-width: 600px;\n  }\n  .table-header-cell,\n  .table-cell {\n    padding: var(--spacing-sm) var(--spacing-md);\n  }\n}\n@media (max-width: 480px) {\n  .search-input {\n    width: 150px;\n  }\n  .page-numbers {\n    display: none;\n  }\n}\n@media print {\n  .table-container {\n    border: 1px solid var(--secondary-300);\n    box-shadow: none;\n  }\n  .table-header,\n  .table-footer {\n    background: white;\n  }\n  .search-container,\n  .pagination {\n    display: none;\n  }\n  .cell-actions {\n    display: none;\n  }\n}\n/*# sourceMappingURL=table.component.css.map */\n"] }]
  }], null, { data: [{
    type: Input
  }], columns: [{
    type: Input
  }], actions: [{
    type: Input
  }], title: [{
    type: Input
  }], subtitle: [{
    type: Input
  }], loading: [{
    type: Input
  }], searchable: [{
    type: Input
  }], sortable: [{
    type: Input
  }], paginated: [{
    type: Input
  }], pageSize: [{
    type: Input
  }], striped: [{
    type: Input
  }], hoverable: [{
    type: Input
  }], selectable: [{
    type: Input
  }], showHeader: [{
    type: Input
  }], showFooter: [{
    type: Input
  }], emptyMessage: [{
    type: Input
  }], emptyDescription: [{
    type: Input
  }], rowClick: [{
    type: Output
  }], sortChange: [{
    type: Output
  }], searchChange: [{
    type: Output
  }], pageChange: [{
    type: Output
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(TableComponent, { className: "TableComponent", filePath: "src/app/shared/components/table/table.component.ts", lineNumber: 236 });
})();

export {
  TableComponent
};
//# sourceMappingURL=chunk-QQYNLSVO.js.map
