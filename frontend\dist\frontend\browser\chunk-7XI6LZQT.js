import {
  Injectable,
  delay,
  map,
  of,
  setClassMetadata,
  ɵɵdefineInjectable
} from "./chunk-3JQLQ36P.js";

// src/app/shared/services/application-selection.service.ts
var ApplicationSelectionService = class _ApplicationSelectionService {
  constructor() {
  }
  /**
   * Get applications for selection dropdown
   */
  getApplicationOptions() {
    return this.getMockApplications().pipe(delay(300), map((apps) => apps.map((app) => ({
      id: app.id,
      name: app.name,
      description: app.description,
      owner: app.owner,
      department: app.department
    }))));
  }
  /**
   * Search applications by name
   */
  searchApplications(query) {
    return this.getApplicationOptions().pipe(map((apps) => apps.filter((app) => app.name.toLowerCase().includes(query.toLowerCase()) || app.description.toLowerCase().includes(query.toLowerCase())).slice(0, 10)));
  }
  getMockApplications() {
    return of([
      {
        id: 1,
        name: "E-Commerce Platform",
        description: "Main customer-facing e-commerce application",
        owner: "<PERSON>",
        department: "Engineering"
      },
      {
        id: 2,
        name: "Customer Portal",
        description: "Self-service customer management portal",
        owner: "<PERSON>",
        department: "Product"
      },
      {
        id: 3,
        name: "Admin Dashboard",
        description: "Internal administration and reporting dashboard",
        owner: "Mike Johnson",
        department: "Operations"
      },
      {
        id: 4,
        name: "Mobile App",
        description: "iOS and Android mobile application",
        owner: "Sarah Wilson",
        department: "Mobile"
      },
      {
        id: 5,
        name: "Analytics Service",
        description: "Data analytics and reporting microservice",
        owner: "David Brown",
        department: "Data"
      },
      {
        id: 6,
        name: "Payment Gateway",
        description: "Payment processing and transaction management",
        owner: "Lisa Davis",
        department: "FinTech"
      },
      {
        id: 7,
        name: "Inventory System",
        description: "Warehouse and inventory management system",
        owner: "Tom Wilson",
        department: "Logistics"
      },
      {
        id: 8,
        name: "CRM System",
        description: "Customer relationship management platform",
        owner: "Emily Johnson",
        department: "Sales"
      }
    ]);
  }
  static \u0275fac = function ApplicationSelectionService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ApplicationSelectionService)();
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _ApplicationSelectionService, factory: _ApplicationSelectionService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ApplicationSelectionService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [], null);
})();

export {
  ApplicationSelectionService
};
//# sourceMappingURL=chunk-7XI6LZQT.js.map
