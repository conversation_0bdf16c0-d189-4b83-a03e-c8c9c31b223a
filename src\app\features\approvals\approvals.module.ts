import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ApprovalsListComponent } from './approvals-list.component';
import { ApprovalService } from '../../core/services/approval.service';

@NgModule({
  imports: [
    CommonModule,
    ApprovalsListComponent,
    RouterModule.forChild([
      { path: '', component: ApprovalsListComponent }
    ])
  ],
  providers: [ApprovalService]
})
export class ApprovalsModule { }