.approvals-list-container {
  padding: 2rem;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);

  h2 {
    margin-bottom: 1.5rem;
    color: #2c3e50;
  }
}

.loading-spinner {
  color: #007bff;
  font-weight: 500;
  margin: 1rem 0;
  text-align: center;
}

.no-approvals {
  color: #6c757d;
  margin: 2rem 0;
  text-align: center;
  font-size: 1.1rem;

  i {
    font-size: 1.5rem;
    vertical-align: middle;
  }
}

.table {
  margin-top: 1rem;

  th {
    font-weight: 600;
    color: #495057;
  }

  td {
    vertical-align: middle;
  }

  a {
    color: #007bff;
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

.badge {
  font-size: 0.85rem;
  padding: 0.4em 0.8em;
  font-weight: 500;
  text-transform: capitalize;
}

.btn-group {
  .btn {
    min-width: 90px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    
    &:not(:last-child) {
      margin-right: 0.5rem;
    }

    i {
      font-size: 0.9rem;
    }

    &:disabled {
      cursor: not-allowed;
      opacity: 0.6;
    }
  }
}

.alert-info {
  background-color: #e3f2fd;
  border-color: #90caf9;
  color: #0d47a1;

  i {
    font-size: 1.1rem;
    vertical-align: middle;
  }
}