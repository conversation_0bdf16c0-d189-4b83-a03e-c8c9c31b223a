{"version": 3, "sources": ["src/app/shared/components/file-upload/file-upload.component.ts", "src/app/shared/components/modals/shared-documentation-modal/shared-documentation-modal.component.ts", "src/app/modules/documentation/documentation.component.ts"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit, ViewChild, ElementRef, Inject, PLATFORM_ID } from '@angular/core';\nimport { CommonModule, isPlatformBrowser } from '@angular/common';\nimport { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';\n\nexport interface FileUploadData {\n  file: File;\n  type: string;\n  description?: string;\n}\n\nexport interface UploadedDocument {\n  id?: number;\n  fileName: string;\n  fileSize: number;\n  mimeType: string;\n  type: string;\n  description?: string;\n  uploadedAt: Date;\n  uploadedBy: string;\n  icon: string;\n}\n\n@Component({\n  selector: 'app-file-upload',\n  standalone: true,\n  imports: [CommonModule, ReactiveFormsModule],\n  template: `\n    <div class=\"file-upload-container\">\n      <form [formGroup]=\"uploadForm\" class=\"upload-form\">\n        <div class=\"form-row\">\n          <div class=\"form-group\">\n            <label class=\"form-label\">Document Type</label>\n            <select formControlName=\"type\" class=\"form-select\">\n              <option value=\"\">Select document type</option>\n              <option value=\"technical_spec\">Technical Specification</option>\n              <option value=\"api_documentation\">API Documentation</option>\n              <option value=\"user_manual\">User Manual</option>\n              <option value=\"deployment_guide\">Deployment Guide</option>\n              <option value=\"architecture_diagram\">Architecture Diagram</option>\n              <option value=\"security_policy\">Security Policy</option>\n              <option value=\"compliance_report\">Compliance Report</option>\n              <option value=\"runbook\">Runbook</option>\n              <option value=\"troubleshooting\">Troubleshooting Guide</option>\n              <option value=\"changelog\">Changelog</option>\n            </select>\n          </div>\n\n          <div class=\"form-group\">\n            <label class=\"form-label\">File</label>\n            <div class=\"file-input-wrapper\">\n              <input\n                #fileInput\n                type=\"file\"\n                (change)=\"onFileSelected($event)\"\n                class=\"file-input\"\n                id=\"file-input\"\n                accept=\".pdf,.doc,.docx,.txt,.md,.png,.jpg,.jpeg,.gif,.svg,.zip,.tar,.gz\"\n              >\n              <label for=\"file-input\" class=\"file-input-label\">\n                <svg class=\"upload-icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n                  <path d=\"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4\"></path>\n                  <polyline points=\"7,10 12,15 17,10\"></polyline>\n                  <line x1=\"12\" y1=\"15\" x2=\"12\" y2=\"3\"></line>\n                </svg>\n                {{ selectedFile ? selectedFile.name : 'Choose file' }}\n              </label>\n            </div>\n          </div>\n\n          <div class=\"form-group\">\n            <button\n              type=\"button\"\n              class=\"add-button\"\n              [disabled]=\"!canAdd\"\n              (click)=\"addDocument()\"\n            >\n              Add Document\n            </button>\n          </div>\n        </div>\n\n        <div class=\"form-group full-width\" *ngIf=\"showDescription\">\n          <label class=\"form-label\">Description (Optional)</label>\n          <textarea\n            formControlName=\"description\"\n            class=\"form-textarea\"\n            placeholder=\"Brief description of the document...\"\n            rows=\"2\"\n          ></textarea>\n        </div>\n      </form>\n\n      <!-- File info display -->\n      <div class=\"file-info\" *ngIf=\"selectedFile\">\n        <div class=\"file-details\">\n          <span class=\"file-name\">{{ selectedFile.name }}</span>\n          <span class=\"file-size\">{{ formatFileSize(selectedFile.size) }}</span>\n          <span class=\"file-type\">{{ selectedFile.type || 'Unknown type' }}</span>\n        </div>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .file-upload-container {\n      display: flex;\n      flex-direction: column;\n      gap: var(--spacing-md);\n    }\n\n    .upload-form {\n      display: flex;\n      flex-direction: column;\n      gap: var(--spacing-md);\n    }\n\n    .form-row {\n      display: grid;\n      grid-template-columns: 1fr 1fr auto;\n      gap: var(--spacing-md);\n      align-items: end;\n    }\n\n    .form-group {\n      display: flex;\n      flex-direction: column;\n      gap: var(--spacing-sm);\n    }\n\n    .form-group.full-width {\n      grid-column: 1 / -1;\n    }\n\n    .form-label {\n      font-size: var(--text-sm);\n      font-weight: 500;\n      color: var(--secondary-700);\n    }\n\n    .form-select,\n    .form-textarea {\n      height: 40px;\n      padding: 0 var(--spacing-md);\n      border: 1px solid var(--secondary-300);\n      border-radius: var(--radius-md);\n      background: white;\n      font-size: var(--text-sm);\n      color: var(--secondary-800);\n      transition: all 0.2s ease;\n\n      &:focus {\n        outline: none;\n        border-color: var(--primary-500);\n        box-shadow: 0 0 0 3px var(--primary-100);\n      }\n    }\n\n    .form-textarea {\n      height: auto;\n      padding: var(--spacing-sm) var(--spacing-md);\n      resize: vertical;\n      min-height: 60px;\n    }\n\n    .file-input-wrapper {\n      position: relative;\n    }\n\n    .file-input {\n      position: absolute;\n      opacity: 0;\n      width: 100%;\n      height: 100%;\n      cursor: pointer;\n    }\n\n    .file-input-label {\n      display: flex;\n      align-items: center;\n      gap: var(--spacing-sm);\n      height: 40px;\n      padding: 0 var(--spacing-md);\n      border: 1px solid var(--secondary-300);\n      border-radius: var(--radius-md);\n      background: white;\n      font-size: var(--text-sm);\n      color: var(--secondary-600);\n      cursor: pointer;\n      transition: all 0.2s ease;\n\n      &:hover {\n        border-color: var(--primary-400);\n        background: var(--primary-50);\n      }\n    }\n\n    .upload-icon {\n      width: 16px;\n      height: 16px;\n      stroke-width: 2;\n    }\n\n    .add-button {\n      height: 40px;\n      padding: 0 var(--spacing-lg);\n      background: var(--primary-600);\n      color: white;\n      border: none;\n      border-radius: var(--radius-md);\n      font-size: var(--text-sm);\n      font-weight: 500;\n      cursor: pointer;\n      transition: all 0.2s ease;\n      white-space: nowrap;\n\n      &:hover:not(:disabled) {\n        background: var(--primary-700);\n      }\n\n      &:disabled {\n        background: var(--secondary-300);\n        cursor: not-allowed;\n      }\n    }\n\n    .file-info {\n      padding: var(--spacing-md);\n      background: var(--secondary-50);\n      border-radius: var(--radius-md);\n      border: 1px solid var(--secondary-200);\n    }\n\n    .file-details {\n      display: flex;\n      gap: var(--spacing-md);\n      align-items: center;\n      flex-wrap: wrap;\n    }\n\n    .file-name {\n      font-weight: 500;\n      color: var(--secondary-900);\n    }\n\n    .file-size,\n    .file-type {\n      font-size: var(--text-sm);\n      color: var(--secondary-600);\n    }\n\n    @media (max-width: 768px) {\n      .form-row {\n        grid-template-columns: 1fr;\n        gap: var(--spacing-sm);\n      }\n\n      .file-details {\n        flex-direction: column;\n        align-items: flex-start;\n        gap: var(--spacing-sm);\n      }\n    }\n  `]\n})\nexport class FileUploadComponent implements OnInit {\n  @Input() showDescription = false;\n  @Output() documentAdded = new EventEmitter<FileUploadData>();\n  @ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>;\n\n  uploadForm!: FormGroup;\n  selectedFile: File | null = null;\n\n  constructor(\n    private fb: FormBuilder,\n    @Inject(PLATFORM_ID) private platformId: Object\n  ) {}\n\n  ngOnInit(): void {\n    this.initializeForm();\n  }\n\n  private initializeForm(): void {\n    this.uploadForm = this.fb.group({\n      type: ['', [Validators.required]],\n      description: ['']\n    });\n  }\n\n  onFileSelected(event: Event): void {\n    const target = event.target as HTMLInputElement;\n    if (target.files && target.files.length > 0) {\n      this.selectedFile = target.files[0];\n    }\n  }\n\n  get canAdd(): boolean {\n    return (this.uploadForm.get('type')?.valid ?? false) && this.selectedFile !== null;\n  }\n\n  addDocument(): void {\n    if (this.canAdd && this.selectedFile) {\n      const formData: FileUploadData = {\n        file: this.selectedFile,\n        type: this.uploadForm.get('type')?.value,\n        description: this.uploadForm.get('description')?.value || undefined\n      };\n\n      this.documentAdded.emit(formData);\n      this.resetForm();\n    }\n  }\n\n  private resetForm(): void {\n    this.uploadForm.reset();\n    this.selectedFile = null;\n\n    // Reset file input - only in browser environment\n    if (isPlatformBrowser(this.platformId) && this.fileInput) {\n      this.fileInput.nativeElement.value = '';\n    }\n  }\n\n  formatFileSize(bytes: number): string {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n\n  getFileIcon(mimeType: string): string {\n    if (mimeType.includes('pdf')) return '📄';\n    if (mimeType.includes('word') || mimeType.includes('document')) return '📝';\n    if (mimeType.includes('image')) return '🖼️';\n    if (mimeType.includes('video')) return '🎥';\n    if (mimeType.includes('audio')) return '🎵';\n    if (mimeType.includes('zip') || mimeType.includes('archive')) return '📦';\n    if (mimeType.includes('text')) return '📄';\n    return '📁';\n  }\n}\n", "import { Component, Input, Output, EventEmitter, OnInit, OnChanges } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';\nimport { SlideModalComponent } from '../../slide-modal/slide-modal.component';\nimport { FileUploadComponent, FileUploadData, UploadedDocument } from '../../file-upload/file-upload.component';\nimport { ApplicationSelectionService, ApplicationOption } from '../../../services/application-selection.service';\nimport { Observable } from 'rxjs';\n\nexport interface SharedDocumentationFormData {\n  applicationId?: number;\n  documents: UploadedDocument[];\n}\n\n@Component({\n  selector: 'app-shared-documentation-modal',\n  standalone: true,\n  imports: [CommonModule, ReactiveFormsModule, SlideModalComponent, FileUploadComponent],\n  template: `\n    <app-slide-modal\n      [isOpen]=\"isOpen\"\n      [title]=\"editMode ? 'Edit Documentation' : 'Add Documentation'\"\n      [subtitle]=\"showApplicationSelection ? 'Upload documents and select application' : 'Upload documents'\"\n      [loading]=\"loading\"\n      [canConfirm]=\"canConfirm\"\n      confirmText=\"Save Documentation\"\n      (closed)=\"onClose()\"\n      (confirmed)=\"onSave()\"\n      (cancelled)=\"onClose()\"\n    >\n      <form [formGroup]=\"documentationForm\" class=\"documentation-form\">\n        <!-- Application Selection (only when not in applications module) -->\n        <div class=\"form-group\" *ngIf=\"showApplicationSelection\">\n          <label class=\"form-label\">Application *</label>\n          <select formControlName=\"applicationId\" class=\"form-select\">\n            <option value=\"\">Select an application</option>\n            <option *ngFor=\"let app of applicationOptions$ | async\" [value]=\"app.id\">\n              {{ app.name }} - {{ app.department }}\n            </option>\n          </select>\n          <div class=\"field-error\" *ngIf=\"getFieldError('applicationId')\">\n            {{ getFieldError('applicationId') }}\n          </div>\n        </div>\n\n        <!-- File Upload Section -->\n        <div class=\"form-section\">\n          <h4 class=\"section-title\">Upload Documents</h4>\n          <app-file-upload\n            [showDescription]=\"true\"\n            (documentAdded)=\"onDocumentAdded($event)\"\n          ></app-file-upload>\n        </div>\n\n        <!-- Uploaded Documents List -->\n        <div class=\"form-section\" *ngIf=\"uploadedDocuments.length > 0\">\n          <h4 class=\"section-title\">Uploaded Documents ({{ uploadedDocuments.length }})</h4>\n          <div class=\"documents-list\">\n            <div class=\"document-item\" *ngFor=\"let doc of uploadedDocuments; let i = index\">\n              <div class=\"document-icon\">{{ doc.icon }}</div>\n              <div class=\"document-info\">\n                <div class=\"document-header\">\n                  <span class=\"document-name\">{{ doc.fileName }}</span>\n                  <button class=\"remove-button\" (click)=\"removeDocument(i)\" type=\"button\">\n                    <svg viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n                      <polyline points=\"3,6 5,6 21,6\"></polyline>\n                      <path d=\"m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2\"></path>\n                    </svg>\n                  </button>\n                </div>\n                <div class=\"document-meta\">\n                  <span class=\"document-type\">{{ getDocumentTypeLabel(doc.type) }}</span>\n                  <span class=\"document-size\">{{ formatFileSize(doc.fileSize) }}</span>\n                  <span class=\"document-mime\">{{ doc.mimeType }}</span>\n                </div>\n                <div class=\"document-upload-info\">\n                  <span class=\"upload-date\">{{ formatDate(doc.uploadedAt) }}</span>\n                  <span class=\"uploaded-by\">by {{ doc.uploadedBy }}</span>\n                </div>\n                <div class=\"document-description\" *ngIf=\"doc.description\">\n                  {{ doc.description }}\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"empty-state\" *ngIf=\"uploadedDocuments.length === 0\">\n          <p>No documents uploaded yet. Use the upload section above to add documents.</p>\n        </div>\n      </form>\n    </app-slide-modal>\n  `,\n  styles: [`\n    .documentation-form {\n      display: flex;\n      flex-direction: column;\n      gap: var(--spacing-lg);\n    }\n\n    .form-group {\n      display: flex;\n      flex-direction: column;\n      gap: var(--spacing-sm);\n    }\n\n    .form-label {\n      font-size: var(--text-sm);\n      font-weight: 500;\n      color: var(--secondary-700);\n    }\n\n    .form-select {\n      height: 40px;\n      padding: 0 var(--spacing-md);\n      border: 1px solid var(--secondary-300);\n      border-radius: var(--radius-md);\n      background: white;\n      font-size: var(--text-sm);\n      color: var(--secondary-800);\n      transition: all 0.2s ease;\n\n      &:focus {\n        outline: none;\n        border-color: var(--primary-500);\n        box-shadow: 0 0 0 3px var(--primary-100);\n      }\n    }\n\n    .field-error {\n      font-size: var(--text-sm);\n      color: var(--error-600);\n    }\n\n    .form-section {\n      padding: var(--spacing-lg);\n      background: var(--secondary-50);\n      border-radius: var(--radius-md);\n      border: 1px solid var(--secondary-200);\n    }\n\n    .section-title {\n      margin: 0 0 var(--spacing-md) 0;\n      font-size: var(--text-lg);\n      font-weight: 600;\n      color: var(--secondary-900);\n    }\n\n    .documents-list {\n      display: flex;\n      flex-direction: column;\n      gap: var(--spacing-md);\n    }\n\n    .document-item {\n      display: flex;\n      gap: var(--spacing-md);\n      padding: var(--spacing-md);\n      background: white;\n      border-radius: var(--radius-md);\n      border: 1px solid var(--secondary-200);\n    }\n\n    .document-icon {\n      font-size: 24px;\n      width: 40px;\n      height: 40px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      background: var(--primary-100);\n      border-radius: var(--radius-md);\n    }\n\n    .document-info {\n      flex: 1;\n      display: flex;\n      flex-direction: column;\n      gap: var(--spacing-xs);\n    }\n\n    .document-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n    }\n\n    .document-name {\n      font-weight: 500;\n      color: var(--secondary-900);\n    }\n\n    .remove-button {\n      padding: var(--spacing-xs);\n      background: none;\n      border: none;\n      color: var(--error-600);\n      cursor: pointer;\n      border-radius: var(--radius-sm);\n      transition: all 0.2s ease;\n\n      &:hover {\n        background: var(--error-100);\n      }\n\n      svg {\n        width: 16px;\n        height: 16px;\n        stroke-width: 2;\n      }\n    }\n\n    .document-meta {\n      display: flex;\n      gap: var(--spacing-md);\n      font-size: var(--text-sm);\n      color: var(--secondary-600);\n    }\n\n    .document-type {\n      padding: 2px 8px;\n      background: var(--primary-100);\n      color: var(--primary-700);\n      border-radius: var(--radius-sm);\n      font-size: var(--text-xs);\n      font-weight: 500;\n    }\n\n    .document-upload-info {\n      display: flex;\n      gap: var(--spacing-sm);\n      font-size: var(--text-xs);\n      color: var(--secondary-500);\n    }\n\n    .document-description {\n      font-size: var(--text-sm);\n      color: var(--secondary-700);\n      font-style: italic;\n    }\n\n    .empty-state {\n      text-align: center;\n      padding: var(--spacing-xl);\n      color: var(--secondary-600);\n    }\n\n    @media (max-width: 768px) {\n      .document-item {\n        flex-direction: column;\n        gap: var(--spacing-sm);\n      }\n\n      .document-meta {\n        flex-direction: column;\n        gap: var(--spacing-xs);\n      }\n    }\n  `]\n})\nexport class SharedDocumentationModalComponent implements OnInit, OnChanges {\n  @Input() isOpen = false;\n  @Input() editMode = false;\n  @Input() initialData: SharedDocumentationFormData | null = null;\n  @Input() loading = false;\n  @Input() showApplicationSelection = true; // Hide when used within applications module\n\n  @Output() closed = new EventEmitter<void>();\n  @Output() saved = new EventEmitter<SharedDocumentationFormData>();\n\n  documentationForm!: FormGroup;\n  uploadedDocuments: UploadedDocument[] = [];\n  applicationOptions$: Observable<ApplicationOption[]>;\n\n  constructor(\n    private fb: FormBuilder,\n    private applicationSelectionService: ApplicationSelectionService\n  ) {\n    this.applicationOptions$ = this.applicationSelectionService.getApplicationOptions();\n  }\n\n  ngOnInit(): void {\n    this.initializeForm();\n  }\n\n  ngOnChanges(): void {\n    if (this.documentationForm && this.initialData) {\n      this.documentationForm.patchValue({\n        applicationId: this.initialData.applicationId\n      });\n      this.uploadedDocuments = [...(this.initialData.documents || [])];\n    }\n  }\n\n  private initializeForm(): void {\n    const formConfig: any = {};\n    \n    if (this.showApplicationSelection) {\n      formConfig.applicationId = ['', [Validators.required]];\n    }\n\n    this.documentationForm = this.fb.group(formConfig);\n\n    if (this.initialData) {\n      this.documentationForm.patchValue({\n        applicationId: this.initialData.applicationId\n      });\n      this.uploadedDocuments = [...(this.initialData.documents || [])];\n    }\n  }\n\n  get canConfirm(): boolean {\n    const formValid = this.documentationForm.valid;\n    const hasDocuments = this.uploadedDocuments.length > 0;\n    return formValid && hasDocuments;\n  }\n\n  onDocumentAdded(fileData: FileUploadData): void {\n    const uploadedDoc: UploadedDocument = {\n      fileName: fileData.file.name,\n      fileSize: fileData.file.size,\n      mimeType: fileData.file.type || 'application/octet-stream',\n      type: fileData.type,\n      description: fileData.description,\n      uploadedAt: new Date(),\n      uploadedBy: 'Current User', // In real app, get from auth service\n      icon: this.getFileIcon(fileData.file.type || 'application/octet-stream')\n    };\n\n    this.uploadedDocuments.push(uploadedDoc);\n  }\n\n  removeDocument(index: number): void {\n    this.uploadedDocuments.splice(index, 1);\n  }\n\n  getFieldError(fieldName: string): string {\n    const field = this.documentationForm.get(fieldName);\n    if (field && field.invalid && field.touched) {\n      if (field.errors?.['required']) {\n        return `${fieldName} is required`;\n      }\n    }\n    return '';\n  }\n\n  getDocumentTypeLabel(type: string): string {\n    const typeLabels: { [key: string]: string } = {\n      'technical_spec': 'Technical Specification',\n      'api_documentation': 'API Documentation',\n      'user_manual': 'User Manual',\n      'deployment_guide': 'Deployment Guide',\n      'architecture_diagram': 'Architecture Diagram',\n      'security_policy': 'Security Policy',\n      'compliance_report': 'Compliance Report',\n      'runbook': 'Runbook',\n      'troubleshooting': 'Troubleshooting Guide',\n      'changelog': 'Changelog'\n    };\n    return typeLabels[type] || type;\n  }\n\n  formatFileSize(bytes: number): string {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n\n  formatDate(date: Date): string {\n    return new Intl.DateTimeFormat('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    }).format(date);\n  }\n\n  getFileIcon(mimeType: string): string {\n    if (mimeType.includes('pdf')) return '📄';\n    if (mimeType.includes('word') || mimeType.includes('document')) return '📝';\n    if (mimeType.includes('image')) return '🖼️';\n    if (mimeType.includes('video')) return '🎥';\n    if (mimeType.includes('audio')) return '🎵';\n    if (mimeType.includes('zip') || mimeType.includes('archive')) return '📦';\n    if (mimeType.includes('text')) return '📄';\n    return '📁';\n  }\n\n  onSave(): void {\n    if (this.canConfirm) {\n      const formData: SharedDocumentationFormData = {\n        documents: this.uploadedDocuments\n      };\n\n      if (this.showApplicationSelection) {\n        formData.applicationId = this.documentationForm.get('applicationId')?.value;\n      }\n\n      this.saved.emit(formData);\n    }\n  }\n\n  onClose(): void {\n    this.closed.emit();\n  }\n}\n", "import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { ReactiveFormsModule, FormBuilder } from '@angular/forms';\nimport { CardComponent } from '../../shared/components/card/card.component';\nimport { ButtonComponent } from '../../shared/components/button/button.component';\nimport { TableComponent, TableColumn, TableAction } from '../../shared/components/table/table.component';\nimport { ChartComponent } from '../../shared/components/chart/chart.component';\nimport { SharedDocumentationModalComponent, SharedDocumentationFormData } from '../../shared/components/modals/shared-documentation-modal/shared-documentation-modal.component';\n\ninterface DocumentationItem {\n  id: number;\n  title: string;\n  type: string;\n  category: string;\n  status: string;\n  application: string;\n  author: string;\n  createdDate: Date;\n  lastModified: Date;\n  version: string;\n  url?: string;\n  description: string;\n  tags: string[];\n}\n\n@Component({\n  selector: 'app-documentation',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    ReactiveFormsModule,\n    CardComponent,\n    ButtonComponent,\n    TableComponent,\n    ChartComponent,\n    SharedDocumentationModalComponent\n  ],\n  template: `\n    <div class=\"documentation-page\">\n      <div class=\"page-content\">\n        <div class=\"page-header\">\n          <div class=\"header-content\">\n            <h1>Documentation</h1>\n            <p class=\"page-subtitle\">Manage project documentation and resources</p>\n          </div>\n          <div class=\"header-actions\">\n            <app-button\n              variant=\"primary\"\n              leftIcon=\"M12 4v16m8-8H4\"\n              (clicked)=\"openAddModal()\"\n            >\n              Add Document\n            </app-button>\n          </div>\n        </div>\n\n      <!-- Statistics Cards -->\n      <div class=\"stats-grid\">\n        <app-card title=\"Total Documents\">\n          <div class=\"stat-content\">\n            <div class=\"stat-number\">{{ stats.total }}</div>\n            <div class=\"stat-change\">Across {{ stats.categories }} categories</div>\n          </div>\n        </app-card>\n\n        <app-card title=\"Recent Updates\">\n          <div class=\"stat-content\">\n            <div class=\"stat-number recent\">{{ stats.recentUpdates }}</div>\n            <div class=\"stat-change\">Updated this week</div>\n          </div>\n        </app-card>\n\n        <app-card title=\"Outdated Docs\">\n          <div class=\"stat-content\">\n            <div class=\"stat-number warning\">{{ stats.outdated }}</div>\n            <div class=\"stat-change\">Need review</div>\n          </div>\n        </app-card>\n\n        <app-card title=\"External Links\">\n          <div class=\"stat-content\">\n            <div class=\"stat-number\">{{ stats.externalLinks }}</div>\n            <div class=\"stat-change\">External resources</div>\n          </div>\n        </app-card>\n      </div>\n\n      <!-- Charts Section -->\n      <div class=\"charts-section\">\n        <app-card title=\"Documents by Category\" subtitle=\"Distribution across categories\">\n          <app-chart\n            type=\"doughnut\"\n            [data]=\"categoryChartData\"\n            [options]=\"chartOptions\"\n            height=\"300px\"\n          ></app-chart>\n        </app-card>\n\n        <app-card title=\"Document Types\" subtitle=\"Distribution by document type\">\n          <app-chart\n            type=\"bar\"\n            [data]=\"typeChartData\"\n            [options]=\"chartOptions\"\n            height=\"300px\"\n          ></app-chart>\n        </app-card>\n      </div>\n\n      <!-- Documentation Table -->\n      <app-card title=\"Documentation Library\" subtitle=\"All project documentation\">\n        <div class=\"table-controls\">\n          <div class=\"search-controls\">\n            <input\n              type=\"text\"\n              placeholder=\"Search documentation...\"\n              class=\"search-input\"\n              (input)=\"onSearchInput($event)\"\n            >\n            <select class=\"filter-select\" (change)=\"onFilterChanged('category', $event)\">\n              <option value=\"\">All Categories</option>\n              <option value=\"api\">API Documentation</option>\n              <option value=\"user_guide\">User Guide</option>\n              <option value=\"technical\">Technical Specs</option>\n              <option value=\"architecture\">Architecture</option>\n              <option value=\"deployment\">Deployment</option>\n              <option value=\"troubleshooting\">Troubleshooting</option>\n            </select>\n            <select class=\"filter-select\" (change)=\"onFilterChanged('type', $event)\">\n              <option value=\"\">All Types</option>\n              <option value=\"document\">Document</option>\n              <option value=\"link\">External Link</option>\n              <option value=\"wiki\">Wiki Page</option>\n              <option value=\"video\">Video</option>\n              <option value=\"tutorial\">Tutorial</option>\n            </select>\n            <select class=\"filter-select\" (change)=\"onFilterChanged('status', $event)\">\n              <option value=\"\">All Statuses</option>\n              <option value=\"current\">Current</option>\n              <option value=\"outdated\">Outdated</option>\n              <option value=\"draft\">Draft</option>\n              <option value=\"archived\">Archived</option>\n            </select>\n          </div>\n        </div>\n\n        <app-table\n          [columns]=\"tableColumns\"\n          [data]=\"filteredDocumentation\"\n          [actions]=\"tableActions\"\n          [loading]=\"loading\"\n          [sortable]=\"true\"\n          (sortChanged)=\"onSortChanged($event)\"\n        ></app-table>\n      </app-card>\n\n      <!-- Documentation Modal -->\n      <app-shared-documentation-modal\n        [isOpen]=\"showDocumentationModal\"\n        [editMode]=\"editMode\"\n        [initialData]=\"editData\"\n        [loading]=\"modalLoading\"\n        [showApplicationSelection]=\"true\"\n        (closed)=\"closeModal()\"\n        (saved)=\"onDocumentationSaved($event)\"\n      ></app-shared-documentation-modal>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .documentation-page {\n      min-height: 100%;\n    }\n\n    .page-content {\n      padding: var(--spacing-xl);\n    }\n\n    .page-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: flex-start;\n      margin-bottom: var(--spacing-xl);\n    }\n\n    .header-content h1 {\n      margin: 0 0 var(--spacing-sm) 0;\n      font-size: var(--text-3xl);\n      font-weight: 700;\n      color: var(--secondary-900);\n    }\n\n    .page-subtitle {\n      margin: 0;\n      font-size: var(--text-lg);\n      color: var(--secondary-600);\n    }\n\n    .stats-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n      gap: var(--spacing-lg);\n      margin-bottom: var(--spacing-xl);\n    }\n\n    .stat-content {\n      display: flex;\n      flex-direction: column;\n      gap: var(--spacing-sm);\n      padding: var(--spacing-lg);\n    }\n\n    .stat-number {\n      font-size: var(--text-3xl);\n      font-weight: 700;\n      color: var(--secondary-900);\n\n      &.recent {\n        color: var(--primary-600);\n      }\n\n      &.warning {\n        color: var(--warning-600);\n      }\n    }\n\n    .stat-change {\n      font-size: var(--text-sm);\n      color: var(--secondary-600);\n    }\n\n    .charts-section {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n      gap: var(--spacing-lg);\n      margin-bottom: var(--spacing-xl);\n    }\n\n    .table-controls {\n      margin-bottom: var(--spacing-lg);\n    }\n\n    .search-controls {\n      display: flex;\n      gap: var(--spacing-md);\n      align-items: center;\n      flex-wrap: wrap;\n    }\n\n    .search-input,\n    .filter-select {\n      height: 40px;\n      padding: 0 var(--spacing-md);\n      border: 1px solid var(--secondary-300);\n      border-radius: var(--radius-md);\n      background: white;\n      font-size: var(--text-sm);\n      color: var(--secondary-800);\n      transition: all 0.2s ease;\n\n      &:focus {\n        outline: none;\n        border-color: var(--primary-500);\n        box-shadow: 0 0 0 3px var(--primary-100);\n      }\n    }\n\n    .search-input {\n      flex: 1;\n      min-width: 200px;\n    }\n\n    .filter-select {\n      min-width: 120px;\n      cursor: pointer;\n      background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e\");\n      background-position: right var(--spacing-sm) center;\n      background-repeat: no-repeat;\n      background-size: 16px;\n      padding-right: 40px;\n      appearance: none;\n    }\n\n    @media (max-width: 768px) {\n      .page-header {\n        flex-direction: column;\n        gap: var(--spacing-md);\n        align-items: stretch;\n      }\n\n      .stats-grid {\n        grid-template-columns: 1fr;\n        gap: var(--spacing-md);\n      }\n\n      .charts-section {\n        grid-template-columns: 1fr;\n        gap: var(--spacing-md);\n      }\n\n      .search-controls {\n        flex-direction: column;\n        align-items: stretch;\n      }\n\n      .search-input,\n      .filter-select {\n        min-width: auto;\n      }\n    }\n  `]\n})\nexport class DocumentationComponent implements OnInit {\n  documentation: DocumentationItem[] = [];\n  filteredDocumentation: DocumentationItem[] = [];\n  loading = false;\n\n  // Modal state\n  showDocumentationModal = false;\n  editMode = false;\n  editData: SharedDocumentationFormData | null = null;\n  modalLoading = false;\n\n  // Statistics\n  stats = {\n    total: 0,\n    recentUpdates: 0,\n    outdated: 0,\n    categories: 0,\n    externalLinks: 0\n  };\n\n  // Chart data\n  categoryChartData: any = null;\n  typeChartData: any = null;\n  chartOptions = {\n    responsive: true,\n    maintainAspectRatio: false\n  };\n\n  // Table configuration\n  tableColumns: TableColumn[] = [\n    { key: 'title', label: 'Title', sortable: true },\n    { key: 'type', label: 'Type', type: 'badge', sortable: true },\n    { key: 'category', label: 'Category', type: 'badge', sortable: true },\n    { key: 'status', label: 'Status', type: 'badge', sortable: true },\n    { key: 'author', label: 'Author', sortable: true },\n    { key: 'version', label: 'Version', sortable: true },\n    { key: 'lastModified', label: 'Last Modified', type: 'date', sortable: true }\n  ];\n\n  tableActions: TableAction[] = [\n    {\n      label: 'View',\n      icon: 'M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7Z',\n      variant: 'ghost',\n      action: (item) => this.viewDocument(item)\n    },\n    {\n      label: 'Edit',\n      icon: 'M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7',\n      variant: 'ghost',\n      action: (item) => this.editDocument(item)\n    },\n    {\n      label: 'Delete',\n      icon: 'M3 6h18m-2 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2',\n      variant: 'error',\n      action: (item) => this.deleteDocument(item)\n    }\n  ];\n\n  constructor(private fb: FormBuilder) {}\n\n  ngOnInit(): void {\n    this.loadDocumentation();\n  }\n\n  private loadDocumentation(): void {\n    this.loading = true;\n\n    // Mock data - replace with actual API call\n    setTimeout(() => {\n      this.documentation = this.generateMockDocumentation();\n      this.filteredDocumentation = [...this.documentation];\n      this.updateStats();\n      this.updateChartData();\n      this.loading = false;\n    }, 1000);\n  }\n\n  private generateMockDocumentation(): DocumentationItem[] {\n    const types = ['document', 'link', 'wiki', 'video', 'tutorial'];\n    const categories = ['api', 'user_guide', 'technical', 'architecture', 'deployment', 'troubleshooting'];\n    const statuses = ['current', 'outdated', 'draft', 'archived'];\n    const applications = ['App 1', 'App 2', 'App 3', 'App 4', 'App 5'];\n\n    return Array.from({ length: 35 }, (_, i) => ({\n      id: i + 1,\n      title: `Documentation ${i + 1}`,\n      type: types[Math.floor(Math.random() * types.length)],\n      category: categories[Math.floor(Math.random() * categories.length)],\n      status: statuses[Math.floor(Math.random() * statuses.length)],\n      application: applications[Math.floor(Math.random() * applications.length)],\n      author: `Author ${Math.floor(Math.random() * 10) + 1}`,\n      createdDate: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000),\n      lastModified: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),\n      version: `${Math.floor(Math.random() * 3) + 1}.${Math.floor(Math.random() * 10)}`,\n      url: Math.random() > 0.5 ? `https://docs.example.com/doc-${i + 1}` : undefined,\n      description: `Description for documentation ${i + 1}`,\n      tags: [`tag${Math.floor(Math.random() * 5) + 1}`, `tag${Math.floor(Math.random() * 5) + 6}`]\n    }));\n  }\n\n  private updateStats(): void {\n    this.stats.total = this.documentation.length;\n    this.stats.recentUpdates = this.documentation.filter(d =>\n      new Date(d.lastModified).getTime() > Date.now() - 7 * 24 * 60 * 60 * 1000\n    ).length;\n    this.stats.outdated = this.documentation.filter(d => d.status === 'outdated').length;\n    this.stats.categories = new Set(this.documentation.map(d => d.category)).size;\n    this.stats.externalLinks = this.documentation.filter(d => d.type === 'link').length;\n  }\n\n  private updateChartData(): void {\n    // Category distribution chart\n    const categoryCount = this.documentation.reduce((acc, doc) => {\n      acc[doc.category] = (acc[doc.category] || 0) + 1;\n      return acc;\n    }, {} as any);\n\n    this.categoryChartData = {\n      labels: Object.keys(categoryCount),\n      datasets: [{\n        data: Object.values(categoryCount),\n        backgroundColor: [\n          '#0ea5e9', '#22c55e', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'\n        ]\n      }]\n    };\n\n    // Type distribution chart\n    const typeCount = this.documentation.reduce((acc, doc) => {\n      acc[doc.type] = (acc[doc.type] || 0) + 1;\n      return acc;\n    }, {} as any);\n\n    this.typeChartData = {\n      labels: Object.keys(typeCount),\n      datasets: [{\n        label: 'Documents',\n        data: Object.values(typeCount),\n        backgroundColor: ['#22c55e', '#0ea5e9', '#f59e0b', '#ef4444', '#8b5cf6']\n      }]\n    };\n  }\n\n  openAddModal(): void {\n    this.editMode = false;\n    this.editData = null;\n    this.showDocumentationModal = true;\n  }\n\n  closeModal(): void {\n    this.showDocumentationModal = false;\n    this.editMode = false;\n    this.editData = null;\n  }\n\n  onDocumentationSaved(data: SharedDocumentationFormData): void {\n    this.modalLoading = true;\n\n    // Simulate API call\n    setTimeout(() => {\n      console.log('Documentation saved:', data);\n      this.modalLoading = false;\n      this.closeModal();\n\n      // In a real app, you would refresh the documentation list here\n      this.loadDocumentation();\n    }, 1000);\n  }\n\n  viewDocument(item: DocumentationItem): void {\n    if (item.url) {\n      window.open(item.url, '_blank');\n    } else {\n      console.log('View document:', item);\n    }\n  }\n\n  editDocument(item: DocumentationItem): void {\n    // Implement edit functionality\n    console.log('Edit document:', item);\n  }\n\n  deleteDocument(item: DocumentationItem): void {\n    if (confirm(`Are you sure you want to delete ${item.title}?`)) {\n      this.documentation = this.documentation.filter(d => d.id !== item.id);\n      this.filteredDocumentation = this.filteredDocumentation.filter(d => d.id !== item.id);\n      this.updateStats();\n      this.updateChartData();\n    }\n  }\n\n  onSearchInput(event: Event): void {\n    const target = event.target as HTMLInputElement;\n    this.onSearchChanged(target.value);\n  }\n\n  onSearchChanged(searchTerm: string): void {\n    this.applyFilters();\n  }\n\n  onFilterChanged(filterType: string, event: Event): void {\n    const target = event.target as HTMLSelectElement;\n    // Apply filter logic here\n    this.applyFilters();\n  }\n\n  onSortChanged(sort: any): void {\n    // Implement sorting logic\n    console.log('Sort changed:', sort);\n  }\n\n  private applyFilters(): void {\n    // Implement filtering logic\n    this.filteredDocumentation = [...this.documentation];\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiFQ,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA2D,GAAA,SAAA,CAAA;AAC/B,IAAA,iBAAA,GAAA,wBAAA;AAAsB,IAAA,uBAAA;AAChD,IAAA,oBAAA,GAAA,YAAA,EAAA;AAMF,IAAA,uBAAA;;;;;AAIF,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA4C,GAAA,OAAA,EAAA,EAChB,GAAA,QAAA,EAAA;AACA,IAAA,iBAAA,CAAA;AAAuB,IAAA,uBAAA;AAC/C,IAAA,yBAAA,GAAA,QAAA,EAAA;AAAwB,IAAA,iBAAA,CAAA;AAAuC,IAAA,uBAAA;AAC/D,IAAA,yBAAA,GAAA,QAAA,EAAA;AAAwB,IAAA,iBAAA,CAAA;AAAyC,IAAA,uBAAA,EAAO,EACpE;;;;AAHoB,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,aAAA,IAAA;AACA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,eAAA,OAAA,aAAA,IAAA,CAAA;AACA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,aAAA,QAAA,cAAA;;;AAsK5B,IAAO,sBAAP,MAAO,qBAAmB;EASpB;EACqB;EATtB,kBAAkB;EACjB,gBAAgB,IAAI,aAAY;EAClB;EAExB;EACA,eAA4B;EAE5B,YACU,IACqB,YAAkB;AADvC,SAAA,KAAA;AACqB,SAAA,aAAA;EAC5B;EAEH,WAAQ;AACN,SAAK,eAAc;EACrB;EAEQ,iBAAc;AACpB,SAAK,aAAa,KAAK,GAAG,MAAM;MAC9B,MAAM,CAAC,IAAI,CAAC,WAAW,QAAQ,CAAC;MAChC,aAAa,CAAC,EAAE;KACjB;EACH;EAEA,eAAe,OAAY;AACzB,UAAM,SAAS,MAAM;AACrB,QAAI,OAAO,SAAS,OAAO,MAAM,SAAS,GAAG;AAC3C,WAAK,eAAe,OAAO,MAAM,CAAC;IACpC;EACF;EAEA,IAAI,SAAM;AACR,YAAQ,KAAK,WAAW,IAAI,MAAM,GAAG,SAAS,UAAU,KAAK,iBAAiB;EAChF;EAEA,cAAW;AACT,QAAI,KAAK,UAAU,KAAK,cAAc;AACpC,YAAM,WAA2B;QAC/B,MAAM,KAAK;QACX,MAAM,KAAK,WAAW,IAAI,MAAM,GAAG;QACnC,aAAa,KAAK,WAAW,IAAI,aAAa,GAAG,SAAS;;AAG5D,WAAK,cAAc,KAAK,QAAQ;AAChC,WAAK,UAAS;IAChB;EACF;EAEQ,YAAS;AACf,SAAK,WAAW,MAAK;AACrB,SAAK,eAAe;AAGpB,QAAI,kBAAkB,KAAK,UAAU,KAAK,KAAK,WAAW;AACxD,WAAK,UAAU,cAAc,QAAQ;IACvC;EACF;EAEA,eAAe,OAAa;AAC1B,QAAI,UAAU;AAAG,aAAO;AACxB,UAAM,IAAI;AACV,UAAM,QAAQ,CAAC,SAAS,MAAM,MAAM,IAAI;AACxC,UAAM,IAAI,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,CAAC;AAClD,WAAO,YAAY,QAAQ,KAAK,IAAI,GAAG,CAAC,GAAG,QAAQ,CAAC,CAAC,IAAI,MAAM,MAAM,CAAC;EACxE;EAEA,YAAY,UAAgB;AAC1B,QAAI,SAAS,SAAS,KAAK;AAAG,aAAO;AACrC,QAAI,SAAS,SAAS,MAAM,KAAK,SAAS,SAAS,UAAU;AAAG,aAAO;AACvE,QAAI,SAAS,SAAS,OAAO;AAAG,aAAO;AACvC,QAAI,SAAS,SAAS,OAAO;AAAG,aAAO;AACvC,QAAI,SAAS,SAAS,OAAO;AAAG,aAAO;AACvC,QAAI,SAAS,SAAS,KAAK,KAAK,SAAS,SAAS,SAAS;AAAG,aAAO;AACrE,QAAI,SAAS,SAAS,MAAM;AAAG,aAAO;AACtC,WAAO;EACT;;qCA3EW,sBAAmB,4BAAA,WAAA,GAAA,4BAUpB,WAAW,CAAA;EAAA;yEAVV,sBAAmB,WAAA,CAAA,CAAA,iBAAA,CAAA,GAAA,WAAA,SAAA,0BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;;;;;;;;;;AA5O5B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAmC,GAAA,QAAA,CAAA,EACkB,GAAA,OAAA,CAAA,EAC3B,GAAA,OAAA,CAAA,EACI,GAAA,SAAA,CAAA;AACI,MAAA,iBAAA,GAAA,eAAA;AAAa,MAAA,uBAAA;AACvC,MAAA,yBAAA,GAAA,UAAA,CAAA,EAAmD,GAAA,UAAA,CAAA;AAChC,MAAA,iBAAA,GAAA,sBAAA;AAAoB,MAAA,uBAAA;AACrC,MAAA,yBAAA,GAAA,UAAA,CAAA;AAA+B,MAAA,iBAAA,IAAA,yBAAA;AAAuB,MAAA,uBAAA;AACtD,MAAA,yBAAA,IAAA,UAAA,CAAA;AAAkC,MAAA,iBAAA,IAAA,mBAAA;AAAiB,MAAA,uBAAA;AACnD,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA4B,MAAA,iBAAA,IAAA,aAAA;AAAW,MAAA,uBAAA;AACvC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAiC,MAAA,iBAAA,IAAA,kBAAA;AAAgB,MAAA,uBAAA;AACjD,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAqC,MAAA,iBAAA,IAAA,sBAAA;AAAoB,MAAA,uBAAA;AACzD,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAgC,MAAA,iBAAA,IAAA,iBAAA;AAAe,MAAA,uBAAA;AAC/C,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAkC,MAAA,iBAAA,IAAA,mBAAA;AAAiB,MAAA,uBAAA;AACnD,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAwB,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA;AAC/B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAgC,MAAA,iBAAA,IAAA,uBAAA;AAAqB,MAAA,uBAAA;AACrD,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA0B,MAAA,iBAAA,IAAA,WAAA;AAAS,MAAA,uBAAA,EAAS,EACrC;AAGX,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,SAAA,CAAA;AACI,MAAA,iBAAA,IAAA,MAAA;AAAI,MAAA,uBAAA;AAC9B,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAgC,IAAA,SAAA,IAAA,CAAA;AAI5B,MAAA,qBAAA,UAAA,SAAA,sDAAA,QAAA;AAAA,QAAA,wBAAA,GAAA;AAAA,eAAA,sBAAU,IAAA,eAAA,MAAA,CAAsB;MAAA,CAAA;AAHlC,MAAA,uBAAA;AAQA,MAAA,yBAAA,IAAA,SAAA,EAAA;;AACE,MAAA,yBAAA,IAAA,OAAA,EAAA;AACE,MAAA,oBAAA,IAAA,QAAA,EAAA,EAA2D,IAAA,YAAA,EAAA,EACZ,IAAA,QAAA,EAAA;AAEjD,MAAA,uBAAA;AACA,MAAA,iBAAA,EAAA;AACF,MAAA,uBAAA,EAAQ,EACJ;;AAGR,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,UAAA,EAAA;AAKpB,MAAA,qBAAA,SAAA,SAAA,wDAAA;AAAA,QAAA,wBAAA,GAAA;AAAA,eAAA,sBAAS,IAAA,YAAA,CAAa;MAAA,CAAA;AAEtB,MAAA,iBAAA,IAAA,gBAAA;AACF,MAAA,uBAAA,EAAS,EACL;AAGR,MAAA,qBAAA,IAAA,qCAAA,GAAA,GAAA,OAAA,EAAA;AASF,MAAA,uBAAA;AAGA,MAAA,qBAAA,IAAA,qCAAA,GAAA,GAAA,OAAA,EAAA;AAOF,MAAA,uBAAA;;;AAxEQ,MAAA,oBAAA;AAAA,MAAA,qBAAA,aAAA,IAAA,UAAA;AAoCI,MAAA,oBAAA,EAAA;AAAA,MAAA,6BAAA,KAAA,IAAA,eAAA,IAAA,aAAA,OAAA,eAAA,GAAA;AASF,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,YAAA,CAAA,IAAA,MAAA;AAQ8B,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,eAAA;AAYd,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,YAAA;;oBApElB,cAAY,MAAE,qBAAmB,oBAAA,gBAAA,8BAAA,sBAAA,4BAAA,iBAAA,sBAAA,oBAAA,eAAA,GAAA,QAAA,CAAA,m5GAAA,EAAA,CAAA;;;sEA8OhC,qBAAmB,CAAA;UAjP/B;uBACW,mBAAiB,YACf,MAAI,SACP,CAAC,cAAc,mBAAmB,GAAC,UAClC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA2ET,QAAA,CAAA,0oGAAA,EAAA,CAAA;;UA4KE;WAAO,WAAW;WATZ,iBAAe,CAAA;UAAvB;MACS,eAAa,CAAA;UAAtB;MACuB,WAAS,CAAA;UAAhC;WAAU,WAAW;;;;6EAHX,qBAAmB,EAAA,WAAA,uBAAA,UAAA,kEAAA,YAAA,IAAA,CAAA;AAAA,GAAA;;;;;ACpOpB,IAAA,yBAAA,GAAA,UAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AAFwD,IAAA,qBAAA,SAAA,OAAA,EAAA;AACtD,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,MAAA,OAAA,OAAA,YAAA,GAAA;;;;;AAGJ,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,cAAA,eAAA,GAAA,GAAA;;;;;AATJ,IAAA,yBAAA,GAAA,OAAA,CAAA,EAAyD,GAAA,SAAA,CAAA;AAC7B,IAAA,iBAAA,GAAA,eAAA;AAAa,IAAA,uBAAA;AACvC,IAAA,yBAAA,GAAA,UAAA,EAAA,EAA4D,GAAA,UAAA,EAAA;AACzC,IAAA,iBAAA,GAAA,uBAAA;AAAqB,IAAA,uBAAA;AACtC,IAAA,qBAAA,GAAA,2DAAA,GAAA,GAAA,UAAA,EAAA;;AAGF,IAAA,uBAAA;AACA,IAAA,qBAAA,GAAA,wDAAA,GAAA,GAAA,OAAA,EAAA;AAGF,IAAA,uBAAA;;;;AAP4B,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,sBAAA,GAAA,GAAA,OAAA,mBAAA,CAAA;AAIA,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,cAAA,eAAA,CAAA;;;;;AAuCpB,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,aAAA,GAAA;;;;;;AAtBN,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAgF,GAAA,OAAA,EAAA;AACnD,IAAA,iBAAA,CAAA;AAAc,IAAA,uBAAA;AACzC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA2B,GAAA,OAAA,EAAA,EACI,GAAA,QAAA,EAAA;AACC,IAAA,iBAAA,CAAA;AAAkB,IAAA,uBAAA;AAC9C,IAAA,yBAAA,GAAA,UAAA,EAAA;AAA8B,IAAA,qBAAA,SAAA,SAAA,iFAAA;AAAA,YAAA,OAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,eAAA,IAAA,CAAiB;IAAA,CAAA;;AACtD,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,YAAA,EAAA,EAA2C,IAAA,QAAA,EAAA;AAE7C,IAAA,uBAAA,EAAM,EACC;;AAEX,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA2B,IAAA,QAAA,EAAA;AACG,IAAA,iBAAA,EAAA;AAAoC,IAAA,uBAAA;AAChE,IAAA,yBAAA,IAAA,QAAA,EAAA;AAA4B,IAAA,iBAAA,EAAA;AAAkC,IAAA,uBAAA;AAC9D,IAAA,yBAAA,IAAA,QAAA,EAAA;AAA4B,IAAA,iBAAA,EAAA;AAAkB,IAAA,uBAAA,EAAO;AAEvD,IAAA,yBAAA,IAAA,OAAA,EAAA,EAAkC,IAAA,QAAA,EAAA;AACN,IAAA,iBAAA,EAAA;AAAgC,IAAA,uBAAA;AAC1D,IAAA,yBAAA,IAAA,QAAA,EAAA;AAA0B,IAAA,iBAAA,EAAA;AAAuB,IAAA,uBAAA,EAAO;AAE1D,IAAA,qBAAA,IAAA,+DAAA,GAAA,GAAA,OAAA,EAAA;AAGF,IAAA,uBAAA,EAAM;;;;;AAvBqB,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,IAAA;AAGK,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,QAAA;AASA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,qBAAA,OAAA,IAAA,CAAA;AACA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,eAAA,OAAA,QAAA,CAAA;AACA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,QAAA;AAGF,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,WAAA,OAAA,UAAA,CAAA;AACA,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,OAAA,OAAA,YAAA,EAAA;AAEO,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,WAAA;;;;;AAxB3C,IAAA,yBAAA,GAAA,OAAA,CAAA,EAA+D,GAAA,MAAA,CAAA;AACnC,IAAA,iBAAA,CAAA;AAAmD,IAAA,uBAAA;AAC7E,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,wDAAA,IAAA,GAAA,OAAA,EAAA;AA0BF,IAAA,uBAAA,EAAM;;;;AA5BoB,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,wBAAA,OAAA,kBAAA,QAAA,GAAA;AAEmB,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,iBAAA;;;;;AA6B/C,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAgE,GAAA,GAAA;AAC3D,IAAA,iBAAA,GAAA,2EAAA;AAAyE,IAAA,uBAAA,EAAI;;;AA4KpF,IAAO,oCAAP,MAAO,mCAAiC;EAelC;EACA;EAfD,SAAS;EACT,WAAW;EACX,cAAkD;EAClD,UAAU;EACV,2BAA2B;;EAE1B,SAAS,IAAI,aAAY;EACzB,QAAQ,IAAI,aAAY;EAElC;EACA,oBAAwC,CAAA;EACxC;EAEA,YACU,IACA,6BAAwD;AADxD,SAAA,KAAA;AACA,SAAA,8BAAA;AAER,SAAK,sBAAsB,KAAK,4BAA4B,sBAAqB;EACnF;EAEA,WAAQ;AACN,SAAK,eAAc;EACrB;EAEA,cAAW;AACT,QAAI,KAAK,qBAAqB,KAAK,aAAa;AAC9C,WAAK,kBAAkB,WAAW;QAChC,eAAe,KAAK,YAAY;OACjC;AACD,WAAK,oBAAoB,CAAC,GAAI,KAAK,YAAY,aAAa,CAAA,CAAG;IACjE;EACF;EAEQ,iBAAc;AACpB,UAAM,aAAkB,CAAA;AAExB,QAAI,KAAK,0BAA0B;AACjC,iBAAW,gBAAgB,CAAC,IAAI,CAAC,WAAW,QAAQ,CAAC;IACvD;AAEA,SAAK,oBAAoB,KAAK,GAAG,MAAM,UAAU;AAEjD,QAAI,KAAK,aAAa;AACpB,WAAK,kBAAkB,WAAW;QAChC,eAAe,KAAK,YAAY;OACjC;AACD,WAAK,oBAAoB,CAAC,GAAI,KAAK,YAAY,aAAa,CAAA,CAAG;IACjE;EACF;EAEA,IAAI,aAAU;AACZ,UAAM,YAAY,KAAK,kBAAkB;AACzC,UAAM,eAAe,KAAK,kBAAkB,SAAS;AACrD,WAAO,aAAa;EACtB;EAEA,gBAAgB,UAAwB;AACtC,UAAM,cAAgC;MACpC,UAAU,SAAS,KAAK;MACxB,UAAU,SAAS,KAAK;MACxB,UAAU,SAAS,KAAK,QAAQ;MAChC,MAAM,SAAS;MACf,aAAa,SAAS;MACtB,YAAY,oBAAI,KAAI;MACpB,YAAY;;MACZ,MAAM,KAAK,YAAY,SAAS,KAAK,QAAQ,0BAA0B;;AAGzE,SAAK,kBAAkB,KAAK,WAAW;EACzC;EAEA,eAAe,OAAa;AAC1B,SAAK,kBAAkB,OAAO,OAAO,CAAC;EACxC;EAEA,cAAc,WAAiB;AAC7B,UAAM,QAAQ,KAAK,kBAAkB,IAAI,SAAS;AAClD,QAAI,SAAS,MAAM,WAAW,MAAM,SAAS;AAC3C,UAAI,MAAM,SAAS,UAAU,GAAG;AAC9B,eAAO,GAAG,SAAS;MACrB;IACF;AACA,WAAO;EACT;EAEA,qBAAqB,MAAY;AAC/B,UAAM,aAAwC;MAC5C,kBAAkB;MAClB,qBAAqB;MACrB,eAAe;MACf,oBAAoB;MACpB,wBAAwB;MACxB,mBAAmB;MACnB,qBAAqB;MACrB,WAAW;MACX,mBAAmB;MACnB,aAAa;;AAEf,WAAO,WAAW,IAAI,KAAK;EAC7B;EAEA,eAAe,OAAa;AAC1B,QAAI,UAAU;AAAG,aAAO;AACxB,UAAM,IAAI;AACV,UAAM,QAAQ,CAAC,SAAS,MAAM,MAAM,IAAI;AACxC,UAAM,IAAI,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,CAAC,CAAC;AAClD,WAAO,YAAY,QAAQ,KAAK,IAAI,GAAG,CAAC,GAAG,QAAQ,CAAC,CAAC,IAAI,MAAM,MAAM,CAAC;EACxE;EAEA,WAAW,MAAU;AACnB,WAAO,IAAI,KAAK,eAAe,SAAS;MACtC,MAAM;MACN,OAAO;MACP,KAAK;MACL,MAAM;MACN,QAAQ;KACT,EAAE,OAAO,IAAI;EAChB;EAEA,YAAY,UAAgB;AAC1B,QAAI,SAAS,SAAS,KAAK;AAAG,aAAO;AACrC,QAAI,SAAS,SAAS,MAAM,KAAK,SAAS,SAAS,UAAU;AAAG,aAAO;AACvE,QAAI,SAAS,SAAS,OAAO;AAAG,aAAO;AACvC,QAAI,SAAS,SAAS,OAAO;AAAG,aAAO;AACvC,QAAI,SAAS,SAAS,OAAO;AAAG,aAAO;AACvC,QAAI,SAAS,SAAS,KAAK,KAAK,SAAS,SAAS,SAAS;AAAG,aAAO;AACrE,QAAI,SAAS,SAAS,MAAM;AAAG,aAAO;AACtC,WAAO;EACT;EAEA,SAAM;AACJ,QAAI,KAAK,YAAY;AACnB,YAAM,WAAwC;QAC5C,WAAW,KAAK;;AAGlB,UAAI,KAAK,0BAA0B;AACjC,iBAAS,gBAAgB,KAAK,kBAAkB,IAAI,eAAe,GAAG;MACxE;AAEA,WAAK,MAAM,KAAK,QAAQ;IAC1B;EACF;EAEA,UAAO;AACL,SAAK,OAAO,KAAI;EAClB;;qCAnJW,oCAAiC,4BAAA,WAAA,GAAA,4BAAA,2BAAA,CAAA;EAAA;yEAAjC,oCAAiC,WAAA,CAAA,CAAA,gCAAA,CAAA,GAAA,QAAA,EAAA,QAAA,UAAA,UAAA,YAAA,aAAA,eAAA,SAAA,WAAA,0BAAA,2BAAA,GAAA,SAAA,EAAA,QAAA,UAAA,OAAA,QAAA,GAAA,UAAA,CAAA,8BAAA,GAAA,OAAA,GAAA,MAAA,IAAA,QAAA,CAAA,CAAA,eAAA,sBAAA,GAAA,UAAA,aAAA,aAAA,UAAA,SAAA,YAAA,WAAA,YAAA,GAAA,CAAA,GAAA,sBAAA,GAAA,WAAA,GAAA,CAAA,SAAA,cAAA,GAAA,MAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,iBAAA,iBAAA,GAAA,CAAA,SAAA,gBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,eAAA,GAAA,MAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,mBAAA,iBAAA,GAAA,aAAA,GAAA,CAAA,SAAA,EAAA,GAAA,CAAA,GAAA,SAAA,GAAA,SAAA,SAAA,GAAA,CAAA,SAAA,eAAA,GAAA,MAAA,GAAA,CAAA,GAAA,OAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,QAAA,UAAA,GAAA,iBAAA,GAAA,OAAA,GAAA,CAAA,WAAA,aAAA,QAAA,QAAA,UAAA,cAAA,GAAA,CAAA,UAAA,cAAA,GAAA,CAAA,KAAA,oFAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,sBAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,SAAA,wBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,sBAAA,GAAA,CAAA,GAAA,aAAA,CAAA,GAAA,UAAA,SAAA,2CAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAjP1C,MAAA,yBAAA,GAAA,mBAAA,CAAA;AAOE,MAAA,qBAAA,UAAA,SAAA,+EAAA;AAAA,eAAU,IAAA,QAAA;MAAS,CAAA,EAAC,aAAA,SAAA,kFAAA;AAAA,eACP,IAAA,OAAA;MAAQ,CAAA,EAAC,aAAA,SAAA,kFAAA;AAAA,eACT,IAAA,QAAA;MAAS,CAAA;AAEtB,MAAA,yBAAA,GAAA,QAAA,CAAA;AAEE,MAAA,qBAAA,GAAA,kDAAA,GAAA,GAAA,OAAA,CAAA;AAcA,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA0B,GAAA,MAAA,CAAA;AACE,MAAA,iBAAA,GAAA,kBAAA;AAAgB,MAAA,uBAAA;AAC1C,MAAA,yBAAA,GAAA,mBAAA,CAAA;AAEE,MAAA,qBAAA,iBAAA,SAAA,oFAAA,QAAA;AAAA,eAAiB,IAAA,gBAAA,MAAA;MAAuB,CAAA;AACzC,MAAA,uBAAA,EAAkB;AAIrB,MAAA,qBAAA,GAAA,kDAAA,GAAA,GAAA,OAAA,CAAA,EAA+D,GAAA,kDAAA,GAAA,GAAA,OAAA,CAAA;AAmCjE,MAAA,uBAAA,EAAO;;;AAtEP,MAAA,qBAAA,UAAA,IAAA,MAAA,EAAiB,SAAA,IAAA,WAAA,uBAAA,mBAAA,EAC8C,YAAA,IAAA,2BAAA,4CAAA,kBAAA,EACuC,WAAA,IAAA,OAAA,EACnF,cAAA,IAAA,UAAA;AAOb,MAAA,oBAAA;AAAA,MAAA,qBAAA,aAAA,IAAA,iBAAA;AAEqB,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,wBAAA;AAiBrB,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,mBAAA,IAAA;AAMuB,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,kBAAA,SAAA,CAAA;AAgCD,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,kBAAA,WAAA,CAAA;;oBAtEtB,cAAY,SAAA,MAAA,WAAE,qBAAmB,oBAAA,gBAAA,8BAAA,4BAAA,iBAAA,sBAAA,oBAAA,iBAAE,qBAAqB,mBAAmB,GAAA,QAAA,CAAA,2hHAAA,EAAA,CAAA;;;sEAmP1E,mCAAiC,CAAA;UAtP7C;uBACW,kCAAgC,YAC9B,MAAI,SACP,CAAC,cAAc,qBAAqB,qBAAqB,mBAAmB,GAAC,UAC5E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA0ET,QAAA,CAAA,20GAAA,EAAA,CAAA;8EAyKQ,QAAM,CAAA;UAAd;MACQ,UAAQ,CAAA;UAAhB;MACQ,aAAW,CAAA;UAAnB;MACQ,SAAO,CAAA;UAAf;MACQ,0BAAwB,CAAA;UAAhC;MAES,QAAM,CAAA;UAAf;MACS,OAAK,CAAA;UAAd;;;;6EARU,mCAAiC,EAAA,WAAA,qCAAA,UAAA,uGAAA,YAAA,IAAA,CAAA;AAAA,GAAA;;;ACsDxC,IAAO,yBAAP,MAAO,wBAAsB;EA4Db;EA3DpB,gBAAqC,CAAA;EACrC,wBAA6C,CAAA;EAC7C,UAAU;;EAGV,yBAAyB;EACzB,WAAW;EACX,WAA+C;EAC/C,eAAe;;EAGf,QAAQ;IACN,OAAO;IACP,eAAe;IACf,UAAU;IACV,YAAY;IACZ,eAAe;;;EAIjB,oBAAyB;EACzB,gBAAqB;EACrB,eAAe;IACb,YAAY;IACZ,qBAAqB;;;EAIvB,eAA8B;IAC5B,EAAE,KAAK,SAAS,OAAO,SAAS,UAAU,KAAI;IAC9C,EAAE,KAAK,QAAQ,OAAO,QAAQ,MAAM,SAAS,UAAU,KAAI;IAC3D,EAAE,KAAK,YAAY,OAAO,YAAY,MAAM,SAAS,UAAU,KAAI;IACnE,EAAE,KAAK,UAAU,OAAO,UAAU,MAAM,SAAS,UAAU,KAAI;IAC/D,EAAE,KAAK,UAAU,OAAO,UAAU,UAAU,KAAI;IAChD,EAAE,KAAK,WAAW,OAAO,WAAW,UAAU,KAAI;IAClD,EAAE,KAAK,gBAAgB,OAAO,iBAAiB,MAAM,QAAQ,UAAU,KAAI;;EAG7E,eAA8B;IAC5B;MACE,OAAO;MACP,MAAM;MACN,SAAS;MACT,QAAQ,CAAC,SAAS,KAAK,aAAa,IAAI;;IAE1C;MACE,OAAO;MACP,MAAM;MACN,SAAS;MACT,QAAQ,CAAC,SAAS,KAAK,aAAa,IAAI;;IAE1C;MACE,OAAO;MACP,MAAM;MACN,SAAS;MACT,QAAQ,CAAC,SAAS,KAAK,eAAe,IAAI;;;EAI9C,YAAoB,IAAe;AAAf,SAAA,KAAA;EAAkB;EAEtC,WAAQ;AACN,SAAK,kBAAiB;EACxB;EAEQ,oBAAiB;AACvB,SAAK,UAAU;AAGf,eAAW,MAAK;AACd,WAAK,gBAAgB,KAAK,0BAAyB;AACnD,WAAK,wBAAwB,CAAC,GAAG,KAAK,aAAa;AACnD,WAAK,YAAW;AAChB,WAAK,gBAAe;AACpB,WAAK,UAAU;IACjB,GAAG,GAAI;EACT;EAEQ,4BAAyB;AAC/B,UAAM,QAAQ,CAAC,YAAY,QAAQ,QAAQ,SAAS,UAAU;AAC9D,UAAM,aAAa,CAAC,OAAO,cAAc,aAAa,gBAAgB,cAAc,iBAAiB;AACrG,UAAM,WAAW,CAAC,WAAW,YAAY,SAAS,UAAU;AAC5D,UAAM,eAAe,CAAC,SAAS,SAAS,SAAS,SAAS,OAAO;AAEjE,WAAO,MAAM,KAAK,EAAE,QAAQ,GAAE,GAAI,CAAC,GAAG,OAAO;MAC3C,IAAI,IAAI;MACR,OAAO,iBAAiB,IAAI,CAAC;MAC7B,MAAM,MAAM,KAAK,MAAM,KAAK,OAAM,IAAK,MAAM,MAAM,CAAC;MACpD,UAAU,WAAW,KAAK,MAAM,KAAK,OAAM,IAAK,WAAW,MAAM,CAAC;MAClE,QAAQ,SAAS,KAAK,MAAM,KAAK,OAAM,IAAK,SAAS,MAAM,CAAC;MAC5D,aAAa,aAAa,KAAK,MAAM,KAAK,OAAM,IAAK,aAAa,MAAM,CAAC;MACzE,QAAQ,UAAU,KAAK,MAAM,KAAK,OAAM,IAAK,EAAE,IAAI,CAAC;MACpD,aAAa,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,OAAM,IAAK,MAAM,KAAK,KAAK,KAAK,GAAI;MAC5E,cAAc,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,OAAM,IAAK,KAAK,KAAK,KAAK,KAAK,GAAI;MAC5E,SAAS,GAAG,KAAK,MAAM,KAAK,OAAM,IAAK,CAAC,IAAI,CAAC,IAAI,KAAK,MAAM,KAAK,OAAM,IAAK,EAAE,CAAC;MAC/E,KAAK,KAAK,OAAM,IAAK,MAAM,gCAAgC,IAAI,CAAC,KAAK;MACrE,aAAa,iCAAiC,IAAI,CAAC;MACnD,MAAM,CAAC,MAAM,KAAK,MAAM,KAAK,OAAM,IAAK,CAAC,IAAI,CAAC,IAAI,MAAM,KAAK,MAAM,KAAK,OAAM,IAAK,CAAC,IAAI,CAAC,EAAE;MAC3F;EACJ;EAEQ,cAAW;AACjB,SAAK,MAAM,QAAQ,KAAK,cAAc;AACtC,SAAK,MAAM,gBAAgB,KAAK,cAAc,OAAO,OACnD,IAAI,KAAK,EAAE,YAAY,EAAE,QAAO,IAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,KAAK,GAAI,EACzE;AACF,SAAK,MAAM,WAAW,KAAK,cAAc,OAAO,OAAK,EAAE,WAAW,UAAU,EAAE;AAC9E,SAAK,MAAM,aAAa,IAAI,IAAI,KAAK,cAAc,IAAI,OAAK,EAAE,QAAQ,CAAC,EAAE;AACzE,SAAK,MAAM,gBAAgB,KAAK,cAAc,OAAO,OAAK,EAAE,SAAS,MAAM,EAAE;EAC/E;EAEQ,kBAAe;AAErB,UAAM,gBAAgB,KAAK,cAAc,OAAO,CAAC,KAAK,QAAO;AAC3D,UAAI,IAAI,QAAQ,KAAK,IAAI,IAAI,QAAQ,KAAK,KAAK;AAC/C,aAAO;IACT,GAAG,CAAA,CAAS;AAEZ,SAAK,oBAAoB;MACvB,QAAQ,OAAO,KAAK,aAAa;MACjC,UAAU,CAAC;QACT,MAAM,OAAO,OAAO,aAAa;QACjC,iBAAiB;UACf;UAAW;UAAW;UAAW;UAAW;UAAW;;OAE1D;;AAIH,UAAM,YAAY,KAAK,cAAc,OAAO,CAAC,KAAK,QAAO;AACvD,UAAI,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,KAAK;AACvC,aAAO;IACT,GAAG,CAAA,CAAS;AAEZ,SAAK,gBAAgB;MACnB,QAAQ,OAAO,KAAK,SAAS;MAC7B,UAAU,CAAC;QACT,OAAO;QACP,MAAM,OAAO,OAAO,SAAS;QAC7B,iBAAiB,CAAC,WAAW,WAAW,WAAW,WAAW,SAAS;OACxE;;EAEL;EAEA,eAAY;AACV,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,yBAAyB;EAChC;EAEA,aAAU;AACR,SAAK,yBAAyB;AAC9B,SAAK,WAAW;AAChB,SAAK,WAAW;EAClB;EAEA,qBAAqB,MAAiC;AACpD,SAAK,eAAe;AAGpB,eAAW,MAAK;AACd,cAAQ,IAAI,wBAAwB,IAAI;AACxC,WAAK,eAAe;AACpB,WAAK,WAAU;AAGf,WAAK,kBAAiB;IACxB,GAAG,GAAI;EACT;EAEA,aAAa,MAAuB;AAClC,QAAI,KAAK,KAAK;AACZ,aAAO,KAAK,KAAK,KAAK,QAAQ;IAChC,OAAO;AACL,cAAQ,IAAI,kBAAkB,IAAI;IACpC;EACF;EAEA,aAAa,MAAuB;AAElC,YAAQ,IAAI,kBAAkB,IAAI;EACpC;EAEA,eAAe,MAAuB;AACpC,QAAI,QAAQ,mCAAmC,KAAK,KAAK,GAAG,GAAG;AAC7D,WAAK,gBAAgB,KAAK,cAAc,OAAO,OAAK,EAAE,OAAO,KAAK,EAAE;AACpE,WAAK,wBAAwB,KAAK,sBAAsB,OAAO,OAAK,EAAE,OAAO,KAAK,EAAE;AACpF,WAAK,YAAW;AAChB,WAAK,gBAAe;IACtB;EACF;EAEA,cAAc,OAAY;AACxB,UAAM,SAAS,MAAM;AACrB,SAAK,gBAAgB,OAAO,KAAK;EACnC;EAEA,gBAAgB,YAAkB;AAChC,SAAK,aAAY;EACnB;EAEA,gBAAgB,YAAoB,OAAY;AAC9C,UAAM,SAAS,MAAM;AAErB,SAAK,aAAY;EACnB;EAEA,cAAc,MAAS;AAErB,YAAQ,IAAI,iBAAiB,IAAI;EACnC;EAEQ,eAAY;AAElB,SAAK,wBAAwB,CAAC,GAAG,KAAK,aAAa;EACrD;;qCAxNW,yBAAsB,4BAAA,WAAA,CAAA;EAAA;yEAAtB,yBAAsB,WAAA,CAAA,CAAA,mBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,IAAA,QAAA,CAAA,CAAA,GAAA,oBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,WAAA,WAAA,YAAA,kBAAA,GAAA,SAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,SAAA,gBAAA,GAAA,CAAA,GAAA,eAAA,QAAA,GAAA,CAAA,SAAA,eAAA,GAAA,CAAA,GAAA,eAAA,SAAA,GAAA,CAAA,SAAA,gBAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,SAAA,yBAAA,YAAA,gCAAA,GAAA,CAAA,QAAA,YAAA,UAAA,SAAA,GAAA,QAAA,SAAA,GAAA,CAAA,SAAA,kBAAA,YAAA,+BAAA,GAAA,CAAA,QAAA,OAAA,UAAA,SAAA,GAAA,QAAA,SAAA,GAAA,CAAA,SAAA,yBAAA,YAAA,2BAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,QAAA,QAAA,eAAA,2BAAA,GAAA,gBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,QAAA,GAAA,CAAA,SAAA,EAAA,GAAA,CAAA,SAAA,KAAA,GAAA,CAAA,SAAA,YAAA,GAAA,CAAA,SAAA,WAAA,GAAA,CAAA,SAAA,cAAA,GAAA,CAAA,SAAA,YAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,CAAA,SAAA,UAAA,GAAA,CAAA,SAAA,MAAA,GAAA,CAAA,SAAA,MAAA,GAAA,CAAA,SAAA,OAAA,GAAA,CAAA,SAAA,UAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,SAAA,UAAA,GAAA,CAAA,SAAA,OAAA,GAAA,CAAA,SAAA,UAAA,GAAA,CAAA,GAAA,eAAA,WAAA,QAAA,WAAA,WAAA,UAAA,GAAA,CAAA,GAAA,UAAA,SAAA,UAAA,YAAA,eAAA,WAAA,0BAAA,CAAA,GAAA,UAAA,SAAA,gCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAjR/B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAgC,GAAA,OAAA,CAAA,EACJ,GAAA,OAAA,CAAA,EACC,GAAA,OAAA,CAAA,EACK,GAAA,IAAA;AACtB,MAAA,iBAAA,GAAA,eAAA;AAAa,MAAA,uBAAA;AACjB,MAAA,yBAAA,GAAA,KAAA,CAAA;AAAyB,MAAA,iBAAA,GAAA,4CAAA;AAA0C,MAAA,uBAAA,EAAI;AAEzE,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA4B,GAAA,cAAA,CAAA;AAIxB,MAAA,qBAAA,WAAA,SAAA,gEAAA;AAAA,eAAW,IAAA,aAAA;MAAc,CAAA;AAEzB,MAAA,iBAAA,IAAA,gBAAA;AACF,MAAA,uBAAA,EAAa,EACT;AAIV,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,YAAA,CAAA,EACY,IAAA,OAAA,CAAA,EACN,IAAA,OAAA,EAAA;AACC,MAAA,iBAAA,EAAA;AAAiB,MAAA,uBAAA;AAC1C,MAAA,yBAAA,IAAA,OAAA,EAAA;AAAyB,MAAA,iBAAA,EAAA;AAAwC,MAAA,uBAAA,EAAM,EACnE;AAGR,MAAA,yBAAA,IAAA,YAAA,EAAA,EAAiC,IAAA,OAAA,CAAA,EACL,IAAA,OAAA,EAAA;AACQ,MAAA,iBAAA,EAAA;AAAyB,MAAA,uBAAA;AACzD,MAAA,yBAAA,IAAA,OAAA,EAAA;AAAyB,MAAA,iBAAA,IAAA,mBAAA;AAAiB,MAAA,uBAAA,EAAM,EAC5C;AAGR,MAAA,yBAAA,IAAA,YAAA,EAAA,EAAgC,IAAA,OAAA,CAAA,EACJ,IAAA,OAAA,EAAA;AACS,MAAA,iBAAA,EAAA;AAAoB,MAAA,uBAAA;AACrD,MAAA,yBAAA,IAAA,OAAA,EAAA;AAAyB,MAAA,iBAAA,IAAA,aAAA;AAAW,MAAA,uBAAA,EAAM,EACtC;AAGR,MAAA,yBAAA,IAAA,YAAA,EAAA,EAAiC,IAAA,OAAA,CAAA,EACL,IAAA,OAAA,EAAA;AACC,MAAA,iBAAA,EAAA;AAAyB,MAAA,uBAAA;AAClD,MAAA,yBAAA,IAAA,OAAA,EAAA;AAAyB,MAAA,iBAAA,IAAA,oBAAA;AAAkB,MAAA,uBAAA,EAAM,EAC7C,EACG;AAIb,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA4B,IAAA,YAAA,EAAA;AAExB,MAAA,oBAAA,IAAA,aAAA,EAAA;AAMF,MAAA,uBAAA;AAEA,MAAA,yBAAA,IAAA,YAAA,EAAA;AACE,MAAA,oBAAA,IAAA,aAAA,EAAA;AAMF,MAAA,uBAAA,EAAW;AAIb,MAAA,yBAAA,IAAA,YAAA,EAAA,EAA6E,IAAA,OAAA,EAAA,EAC/C,IAAA,OAAA,EAAA,EACG,IAAA,SAAA,EAAA;AAKzB,MAAA,qBAAA,SAAA,SAAA,wDAAA,QAAA;AAAA,eAAS,IAAA,cAAA,MAAA;MAAqB,CAAA;AAJhC,MAAA,uBAAA;AAMA,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA8B,MAAA,qBAAA,UAAA,SAAA,0DAAA,QAAA;AAAA,eAAU,IAAA,gBAAgB,YAAU,MAAA;MAAS,CAAA;AACzE,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAiB,MAAA,iBAAA,IAAA,gBAAA;AAAc,MAAA,uBAAA;AAC/B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAoB,MAAA,iBAAA,IAAA,mBAAA;AAAiB,MAAA,uBAAA;AACrC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA2B,MAAA,iBAAA,IAAA,YAAA;AAAU,MAAA,uBAAA;AACrC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA0B,MAAA,iBAAA,IAAA,iBAAA;AAAe,MAAA,uBAAA;AACzC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA6B,MAAA,iBAAA,IAAA,cAAA;AAAY,MAAA,uBAAA;AACzC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA2B,MAAA,iBAAA,IAAA,YAAA;AAAU,MAAA,uBAAA;AACrC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAgC,MAAA,iBAAA,IAAA,iBAAA;AAAe,MAAA,uBAAA,EAAS;AAE1D,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA8B,MAAA,qBAAA,UAAA,SAAA,0DAAA,QAAA;AAAA,eAAU,IAAA,gBAAgB,QAAM,MAAA;MAAS,CAAA;AACrE,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAiB,MAAA,iBAAA,IAAA,WAAA;AAAS,MAAA,uBAAA;AAC1B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAyB,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA;AACjC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAqB,MAAA,iBAAA,IAAA,eAAA;AAAa,MAAA,uBAAA;AAClC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAqB,MAAA,iBAAA,IAAA,WAAA;AAAS,MAAA,uBAAA;AAC9B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAsB,MAAA,iBAAA,IAAA,OAAA;AAAK,MAAA,uBAAA;AAC3B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAyB,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA,EAAS;AAE5C,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA8B,MAAA,qBAAA,UAAA,SAAA,0DAAA,QAAA;AAAA,eAAU,IAAA,gBAAgB,UAAQ,MAAA;MAAS,CAAA;AACvE,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAiB,MAAA,iBAAA,IAAA,cAAA;AAAY,MAAA,uBAAA;AAC7B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAwB,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA;AAC/B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAyB,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA;AACjC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAsB,MAAA,iBAAA,IAAA,OAAA;AAAK,MAAA,uBAAA;AAC3B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAyB,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA,EAAS,EACnC,EACL;AAGR,MAAA,yBAAA,IAAA,aAAA,EAAA;AAME,MAAA,qBAAA,eAAA,SAAA,kEAAA,QAAA;AAAA,eAAe,IAAA,cAAA,MAAA;MAAqB,CAAA;AACrC,MAAA,uBAAA,EAAY;AAIf,MAAA,yBAAA,IAAA,kCAAA,EAAA;AAME,MAAA,qBAAA,UAAA,SAAA,oFAAA;AAAA,eAAU,IAAA,WAAA;MAAY,CAAA,EAAC,SAAA,SAAA,iFAAA,QAAA;AAAA,eACd,IAAA,qBAAA,MAAA;MAA4B,CAAA;AACtC,MAAA,uBAAA,EAAiC,EAC5B;;;AAzGyB,MAAA,oBAAA,EAAA;AAAA,MAAA,4BAAA,IAAA,MAAA,KAAA;AACA,MAAA,oBAAA,CAAA;AAAA,MAAA,6BAAA,WAAA,IAAA,MAAA,YAAA,aAAA;AAMO,MAAA,oBAAA,CAAA;AAAA,MAAA,4BAAA,IAAA,MAAA,aAAA;AAOC,MAAA,oBAAA,CAAA;AAAA,MAAA,4BAAA,IAAA,MAAA,QAAA;AAOR,MAAA,oBAAA,CAAA;AAAA,MAAA,4BAAA,IAAA,MAAA,aAAA;AAWzB,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,iBAAA,EAA0B,WAAA,IAAA,YAAA;AAS1B,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,aAAA,EAAsB,WAAA,IAAA,YAAA;AA6CxB,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,WAAA,IAAA,YAAA,EAAwB,QAAA,IAAA,qBAAA,EACM,WAAA,IAAA,YAAA,EACN,WAAA,IAAA,OAAA,EACL,YAAA,IAAA;AAQrB,MAAA,oBAAA;AAAA,MAAA,qBAAA,UAAA,IAAA,sBAAA,EAAiC,YAAA,IAAA,QAAA,EACZ,eAAA,IAAA,QAAA,EACG,WAAA,IAAA,YAAA,EACA,4BAAA,IAAA;;;IApI5B;IACA;IACA;IAAmB;IAAA;IACnB;IACA;IACA;IACA;IACA;EAAiC,GAAA,QAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wDAAA,EAAA,CAAA;;;sEAoRxB,wBAAsB,CAAA;UA/RlC;uBACW,qBAAmB,YACjB,MAAI,SACP;MACP;MACA;MACA;MACA;MACA;MACA;MACA;MACA;OACD,UACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAkIT,QAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAAA,EAAA,CAAA;;;;6EAgJU,wBAAsB,EAAA,WAAA,0BAAA,UAAA,4DAAA,YAAA,IAAA,CAAA;AAAA,GAAA;", "names": []}