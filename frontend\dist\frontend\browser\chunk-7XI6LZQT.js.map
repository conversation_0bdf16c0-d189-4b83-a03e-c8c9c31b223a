{"version": 3, "sources": ["src/app/shared/services/application-selection.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { Observable, of } from 'rxjs';\nimport { delay, map } from 'rxjs/operators';\n\nexport interface ApplicationOption {\n  id: number;\n  name: string;\n  description: string;\n  owner: string;\n  department: string;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ApplicationSelectionService {\n\n  constructor() { }\n\n  /**\n   * Get applications for selection dropdown\n   */\n  getApplicationOptions(): Observable<ApplicationOption[]> {\n    return this.getMockApplications().pipe(\n      delay(300),\n      map(apps => apps.map(app => ({\n        id: app.id!,\n        name: app.name,\n        description: app.description,\n        owner: app.owner,\n        department: app.department\n      })))\n    );\n  }\n\n  /**\n   * Search applications by name\n   */\n  searchApplications(query: string): Observable<ApplicationOption[]> {\n    return this.getApplicationOptions().pipe(\n      map(apps => \n        apps.filter(app => \n          app.name.toLowerCase().includes(query.toLowerCase()) ||\n          app.description.toLowerCase().includes(query.toLowerCase())\n        ).slice(0, 10)\n      )\n    );\n  }\n\n  private getMockApplications() {\n    return of([\n      {\n        id: 1,\n        name: 'E-Commerce Platform',\n        description: 'Main customer-facing e-commerce application',\n        owner: 'John Doe',\n        department: 'Engineering'\n      },\n      {\n        id: 2,\n        name: 'Customer Portal',\n        description: 'Self-service customer management portal',\n        owner: 'Jane Smith',\n        department: 'Product'\n      },\n      {\n        id: 3,\n        name: 'Admin Dashboard',\n        description: 'Internal administration and reporting dashboard',\n        owner: 'Mike Johnson',\n        department: 'Operations'\n      },\n      {\n        id: 4,\n        name: 'Mobile App',\n        description: 'iOS and Android mobile application',\n        owner: 'Sarah Wilson',\n        department: 'Mobile'\n      },\n      {\n        id: 5,\n        name: 'Analytics Service',\n        description: 'Data analytics and reporting microservice',\n        owner: 'David Brown',\n        department: 'Data'\n      },\n      {\n        id: 6,\n        name: 'Payment Gateway',\n        description: 'Payment processing and transaction management',\n        owner: 'Lisa Davis',\n        department: 'FinTech'\n      },\n      {\n        id: 7,\n        name: 'Inventory System',\n        description: 'Warehouse and inventory management system',\n        owner: 'Tom Wilson',\n        department: 'Logistics'\n      },\n      {\n        id: 8,\n        name: 'CRM System',\n        description: 'Customer relationship management platform',\n        owner: 'Emily Johnson',\n        department: 'Sales'\n      }\n    ]);\n  }\n}\n"], "mappings": ";;;;;;;;;;AAeM,IAAO,8BAAP,MAAO,6BAA2B;EAEtC,cAAA;EAAgB;;;;EAKhB,wBAAqB;AACnB,WAAO,KAAK,oBAAmB,EAAG,KAChC,MAAM,GAAG,GACT,IAAI,UAAQ,KAAK,IAAI,UAAQ;MAC3B,IAAI,IAAI;MACR,MAAM,IAAI;MACV,aAAa,IAAI;MACjB,OAAO,IAAI;MACX,YAAY,IAAI;MAChB,CAAC,CAAC;EAER;;;;EAKA,mBAAmB,OAAa;AAC9B,WAAO,KAAK,sBAAqB,EAAG,KAClC,IAAI,UACF,KAAK,OAAO,SACV,IAAI,KAAK,YAAW,EAAG,SAAS,MAAM,YAAW,CAAE,KACnD,IAAI,YAAY,YAAW,EAAG,SAAS,MAAM,YAAW,CAAE,CAAC,EAC3D,MAAM,GAAG,EAAE,CAAC,CACf;EAEL;EAEQ,sBAAmB;AACzB,WAAO,GAAG;MACR;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,YAAY;;MAEd;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,YAAY;;MAEd;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,YAAY;;MAEd;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,YAAY;;MAEd;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,YAAY;;MAEd;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,YAAY;;MAEd;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,YAAY;;MAEd;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,YAAY;;KAEf;EACH;;qCA7FW,8BAA2B;EAAA;4EAA3B,8BAA2B,SAA3B,6BAA2B,WAAA,YAF1B,OAAM,CAAA;;;sEAEP,6BAA2B,CAAA;UAHvC;WAAW;MACV,YAAY;KACb;;;", "names": []}