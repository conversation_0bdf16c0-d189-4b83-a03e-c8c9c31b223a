{"version": 3, "sources": ["src/app/modules/security/assessments/assessments.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { CardComponent } from '../../../shared/components/card/card.component';\n\n@Component({\n  selector: 'app-assessments',\n  standalone: true,\n  imports: [CommonModule, CardComponent],\n  template: `\n    <div class=\"assessments-page\">\n      <h1>Security Assessments</h1>\n      <app-card title=\"Assessment Management\" subtitle=\"Schedule and track security assessments\">\n        <div class=\"placeholder\">\n          <p>Security assessments module coming soon...</p>\n        </div>\n      </app-card>\n    </div>\n  `,\n  styles: [`\n    .assessments-page {\n      min-height: 100%;\n    }\n    .placeholder {\n      padding: var(--spacing-xl);\n      text-align: center;\n      color: var(--secondary-600);\n    }\n  `]\n})\nexport class AssessmentsComponent {}\n"], "mappings": ";;;;;;;;;;;;;;;;;AA6BM,IAAO,uBAAP,MAAO,sBAAoB;;qCAApB,uBAAoB;EAAA;yEAApB,uBAAoB,WAAA,CAAA,CAAA,iBAAA,CAAA,GAAA,OAAA,GAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,SAAA,yBAAA,YAAA,yCAAA,GAAA,CAAA,GAAA,aAAA,CAAA,GAAA,UAAA,SAAA,8BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AApB7B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA8B,GAAA,IAAA;AACxB,MAAA,iBAAA,GAAA,sBAAA;AAAoB,MAAA,uBAAA;AACxB,MAAA,yBAAA,GAAA,YAAA,CAAA,EAA2F,GAAA,OAAA,CAAA,EAChE,GAAA,GAAA;AACpB,MAAA,iBAAA,GAAA,4CAAA;AAA0C,MAAA,uBAAA,EAAI,EAC7C,EACG;;oBARL,cAAc,aAAa,GAAA,QAAA,CAAA,uPAAA,EAAA,CAAA;;;sEAsB1B,sBAAoB,CAAA;UAzBhC;uBACW,mBAAiB,YACf,MAAI,SACP,CAAC,cAAc,aAAa,GAAC,UAC5B;;;;;;;;;KAST,QAAA,CAAA,wbAAA,EAAA,CAAA;;;;6EAYU,sBAAoB,EAAA,WAAA,wBAAA,UAAA,iEAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}