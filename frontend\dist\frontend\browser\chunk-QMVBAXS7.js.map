{"version": 3, "sources": ["src/app/shared/components/button/button.component.ts"], "sourcesContent": ["import { Component, Input, Output, EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\nexport type ButtonVariant = 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'ghost' | 'outline';\nexport type ButtonSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl';\n\n@Component({\n  selector: 'app-button',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <button\n      [type]=\"type\"\n      [disabled]=\"disabled || loading\"\n      [class]=\"buttonClasses\"\n      (click)=\"onClick($event)\"\n    >\n      <!-- Loading Spinner -->\n      <svg \n        *ngIf=\"loading\" \n        class=\"button-spinner\"\n        viewBox=\"0 0 24 24\" \n        fill=\"none\"\n      >\n        <circle \n          cx=\"12\" \n          cy=\"12\" \n          r=\"10\" \n          stroke=\"currentColor\" \n          stroke-width=\"2\" \n          stroke-linecap=\"round\" \n          stroke-dasharray=\"31.416\" \n          stroke-dashoffset=\"31.416\"\n        >\n          <animate \n            attributeName=\"stroke-dasharray\" \n            dur=\"2s\" \n            values=\"0 31.416;15.708 15.708;0 31.416;0 31.416\" \n            repeatCount=\"indefinite\"\n          />\n          <animate \n            attributeName=\"stroke-dashoffset\" \n            dur=\"2s\" \n            values=\"0;-15.708;-31.416;-31.416\" \n            repeatCount=\"indefinite\"\n          />\n        </circle>\n      </svg>\n\n      <!-- Left Icon -->\n      <svg \n        *ngIf=\"leftIcon && !loading\" \n        class=\"button-icon button-icon-left\"\n        viewBox=\"0 0 24 24\" \n        fill=\"none\" \n        stroke=\"currentColor\"\n      >\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" [attr.d]=\"leftIcon\" />\n      </svg>\n\n      <!-- Button Content -->\n      <span class=\"button-content\" [class.sr-only]=\"loading && loadingText\">\n        <ng-content></ng-content>\n      </span>\n\n      <!-- Loading Text -->\n      <span *ngIf=\"loading && loadingText\" class=\"button-loading-text\">\n        {{ loadingText }}\n      </span>\n\n      <!-- Right Icon -->\n      <svg \n        *ngIf=\"rightIcon && !loading\" \n        class=\"button-icon button-icon-right\"\n        viewBox=\"0 0 24 24\" \n        fill=\"none\" \n        stroke=\"currentColor\"\n      >\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" [attr.d]=\"rightIcon\" />\n      </svg>\n\n      <!-- Badge -->\n      <span *ngIf=\"badge\" class=\"button-badge\">{{ badge }}</span>\n    </button>\n  `,\n  styleUrls: ['./button.component.scss']\n})\nexport class ButtonComponent {\n  @Input() variant: ButtonVariant = 'primary';\n  @Input() size: ButtonSize = 'md';\n  @Input() type: 'button' | 'submit' | 'reset' = 'button';\n  @Input() disabled = false;\n  @Input() loading = false;\n  @Input() loadingText = '';\n  @Input() leftIcon = '';\n  @Input() rightIcon = '';\n  @Input() badge = '';\n  @Input() fullWidth = false;\n  @Input() rounded = false;\n\n  @Output() clicked = new EventEmitter<Event>();\n\n  get buttonClasses(): string {\n    const classes = [\n      'btn',\n      `btn-${this.variant}`,\n      `btn-${this.size}`\n    ];\n\n    if (this.fullWidth) classes.push('btn-full-width');\n    if (this.rounded) classes.push('btn-rounded');\n    if (this.loading) classes.push('btn-loading');\n    if (this.disabled) classes.push('btn-disabled');\n\n    return classes.join(' ');\n  }\n\n  onClick(event: Event): void {\n    if (!this.disabled && !this.loading) {\n      this.clicked.emit(event);\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBM,IAAA,yBAAA,GAAA,OAAA,CAAA,EAKC,GAAA,UAAA,CAAA;AAWG,IAAA,oBAAA,GAAA,WAAA,CAAA,EAKE,GAAA,WAAA,EAAA;AAOJ,IAAA,uBAAA,EAAS;;;;;;AAIX,IAAA,yBAAA,GAAA,OAAA,EAAA;AAOE,IAAA,oBAAA,GAAA,QAAA,EAAA;AACF,IAAA,uBAAA;;;;AADwE,IAAA,oBAAA;;;;;;AASxE,IAAA,yBAAA,GAAA,QAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,aAAA,GAAA;;;;;;AAIF,IAAA,yBAAA,GAAA,OAAA,EAAA;AAOE,IAAA,oBAAA,GAAA,QAAA,EAAA;AACF,IAAA,uBAAA;;;;AADwE,IAAA,oBAAA;;;;;;AAIxE,IAAA,yBAAA,GAAA,QAAA,EAAA;AAAyC,IAAA,iBAAA,CAAA;AAAW,IAAA,uBAAA;;;;AAAX,IAAA,oBAAA;AAAA,IAAA,4BAAA,OAAA,KAAA;;;AAKzC,IAAO,kBAAP,MAAO,iBAAe;EACjB,UAAyB;EACzB,OAAmB;EACnB,OAAsC;EACtC,WAAW;EACX,UAAU;EACV,cAAc;EACd,WAAW;EACX,YAAY;EACZ,QAAQ;EACR,YAAY;EACZ,UAAU;EAET,UAAU,IAAI,aAAY;EAEpC,IAAI,gBAAa;AACf,UAAM,UAAU;MACd;MACA,OAAO,KAAK,OAAO;MACnB,OAAO,KAAK,IAAI;;AAGlB,QAAI,KAAK;AAAW,cAAQ,KAAK,gBAAgB;AACjD,QAAI,KAAK;AAAS,cAAQ,KAAK,aAAa;AAC5C,QAAI,KAAK;AAAS,cAAQ,KAAK,aAAa;AAC5C,QAAI,KAAK;AAAU,cAAQ,KAAK,cAAc;AAE9C,WAAO,QAAQ,KAAK,GAAG;EACzB;EAEA,QAAQ,OAAY;AAClB,QAAI,CAAC,KAAK,YAAY,CAAC,KAAK,SAAS;AACnC,WAAK,QAAQ,KAAK,KAAK;IACzB;EACF;;qCAlCW,kBAAe;EAAA;yEAAf,kBAAe,WAAA,CAAA,CAAA,YAAA,CAAA,GAAA,QAAA,EAAA,SAAA,WAAA,MAAA,QAAA,MAAA,QAAA,UAAA,YAAA,SAAA,WAAA,aAAA,eAAA,UAAA,YAAA,WAAA,aAAA,OAAA,SAAA,WAAA,aAAA,SAAA,UAAA,GAAA,SAAA,EAAA,SAAA,UAAA,GAAA,oBAAA,KAAA,OAAA,GAAA,MAAA,IAAA,QAAA,CAAA,CAAA,GAAA,SAAA,QAAA,UAAA,GAAA,CAAA,SAAA,kBAAA,WAAA,aAAA,QAAA,QAAA,GAAA,MAAA,GAAA,CAAA,SAAA,gCAAA,WAAA,aAAA,QAAA,QAAA,UAAA,gBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,SAAA,uBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,iCAAA,WAAA,aAAA,QAAA,QAAA,UAAA,gBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,gBAAA,GAAA,MAAA,GAAA,CAAA,WAAA,aAAA,QAAA,QAAA,GAAA,gBAAA,GAAA,CAAA,MAAA,MAAA,MAAA,MAAA,KAAA,MAAA,UAAA,gBAAA,gBAAA,KAAA,kBAAA,SAAA,oBAAA,UAAA,qBAAA,QAAA,GAAA,CAAA,iBAAA,oBAAA,OAAA,MAAA,UAAA,4CAAA,eAAA,YAAA,GAAA,CAAA,iBAAA,qBAAA,OAAA,MAAA,UAAA,6BAAA,eAAA,YAAA,GAAA,CAAA,WAAA,aAAA,QAAA,QAAA,UAAA,gBAAA,GAAA,eAAA,kBAAA,GAAA,CAAA,kBAAA,SAAA,mBAAA,SAAA,gBAAA,GAAA,GAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,WAAA,aAAA,QAAA,QAAA,UAAA,gBAAA,GAAA,eAAA,mBAAA,GAAA,CAAA,GAAA,cAAA,CAAA,GAAA,UAAA,SAAA,yBAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;;AA5ExB,MAAA,yBAAA,GAAA,UAAA,CAAA;AAIE,MAAA,qBAAA,SAAA,SAAA,iDAAA,QAAA;AAAA,eAAS,IAAA,QAAA,MAAA;MAAe,CAAA;AAGxB,MAAA,qBAAA,GAAA,qCAAA,GAAA,GAAA,OAAA,CAAA,EAKC,GAAA,qCAAA,GAAA,GAAA,OAAA,CAAA;AAsCD,MAAA,yBAAA,GAAA,QAAA,CAAA;AACE,MAAA,uBAAA,CAAA;AACF,MAAA,uBAAA;AAGA,MAAA,qBAAA,GAAA,iCAAA,GAAA,GAAA,QAAA,CAAA,EAAiE,GAAA,qCAAA,GAAA,GAAA,OAAA,CAAA,EAWhE,GAAA,iCAAA,GAAA,GAAA,QAAA,CAAA;AAMH,MAAA,uBAAA;;;AArEE,MAAA,qBAAA,IAAA,aAAA;AAFA,MAAA,qBAAA,QAAA,IAAA,IAAA,EAAa,YAAA,IAAA,YAAA,IAAA,OAAA;AAOV,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,OAAA;AAgCA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,YAAA,CAAA,IAAA,OAAA;AAU0B,MAAA,oBAAA;AAAA,MAAA,sBAAA,WAAA,IAAA,WAAA,IAAA,WAAA;AAKtB,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,WAAA,IAAA,WAAA;AAMJ,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,aAAA,CAAA,IAAA,OAAA;AAUI,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,KAAA;;oBAzED,cAAY,IAAA,GAAA,QAAA,CAAA,q8PAAA,EAAA,CAAA;;;sEA8EX,iBAAe,CAAA;UAjF3B;uBACW,cAAY,YACV,MAAI,SACP,CAAC,YAAY,GAAC,UACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA0ET,QAAA,CAAA,yqNAAA,EAAA,CAAA;cAIQ,SAAO,CAAA;UAAf;MACQ,MAAI,CAAA;UAAZ;MACQ,MAAI,CAAA;UAAZ;MACQ,UAAQ,CAAA;UAAhB;MACQ,SAAO,CAAA;UAAf;MACQ,aAAW,CAAA;UAAnB;MACQ,UAAQ,CAAA;UAAhB;MACQ,WAAS,CAAA;UAAjB;MACQ,OAAK,CAAA;UAAb;MACQ,WAAS,CAAA;UAAjB;MACQ,SAAO,CAAA;UAAf;MAES,SAAO,CAAA;UAAhB;;;;6EAbU,iBAAe,EAAA,WAAA,mBAAA,UAAA,wDAAA,YAAA,GAAA,CAAA;AAAA,GAAA;", "names": []}