.settings-container {
  padding: 2rem;
  background: #ffffff;
  border-radius: 8px;
  max-width: 800px;
  margin: 2rem auto;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);

  h2 {
    color: #1976d2;
    margin-bottom: 2rem;
    font-size: 1.8rem;
  }

  h3 {
    color: #2c3e50;
    margin: 1.5rem 0 1rem;
    font-size: 1.4rem;
  }
}

section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 6px;
}

.field-list, .rules-list {
  margin-bottom: 1.5rem;
}

.field-item, .rule-item {
  background: white;
  padding: 1rem;
  margin-bottom: 1rem;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  }
}

.field-header, .rule-header {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;

  .field-name, .rule-field {
    font-weight: 600;
    margin-right: 1rem;
  }

  .field-type {
    color: #666;
    background: #e9ecef;
    padding: 0.2rem 0.5rem;
    border-radius: 3px;
    font-size: 0.9rem;
  }

  .field-required {
    color: #dc3545;
    font-size: 0.9rem;
    margin-left: auto;
    margin-right: 1rem;
  }

  .remove-button {
    background: #dc3545;
    color: white;
    border: none;
    padding: 0.2rem 0.5rem;
    border-radius: 3px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: background-color 0.3s ease;

    &:hover {
      background: #c82333;
    }
  }
}

.add-form {
  background: white;
  padding: 1.5rem;
  border-radius: 4px;
  margin-top: 1rem;
  border: 1px solid #e0e0e0;

  .form-group {
    margin-bottom: 1rem;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .form-control {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 1rem;

    &:focus {
      outline: none;
      border-color: #80bdff;
      box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
    }
  }

  select.form-control {
    height: 38px;
  }

  .checkbox {
    display: flex;
    align-items: center;
    
    label {
      margin: 0;
      display: flex;
      align-items: center;
      cursor: pointer;

      input[type="checkbox"] {
        margin-right: 0.5rem;
      }
    }
  }
}

.field-options, .rule-pattern, .rule-message {
  color: #666;
  font-size: 0.9rem;
  margin-top: 0.5rem;
}

.add-button {
  background: #28a745;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;

  &:hover {
    background: #218838;
  }
}

.actions {
  margin-top: 2rem;
  text-align: right;

  .save-button {
    background: #1976d2;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s ease;

    &:hover {
      background: #1565c0;
    }
  }
}