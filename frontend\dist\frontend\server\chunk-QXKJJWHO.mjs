import './polyfills.server.mjs';
import {
  ChartComponent
} from "./chunk-CIUJRO3I.mjs";
import {
  BaseApiService
} from "./chunk-EQ3AZOB3.mjs";
import {
  CardComponent
} from "./chunk-UJ4MSIYR.mjs";
import {
  ButtonComponent
} from "./chunk-NVGHB2VF.mjs";
import {
  RouterLink,
  RouterModule
} from "./chunk-MUYKQ5QZ.mjs";
import {
  BehaviorSubject,
  CommonModule,
  Component,
  DatePipe,
  Injectable,
  NgForOf,
  NgIf,
  Subject,
  delay,
  map,
  of,
  setClassMetadata,
  takeUntil,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵattribute,
  ɵɵclassMap,
  ɵɵdefineComponent,
  ɵɵdefineInjectable,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵinject,
  ɵɵlistener,
  ɵɵnamespaceHTML,
  ɵɵnamespaceSVG,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind2,
  ɵɵproperty,
  ɵɵpureFunction0,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵstyleProp,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1
} from "./chunk-IPMSWJNG.mjs";
import "./chunk-5WKMABBB.mjs";

// src/app/shared/models/dashboard.model.ts
var ActivityType;
(function(ActivityType2) {
  ActivityType2["APPLICATION_CREATED"] = "application_created";
  ActivityType2["APPLICATION_UPDATED"] = "application_updated";
  ActivityType2["APPLICATION_DELETED"] = "application_deleted";
  ActivityType2["DEPENDENCY_ADDED"] = "dependency_added";
  ActivityType2["DEPENDENCY_UPDATED"] = "dependency_updated";
  ActivityType2["VULNERABILITY_DISCOVERED"] = "vulnerability_discovered";
  ActivityType2["VULNERABILITY_RESOLVED"] = "vulnerability_resolved";
  ActivityType2["SECURITY_ASSESSMENT"] = "security_assessment";
  ActivityType2["DOCUMENTATION_UPLOADED"] = "documentation_uploaded";
  ActivityType2["STAKEHOLDER_ADDED"] = "stakeholder_added";
  ActivityType2["TECH_STACK_UPDATED"] = "tech_stack_updated";
})(ActivityType || (ActivityType = {}));
var TaskType;
(function(TaskType2) {
  TaskType2["SECURITY_ASSESSMENT"] = "security_assessment";
  TaskType2["DEPENDENCY_UPDATE"] = "dependency_update";
  TaskType2["VULNERABILITY_REMEDIATION"] = "vulnerability_remediation";
  TaskType2["DOCUMENTATION_UPDATE"] = "documentation_update";
  TaskType2["COMPLIANCE_REVIEW"] = "compliance_review";
  TaskType2["TECH_STACK_UPGRADE"] = "tech_stack_upgrade";
  TaskType2["STAKEHOLDER_REVIEW"] = "stakeholder_review";
})(TaskType || (TaskType = {}));
var TaskPriority;
(function(TaskPriority2) {
  TaskPriority2["LOW"] = "low";
  TaskPriority2["MEDIUM"] = "medium";
  TaskPriority2["HIGH"] = "high";
  TaskPriority2["CRITICAL"] = "critical";
})(TaskPriority || (TaskPriority = {}));
var TaskStatus;
(function(TaskStatus2) {
  TaskStatus2["PENDING"] = "pending";
  TaskStatus2["IN_PROGRESS"] = "in_progress";
  TaskStatus2["COMPLETED"] = "completed";
  TaskStatus2["OVERDUE"] = "overdue";
  TaskStatus2["CANCELLED"] = "cancelled";
})(TaskStatus || (TaskStatus = {}));
var HealthStatus;
(function(HealthStatus2) {
  HealthStatus2["HEALTHY"] = "healthy";
  HealthStatus2["WARNING"] = "warning";
  HealthStatus2["CRITICAL"] = "critical";
  HealthStatus2["UNKNOWN"] = "unknown";
})(HealthStatus || (HealthStatus = {}));
var WidgetType;
(function(WidgetType2) {
  WidgetType2["CHART"] = "chart";
  WidgetType2["METRIC"] = "metric";
  WidgetType2["TABLE"] = "table";
  WidgetType2["LIST"] = "list";
  WidgetType2["GAUGE"] = "gauge";
  WidgetType2["MAP"] = "map";
})(WidgetType || (WidgetType = {}));
var WidgetSize;
(function(WidgetSize2) {
  WidgetSize2["SMALL"] = "small";
  WidgetSize2["MEDIUM"] = "medium";
  WidgetSize2["LARGE"] = "large";
  WidgetSize2["EXTRA_LARGE"] = "extra_large";
})(WidgetSize || (WidgetSize = {}));
var ExportFormat;
(function(ExportFormat2) {
  ExportFormat2["PDF"] = "pdf";
  ExportFormat2["EXCEL"] = "excel";
  ExportFormat2["CSV"] = "csv";
  ExportFormat2["JSON"] = "json";
})(ExportFormat || (ExportFormat = {}));

// src/app/modules/dashboard/dashboard.service.ts
var DashboardService = class _DashboardService {
  apiService;
  metricsSubject = new BehaviorSubject(null);
  metrics$ = this.metricsSubject.asObservable();
  constructor(apiService) {
    this.apiService = apiService;
  }
  /**
   * Get dashboard metrics
   */
  getDashboardMetrics(filters) {
    return this.getMockDashboardMetrics().pipe(
      delay(500),
      // Simulate API delay
      map((metrics) => {
        this.metricsSubject.next(metrics);
        return metrics;
      })
    );
  }
  /**
   * Get application status distribution
   */
  getApplicationStatusDistribution() {
    return this.getMockApplicationStatusData();
  }
  /**
   * Get tech stack distribution
   */
  getTechStackDistribution() {
    return this.getMockTechStackData();
  }
  /**
   * Get security metrics over time
   */
  getSecurityMetricsOverTime(timeframe = "30d") {
    return this.getMockSecurityTrendsData();
  }
  /**
   * Get dependency health trends
   */
  getDependencyHealthTrends(timeframe = "30d") {
    return this.getMockDependencyTrendsData();
  }
  /**
   * Get recent activity
   */
  getRecentActivity(limit = 10) {
    return this.getMockActivityData().pipe(map((activities) => activities.slice(0, limit)));
  }
  /**
   * Get upcoming tasks
   */
  getUpcomingTasks(limit = 10) {
    return this.getMockTaskData().pipe(map((tasks) => tasks.slice(0, limit)));
  }
  /**
   * Export dashboard data
   */
  exportDashboardData(format) {
    return of(new Blob(["Mock export data"], { type: "text/plain" }));
  }
  /**
   * Mock data methods (replace with real API calls)
   */
  getMockDashboardMetrics() {
    const mockMetrics = {
      totalApplications: 127,
      applicationsByStatus: [
        { status: "Production", count: 85, percentage: 66.9, color: "#22c55e" },
        { status: "Development", count: 25, percentage: 19.7, color: "#3b82f6" },
        { status: "Testing", count: 12, percentage: 9.4, color: "#f59e0b" },
        { status: "Deprecated", count: 5, percentage: 3.9, color: "#ef4444" }
      ],
      applicationsByCriticality: [
        { criticality: "Critical", count: 15, percentage: 11.8, color: "#ef4444" },
        { criticality: "High", count: 35, percentage: 27.6, color: "#f59e0b" },
        { criticality: "Medium", count: 52, percentage: 40.9, color: "#3b82f6" },
        { criticality: "Low", count: 25, percentage: 19.7, color: "#22c55e" }
      ],
      applicationsByDepartment: [
        {
          department: "Engineering",
          applicationCount: 45,
          percentage: 35.4,
          averageSecurityScore: 8.2,
          criticalVulnerabilities: 3
        },
        {
          department: "Product",
          applicationCount: 28,
          percentage: 22,
          averageSecurityScore: 7.8,
          criticalVulnerabilities: 2
        },
        {
          department: "Marketing",
          applicationCount: 18,
          percentage: 14.2,
          averageSecurityScore: 8.5,
          criticalVulnerabilities: 0
        },
        {
          department: "Sales",
          applicationCount: 22,
          percentage: 17.3,
          averageSecurityScore: 7.9,
          criticalVulnerabilities: 1
        },
        {
          department: "Operations",
          applicationCount: 14,
          percentage: 11,
          averageSecurityScore: 8.7,
          criticalVulnerabilities: 0
        }
      ],
      techStackDistribution: [
        {
          technology: "Angular",
          category: "Frontend",
          usageCount: 42,
          percentage: 33.1,
          averageVersion: "16.2",
          endOfLifeCount: 2,
          securityIssues: 1
        },
        {
          technology: ".NET Core",
          category: "Backend",
          usageCount: 38,
          percentage: 29.9,
          averageVersion: "7.0",
          endOfLifeCount: 0,
          securityIssues: 0
        },
        {
          technology: "SQL Server",
          category: "Database",
          usageCount: 35,
          percentage: 27.6,
          averageVersion: "2019",
          endOfLifeCount: 3,
          securityIssues: 2
        },
        {
          technology: "Docker",
          category: "Infrastructure",
          usageCount: 48,
          percentage: 37.8,
          averageVersion: "24.0",
          endOfLifeCount: 0,
          securityIssues: 0
        }
      ],
      dependencyHealth: {
        totalDependencies: 1247,
        healthyDependencies: 1089,
        outdatedDependencies: 98,
        vulnerableDependencies: 45,
        endOfLifeDependencies: 15,
        healthScore: 87.3,
        criticalUpdatesRequired: 12
      },
      securityMetrics: {
        averageSecurityScore: 8.1,
        totalVulnerabilities: 67,
        criticalVulnerabilities: 8,
        highVulnerabilities: 15,
        mediumVulnerabilities: 28,
        lowVulnerabilities: 16,
        resolvedVulnerabilities: 145,
        openVulnerabilities: 67,
        averageResolutionTime: 5.2,
        complianceScore: 92.5,
        lastAssessmentDate: /* @__PURE__ */ new Date("2024-01-15"),
        overdueAssessments: 3
      },
      recentActivity: this.getMockActivityItems(),
      upcomingTasks: this.getMockTaskItems(),
      systemHealth: [
        {
          component: "API Gateway",
          status: HealthStatus.HEALTHY,
          uptime: 99.9,
          responseTime: 145,
          errorRate: 0.1,
          lastChecked: /* @__PURE__ */ new Date(),
          issues: []
        },
        {
          component: "Database",
          status: HealthStatus.HEALTHY,
          uptime: 99.8,
          responseTime: 23,
          errorRate: 0,
          lastChecked: /* @__PURE__ */ new Date(),
          issues: []
        },
        {
          component: "Authentication Service",
          status: HealthStatus.WARNING,
          uptime: 98.5,
          responseTime: 890,
          errorRate: 1.2,
          lastChecked: /* @__PURE__ */ new Date(),
          issues: ["High response time"]
        }
      ]
    };
    return of(mockMetrics);
  }
  getMockApplicationStatusData() {
    return of({
      labels: ["Production", "Development", "Testing", "Staging", "Deprecated"],
      datasets: [{
        label: "Applications",
        data: [85, 25, 12, 8, 5],
        backgroundColor: ["#22c55e", "#3b82f6", "#f59e0b", "#8b5cf6", "#ef4444"]
      }]
    });
  }
  getMockTechStackData() {
    return of({
      labels: ["Angular", ".NET Core", "SQL Server", "Docker", "Redis", "MongoDB"],
      datasets: [{
        label: "Usage Count",
        data: [42, 38, 35, 48, 22, 18],
        backgroundColor: ["#0ea5e9", "#22c55e", "#f59e0b", "#ef4444", "#8b5cf6", "#06b6d4"]
      }]
    });
  }
  getMockSecurityTrendsData() {
    return of({
      labels: ["Week 1", "Week 2", "Week 3", "Week 4"],
      datasets: [
        {
          label: "Security Score",
          data: [7.8, 8, 8.1, 8.1],
          borderColor: "#22c55e",
          backgroundColor: "rgba(34, 197, 94, 0.1)",
          fill: true
        },
        {
          label: "Vulnerabilities",
          data: [78, 72, 69, 67],
          borderColor: "#ef4444",
          backgroundColor: "rgba(239, 68, 68, 0.1)",
          fill: true
        }
      ]
    });
  }
  getMockDependencyTrendsData() {
    return of({
      labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
      datasets: [
        {
          label: "Healthy",
          data: [1020, 1045, 1067, 1078, 1085, 1089],
          borderColor: "#22c55e",
          backgroundColor: "#22c55e"
        },
        {
          label: "Outdated",
          data: [125, 118, 110, 105, 102, 98],
          borderColor: "#f59e0b",
          backgroundColor: "#f59e0b"
        },
        {
          label: "Vulnerable",
          data: [58, 52, 48, 47, 46, 45],
          borderColor: "#ef4444",
          backgroundColor: "#ef4444"
        }
      ]
    });
  }
  getMockActivityData() {
    return of(this.getMockActivityItems());
  }
  getMockTaskData() {
    return of(this.getMockTaskItems());
  }
  getMockActivityItems() {
    return [
      {
        id: "1",
        type: ActivityType.VULNERABILITY_DISCOVERED,
        title: "Critical Vulnerability Detected",
        description: "SQL injection vulnerability found in Payment Service",
        applicationName: "Payment Service",
        applicationId: 1,
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1e3),
        user: "Security Scanner",
        severity: "critical",
        icon: "M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z",
        color: "#ef4444"
      },
      {
        id: "2",
        type: ActivityType.APPLICATION_UPDATED,
        title: "Application Updated",
        description: "User Management system updated to version 2.1.0",
        applicationName: "User Management",
        applicationId: 2,
        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1e3),
        user: "John Doe",
        icon: "M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12",
        color: "#22c55e"
      },
      {
        id: "3",
        type: ActivityType.DEPENDENCY_UPDATED,
        title: "Dependency Updated",
        description: "Angular updated from 16.1 to 16.2 in CRM System",
        applicationName: "CRM System",
        applicationId: 3,
        timestamp: new Date(Date.now() - 6 * 60 * 60 * 1e3),
        user: "Jane Smith",
        icon: "M8 16l2.879-2.879m0 0a3 3 0 104.243-4.242 3 3 0 00-4.243 4.242zM21 12a9 9 0 11-18 0 9 9 0 0118 0z",
        color: "#3b82f6"
      }
    ];
  }
  getMockTaskItems() {
    return [
      {
        id: "1",
        type: TaskType.SECURITY_ASSESSMENT,
        title: "Security Assessment Due",
        description: "Quarterly security assessment for E-commerce Platform",
        applicationName: "E-commerce Platform",
        applicationId: 4,
        dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1e3),
        priority: TaskPriority.HIGH,
        assignee: "Security Team",
        status: TaskStatus.PENDING,
        estimatedEffort: 8
      },
      {
        id: "2",
        type: TaskType.DEPENDENCY_UPDATE,
        title: "Critical Dependency Update",
        description: "Update vulnerable lodash library in Analytics Dashboard",
        applicationName: "Analytics Dashboard",
        applicationId: 5,
        dueDate: new Date(Date.now() + 1 * 24 * 60 * 60 * 1e3),
        priority: TaskPriority.CRITICAL,
        assignee: "Development Team",
        status: TaskStatus.IN_PROGRESS,
        estimatedEffort: 4
      },
      {
        id: "3",
        type: TaskType.DOCUMENTATION_UPDATE,
        title: "API Documentation Update",
        description: "Update API documentation for Notification Service v3.0",
        applicationName: "Notification Service",
        applicationId: 6,
        dueDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1e3),
        priority: TaskPriority.MEDIUM,
        assignee: "Technical Writer",
        status: TaskStatus.PENDING,
        estimatedEffort: 6
      }
    ];
  }
  static \u0275fac = function DashboardService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _DashboardService)(\u0275\u0275inject(BaseApiService));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _DashboardService, factory: _DashboardService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(DashboardService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: BaseApiService }], null);
})();

// src/app/modules/dashboard/dashboard.component.ts
var _c0 = () => [1, 2, 3, 4, 5, 6, 7, 8];
function DashboardComponent_div_13_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "div", 15);
  }
}
function DashboardComponent_div_13_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 12)(1, "div", 13);
    \u0275\u0275template(2, DashboardComponent_div_13_div_2_Template, 1, 0, "div", 14);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    \u0275\u0275advance(2);
    \u0275\u0275property("ngForOf", \u0275\u0275pureFunction0(1, _c0));
  }
}
function DashboardComponent_div_14_div_58_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 48)(1, "div", 49);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(2, "svg", 50);
    \u0275\u0275element(3, "path", 51);
    \u0275\u0275elementEnd()();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(4, "div", 52)(5, "div", 53);
    \u0275\u0275text(6);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "div", 54);
    \u0275\u0275text(8);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(9, "div", 55)(10, "span", 56);
    \u0275\u0275text(11);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(12, "span", 57);
    \u0275\u0275text(13);
    \u0275\u0275pipe(14, "date");
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    const activity_r1 = ctx.$implicit;
    \u0275\u0275advance();
    \u0275\u0275styleProp("color", activity_r1.color);
    \u0275\u0275advance(2);
    \u0275\u0275attribute("d", activity_r1.icon);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(activity_r1.title);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(activity_r1.description);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(activity_r1.applicationName);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(\u0275\u0275pipeBind2(14, 7, activity_r1.timestamp, "short"));
  }
}
function DashboardComponent_div_14_div_64_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 58);
    \u0275\u0275element(1, "div", 59);
    \u0275\u0275elementStart(2, "div", 60)(3, "div", 61);
    \u0275\u0275text(4);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "div", 62);
    \u0275\u0275text(6);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "div", 63)(8, "span", 64);
    \u0275\u0275text(9);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "span", 65);
    \u0275\u0275text(11);
    \u0275\u0275pipe(12, "date");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(13, "span", 66);
    \u0275\u0275text(14);
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    const task_r2 = ctx.$implicit;
    \u0275\u0275advance();
    \u0275\u0275classMap("priority-" + task_r2.priority);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(task_r2.title);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(task_r2.description);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(task_r2.applicationName);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1("Due ", \u0275\u0275pipeBind2(12, 7, task_r2.dueDate, "MMM d"), "");
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(task_r2.assignee);
  }
}
function DashboardComponent_div_14_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 16)(1, "div", 17)(2, "app-card", 18)(3, "div", 19)(4, "div", 20);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "div", 21);
    \u0275\u0275text(7, "Total Applications");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(8, "div", 22);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(9, "svg", 23);
    \u0275\u0275element(10, "polyline", 24)(11, "polyline", 25);
    \u0275\u0275elementEnd();
    \u0275\u0275text(12, " +12% from last month ");
    \u0275\u0275elementEnd()()();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(13, "app-card", 18)(14, "div", 19)(15, "div", 20);
    \u0275\u0275text(16);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(17, "div", 21);
    \u0275\u0275text(18, "Security Score");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(19, "div", 22);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(20, "svg", 23);
    \u0275\u0275element(21, "polyline", 24)(22, "polyline", 25);
    \u0275\u0275elementEnd();
    \u0275\u0275text(23, " +0.3 from last week ");
    \u0275\u0275elementEnd()()();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(24, "app-card", 18)(25, "div", 19)(26, "div", 20);
    \u0275\u0275text(27);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(28, "div", 21);
    \u0275\u0275text(29, "Dependency Health");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(30, "div", 26);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(31, "svg", 23);
    \u0275\u0275element(32, "line", 27);
    \u0275\u0275elementEnd();
    \u0275\u0275text(33, " No change ");
    \u0275\u0275elementEnd()()();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(34, "app-card", 18)(35, "div", 19)(36, "div", 20);
    \u0275\u0275text(37);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(38, "div", 21);
    \u0275\u0275text(39, "Critical Vulnerabilities");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(40, "div", 28);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(41, "svg", 23);
    \u0275\u0275element(42, "polyline", 29)(43, "polyline", 30);
    \u0275\u0275elementEnd();
    \u0275\u0275text(44, " +2 from last week ");
    \u0275\u0275elementEnd()()()();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(45, "div", 31)(46, "app-card", 32);
    \u0275\u0275element(47, "app-chart", 33);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(48, "app-card", 34);
    \u0275\u0275element(49, "app-chart", 35);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(50, "app-card", 36);
    \u0275\u0275element(51, "app-chart", 37);
    \u0275\u0275elementStart(52, "div", 38)(53, "app-button", 39);
    \u0275\u0275text(54, " View All Vulnerabilities ");
    \u0275\u0275elementEnd()()()();
    \u0275\u0275elementStart(55, "div", 40)(56, "app-card", 41)(57, "div", 42);
    \u0275\u0275template(58, DashboardComponent_div_14_div_58_Template, 15, 10, "div", 43);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(59, "div", 38)(60, "app-button", 44);
    \u0275\u0275text(61, " View All Activity ");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(62, "app-card", 45)(63, "div", 46);
    \u0275\u0275template(64, DashboardComponent_div_14_div_64_Template, 15, 10, "div", 47);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(65, "div", 38)(66, "app-button", 44);
    \u0275\u0275text(67, " View All Tasks ");
    \u0275\u0275elementEnd()()()()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275advance(5);
    \u0275\u0275textInterpolate(ctx_r2.metrics.totalApplications);
    \u0275\u0275advance(11);
    \u0275\u0275textInterpolate1("", ctx_r2.metrics.securityMetrics.averageSecurityScore, "/10");
    \u0275\u0275advance(11);
    \u0275\u0275textInterpolate1("", ctx_r2.metrics.dependencyHealth.healthScore, "%");
    \u0275\u0275advance(10);
    \u0275\u0275textInterpolate(ctx_r2.metrics.securityMetrics.criticalVulnerabilities);
    \u0275\u0275advance(10);
    \u0275\u0275property("data", ctx_r2.applicationStatusChartData)("loading", ctx_r2.isLoading);
    \u0275\u0275advance(2);
    \u0275\u0275property("data", ctx_r2.techStackChartData)("loading", ctx_r2.isLoading);
    \u0275\u0275advance(2);
    \u0275\u0275property("data", ctx_r2.securityTrendsChartData)("loading", ctx_r2.isLoading);
    \u0275\u0275advance(2);
    \u0275\u0275property("fullWidth", true);
    \u0275\u0275advance(5);
    \u0275\u0275property("ngForOf", ctx_r2.recentActivity);
    \u0275\u0275advance(2);
    \u0275\u0275property("fullWidth", true);
    \u0275\u0275advance(4);
    \u0275\u0275property("ngForOf", ctx_r2.upcomingTasks);
    \u0275\u0275advance(2);
    \u0275\u0275property("fullWidth", true);
  }
}
function DashboardComponent_div_15_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 67)(1, "div", 68);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(2, "svg", 69);
    \u0275\u0275element(3, "circle", 70)(4, "line", 71)(5, "line", 72);
    \u0275\u0275elementEnd();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(6, "h3");
    \u0275\u0275text(7, "Failed to load dashboard");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(8, "p");
    \u0275\u0275text(9, "There was an error loading the dashboard data. Please try again.");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "app-button", 73);
    \u0275\u0275listener("clicked", function DashboardComponent_div_15_Template_app_button_clicked_10_listener() {
      \u0275\u0275restoreView(_r4);
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.loadDashboard());
    });
    \u0275\u0275text(11, " Retry ");
    \u0275\u0275elementEnd()()();
  }
}
var DashboardComponent = class _DashboardComponent {
  dashboardService;
  metrics = null;
  recentActivity = [];
  upcomingTasks = [];
  isLoading = true;
  hasError = false;
  isExporting = false;
  // Chart data
  applicationStatusChartData = null;
  techStackChartData = null;
  securityTrendsChartData = null;
  destroy$ = new Subject();
  constructor(dashboardService) {
    this.dashboardService = dashboardService;
  }
  ngOnInit() {
    this.loadDashboard();
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  loadDashboard() {
    this.isLoading = true;
    this.hasError = false;
    this.dashboardService.getDashboardMetrics().pipe(takeUntil(this.destroy$)).subscribe({
      next: (metrics) => {
        this.metrics = metrics;
        this.recentActivity = metrics.recentActivity.slice(0, 5);
        this.upcomingTasks = metrics.upcomingTasks.slice(0, 5);
        this.loadChartData();
        this.isLoading = false;
      },
      error: (error) => {
        console.error("Error loading dashboard:", error);
        this.hasError = true;
        this.isLoading = false;
      }
    });
  }
  loadChartData() {
    this.dashboardService.getApplicationStatusDistribution().pipe(takeUntil(this.destroy$)).subscribe((data) => {
      this.applicationStatusChartData = data;
    });
    this.dashboardService.getTechStackDistribution().pipe(takeUntil(this.destroy$)).subscribe((data) => {
      this.techStackChartData = data;
    });
    this.dashboardService.getSecurityMetricsOverTime().pipe(takeUntil(this.destroy$)).subscribe((data) => {
      this.securityTrendsChartData = data;
    });
  }
  exportDashboard() {
    this.isExporting = true;
    this.dashboardService.exportDashboardData("pdf").pipe(takeUntil(this.destroy$)).subscribe({
      next: (blob) => {
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = `dashboard-export-${(/* @__PURE__ */ new Date()).toISOString().split("T")[0]}.pdf`;
        link.click();
        window.URL.revokeObjectURL(url);
        this.isExporting = false;
      },
      error: (error) => {
        console.error("Error exporting dashboard:", error);
        this.isExporting = false;
      }
    });
  }
  static \u0275fac = function DashboardComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _DashboardComponent)(\u0275\u0275directiveInject(DashboardService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _DashboardComponent, selectors: [["app-dashboard"]], decls: 16, vars: 4, consts: [[1, "dashboard"], [1, "dashboard-header"], [1, "header-content"], [1, "header-info"], [1, "dashboard-title"], [1, "dashboard-subtitle"], [1, "header-actions"], ["variant", "outline", "size", "sm", "leftIcon", "M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l4-4m-4 4V4", 3, "clicked", "loading"], ["variant", "primary", "size", "sm", "leftIcon", "M12 4v16m8-8H4", "routerLink", "/applications/new"], ["class", "dashboard-loading", 4, "ngIf"], ["class", "dashboard-content", 4, "ngIf"], ["class", "dashboard-error", 4, "ngIf"], [1, "dashboard-loading"], [1, "loading-grid"], ["class", "loading-card", 4, "ngFor", "ngForOf"], [1, "loading-card"], [1, "dashboard-content"], [1, "metrics-grid"], ["variant", "default", "size", "sm", 1, "metric-card"], [1, "metric-content"], [1, "metric-value"], [1, "metric-label"], [1, "metric-change", "positive"], ["viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", 1, "change-icon"], ["points", "23,6 13.5,15.5 8.5,10.5 1,18"], ["points", "17,6 23,6 23,12"], [1, "metric-change", "neutral"], ["x1", "5", "y1", "12", "x2", "19", "y2", "12"], [1, "metric-change", "negative"], ["points", "23,18 13.5,8.5 8.5,13.5 1,6"], ["points", "17,18 23,18 23,12"], [1, "analytics-grid"], ["title", "Application Status", "subtitle", "Distribution by lifecycle status", 1, "chart-card"], ["type", "doughnut", "height", "300px", 3, "data", "loading"], ["title", "Tech Stack", "subtitle", "Most used technologies", 1, "chart-card"], ["type", "bar", "height", "300px", 3, "data", "loading"], ["title", "Security Trends", "subtitle", "Security score and vulnerabilities over time", 1, "chart-card"], ["type", "line", "height", "300px", 3, "data", "loading"], ["slot", "footer"], ["variant", "outline", "size", "sm", "routerLink", "/security/vulnerabilities", 3, "fullWidth"], [1, "activity-grid"], ["title", "Recent Activity", "subtitle", "Latest updates and changes", 1, "activity-card"], [1, "activity-list"], ["class", "activity-item", 4, "ngFor", "ngForOf"], ["variant", "ghost", "size", "sm", 3, "fullWidth"], ["title", "Upcoming Tasks", "subtitle", "Items requiring attention", 1, "tasks-card"], [1, "tasks-list"], ["class", "task-item", 4, "ngFor", "ngForOf"], [1, "activity-item"], [1, "activity-icon"], ["viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2"], [1, "activity-content"], [1, "activity-title"], [1, "activity-description"], [1, "activity-meta"], [1, "activity-app"], [1, "activity-time"], [1, "task-item"], [1, "task-priority"], [1, "task-content"], [1, "task-title"], [1, "task-description"], [1, "task-meta"], [1, "task-app"], [1, "task-due"], [1, "task-assignee"], [1, "dashboard-error"], [1, "error-content"], ["viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", 1, "error-icon"], ["cx", "12", "cy", "12", "r", "10"], ["x1", "15", "y1", "9", "x2", "9", "y2", "15"], ["x1", "9", "y1", "9", "x2", "15", "y2", "15"], ["variant", "primary", 3, "clicked"]], template: function DashboardComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "div", 3)(4, "h1", 4);
      \u0275\u0275text(5, "Dashboard");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(6, "p", 5);
      \u0275\u0275text(7, " Welcome back! Here's what's happening with your applications. ");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(8, "div", 6)(9, "app-button", 7);
      \u0275\u0275listener("clicked", function DashboardComponent_Template_app_button_clicked_9_listener() {
        return ctx.exportDashboard();
      });
      \u0275\u0275text(10, " Export ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(11, "app-button", 8);
      \u0275\u0275text(12, " Add Application ");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275template(13, DashboardComponent_div_13_Template, 3, 2, "div", 9)(14, DashboardComponent_div_14_Template, 68, 15, "div", 10)(15, DashboardComponent_div_15_Template, 12, 0, "div", 11);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275advance(9);
      \u0275\u0275property("loading", ctx.isExporting);
      \u0275\u0275advance(4);
      \u0275\u0275property("ngIf", ctx.isLoading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.isLoading && ctx.metrics);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.hasError);
    }
  }, dependencies: [CommonModule, NgForOf, NgIf, DatePipe, RouterModule, RouterLink, CardComponent, ButtonComponent, ChartComponent], styles: ['\n\n.dashboard[_ngcontent-%COMP%] {\n  min-height: 100%;\n  background: var(--neutral-50);\n}\n.dashboard-header[_ngcontent-%COMP%] {\n  margin-bottom: var(--spacing-xl);\n}\n.header-content[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: flex-start;\n  justify-content: space-between;\n  gap: var(--spacing-lg);\n}\n.header-info[_ngcontent-%COMP%] {\n  flex: 1;\n}\n.dashboard-title[_ngcontent-%COMP%] {\n  margin: 0 0 var(--spacing-xs) 0;\n  font-size: var(--text-3xl);\n  font-weight: 700;\n  color: var(--secondary-900);\n}\n.dashboard-subtitle[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: var(--text-base);\n  color: var(--secondary-600);\n  line-height: var(--leading-relaxed);\n}\n.header-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: var(--spacing-sm);\n  flex-shrink: 0;\n}\n.dashboard-loading[_ngcontent-%COMP%] {\n  margin-top: var(--spacing-xl);\n}\n.loading-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: var(--spacing-lg);\n}\n.loading-card[_ngcontent-%COMP%] {\n  height: 200px;\n  background: white;\n  border-radius: var(--radius-lg);\n  border: 1px solid var(--secondary-200);\n  position: relative;\n  overflow: hidden;\n}\n.loading-card[_ngcontent-%COMP%]::after {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background:\n    linear-gradient(\n      90deg,\n      transparent,\n      rgba(255, 255, 255, 0.4),\n      transparent);\n  animation: _ngcontent-%COMP%_shimmer 1.5s infinite;\n}\n@keyframes _ngcontent-%COMP%_shimmer {\n  0% {\n    left: -100%;\n  }\n  100% {\n    left: 100%;\n  }\n}\n.dashboard-content[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-xl);\n}\n.metrics-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: var(--spacing-lg);\n}\n.metric-card[_ngcontent-%COMP%]   .metric-content[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: var(--spacing-lg);\n}\n.metric-card[_ngcontent-%COMP%]   .metric-value[_ngcontent-%COMP%] {\n  font-size: var(--text-3xl);\n  font-weight: 700;\n  color: var(--primary-600);\n  line-height: 1;\n  margin-bottom: var(--spacing-sm);\n}\n.metric-card[_ngcontent-%COMP%]   .metric-label[_ngcontent-%COMP%] {\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n  font-weight: 500;\n  margin-bottom: var(--spacing-md);\n}\n.metric-card[_ngcontent-%COMP%]   .metric-change[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: var(--spacing-xs);\n  font-size: var(--text-xs);\n  font-weight: 500;\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border-radius: var(--radius-sm);\n}\n.metric-card[_ngcontent-%COMP%]   .metric-change[_ngcontent-%COMP%]   .change-icon[_ngcontent-%COMP%] {\n  width: 12px;\n  height: 12px;\n}\n.metric-card[_ngcontent-%COMP%]   .metric-change.positive[_ngcontent-%COMP%] {\n  background: var(--success-100);\n  color: var(--success-700);\n}\n.metric-card[_ngcontent-%COMP%]   .metric-change.negative[_ngcontent-%COMP%] {\n  background: var(--error-100);\n  color: var(--error-700);\n}\n.metric-card[_ngcontent-%COMP%]   .metric-change.neutral[_ngcontent-%COMP%] {\n  background: var(--secondary-100);\n  color: var(--secondary-700);\n}\n.analytics-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n  gap: var(--spacing-lg);\n}\n.chart-card[_ngcontent-%COMP%]   .chart-container[_ngcontent-%COMP%] {\n  padding: var(--spacing-lg);\n}\n.chart-card[_ngcontent-%COMP%]   .chart-placeholder[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  background: var(--neutral-100);\n  border-radius: var(--radius-md);\n  color: var(--secondary-500);\n  margin-bottom: var(--spacing-lg);\n}\n.chart-card[_ngcontent-%COMP%]   .chart-placeholder[_ngcontent-%COMP%]   .chart-icon[_ngcontent-%COMP%] {\n  width: 48px;\n  height: 48px;\n  margin-bottom: var(--spacing-md);\n}\n.chart-card[_ngcontent-%COMP%]   .chart-placeholder[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: var(--text-sm);\n}\n.chart-card[_ngcontent-%COMP%]   .chart-legend[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-sm);\n}\n.chart-card[_ngcontent-%COMP%]   .legend-item[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n}\n.chart-card[_ngcontent-%COMP%]   .legend-color[_ngcontent-%COMP%] {\n  width: 12px;\n  height: 12px;\n  border-radius: 2px;\n  flex-shrink: 0;\n}\n.chart-card[_ngcontent-%COMP%]   .legend-label[_ngcontent-%COMP%] {\n  flex: 1;\n  font-size: var(--text-sm);\n  color: var(--secondary-700);\n}\n.chart-card[_ngcontent-%COMP%]   .legend-value[_ngcontent-%COMP%] {\n  font-size: var(--text-sm);\n  font-weight: 600;\n  color: var(--secondary-900);\n}\n.tech-stack-list[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-md);\n  padding: var(--spacing-lg);\n}\n.tech-item[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  gap: var(--spacing-md);\n}\n.tech-info[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  flex: 1;\n}\n.tech-name[_ngcontent-%COMP%] {\n  font-size: var(--text-sm);\n  font-weight: 500;\n  color: var(--secondary-900);\n}\n.tech-category[_ngcontent-%COMP%] {\n  font-size: var(--text-xs);\n  color: var(--secondary-500);\n}\n.tech-stats[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n  min-width: 80px;\n}\n.tech-count[_ngcontent-%COMP%] {\n  font-size: var(--text-sm);\n  font-weight: 600;\n  color: var(--secondary-700);\n  min-width: 24px;\n  text-align: right;\n}\n.tech-bar[_ngcontent-%COMP%] {\n  width: 40px;\n  height: 4px;\n  background: var(--secondary-200);\n  border-radius: 2px;\n  overflow: hidden;\n}\n.tech-bar-fill[_ngcontent-%COMP%] {\n  height: 100%;\n  background: var(--primary-500);\n  transition: width 0.3s ease;\n}\n.security-overview[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: var(--spacing-lg);\n  padding: var(--spacing-lg);\n}\n.security-metric[_ngcontent-%COMP%] {\n  text-align: center;\n}\n.security-value[_ngcontent-%COMP%] {\n  font-size: var(--text-2xl);\n  font-weight: 700;\n  margin-bottom: var(--spacing-xs);\n}\n.security-value.critical[_ngcontent-%COMP%] {\n  color: var(--error-600);\n}\n.security-value.high[_ngcontent-%COMP%] {\n  color: var(--warning-600);\n}\n.security-value.medium[_ngcontent-%COMP%] {\n  color: var(--primary-600);\n}\n.security-value.low[_ngcontent-%COMP%] {\n  color: var(--success-600);\n}\n.security-label[_ngcontent-%COMP%] {\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n  font-weight: 500;\n}\n.activity-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n  gap: var(--spacing-lg);\n}\n.activity-list[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-md);\n  padding: var(--spacing-lg);\n}\n.activity-item[_ngcontent-%COMP%] {\n  display: flex;\n  gap: var(--spacing-md);\n  padding: var(--spacing-md);\n  border-radius: var(--radius-md);\n  transition: background-color 0.2s ease;\n}\n.activity-item[_ngcontent-%COMP%]:hover {\n  background: var(--neutral-50);\n}\n.activity-icon[_ngcontent-%COMP%] {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background: var(--neutral-100);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n}\n.activity-icon[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\n  width: 16px;\n  height: 16px;\n}\n.activity-content[_ngcontent-%COMP%] {\n  flex: 1;\n  min-width: 0;\n}\n.activity-title[_ngcontent-%COMP%] {\n  font-size: var(--text-sm);\n  font-weight: 500;\n  color: var(--secondary-900);\n  margin-bottom: var(--spacing-xs);\n}\n.activity-description[_ngcontent-%COMP%] {\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n  line-height: var(--leading-relaxed);\n  margin-bottom: var(--spacing-xs);\n}\n.activity-meta[_ngcontent-%COMP%] {\n  display: flex;\n  gap: var(--spacing-md);\n  font-size: var(--text-xs);\n  color: var(--secondary-500);\n}\n.activity-app[_ngcontent-%COMP%] {\n  font-weight: 500;\n}\n.tasks-list[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-md);\n  padding: var(--spacing-lg);\n}\n.task-item[_ngcontent-%COMP%] {\n  display: flex;\n  gap: var(--spacing-md);\n  padding: var(--spacing-md);\n  border-radius: var(--radius-md);\n  border-left: 3px solid transparent;\n  transition: all 0.2s ease;\n}\n.task-item[_ngcontent-%COMP%]:hover {\n  background: var(--neutral-50);\n}\n.task-priority[_ngcontent-%COMP%] {\n  width: 3px;\n  border-radius: 2px;\n  margin-left: -3px;\n}\n.task-priority.priority-critical[_ngcontent-%COMP%] {\n  background: var(--error-500);\n}\n.task-priority.priority-high[_ngcontent-%COMP%] {\n  background: var(--warning-500);\n}\n.task-priority.priority-medium[_ngcontent-%COMP%] {\n  background: var(--primary-500);\n}\n.task-priority.priority-low[_ngcontent-%COMP%] {\n  background: var(--success-500);\n}\n.task-content[_ngcontent-%COMP%] {\n  flex: 1;\n  min-width: 0;\n}\n.task-title[_ngcontent-%COMP%] {\n  font-size: var(--text-sm);\n  font-weight: 500;\n  color: var(--secondary-900);\n  margin-bottom: var(--spacing-xs);\n}\n.task-description[_ngcontent-%COMP%] {\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n  line-height: var(--leading-relaxed);\n  margin-bottom: var(--spacing-xs);\n}\n.task-meta[_ngcontent-%COMP%] {\n  display: flex;\n  gap: var(--spacing-md);\n  font-size: var(--text-xs);\n  color: var(--secondary-500);\n}\n.task-app[_ngcontent-%COMP%] {\n  font-weight: 500;\n}\n.task-due[_ngcontent-%COMP%] {\n  color: var(--warning-600);\n  font-weight: 500;\n}\n.dashboard-error[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 400px;\n}\n.error-content[_ngcontent-%COMP%] {\n  text-align: center;\n  max-width: 400px;\n}\n.error-icon[_ngcontent-%COMP%] {\n  width: 64px;\n  height: 64px;\n  color: var(--error-500);\n  margin: 0 auto var(--spacing-lg);\n}\n.error-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin-bottom: var(--spacing-sm);\n  color: var(--secondary-900);\n}\n.error-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin-bottom: var(--spacing-lg);\n  color: var(--secondary-600);\n}\n@media (max-width: 768px) {\n  .header-content[_ngcontent-%COMP%] {\n    flex-direction: column;\n    align-items: stretch;\n    gap: var(--spacing-md);\n  }\n  .header-actions[_ngcontent-%COMP%] {\n    justify-content: flex-end;\n  }\n  .metrics-grid[_ngcontent-%COMP%] {\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n    gap: var(--spacing-md);\n  }\n  .analytics-grid[_ngcontent-%COMP%], \n   .activity-grid[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-md);\n  }\n  .security-overview[_ngcontent-%COMP%] {\n    grid-template-columns: repeat(2, 1fr);\n    gap: var(--spacing-md);\n  }\n  .activity-meta[_ngcontent-%COMP%], \n   .task-meta[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: var(--spacing-xs);\n  }\n}\n@media (max-width: 480px) {\n  .metrics-grid[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n  }\n  .security-overview[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-md);\n  }\n}\n/*# sourceMappingURL=dashboard.component.css.map */'] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(DashboardComponent, [{
    type: Component,
    args: [{ selector: "app-dashboard", standalone: true, imports: [CommonModule, RouterModule, CardComponent, ButtonComponent, ChartComponent], template: `
    <div class="dashboard">
      <!-- Dashboard Header -->
      <div class="dashboard-header">
        <div class="header-content">
          <div class="header-info">
            <h1 class="dashboard-title">Dashboard</h1>
            <p class="dashboard-subtitle">
              Welcome back! Here's what's happening with your applications.
            </p>
          </div>
          <div class="header-actions">
            <app-button
              variant="outline"
              size="sm"
              leftIcon="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l4-4m-4 4V4"
              (clicked)="exportDashboard()"
              [loading]="isExporting"
            >
              Export
            </app-button>
            <app-button
              variant="primary"
              size="sm"
              leftIcon="M12 4v16m8-8H4"
              routerLink="/applications/new"
            >
              Add Application
            </app-button>
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div *ngIf="isLoading" class="dashboard-loading">
        <div class="loading-grid">
          <div class="loading-card" *ngFor="let item of [1,2,3,4,5,6,7,8]"></div>
        </div>
      </div>

      <!-- Dashboard Content -->
      <div *ngIf="!isLoading && metrics" class="dashboard-content">
        <!-- Key Metrics Row -->
        <div class="metrics-grid">
          <app-card variant="default" size="sm" class="metric-card">
            <div class="metric-content">
              <div class="metric-value">{{ metrics.totalApplications }}</div>
              <div class="metric-label">Total Applications</div>
              <div class="metric-change positive">
                <svg class="change-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <polyline points="23,6 13.5,15.5 8.5,10.5 1,18"></polyline>
                  <polyline points="17,6 23,6 23,12"></polyline>
                </svg>
                +12% from last month
              </div>
            </div>
          </app-card>

          <app-card variant="default" size="sm" class="metric-card">
            <div class="metric-content">
              <div class="metric-value">{{ metrics.securityMetrics.averageSecurityScore }}/10</div>
              <div class="metric-label">Security Score</div>
              <div class="metric-change positive">
                <svg class="change-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <polyline points="23,6 13.5,15.5 8.5,10.5 1,18"></polyline>
                  <polyline points="17,6 23,6 23,12"></polyline>
                </svg>
                +0.3 from last week
              </div>
            </div>
          </app-card>

          <app-card variant="default" size="sm" class="metric-card">
            <div class="metric-content">
              <div class="metric-value">{{ metrics.dependencyHealth.healthScore }}%</div>
              <div class="metric-label">Dependency Health</div>
              <div class="metric-change neutral">
                <svg class="change-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
                No change
              </div>
            </div>
          </app-card>

          <app-card variant="default" size="sm" class="metric-card">
            <div class="metric-content">
              <div class="metric-value">{{ metrics.securityMetrics.criticalVulnerabilities }}</div>
              <div class="metric-label">Critical Vulnerabilities</div>
              <div class="metric-change negative">
                <svg class="change-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <polyline points="23,18 13.5,8.5 8.5,13.5 1,6"></polyline>
                  <polyline points="17,18 23,18 23,12"></polyline>
                </svg>
                +2 from last week
              </div>
            </div>
          </app-card>
        </div>

        <!-- Charts and Analytics Row -->
        <div class="analytics-grid">
          <!-- Application Status Distribution -->
          <app-card title="Application Status" subtitle="Distribution by lifecycle status" class="chart-card">
            <app-chart
              type="doughnut"
              [data]="applicationStatusChartData"
              [loading]="isLoading"
              height="300px"
            ></app-chart>
          </app-card>

          <!-- Tech Stack Distribution -->
          <app-card title="Tech Stack" subtitle="Most used technologies" class="chart-card">
            <app-chart
              type="bar"
              [data]="techStackChartData"
              [loading]="isLoading"
              height="300px"
            ></app-chart>
          </app-card>

          <!-- Security Trends -->
          <app-card title="Security Trends" subtitle="Security score and vulnerabilities over time" class="chart-card">
            <app-chart
              type="line"
              [data]="securityTrendsChartData"
              [loading]="isLoading"
              height="300px"
            ></app-chart>
            <div slot="footer">
              <app-button variant="outline" size="sm" [fullWidth]="true" routerLink="/security/vulnerabilities">
                View All Vulnerabilities
              </app-button>
            </div>
          </app-card>
        </div>

        <!-- Activity and Tasks Row -->
        <div class="activity-grid">
          <!-- Recent Activity -->
          <app-card title="Recent Activity" subtitle="Latest updates and changes" class="activity-card">
            <div class="activity-list">
              <div class="activity-item" *ngFor="let activity of recentActivity">
                <div class="activity-icon" [style.color]="activity.color">
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" [attr.d]="activity.icon" />
                  </svg>
                </div>
                <div class="activity-content">
                  <div class="activity-title">{{ activity.title }}</div>
                  <div class="activity-description">{{ activity.description }}</div>
                  <div class="activity-meta">
                    <span class="activity-app">{{ activity.applicationName }}</span>
                    <span class="activity-time">{{ activity.timestamp | date:'short' }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div slot="footer">
              <app-button variant="ghost" size="sm" [fullWidth]="true">
                View All Activity
              </app-button>
            </div>
          </app-card>

          <!-- Upcoming Tasks -->
          <app-card title="Upcoming Tasks" subtitle="Items requiring attention" class="tasks-card">
            <div class="tasks-list">
              <div class="task-item" *ngFor="let task of upcomingTasks">
                <div class="task-priority" [class]="'priority-' + task.priority"></div>
                <div class="task-content">
                  <div class="task-title">{{ task.title }}</div>
                  <div class="task-description">{{ task.description }}</div>
                  <div class="task-meta">
                    <span class="task-app">{{ task.applicationName }}</span>
                    <span class="task-due">Due {{ task.dueDate | date:'MMM d' }}</span>
                    <span class="task-assignee">{{ task.assignee }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div slot="footer">
              <app-button variant="ghost" size="sm" [fullWidth]="true">
                View All Tasks
              </app-button>
            </div>
          </app-card>
        </div>
      </div>

      <!-- Error State -->
      <div *ngIf="hasError" class="dashboard-error">
        <div class="error-content">
          <svg class="error-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="15" y1="9" x2="9" y2="15"></line>
            <line x1="9" y1="9" x2="15" y2="15"></line>
          </svg>
          <h3>Failed to load dashboard</h3>
          <p>There was an error loading the dashboard data. Please try again.</p>
          <app-button variant="primary" (clicked)="loadDashboard()">
            Retry
          </app-button>
        </div>
      </div>
    </div>
  `, styles: ['/* src/app/modules/dashboard/dashboard.component.scss */\n.dashboard {\n  min-height: 100%;\n  background: var(--neutral-50);\n}\n.dashboard-header {\n  margin-bottom: var(--spacing-xl);\n}\n.header-content {\n  display: flex;\n  align-items: flex-start;\n  justify-content: space-between;\n  gap: var(--spacing-lg);\n}\n.header-info {\n  flex: 1;\n}\n.dashboard-title {\n  margin: 0 0 var(--spacing-xs) 0;\n  font-size: var(--text-3xl);\n  font-weight: 700;\n  color: var(--secondary-900);\n}\n.dashboard-subtitle {\n  margin: 0;\n  font-size: var(--text-base);\n  color: var(--secondary-600);\n  line-height: var(--leading-relaxed);\n}\n.header-actions {\n  display: flex;\n  gap: var(--spacing-sm);\n  flex-shrink: 0;\n}\n.dashboard-loading {\n  margin-top: var(--spacing-xl);\n}\n.loading-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n  gap: var(--spacing-lg);\n}\n.loading-card {\n  height: 200px;\n  background: white;\n  border-radius: var(--radius-lg);\n  border: 1px solid var(--secondary-200);\n  position: relative;\n  overflow: hidden;\n}\n.loading-card::after {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background:\n    linear-gradient(\n      90deg,\n      transparent,\n      rgba(255, 255, 255, 0.4),\n      transparent);\n  animation: shimmer 1.5s infinite;\n}\n@keyframes shimmer {\n  0% {\n    left: -100%;\n  }\n  100% {\n    left: 100%;\n  }\n}\n.dashboard-content {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-xl);\n}\n.metrics-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n  gap: var(--spacing-lg);\n}\n.metric-card .metric-content {\n  text-align: center;\n  padding: var(--spacing-lg);\n}\n.metric-card .metric-value {\n  font-size: var(--text-3xl);\n  font-weight: 700;\n  color: var(--primary-600);\n  line-height: 1;\n  margin-bottom: var(--spacing-sm);\n}\n.metric-card .metric-label {\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n  font-weight: 500;\n  margin-bottom: var(--spacing-md);\n}\n.metric-card .metric-change {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: var(--spacing-xs);\n  font-size: var(--text-xs);\n  font-weight: 500;\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border-radius: var(--radius-sm);\n}\n.metric-card .metric-change .change-icon {\n  width: 12px;\n  height: 12px;\n}\n.metric-card .metric-change.positive {\n  background: var(--success-100);\n  color: var(--success-700);\n}\n.metric-card .metric-change.negative {\n  background: var(--error-100);\n  color: var(--error-700);\n}\n.metric-card .metric-change.neutral {\n  background: var(--secondary-100);\n  color: var(--secondary-700);\n}\n.analytics-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n  gap: var(--spacing-lg);\n}\n.chart-card .chart-container {\n  padding: var(--spacing-lg);\n}\n.chart-card .chart-placeholder {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  background: var(--neutral-100);\n  border-radius: var(--radius-md);\n  color: var(--secondary-500);\n  margin-bottom: var(--spacing-lg);\n}\n.chart-card .chart-placeholder .chart-icon {\n  width: 48px;\n  height: 48px;\n  margin-bottom: var(--spacing-md);\n}\n.chart-card .chart-placeholder p {\n  margin: 0;\n  font-size: var(--text-sm);\n}\n.chart-card .chart-legend {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-sm);\n}\n.chart-card .legend-item {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n}\n.chart-card .legend-color {\n  width: 12px;\n  height: 12px;\n  border-radius: 2px;\n  flex-shrink: 0;\n}\n.chart-card .legend-label {\n  flex: 1;\n  font-size: var(--text-sm);\n  color: var(--secondary-700);\n}\n.chart-card .legend-value {\n  font-size: var(--text-sm);\n  font-weight: 600;\n  color: var(--secondary-900);\n}\n.tech-stack-list {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-md);\n  padding: var(--spacing-lg);\n}\n.tech-item {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  gap: var(--spacing-md);\n}\n.tech-info {\n  display: flex;\n  flex-direction: column;\n  flex: 1;\n}\n.tech-name {\n  font-size: var(--text-sm);\n  font-weight: 500;\n  color: var(--secondary-900);\n}\n.tech-category {\n  font-size: var(--text-xs);\n  color: var(--secondary-500);\n}\n.tech-stats {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n  min-width: 80px;\n}\n.tech-count {\n  font-size: var(--text-sm);\n  font-weight: 600;\n  color: var(--secondary-700);\n  min-width: 24px;\n  text-align: right;\n}\n.tech-bar {\n  width: 40px;\n  height: 4px;\n  background: var(--secondary-200);\n  border-radius: 2px;\n  overflow: hidden;\n}\n.tech-bar-fill {\n  height: 100%;\n  background: var(--primary-500);\n  transition: width 0.3s ease;\n}\n.security-overview {\n  display: grid;\n  grid-template-columns: repeat(2, 1fr);\n  gap: var(--spacing-lg);\n  padding: var(--spacing-lg);\n}\n.security-metric {\n  text-align: center;\n}\n.security-value {\n  font-size: var(--text-2xl);\n  font-weight: 700;\n  margin-bottom: var(--spacing-xs);\n}\n.security-value.critical {\n  color: var(--error-600);\n}\n.security-value.high {\n  color: var(--warning-600);\n}\n.security-value.medium {\n  color: var(--primary-600);\n}\n.security-value.low {\n  color: var(--success-600);\n}\n.security-label {\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n  font-weight: 500;\n}\n.activity-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n  gap: var(--spacing-lg);\n}\n.activity-list {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-md);\n  padding: var(--spacing-lg);\n}\n.activity-item {\n  display: flex;\n  gap: var(--spacing-md);\n  padding: var(--spacing-md);\n  border-radius: var(--radius-md);\n  transition: background-color 0.2s ease;\n}\n.activity-item:hover {\n  background: var(--neutral-50);\n}\n.activity-icon {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  background: var(--neutral-100);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n}\n.activity-icon svg {\n  width: 16px;\n  height: 16px;\n}\n.activity-content {\n  flex: 1;\n  min-width: 0;\n}\n.activity-title {\n  font-size: var(--text-sm);\n  font-weight: 500;\n  color: var(--secondary-900);\n  margin-bottom: var(--spacing-xs);\n}\n.activity-description {\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n  line-height: var(--leading-relaxed);\n  margin-bottom: var(--spacing-xs);\n}\n.activity-meta {\n  display: flex;\n  gap: var(--spacing-md);\n  font-size: var(--text-xs);\n  color: var(--secondary-500);\n}\n.activity-app {\n  font-weight: 500;\n}\n.tasks-list {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-md);\n  padding: var(--spacing-lg);\n}\n.task-item {\n  display: flex;\n  gap: var(--spacing-md);\n  padding: var(--spacing-md);\n  border-radius: var(--radius-md);\n  border-left: 3px solid transparent;\n  transition: all 0.2s ease;\n}\n.task-item:hover {\n  background: var(--neutral-50);\n}\n.task-priority {\n  width: 3px;\n  border-radius: 2px;\n  margin-left: -3px;\n}\n.task-priority.priority-critical {\n  background: var(--error-500);\n}\n.task-priority.priority-high {\n  background: var(--warning-500);\n}\n.task-priority.priority-medium {\n  background: var(--primary-500);\n}\n.task-priority.priority-low {\n  background: var(--success-500);\n}\n.task-content {\n  flex: 1;\n  min-width: 0;\n}\n.task-title {\n  font-size: var(--text-sm);\n  font-weight: 500;\n  color: var(--secondary-900);\n  margin-bottom: var(--spacing-xs);\n}\n.task-description {\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n  line-height: var(--leading-relaxed);\n  margin-bottom: var(--spacing-xs);\n}\n.task-meta {\n  display: flex;\n  gap: var(--spacing-md);\n  font-size: var(--text-xs);\n  color: var(--secondary-500);\n}\n.task-app {\n  font-weight: 500;\n}\n.task-due {\n  color: var(--warning-600);\n  font-weight: 500;\n}\n.dashboard-error {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 400px;\n}\n.error-content {\n  text-align: center;\n  max-width: 400px;\n}\n.error-icon {\n  width: 64px;\n  height: 64px;\n  color: var(--error-500);\n  margin: 0 auto var(--spacing-lg);\n}\n.error-content h3 {\n  margin-bottom: var(--spacing-sm);\n  color: var(--secondary-900);\n}\n.error-content p {\n  margin-bottom: var(--spacing-lg);\n  color: var(--secondary-600);\n}\n@media (max-width: 768px) {\n  .header-content {\n    flex-direction: column;\n    align-items: stretch;\n    gap: var(--spacing-md);\n  }\n  .header-actions {\n    justify-content: flex-end;\n  }\n  .metrics-grid {\n    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n    gap: var(--spacing-md);\n  }\n  .analytics-grid,\n  .activity-grid {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-md);\n  }\n  .security-overview {\n    grid-template-columns: repeat(2, 1fr);\n    gap: var(--spacing-md);\n  }\n  .activity-meta,\n  .task-meta {\n    flex-direction: column;\n    gap: var(--spacing-xs);\n  }\n}\n@media (max-width: 480px) {\n  .metrics-grid {\n    grid-template-columns: 1fr;\n  }\n  .security-overview {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-md);\n  }\n}\n/*# sourceMappingURL=dashboard.component.css.map */\n'] }]
  }], () => [{ type: DashboardService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(DashboardComponent, { className: "DashboardComponent", filePath: "src/app/modules/dashboard/dashboard.component.ts", lineNumber: 227 });
})();
export {
  DashboardComponent
};
//# sourceMappingURL=chunk-QXKJJWHO.mjs.map
