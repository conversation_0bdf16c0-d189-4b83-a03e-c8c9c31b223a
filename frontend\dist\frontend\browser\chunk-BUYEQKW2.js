import {
  HttpClient,
  HttpHeaders,
  HttpParams
} from "./chunk-IKU57TF7.js";
import {
  BehaviorSubject,
  Injectable,
  catchError,
  map,
  retry,
  setClassMetadata,
  throwError,
  timeout,
  ɵɵdefineInjectable,
  ɵɵinject
} from "./chunk-3JQLQ36P.js";

// src/environments/environment.ts
var environment = {
  production: false,
  apiUrl: "http://localhost:5000/api",
  appName: "Application Catalog System",
  version: "1.0.0",
  features: {
    enableMockData: true,
    enableAnalytics: false,
    enableNotifications: true,
    enableFileUpload: true,
    enableExport: true
  },
  api: {
    timeout: 3e4,
    retryAttempts: 3,
    pageSize: 20
  },
  chart: {
    defaultColors: [
      "#0ea5e9",
      "#22c55e",
      "#f59e0b",
      "#ef4444",
      "#8b5cf6",
      "#06b6d4",
      "#84cc16",
      "#f97316",
      "#ec4899",
      "#6366f1"
    ],
    animationDuration: 750
  },
  storage: {
    prefix: "app_catalog_",
    tokenKey: "auth_token",
    userPreferencesKey: "user_preferences"
  }
};

// src/app/shared/services/base-api.service.ts
var BaseApiService = class _BaseApiService {
  http;
  baseUrl = environment.apiUrl || "http://localhost:5000/api";
  defaultTimeout = 3e4;
  // 30 seconds
  maxRetries = 3;
  loadingSubject = new BehaviorSubject(false);
  loading$ = this.loadingSubject.asObservable();
  constructor(http) {
    this.http = http;
  }
  /**
   * GET request with error handling and loading state
   */
  get(endpoint, params) {
    this.setLoading(true);
    const httpParams = this.buildHttpParams(params);
    const options = {
      params: httpParams,
      headers: this.getHeaders()
    };
    return this.http.get(`${this.baseUrl}/${endpoint}`, options).pipe(timeout(this.defaultTimeout), retry(this.maxRetries), map((response) => this.handleSuccess(response)), catchError((error) => this.handleError(error)));
  }
  /**
   * POST request with error handling and loading state
   */
  post(endpoint, data) {
    this.setLoading(true);
    const options = {
      headers: this.getHeaders()
    };
    return this.http.post(`${this.baseUrl}/${endpoint}`, data, options).pipe(timeout(this.defaultTimeout), map((response) => this.handleSuccess(response)), catchError((error) => this.handleError(error)));
  }
  /**
   * PUT request with error handling and loading state
   */
  put(endpoint, data) {
    this.setLoading(true);
    const options = {
      headers: this.getHeaders()
    };
    return this.http.put(`${this.baseUrl}/${endpoint}`, data, options).pipe(timeout(this.defaultTimeout), map((response) => this.handleSuccess(response)), catchError((error) => this.handleError(error)));
  }
  /**
   * PATCH request with error handling and loading state
   */
  patch(endpoint, data) {
    this.setLoading(true);
    const options = {
      headers: this.getHeaders()
    };
    return this.http.patch(`${this.baseUrl}/${endpoint}`, data, options).pipe(timeout(this.defaultTimeout), map((response) => this.handleSuccess(response)), catchError((error) => this.handleError(error)));
  }
  /**
   * DELETE request with error handling and loading state
   */
  delete(endpoint) {
    this.setLoading(true);
    const options = {
      headers: this.getHeaders()
    };
    return this.http.delete(`${this.baseUrl}/${endpoint}`, options).pipe(timeout(this.defaultTimeout), map((response) => this.handleSuccess(response)), catchError((error) => this.handleError(error)));
  }
  /**
   * Upload file with progress tracking
   */
  uploadFile(endpoint, file, additionalData) {
    this.setLoading(true);
    const formData = new FormData();
    formData.append("file", file);
    if (additionalData) {
      Object.keys(additionalData).forEach((key) => {
        formData.append(key, additionalData[key]);
      });
    }
    const headers = new HttpHeaders();
    return this.http.post(`${this.baseUrl}/${endpoint}`, formData, { headers }).pipe(
      timeout(6e4),
      // Longer timeout for file uploads
      map((response) => this.handleSuccess(response)),
      catchError((error) => this.handleError(error))
    );
  }
  /**
   * Download file
   */
  downloadFile(endpoint, filename) {
    this.setLoading(true);
    const options = {
      headers: this.getHeaders(),
      responseType: "blob"
    };
    return this.http.get(`${this.baseUrl}/${endpoint}`, options).pipe(
      timeout(6e4),
      // Longer timeout for downloads
      map((blob) => {
        this.setLoading(false);
        return blob;
      }),
      catchError((error) => this.handleError(error))
    );
  }
  /**
   * Build HTTP parameters from query params
   */
  buildHttpParams(params) {
    let httpParams = new HttpParams();
    if (params) {
      if (params.page !== void 0) {
        httpParams = httpParams.set("page", params.page.toString());
      }
      if (params.pageSize !== void 0) {
        httpParams = httpParams.set("pageSize", params.pageSize.toString());
      }
      if (params.sortBy) {
        httpParams = httpParams.set("sortBy", params.sortBy);
      }
      if (params.sortOrder) {
        httpParams = httpParams.set("sortOrder", params.sortOrder);
      }
      if (params.search) {
        httpParams = httpParams.set("search", params.search);
      }
      if (params.filters) {
        Object.keys(params.filters).forEach((key) => {
          const value = params.filters[key];
          if (value !== null && value !== void 0 && value !== "") {
            if (Array.isArray(value)) {
              value.forEach((v) => {
                httpParams = httpParams.append(`filters.${key}`, v.toString());
              });
            } else {
              httpParams = httpParams.set(`filters.${key}`, value.toString());
            }
          }
        });
      }
    }
    return httpParams;
  }
  /**
   * Get default headers
   */
  getHeaders() {
    let headers = new HttpHeaders({
      "Content-Type": "application/json",
      "Accept": "application/json"
    });
    const token = this.getAuthToken();
    if (token) {
      headers = headers.set("Authorization", `Bearer ${token}`);
    }
    return headers;
  }
  /**
   * Get authentication token from storage
   */
  getAuthToken() {
    return localStorage.getItem("auth_token") || sessionStorage.getItem("auth_token");
  }
  /**
   * Handle successful response
   */
  handleSuccess(response) {
    this.setLoading(false);
    return response;
  }
  /**
   * Handle error response
   */
  handleError(error) {
    this.setLoading(false);
    let errorMessage = "An unexpected error occurred";
    if (error.error instanceof ErrorEvent) {
      errorMessage = `Client Error: ${error.error.message}`;
    } else {
      switch (error.status) {
        case 400:
          errorMessage = "Bad Request: Please check your input";
          break;
        case 401:
          errorMessage = "Unauthorized: Please log in again";
          break;
        case 403:
          errorMessage = "Forbidden: You do not have permission to perform this action";
          break;
        case 404:
          errorMessage = "Not Found: The requested resource was not found";
          break;
        case 409:
          errorMessage = "Conflict: The resource already exists or there is a conflict";
          break;
        case 422:
          errorMessage = "Validation Error: Please check your input";
          break;
        case 500:
          errorMessage = "Internal Server Error: Please try again later";
          break;
        case 503:
          errorMessage = "Service Unavailable: The server is temporarily unavailable";
          break;
        default:
          errorMessage = `Server Error: ${error.status} - ${error.message}`;
      }
      if (error.error && error.error.message) {
        errorMessage = error.error.message;
      }
    }
    console.error("API Error:", error);
    return throwError(() => new Error(errorMessage));
  }
  /**
   * Set loading state
   */
  setLoading(loading) {
    this.loadingSubject.next(loading);
  }
  /**
   * Get current loading state
   */
  get isLoading() {
    return this.loadingSubject.value;
  }
  static \u0275fac = function BaseApiService_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _BaseApiService)(\u0275\u0275inject(HttpClient));
  };
  static \u0275prov = /* @__PURE__ */ \u0275\u0275defineInjectable({ token: _BaseApiService, factory: _BaseApiService.\u0275fac, providedIn: "root" });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(BaseApiService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{ type: HttpClient }], null);
})();

export {
  BaseApiService
};
//# sourceMappingURL=chunk-BUYEQKW2.js.map
