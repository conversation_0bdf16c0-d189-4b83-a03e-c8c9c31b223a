import { Component, Input, Output, EventEmitter, OnInit, OnChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { SlideModalComponent } from '../../slide-modal/slide-modal.component';
import { FormInputComponent } from '../../form-input/form-input.component';
import { ApplicationSelectionService, ApplicationOption } from '../../../services/application-selection.service';
import { Observable } from 'rxjs';

export interface SharedSecurityFormData {
  applicationId?: number;
  title: string;
  type: string;
  severity: string;
  status: string;
  description: string;
  impact: string;
  recommendation: string;
  cveId?: string;
  cvssScore?: number;
  discoveredDate: Date;
  discoveredBy: string;
  assignedTo?: string;
  dueDate?: Date;
  component?: string;
  version?: string;
}

@Component({
  selector: 'app-shared-security-modal',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, SlideModalComponent, FormInputComponent],
  template: `
    <app-slide-modal
      [isOpen]="isOpen"
      [title]="editMode ? 'Edit Security Item' : 'Add Security Item'"
      [subtitle]="showApplicationSelection ? 'Configure security details and select application' : 'Configure security details'"
      [loading]="loading"
      [canConfirm]="securityForm.valid"
      confirmText="Save Security Item"
      (closed)="onClose()"
      (confirmed)="onSave()"
      (cancelled)="onClose()"
    >
      <form [formGroup]="securityForm" class="security-form">
        <!-- Application Selection (only when not in applications module) -->
        <div class="form-group" *ngIf="showApplicationSelection">
          <label class="form-label">Application *</label>
          <select formControlName="applicationId" class="form-select">
            <option value="">Select an application</option>
            <option *ngFor="let app of applicationOptions$ | async" [value]="app.id">
              {{ app.name }} - {{ app.department }}
            </option>
          </select>
          <div class="field-error" *ngIf="getFieldError('applicationId')">
            {{ getFieldError('applicationId') }}
          </div>
        </div>

        <div class="form-grid">
          <app-form-input
            label="Title"
            placeholder="e.g., SQL Injection in User Search"
            formControlName="title"
            [required]="true"
            [errorMessage]="getFieldError('title')"
          ></app-form-input>

          <div class="form-group">
            <label class="form-label">Type</label>
            <select formControlName="type" class="form-select">
              <option value="vulnerability">Vulnerability</option>
              <option value="security_assessment">Security Assessment</option>
              <option value="penetration_test">Penetration Test</option>
              <option value="code_review">Code Review</option>
              <option value="compliance_check">Compliance Check</option>
              <option value="risk_assessment">Risk Assessment</option>
              <option value="incident">Security Incident</option>
              <option value="audit_finding">Audit Finding</option>
            </select>
          </div>

          <div class="form-group">
            <label class="form-label">Severity</label>
            <select formControlName="severity" class="form-select">
              <option value="critical">Critical</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="low">Low</option>
              <option value="informational">Informational</option>
            </select>
          </div>

          <div class="form-group">
            <label class="form-label">Status</label>
            <select formControlName="status" class="form-select">
              <option value="open">Open</option>
              <option value="in_progress">In Progress</option>
              <option value="resolved">Resolved</option>
              <option value="verified">Verified</option>
              <option value="closed">Closed</option>
              <option value="false_positive">False Positive</option>
              <option value="accepted_risk">Accepted Risk</option>
            </select>
          </div>

          <app-form-input
            label="CVE ID"
            placeholder="e.g., CVE-2023-1234"
            formControlName="cveId"
            [errorMessage]="getFieldError('cveId')"
          ></app-form-input>

          <app-form-input
            label="CVSS Score"
            placeholder="e.g., 7.5"
            formControlName="cvssScore"
            type="number"
            [errorMessage]="getFieldError('cvssScore')"
          ></app-form-input>

          <app-form-input
            label="Discovered By"
            placeholder="e.g., Security Team, External Audit"
            formControlName="discoveredBy"
            [required]="true"
            [errorMessage]="getFieldError('discoveredBy')"
          ></app-form-input>

          <app-form-input
            label="Assigned To"
            placeholder="e.g., John Doe, Security Team"
            formControlName="assignedTo"
            [errorMessage]="getFieldError('assignedTo')"
          ></app-form-input>

          <app-form-input
            label="Component"
            placeholder="e.g., User Authentication Module"
            formControlName="component"
            [errorMessage]="getFieldError('component')"
          ></app-form-input>

          <app-form-input
            label="Version"
            placeholder="e.g., 2.1.0"
            formControlName="version"
            [errorMessage]="getFieldError('version')"
          ></app-form-input>

          <app-form-input
            label="Discovered Date"
            formControlName="discoveredDate"
            type="date"
            [required]="true"
            [errorMessage]="getFieldError('discoveredDate')"
          ></app-form-input>

          <app-form-input
            label="Due Date"
            formControlName="dueDate"
            type="date"
            [errorMessage]="getFieldError('dueDate')"
          ></app-form-input>

          <div class="form-group full-width">
            <label class="form-label">Description</label>
            <textarea 
              formControlName="description"
              class="form-textarea"
              placeholder="Detailed description of the security issue..."
              rows="3"
            ></textarea>
          </div>

          <div class="form-group full-width">
            <label class="form-label">Impact</label>
            <textarea 
              formControlName="impact"
              class="form-textarea"
              placeholder="Describe the potential impact of this security issue..."
              rows="2"
            ></textarea>
          </div>

          <div class="form-group full-width">
            <label class="form-label">Recommendation</label>
            <textarea 
              formControlName="recommendation"
              class="form-textarea"
              placeholder="Recommended actions to address this security issue..."
              rows="2"
            ></textarea>
          </div>
        </div>
      </form>
    </app-slide-modal>
  `,
  styles: [`
    .security-form {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-lg);
    }

    .form-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-lg);
    }

    .form-group {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-sm);
    }

    .form-group.full-width {
      grid-column: 1 / -1;
    }

    .form-label {
      font-size: var(--text-sm);
      font-weight: 500;
      color: var(--secondary-700);
    }

    .form-select,
    .form-textarea {
      height: 40px;
      padding: 0 var(--spacing-md);
      border: 1px solid var(--secondary-300);
      border-radius: var(--radius-md);
      background: white;
      font-size: var(--text-sm);
      color: var(--secondary-800);
      transition: all 0.2s ease;

      &:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px var(--primary-100);
      }
    }

    .form-textarea {
      height: auto;
      padding: var(--spacing-sm) var(--spacing-md);
      resize: vertical;
      min-height: 60px;
    }

    .field-error {
      font-size: var(--text-sm);
      color: var(--error-600);
    }

    @media (max-width: 768px) {
      .form-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
      }
    }
  `]
})
export class SharedSecurityModalComponent implements OnInit, OnChanges {
  @Input() isOpen = false;
  @Input() editMode = false;
  @Input() initialData: SharedSecurityFormData | null = null;
  @Input() loading = false;
  @Input() showApplicationSelection = true; // Hide when used within applications module

  @Output() closed = new EventEmitter<void>();
  @Output() saved = new EventEmitter<SharedSecurityFormData>();

  securityForm!: FormGroup;
  applicationOptions$: Observable<ApplicationOption[]>;

  constructor(
    private fb: FormBuilder,
    private applicationSelectionService: ApplicationSelectionService
  ) {
    this.applicationOptions$ = this.applicationSelectionService.getApplicationOptions();
  }

  ngOnInit(): void {
    this.initializeForm();
  }

  ngOnChanges(): void {
    if (this.securityForm && this.initialData) {
      this.securityForm.patchValue(this.initialData);
    }
  }

  private initializeForm(): void {
    const formConfig: any = {
      title: ['', [Validators.required, Validators.minLength(3)]],
      type: ['vulnerability', [Validators.required]],
      severity: ['medium', [Validators.required]],
      status: ['open', [Validators.required]],
      description: ['', [Validators.required]],
      impact: [''],
      recommendation: [''],
      cveId: [''],
      cvssScore: ['', [Validators.min(0), Validators.max(10)]],
      discoveredDate: [new Date().toISOString().split('T')[0], [Validators.required]],
      discoveredBy: ['', [Validators.required]],
      assignedTo: [''],
      dueDate: [''],
      component: [''],
      version: ['']
    };

    if (this.showApplicationSelection) {
      formConfig.applicationId = ['', [Validators.required]];
    }

    this.securityForm = this.fb.group(formConfig);

    if (this.initialData) {
      this.securityForm.patchValue(this.initialData);
    }
  }

  getFieldError(fieldName: string): string {
    const field = this.securityForm.get(fieldName);
    if (field && field.invalid && field.touched) {
      if (field.errors?.['required']) {
        return `${fieldName} is required`;
      }
      if (field.errors?.['minlength']) {
        return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;
      }
      if (field.errors?.['min']) {
        return `${fieldName} must be at least ${field.errors['min'].min}`;
      }
      if (field.errors?.['max']) {
        return `${fieldName} must be at most ${field.errors['max'].max}`;
      }
    }
    return '';
  }

  onSave(): void {
    if (this.securityForm.valid) {
      const formData: SharedSecurityFormData = this.securityForm.value;
      this.saved.emit(formData);
    }
  }

  onClose(): void {
    this.closed.emit();
  }
}
