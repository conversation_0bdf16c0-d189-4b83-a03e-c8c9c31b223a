import { Component, Input, Output, EventEmitter, OnInit, OnChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { SlideModalComponent } from '../../slide-modal/slide-modal.component';
import { FormInputComponent } from '../../form-input/form-input.component';
import { ApplicationSelectionService, ApplicationOption } from '../../../services/application-selection.service';
import { Observable } from 'rxjs';

export interface SharedDependencyFormData {
  applicationId?: number;
  name: string;
  type: string;
  version: string;
  description: string;
  criticality: string;
  status: string;
  maintainer: string;
  licenseType: string;
  repository?: string;
  documentation?: string;
}

@Component({
  selector: 'app-shared-dependency-modal',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, SlideModalComponent, FormInputComponent],
  template: `
    <app-slide-modal
      [isOpen]="isOpen"
      [title]="editMode ? 'Edit Dependency' : 'Add Dependency'"
      [subtitle]="showApplicationSelection ? 'Configure dependency details and select application' : 'Configure dependency details'"
      [loading]="loading"
      [canConfirm]="dependencyForm.valid"
      confirmText="Save Dependency"
      (closed)="onClose()"
      (confirmed)="onSave()"
      (cancelled)="onClose()"
    >
      <form [formGroup]="dependencyForm" class="dependency-form">
        <!-- Application Selection (only when not in applications module) -->
        <div class="form-group" *ngIf="showApplicationSelection">
          <label class="form-label">Application *</label>
          <select formControlName="applicationId" class="form-select">
            <option value="">Select an application</option>
            <option *ngFor="let app of applicationOptions$ | async" [value]="app.id">
              {{ app.name }} - {{ app.department }}
            </option>
          </select>
          <div class="field-error" *ngIf="getFieldError('applicationId')">
            {{ getFieldError('applicationId') }}
          </div>
        </div>

        <div class="form-grid">
          <app-form-input
            label="Dependency Name"
            placeholder="e.g., Angular, Express.js"
            formControlName="name"
            [required]="true"
            [errorMessage]="getFieldError('name')"
          ></app-form-input>

          <div class="form-group">
            <label class="form-label">Type</label>
            <select formControlName="type" class="form-select">
              <option value="library">Library</option>
              <option value="framework">Framework</option>
              <option value="service">Service</option>
              <option value="database">Database</option>
              <option value="api">API</option>
              <option value="tool">Tool</option>
              <option value="infrastructure">Infrastructure</option>
            </select>
          </div>

          <app-form-input
            label="Version"
            placeholder="e.g., 16.2.0, ^4.18.2"
            formControlName="version"
            [required]="true"
            [errorMessage]="getFieldError('version')"
          ></app-form-input>

          <div class="form-group">
            <label class="form-label">Criticality</label>
            <select formControlName="criticality" class="form-select">
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="critical">Critical</option>
            </select>
          </div>

          <div class="form-group">
            <label class="form-label">Status</label>
            <select formControlName="status" class="form-select">
              <option value="active">Active</option>
              <option value="deprecated">Deprecated</option>
              <option value="outdated">Outdated</option>
              <option value="vulnerable">Vulnerable</option>
            </select>
          </div>

          <app-form-input
            label="Maintainer"
            placeholder="e.g., Google, Microsoft"
            formControlName="maintainer"
            [required]="true"
            [errorMessage]="getFieldError('maintainer')"
          ></app-form-input>

          <div class="form-group">
            <label class="form-label">License Type</label>
            <select formControlName="licenseType" class="form-select">
              <option value="MIT">MIT</option>
              <option value="Apache-2.0">Apache 2.0</option>
              <option value="GPL-3.0">GPL 3.0</option>
              <option value="BSD-3-Clause">BSD 3-Clause</option>
              <option value="ISC">ISC</option>
              <option value="MPL-2.0">Mozilla Public License 2.0</option>
              <option value="proprietary">Proprietary</option>
              <option value="other">Other</option>
            </select>
          </div>

          <app-form-input
            label="Repository URL"
            placeholder="https://github.com/..."
            formControlName="repository"
            [errorMessage]="getFieldError('repository')"
          ></app-form-input>

          <app-form-input
            label="Documentation URL"
            placeholder="https://docs.example.com/..."
            formControlName="documentation"
            [errorMessage]="getFieldError('documentation')"
          ></app-form-input>

          <div class="form-group full-width">
            <label class="form-label">Description</label>
            <textarea 
              formControlName="description"
              class="form-textarea"
              placeholder="Describe the purpose and usage of this dependency..."
              rows="3"
            ></textarea>
          </div>
        </div>
      </form>
    </app-slide-modal>
  `,
  styles: [`
    .dependency-form {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-lg);
    }

    .form-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-lg);
    }

    .form-group {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-sm);
    }

    .form-group.full-width {
      grid-column: 1 / -1;
    }

    .form-label {
      font-size: var(--text-sm);
      font-weight: 500;
      color: var(--secondary-700);
    }

    .form-select,
    .form-textarea {
      height: 40px;
      padding: 0 var(--spacing-md);
      border: 1px solid var(--secondary-300);
      border-radius: var(--radius-md);
      background: white;
      font-size: var(--text-sm);
      color: var(--secondary-800);
      transition: all 0.2s ease;

      &:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px var(--primary-100);
      }
    }

    .form-textarea {
      height: auto;
      padding: var(--spacing-sm) var(--spacing-md);
      resize: vertical;
      min-height: 80px;
    }

    .field-error {
      font-size: var(--text-sm);
      color: var(--error-600);
    }

    @media (max-width: 768px) {
      .form-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
      }
    }
  `]
})
export class SharedDependencyModalComponent implements OnInit, OnChanges {
  @Input() isOpen = false;
  @Input() editMode = false;
  @Input() initialData: SharedDependencyFormData | null = null;
  @Input() loading = false;
  @Input() showApplicationSelection = true; // Hide when used within applications module

  @Output() closed = new EventEmitter<void>();
  @Output() saved = new EventEmitter<SharedDependencyFormData>();

  dependencyForm!: FormGroup;
  applicationOptions$: Observable<ApplicationOption[]>;

  constructor(
    private fb: FormBuilder,
    private applicationSelectionService: ApplicationSelectionService
  ) {
    this.applicationOptions$ = this.applicationSelectionService.getApplicationOptions();
  }

  ngOnInit(): void {
    this.initializeForm();
  }

  ngOnChanges(): void {
    if (this.dependencyForm && this.initialData) {
      this.dependencyForm.patchValue(this.initialData);
    }
  }

  private initializeForm(): void {
    const formConfig: any = {
      name: ['', [Validators.required, Validators.minLength(2)]],
      type: ['library', [Validators.required]],
      version: ['', [Validators.required]],
      description: [''],
      criticality: ['medium', [Validators.required]],
      status: ['active', [Validators.required]],
      maintainer: ['', [Validators.required]],
      licenseType: ['MIT', [Validators.required]],
      repository: [''],
      documentation: ['']
    };

    if (this.showApplicationSelection) {
      formConfig.applicationId = ['', [Validators.required]];
    }

    this.dependencyForm = this.fb.group(formConfig);

    if (this.initialData) {
      this.dependencyForm.patchValue(this.initialData);
    }
  }

  getFieldError(fieldName: string): string {
    const field = this.dependencyForm.get(fieldName);
    if (field && field.invalid && field.touched) {
      if (field.errors?.['required']) {
        return `${fieldName} is required`;
      }
      if (field.errors?.['minlength']) {
        return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;
      }
      if (field.errors?.['url']) {
        return `${fieldName} must be a valid URL`;
      }
    }
    return '';
  }

  onSave(): void {
    if (this.dependencyForm.valid) {
      const formData: SharedDependencyFormData = this.dependencyForm.value;
      this.saved.emit(formData);
    }
  }

  onClose(): void {
    this.closed.emit();
  }
}
