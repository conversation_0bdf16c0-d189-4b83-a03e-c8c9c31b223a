import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { SlideModalComponent } from '../../../../shared/components/slide-modal/slide-modal.component';
import { FormInputComponent } from '../../../../shared/components/form-input/form-input.component';

export interface DocumentationFormData {
  title: string;
  type: string;
  version: string;
  url: string;
  isPublic: boolean;
  description: string;
}

@Component({
  selector: 'app-documentation-modal',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, SlideModalComponent, FormInputComponent],
  template: `
    <app-slide-modal
      [isOpen]="isOpen"
      [title]="editMode ? 'Edit Documentation' : 'Add Documentation'"
      subtitle="Configure documentation details"
      [loading]="loading"
      [canConfirm]="documentationForm.valid"
      confirmText="Save Documentation"
      (closed)="onClose()"
      (confirmed)="onSave()"
      (cancelled)="onClose()"
    >
      <form [formGroup]="documentationForm" class="documentation-form">
        <div class="form-grid">
          <app-form-input
            label="Document Title"
            placeholder="e.g., API Documentation, User Guide"
            formControlName="title"
            [required]="true"
            [errorMessage]="getFieldError('title')"
          ></app-form-input>

          <div class="form-group">
            <label class="form-label">Document Type</label>
            <select formControlName="type" class="form-select">
              <option value="technical_spec">Technical Specification</option>
              <option value="api_documentation">API Documentation</option>
              <option value="user_manual">User Manual</option>
              <option value="installation_guide">Installation Guide</option>
              <option value="troubleshooting">Troubleshooting Guide</option>
              <option value="architecture_diagram">Architecture Diagram</option>
              <option value="deployment_guide">Deployment Guide</option>
              <option value="security_policy">Security Policy</option>
              <option value="runbook">Runbook</option>
              <option value="other">Other</option>
            </select>
          </div>

          <app-form-input
            label="Version"
            placeholder="e.g., 1.0, 2.1.0"
            formControlName="version"
            [required]="true"
            [errorMessage]="getFieldError('version')"
          ></app-form-input>

          <app-form-input
            label="URL or Location"
            placeholder="e.g., https://docs.company.com/api"
            formControlName="url"
            [required]="true"
            [errorMessage]="getFieldError('url')"
          ></app-form-input>

          <div class="form-group full-width">
            <label class="checkbox-label">
              <input 
                type="checkbox" 
                formControlName="isPublic"
                class="checkbox-input"
              >
              <span class="checkbox-text">Public Documentation</span>
              <span class="checkbox-description">This documentation is publicly accessible</span>
            </label>
          </div>

          <div class="form-group full-width">
            <label class="form-label">Description</label>
            <textarea 
              formControlName="description"
              class="form-textarea"
              placeholder="Describe the content and purpose of this documentation..."
              rows="3"
            ></textarea>
          </div>
        </div>
      </form>
    </app-slide-modal>
  `,
  styles: [`
    .documentation-form {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-lg);
    }

    .form-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-lg);
    }

    .form-group {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-sm);
    }

    .form-group.full-width {
      grid-column: 1 / -1;
    }

    .form-label {
      font-size: var(--text-sm);
      font-weight: 500;
      color: var(--secondary-700);
    }

    .form-select,
    .form-textarea {
      padding: var(--spacing-md);
      border: 1px solid var(--secondary-300);
      border-radius: var(--radius-md);
      background: white;
      font-size: var(--text-sm);
      color: var(--secondary-800);
      transition: all 0.2s ease;

      &:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px var(--primary-100);
      }
    }

    .form-textarea {
      resize: vertical;
      min-height: 80px;
      font-family: inherit;
    }

    .checkbox-label {
      display: flex;
      align-items: flex-start;
      gap: var(--spacing-sm);
      cursor: pointer;
      padding: var(--spacing-md);
      border: 1px solid var(--secondary-200);
      border-radius: var(--radius-md);
      transition: all 0.2s ease;

      &:hover {
        border-color: var(--primary-300);
        background: var(--primary-25);
      }
    }

    .checkbox-input {
      margin: 0;
      margin-top: 2px;
    }

    .checkbox-text {
      font-size: var(--text-sm);
      font-weight: 500;
      color: var(--secondary-900);
      margin-bottom: var(--spacing-xs);
    }

    .checkbox-description {
      font-size: var(--text-xs);
      color: var(--secondary-600);
      display: block;
    }

    @media (max-width: 768px) {
      .form-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
      }
    }
  `]
})
export class DocumentationModalComponent implements OnInit {
  @Input() isOpen = false;
  @Input() editMode = false;
  @Input() initialData: DocumentationFormData | null = null;
  @Input() loading = false;

  @Output() closed = new EventEmitter<void>();
  @Output() saved = new EventEmitter<DocumentationFormData>();

  documentationForm!: FormGroup;

  constructor(private fb: FormBuilder) {}

  ngOnInit(): void {
    this.initializeForm();
  }

  ngOnChanges(): void {
    if (this.documentationForm && this.initialData) {
      this.documentationForm.patchValue(this.initialData);
    }
  }

  private initializeForm(): void {
    this.documentationForm = this.fb.group({
      title: ['', [Validators.required, Validators.minLength(2)]],
      type: ['technical_spec', [Validators.required]],
      version: ['1.0', [Validators.required]],
      url: ['', [Validators.required]],
      isPublic: [false],
      description: ['']
    });

    if (this.initialData) {
      this.documentationForm.patchValue(this.initialData);
    }
  }

  getFieldError(fieldName: string): string {
    const field = this.documentationForm.get(fieldName);
    if (field && field.invalid && field.touched) {
      if (field.errors?.['required']) {
        return `${fieldName} is required`;
      }
      if (field.errors?.['minlength']) {
        return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;
      }
    }
    return '';
  }

  onClose(): void {
    this.documentationForm.reset();
    this.closed.emit();
  }

  onSave(): void {
    if (this.documentationForm.valid) {
      this.saved.emit(this.documentationForm.value);
    }
  }
}
