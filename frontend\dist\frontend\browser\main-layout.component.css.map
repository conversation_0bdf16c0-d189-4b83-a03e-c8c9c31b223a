{"version": 3, "sources": ["src/app/core/layout/main-layout/main-layout.component.scss"], "sourcesContent": [".layout-container {\n  min-height: 100vh;\n  background-color: var(--neutral-50);\n}\n\n.main-content {\n  margin-top: 64px; /* Header height */\n  margin-left: 0;\n  min-height: calc(100vh - 64px);\n  transition: margin-left 0.3s ease;\n  background-color: var(--neutral-50);\n\n  &.sidebar-open {\n    margin-left: 280px; /* Sidebar width */\n  }\n}\n\n.auth-content {\n  min-height: 100vh;\n  width: 100%;\n  background-color: var(--neutral-50);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n\n  /* Add a subtle gradient background */\n  background-image: linear-gradient(\n    135deg,\n    var(--neutral-50) 0%,\n    var(--neutral-100) 100%\n  );\n}\n\n.content-wrapper {\n  padding: var(--spacing-xl);\n  max-width: 100%;\n  min-height: calc(100vh - 64px - 2 * var(--spacing-xl));\n}\n\n/* Loading Overlay */\n.loading-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(255, 255, 255, 0.8);\n  backdrop-filter: blur(2px);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: var(--z-modal);\n}\n\n.loading-spinner {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: var(--spacing-md);\n}\n\n.spinner {\n  width: 40px;\n  height: 40px;\n  border: 3px solid var(--secondary-200);\n  border-top: 3px solid var(--primary-500);\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n\n.loading-text {\n  margin: 0;\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n  font-weight: 500;\n}\n\n@keyframes spin {\n  0% { transform: rotate(0deg); }\n  100% { transform: rotate(360deg); }\n}\n\n/* Responsive Design */\n@media (max-width: 767px) {\n  .main-content {\n    margin-left: 0 !important;\n  }\n\n  .content-wrapper {\n    padding: var(--spacing-lg);\n  }\n}\n\n@media (min-width: 768px) and (max-width: 1024px) {\n  .content-wrapper {\n    padding: var(--spacing-lg) var(--spacing-xl);\n  }\n}\n\n/* Print Styles */\n@media print {\n  .layout-container {\n    background: white;\n  }\n\n  .main-content {\n    margin: 0;\n    min-height: auto;\n  }\n\n  .content-wrapper {\n    padding: 0;\n  }\n\n  .loading-overlay {\n    display: none;\n  }\n}\n"], "mappings": ";AAAA,CAAA;AACE,cAAA;AACA,oBAAA,IAAA;;AAGF,CAAA;AACE,cAAA;AACA,eAAA;AACA,cAAA,KAAA,MAAA,EAAA;AACA,cAAA,YAAA,KAAA;AACA,oBAAA,IAAA;;AAEA,CAPF,YAOE,CAAA;AACE,eAAA;;AAIJ,CAAA;AACE,cAAA;AACA,SAAA;AACA,oBAAA,IAAA;AACA,WAAA;AACA,kBAAA;AACA,eAAA;AACA,mBAAA;AAGA;IAAA;MAAA,MAAA;MAAA,IAAA,cAAA,EAAA;MAAA,IAAA,eAAA;;AAOF,CAAA;AACE,WAAA,IAAA;AACA,aAAA;AACA,cAAA,KAAA,MAAA,EAAA,KAAA,EAAA,EAAA,EAAA,IAAA;;AAIF,CAAA;AACE,YAAA;AACA,OAAA;AACA,QAAA;AACA,SAAA;AACA,UAAA;AACA,cAAA,KAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA;AACA,2BAAA,KAAA;AAAA,mBAAA,KAAA;AACA,WAAA;AACA,eAAA;AACA,mBAAA;AACA,WAAA,IAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,eAAA;AACA,OAAA,IAAA;;AAGF,CAAA;AACE,SAAA;AACA,UAAA;AACA,UAAA,IAAA,MAAA,IAAA;AACA,cAAA,IAAA,MAAA,IAAA;AACA,iBAAA;AACA,aAAA,KAAA,GAAA,OAAA;;AAGF,CAAA;AACE,UAAA;AACA,aAAA,IAAA;AACA,SAAA,IAAA;AACA,eAAA;;AAGF,WAVE;AAWA;AAAK,eAAA,OAAA;;AACL;AAAO,eAAA,OAAA;;;AAIT,OAAA,CAAA,SAAA,EAAA;AACE,GAhFF;AAiFI,iBAAA;;AAGF,GAvDF;AAwDI,aAAA,IAAA;;;AAIJ,OAAA,CAAA,SAAA,EAAA,OAAA,IAAA,CAAA,SAAA,EAAA;AACE,GA7DF;AA8DI,aAAA,IAAA,cAAA,IAAA;;;AAKJ,OAAA;AACE,GAtGF;AAuGI,gBAAA;;AAGF,GArGF;AAsGI,YAAA;AACA,gBAAA;;AAGF,GA7EF;AA8EI,aAAA;;AAGF,GA1EF;AA2EI,aAAA;;;", "names": []}