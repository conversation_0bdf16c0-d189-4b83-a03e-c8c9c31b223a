import {
  ApplicationSelectionService
} from "./chunk-7XI6LZQT.js";
import {
  TableComponent
} from "./chunk-QQYNLSVO.js";
import {
  ChartComponent
} from "./chunk-HCVU2C6O.js";
import {
  SlideModalComponent
} from "./chunk-6DLBFET6.js";
import {
  DefaultValueAccessor,
  FormBuilder,
  FormControlName,
  FormGroupDirective,
  NgControlStatus,
  NgControlStatusGroup,
  NgSelectOption,
  ReactiveFormsModule,
  SelectControlValueAccessor,
  Validators,
  ɵNgNoValidate,
  ɵNgSelectMultipleOption
} from "./chunk-SM75SJZE.js";
import {
  CardComponent
} from "./chunk-SLWZQAXJ.js";
import {
  ButtonComponent
} from "./chunk-QMVBAXS7.js";
import {
  RouterModule
} from "./chunk-IKU57TF7.js";
import {
  AsyncPipe,
  CommonModule,
  Component,
  EventEmitter,
  Inject,
  Input,
  NgForOf,
  NgIf,
  Output,
  PLATFORM_ID,
  ViewChild,
  isPlatformBrowser,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵNgOnChangesFeature,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵloadQuery,
  ɵɵnamespaceHTML,
  ɵɵnamespaceSVG,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind1,
  ɵɵproperty,
  ɵɵqueryRefresh,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtextInterpolate2,
  ɵɵviewQuery
} from "./chunk-3JQLQ36P.js";
import "./chunk-WDMUDEB6.js";

// src/app/shared/components/file-upload/file-upload.component.ts
var _c0 = ["fileInput"];
function FileUploadComponent_div_44_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 28)(1, "label", 5);
    \u0275\u0275text(2, "Description (Optional)");
    \u0275\u0275elementEnd();
    \u0275\u0275element(3, "textarea", 29);
    \u0275\u0275elementEnd();
  }
}
function FileUploadComponent_div_45_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 30)(1, "div", 31)(2, "span", 32);
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "span", 33);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "span", 34);
    \u0275\u0275text(7);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(ctx_r1.selectedFile.name);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(ctx_r1.formatFileSize(ctx_r1.selectedFile.size));
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(ctx_r1.selectedFile.type || "Unknown type");
  }
}
var FileUploadComponent = class _FileUploadComponent {
  fb;
  platformId;
  showDescription = false;
  documentAdded = new EventEmitter();
  fileInput;
  uploadForm;
  selectedFile = null;
  constructor(fb, platformId) {
    this.fb = fb;
    this.platformId = platformId;
  }
  ngOnInit() {
    this.initializeForm();
  }
  initializeForm() {
    this.uploadForm = this.fb.group({
      type: ["", [Validators.required]],
      description: [""]
    });
  }
  onFileSelected(event) {
    const target = event.target;
    if (target.files && target.files.length > 0) {
      this.selectedFile = target.files[0];
    }
  }
  get canAdd() {
    return (this.uploadForm.get("type")?.valid ?? false) && this.selectedFile !== null;
  }
  addDocument() {
    if (this.canAdd && this.selectedFile) {
      const formData = {
        file: this.selectedFile,
        type: this.uploadForm.get("type")?.value,
        description: this.uploadForm.get("description")?.value || void 0
      };
      this.documentAdded.emit(formData);
      this.resetForm();
    }
  }
  resetForm() {
    this.uploadForm.reset();
    this.selectedFile = null;
    if (isPlatformBrowser(this.platformId) && this.fileInput) {
      this.fileInput.nativeElement.value = "";
    }
  }
  formatFileSize(bytes) {
    if (bytes === 0)
      return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }
  getFileIcon(mimeType) {
    if (mimeType.includes("pdf"))
      return "\u{1F4C4}";
    if (mimeType.includes("word") || mimeType.includes("document"))
      return "\u{1F4DD}";
    if (mimeType.includes("image"))
      return "\u{1F5BC}\uFE0F";
    if (mimeType.includes("video"))
      return "\u{1F3A5}";
    if (mimeType.includes("audio"))
      return "\u{1F3B5}";
    if (mimeType.includes("zip") || mimeType.includes("archive"))
      return "\u{1F4E6}";
    if (mimeType.includes("text"))
      return "\u{1F4C4}";
    return "\u{1F4C1}";
  }
  static \u0275fac = function FileUploadComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _FileUploadComponent)(\u0275\u0275directiveInject(FormBuilder), \u0275\u0275directiveInject(PLATFORM_ID));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _FileUploadComponent, selectors: [["app-file-upload"]], viewQuery: function FileUploadComponent_Query(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275viewQuery(_c0, 5);
    }
    if (rf & 2) {
      let _t;
      \u0275\u0275queryRefresh(_t = \u0275\u0275loadQuery()) && (ctx.fileInput = _t.first);
    }
  }, inputs: { showDescription: "showDescription" }, outputs: { documentAdded: "documentAdded" }, decls: 46, vars: 5, consts: [["fileInput", ""], [1, "file-upload-container"], [1, "upload-form", 3, "formGroup"], [1, "form-row"], [1, "form-group"], [1, "form-label"], ["formControlName", "type", 1, "form-select"], ["value", ""], ["value", "technical_spec"], ["value", "api_documentation"], ["value", "user_manual"], ["value", "deployment_guide"], ["value", "architecture_diagram"], ["value", "security_policy"], ["value", "compliance_report"], ["value", "runbook"], ["value", "troubleshooting"], ["value", "changelog"], [1, "file-input-wrapper"], ["type", "file", "id", "file-input", "accept", ".pdf,.doc,.docx,.txt,.md,.png,.jpg,.jpeg,.gif,.svg,.zip,.tar,.gz", 1, "file-input", 3, "change"], ["for", "file-input", 1, "file-input-label"], ["viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", 1, "upload-icon"], ["d", "M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"], ["points", "7,10 12,15 17,10"], ["x1", "12", "y1", "15", "x2", "12", "y2", "3"], ["type", "button", 1, "add-button", 3, "click", "disabled"], ["class", "form-group full-width", 4, "ngIf"], ["class", "file-info", 4, "ngIf"], [1, "form-group", "full-width"], ["formControlName", "description", "placeholder", "Brief description of the document...", "rows", "2", 1, "form-textarea"], [1, "file-info"], [1, "file-details"], [1, "file-name"], [1, "file-size"], [1, "file-type"]], template: function FileUploadComponent_Template(rf, ctx) {
    if (rf & 1) {
      const _r1 = \u0275\u0275getCurrentView();
      \u0275\u0275elementStart(0, "div", 1)(1, "form", 2)(2, "div", 3)(3, "div", 4)(4, "label", 5);
      \u0275\u0275text(5, "Document Type");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(6, "select", 6)(7, "option", 7);
      \u0275\u0275text(8, "Select document type");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(9, "option", 8);
      \u0275\u0275text(10, "Technical Specification");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(11, "option", 9);
      \u0275\u0275text(12, "API Documentation");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(13, "option", 10);
      \u0275\u0275text(14, "User Manual");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(15, "option", 11);
      \u0275\u0275text(16, "Deployment Guide");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(17, "option", 12);
      \u0275\u0275text(18, "Architecture Diagram");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(19, "option", 13);
      \u0275\u0275text(20, "Security Policy");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(21, "option", 14);
      \u0275\u0275text(22, "Compliance Report");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(23, "option", 15);
      \u0275\u0275text(24, "Runbook");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(25, "option", 16);
      \u0275\u0275text(26, "Troubleshooting Guide");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(27, "option", 17);
      \u0275\u0275text(28, "Changelog");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(29, "div", 4)(30, "label", 5);
      \u0275\u0275text(31, "File");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(32, "div", 18)(33, "input", 19, 0);
      \u0275\u0275listener("change", function FileUploadComponent_Template_input_change_33_listener($event) {
        \u0275\u0275restoreView(_r1);
        return \u0275\u0275resetView(ctx.onFileSelected($event));
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(35, "label", 20);
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(36, "svg", 21);
      \u0275\u0275element(37, "path", 22)(38, "polyline", 23)(39, "line", 24);
      \u0275\u0275elementEnd();
      \u0275\u0275text(40);
      \u0275\u0275elementEnd()()();
      \u0275\u0275namespaceHTML();
      \u0275\u0275elementStart(41, "div", 4)(42, "button", 25);
      \u0275\u0275listener("click", function FileUploadComponent_Template_button_click_42_listener() {
        \u0275\u0275restoreView(_r1);
        return \u0275\u0275resetView(ctx.addDocument());
      });
      \u0275\u0275text(43, " Add Document ");
      \u0275\u0275elementEnd()()();
      \u0275\u0275template(44, FileUploadComponent_div_44_Template, 4, 0, "div", 26);
      \u0275\u0275elementEnd();
      \u0275\u0275template(45, FileUploadComponent_div_45_Template, 8, 3, "div", 27);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275advance();
      \u0275\u0275property("formGroup", ctx.uploadForm);
      \u0275\u0275advance(39);
      \u0275\u0275textInterpolate1(" ", ctx.selectedFile ? ctx.selectedFile.name : "Choose file", " ");
      \u0275\u0275advance(2);
      \u0275\u0275property("disabled", !ctx.canAdd);
      \u0275\u0275advance(2);
      \u0275\u0275property("ngIf", ctx.showDescription);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.selectedFile);
    }
  }, dependencies: [CommonModule, NgIf, ReactiveFormsModule, \u0275NgNoValidate, NgSelectOption, \u0275NgSelectMultipleOption, DefaultValueAccessor, SelectControlValueAccessor, NgControlStatus, NgControlStatusGroup, FormGroupDirective, FormControlName], styles: ["\n\n.file-upload-container[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-md);\n}\n.upload-form[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-md);\n}\n.form-row[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: 1fr 1fr auto;\n  gap: var(--spacing-md);\n  align-items: end;\n}\n.form-group[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-sm);\n}\n.form-group.full-width[_ngcontent-%COMP%] {\n  grid-column: 1/-1;\n}\n.form-label[_ngcontent-%COMP%] {\n  font-size: var(--text-sm);\n  font-weight: 500;\n  color: var(--secondary-700);\n}\n.form-select[_ngcontent-%COMP%], \n.form-textarea[_ngcontent-%COMP%] {\n  height: 40px;\n  padding: 0 var(--spacing-md);\n  border: 1px solid var(--secondary-300);\n  border-radius: var(--radius-md);\n  background: white;\n  font-size: var(--text-sm);\n  color: var(--secondary-800);\n  transition: all 0.2s ease;\n}\n.form-select[_ngcontent-%COMP%]:focus, \n.form-textarea[_ngcontent-%COMP%]:focus {\n  outline: none;\n  border-color: var(--primary-500);\n  box-shadow: 0 0 0 3px var(--primary-100);\n}\n.form-textarea[_ngcontent-%COMP%] {\n  height: auto;\n  padding: var(--spacing-sm) var(--spacing-md);\n  resize: vertical;\n  min-height: 60px;\n}\n.file-input-wrapper[_ngcontent-%COMP%] {\n  position: relative;\n}\n.file-input[_ngcontent-%COMP%] {\n  position: absolute;\n  opacity: 0;\n  width: 100%;\n  height: 100%;\n  cursor: pointer;\n}\n.file-input-label[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n  height: 40px;\n  padding: 0 var(--spacing-md);\n  border: 1px solid var(--secondary-300);\n  border-radius: var(--radius-md);\n  background: white;\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n.file-input-label[_ngcontent-%COMP%]:hover {\n  border-color: var(--primary-400);\n  background: var(--primary-50);\n}\n.upload-icon[_ngcontent-%COMP%] {\n  width: 16px;\n  height: 16px;\n  stroke-width: 2;\n}\n.add-button[_ngcontent-%COMP%] {\n  height: 40px;\n  padding: 0 var(--spacing-lg);\n  background: var(--primary-600);\n  color: white;\n  border: none;\n  border-radius: var(--radius-md);\n  font-size: var(--text-sm);\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  white-space: nowrap;\n}\n.add-button[_ngcontent-%COMP%]:hover:not(:disabled) {\n  background: var(--primary-700);\n}\n.add-button[_ngcontent-%COMP%]:disabled {\n  background: var(--secondary-300);\n  cursor: not-allowed;\n}\n.file-info[_ngcontent-%COMP%] {\n  padding: var(--spacing-md);\n  background: var(--secondary-50);\n  border-radius: var(--radius-md);\n  border: 1px solid var(--secondary-200);\n}\n.file-details[_ngcontent-%COMP%] {\n  display: flex;\n  gap: var(--spacing-md);\n  align-items: center;\n  flex-wrap: wrap;\n}\n.file-name[_ngcontent-%COMP%] {\n  font-weight: 500;\n  color: var(--secondary-900);\n}\n.file-size[_ngcontent-%COMP%], \n.file-type[_ngcontent-%COMP%] {\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n}\n@media (max-width: 768px) {\n  .form-row[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-sm);\n  }\n  .file-details[_ngcontent-%COMP%] {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: var(--spacing-sm);\n  }\n}\n/*# sourceMappingURL=file-upload.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(FileUploadComponent, [{
    type: Component,
    args: [{ selector: "app-file-upload", standalone: true, imports: [CommonModule, ReactiveFormsModule], template: `
    <div class="file-upload-container">
      <form [formGroup]="uploadForm" class="upload-form">
        <div class="form-row">
          <div class="form-group">
            <label class="form-label">Document Type</label>
            <select formControlName="type" class="form-select">
              <option value="">Select document type</option>
              <option value="technical_spec">Technical Specification</option>
              <option value="api_documentation">API Documentation</option>
              <option value="user_manual">User Manual</option>
              <option value="deployment_guide">Deployment Guide</option>
              <option value="architecture_diagram">Architecture Diagram</option>
              <option value="security_policy">Security Policy</option>
              <option value="compliance_report">Compliance Report</option>
              <option value="runbook">Runbook</option>
              <option value="troubleshooting">Troubleshooting Guide</option>
              <option value="changelog">Changelog</option>
            </select>
          </div>

          <div class="form-group">
            <label class="form-label">File</label>
            <div class="file-input-wrapper">
              <input
                #fileInput
                type="file"
                (change)="onFileSelected($event)"
                class="file-input"
                id="file-input"
                accept=".pdf,.doc,.docx,.txt,.md,.png,.jpg,.jpeg,.gif,.svg,.zip,.tar,.gz"
              >
              <label for="file-input" class="file-input-label">
                <svg class="upload-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                  <polyline points="7,10 12,15 17,10"></polyline>
                  <line x1="12" y1="15" x2="12" y2="3"></line>
                </svg>
                {{ selectedFile ? selectedFile.name : 'Choose file' }}
              </label>
            </div>
          </div>

          <div class="form-group">
            <button
              type="button"
              class="add-button"
              [disabled]="!canAdd"
              (click)="addDocument()"
            >
              Add Document
            </button>
          </div>
        </div>

        <div class="form-group full-width" *ngIf="showDescription">
          <label class="form-label">Description (Optional)</label>
          <textarea
            formControlName="description"
            class="form-textarea"
            placeholder="Brief description of the document..."
            rows="2"
          ></textarea>
        </div>
      </form>

      <!-- File info display -->
      <div class="file-info" *ngIf="selectedFile">
        <div class="file-details">
          <span class="file-name">{{ selectedFile.name }}</span>
          <span class="file-size">{{ formatFileSize(selectedFile.size) }}</span>
          <span class="file-type">{{ selectedFile.type || 'Unknown type' }}</span>
        </div>
      </div>
    </div>
  `, styles: ["/* angular:styles/component:scss;0d2ab3c44c9606b544e9631d50735b8eaaa00e6064c713942e1569c706f6f750;C:/Users/<USER>/Documents/VibeCoding/AppCatalog/app-catalog/frontend/src/app/shared/components/file-upload/file-upload.component.ts */\n.file-upload-container {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-md);\n}\n.upload-form {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-md);\n}\n.form-row {\n  display: grid;\n  grid-template-columns: 1fr 1fr auto;\n  gap: var(--spacing-md);\n  align-items: end;\n}\n.form-group {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-sm);\n}\n.form-group.full-width {\n  grid-column: 1/-1;\n}\n.form-label {\n  font-size: var(--text-sm);\n  font-weight: 500;\n  color: var(--secondary-700);\n}\n.form-select,\n.form-textarea {\n  height: 40px;\n  padding: 0 var(--spacing-md);\n  border: 1px solid var(--secondary-300);\n  border-radius: var(--radius-md);\n  background: white;\n  font-size: var(--text-sm);\n  color: var(--secondary-800);\n  transition: all 0.2s ease;\n}\n.form-select:focus,\n.form-textarea:focus {\n  outline: none;\n  border-color: var(--primary-500);\n  box-shadow: 0 0 0 3px var(--primary-100);\n}\n.form-textarea {\n  height: auto;\n  padding: var(--spacing-sm) var(--spacing-md);\n  resize: vertical;\n  min-height: 60px;\n}\n.file-input-wrapper {\n  position: relative;\n}\n.file-input {\n  position: absolute;\n  opacity: 0;\n  width: 100%;\n  height: 100%;\n  cursor: pointer;\n}\n.file-input-label {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n  height: 40px;\n  padding: 0 var(--spacing-md);\n  border: 1px solid var(--secondary-300);\n  border-radius: var(--radius-md);\n  background: white;\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n.file-input-label:hover {\n  border-color: var(--primary-400);\n  background: var(--primary-50);\n}\n.upload-icon {\n  width: 16px;\n  height: 16px;\n  stroke-width: 2;\n}\n.add-button {\n  height: 40px;\n  padding: 0 var(--spacing-lg);\n  background: var(--primary-600);\n  color: white;\n  border: none;\n  border-radius: var(--radius-md);\n  font-size: var(--text-sm);\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  white-space: nowrap;\n}\n.add-button:hover:not(:disabled) {\n  background: var(--primary-700);\n}\n.add-button:disabled {\n  background: var(--secondary-300);\n  cursor: not-allowed;\n}\n.file-info {\n  padding: var(--spacing-md);\n  background: var(--secondary-50);\n  border-radius: var(--radius-md);\n  border: 1px solid var(--secondary-200);\n}\n.file-details {\n  display: flex;\n  gap: var(--spacing-md);\n  align-items: center;\n  flex-wrap: wrap;\n}\n.file-name {\n  font-weight: 500;\n  color: var(--secondary-900);\n}\n.file-size,\n.file-type {\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n}\n@media (max-width: 768px) {\n  .form-row {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-sm);\n  }\n  .file-details {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: var(--spacing-sm);\n  }\n}\n/*# sourceMappingURL=file-upload.component.css.map */\n"] }]
  }], () => [{ type: FormBuilder }, { type: Object, decorators: [{
    type: Inject,
    args: [PLATFORM_ID]
  }] }], { showDescription: [{
    type: Input
  }], documentAdded: [{
    type: Output
  }], fileInput: [{
    type: ViewChild,
    args: ["fileInput"]
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(FileUploadComponent, { className: "FileUploadComponent", filePath: "src/app/shared/components/file-upload/file-upload.component.ts", lineNumber: 264 });
})();

// src/app/shared/components/modals/shared-documentation-modal/shared-documentation-modal.component.ts
function SharedDocumentationModalComponent_div_2_option_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "option", 14);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const app_r1 = ctx.$implicit;
    \u0275\u0275property("value", app_r1.id);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate2(" ", app_r1.name, " - ", app_r1.department, " ");
  }
}
function SharedDocumentationModalComponent_div_2_div_8_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 15);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r1.getFieldError("applicationId"), " ");
  }
}
function SharedDocumentationModalComponent_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 8)(1, "label", 9);
    \u0275\u0275text(2, "Application *");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "select", 10)(4, "option", 11);
    \u0275\u0275text(5, "Select an application");
    \u0275\u0275elementEnd();
    \u0275\u0275template(6, SharedDocumentationModalComponent_div_2_option_6_Template, 2, 3, "option", 12);
    \u0275\u0275pipe(7, "async");
    \u0275\u0275elementEnd();
    \u0275\u0275template(8, SharedDocumentationModalComponent_div_2_div_8_Template, 2, 1, "div", 13);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(6);
    \u0275\u0275property("ngForOf", \u0275\u0275pipeBind1(7, 2, ctx_r1.applicationOptions$));
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", ctx_r1.getFieldError("applicationId"));
  }
}
function SharedDocumentationModalComponent_div_7_div_4_div_23_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 35);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const doc_r5 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", doc_r5.description, " ");
  }
}
function SharedDocumentationModalComponent_div_7_div_4_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 18)(1, "div", 19);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "div", 20)(4, "div", 21)(5, "span", 22);
    \u0275\u0275text(6);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "button", 23);
    \u0275\u0275listener("click", function SharedDocumentationModalComponent_div_7_div_4_Template_button_click_7_listener() {
      const i_r4 = \u0275\u0275restoreView(_r3).index;
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.removeDocument(i_r4));
    });
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(8, "svg", 24);
    \u0275\u0275element(9, "polyline", 25)(10, "path", 26);
    \u0275\u0275elementEnd()()();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(11, "div", 27)(12, "span", 28);
    \u0275\u0275text(13);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(14, "span", 29);
    \u0275\u0275text(15);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(16, "span", 30);
    \u0275\u0275text(17);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(18, "div", 31)(19, "span", 32);
    \u0275\u0275text(20);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(21, "span", 33);
    \u0275\u0275text(22);
    \u0275\u0275elementEnd()();
    \u0275\u0275template(23, SharedDocumentationModalComponent_div_7_div_4_div_23_Template, 2, 1, "div", 34);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const doc_r5 = ctx.$implicit;
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(doc_r5.icon);
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(doc_r5.fileName);
    \u0275\u0275advance(7);
    \u0275\u0275textInterpolate(ctx_r1.getDocumentTypeLabel(doc_r5.type));
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(ctx_r1.formatFileSize(doc_r5.fileSize));
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(doc_r5.mimeType);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(ctx_r1.formatDate(doc_r5.uploadedAt));
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1("by ", doc_r5.uploadedBy, "");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", doc_r5.description);
  }
}
function SharedDocumentationModalComponent_div_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 3)(1, "h4", 4);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "div", 16);
    \u0275\u0275template(4, SharedDocumentationModalComponent_div_7_div_4_Template, 24, 8, "div", 17);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate1("Uploaded Documents (", ctx_r1.uploadedDocuments.length, ")");
    \u0275\u0275advance(2);
    \u0275\u0275property("ngForOf", ctx_r1.uploadedDocuments);
  }
}
function SharedDocumentationModalComponent_div_8_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 36)(1, "p");
    \u0275\u0275text(2, "No documents uploaded yet. Use the upload section above to add documents.");
    \u0275\u0275elementEnd()();
  }
}
var SharedDocumentationModalComponent = class _SharedDocumentationModalComponent {
  fb;
  applicationSelectionService;
  isOpen = false;
  editMode = false;
  initialData = null;
  loading = false;
  showApplicationSelection = true;
  // Hide when used within applications module
  closed = new EventEmitter();
  saved = new EventEmitter();
  documentationForm;
  uploadedDocuments = [];
  applicationOptions$;
  constructor(fb, applicationSelectionService) {
    this.fb = fb;
    this.applicationSelectionService = applicationSelectionService;
    this.applicationOptions$ = this.applicationSelectionService.getApplicationOptions();
  }
  ngOnInit() {
    this.initializeForm();
  }
  ngOnChanges() {
    if (this.documentationForm && this.initialData) {
      this.documentationForm.patchValue({
        applicationId: this.initialData.applicationId
      });
      this.uploadedDocuments = [...this.initialData.documents || []];
    }
  }
  initializeForm() {
    const formConfig = {};
    if (this.showApplicationSelection) {
      formConfig.applicationId = ["", [Validators.required]];
    }
    this.documentationForm = this.fb.group(formConfig);
    if (this.initialData) {
      this.documentationForm.patchValue({
        applicationId: this.initialData.applicationId
      });
      this.uploadedDocuments = [...this.initialData.documents || []];
    }
  }
  get canConfirm() {
    const formValid = this.documentationForm.valid;
    const hasDocuments = this.uploadedDocuments.length > 0;
    return formValid && hasDocuments;
  }
  onDocumentAdded(fileData) {
    const uploadedDoc = {
      fileName: fileData.file.name,
      fileSize: fileData.file.size,
      mimeType: fileData.file.type || "application/octet-stream",
      type: fileData.type,
      description: fileData.description,
      uploadedAt: /* @__PURE__ */ new Date(),
      uploadedBy: "Current User",
      // In real app, get from auth service
      icon: this.getFileIcon(fileData.file.type || "application/octet-stream")
    };
    this.uploadedDocuments.push(uploadedDoc);
  }
  removeDocument(index) {
    this.uploadedDocuments.splice(index, 1);
  }
  getFieldError(fieldName) {
    const field = this.documentationForm.get(fieldName);
    if (field && field.invalid && field.touched) {
      if (field.errors?.["required"]) {
        return `${fieldName} is required`;
      }
    }
    return "";
  }
  getDocumentTypeLabel(type) {
    const typeLabels = {
      "technical_spec": "Technical Specification",
      "api_documentation": "API Documentation",
      "user_manual": "User Manual",
      "deployment_guide": "Deployment Guide",
      "architecture_diagram": "Architecture Diagram",
      "security_policy": "Security Policy",
      "compliance_report": "Compliance Report",
      "runbook": "Runbook",
      "troubleshooting": "Troubleshooting Guide",
      "changelog": "Changelog"
    };
    return typeLabels[type] || type;
  }
  formatFileSize(bytes) {
    if (bytes === 0)
      return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }
  formatDate(date) {
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    }).format(date);
  }
  getFileIcon(mimeType) {
    if (mimeType.includes("pdf"))
      return "\u{1F4C4}";
    if (mimeType.includes("word") || mimeType.includes("document"))
      return "\u{1F4DD}";
    if (mimeType.includes("image"))
      return "\u{1F5BC}\uFE0F";
    if (mimeType.includes("video"))
      return "\u{1F3A5}";
    if (mimeType.includes("audio"))
      return "\u{1F3B5}";
    if (mimeType.includes("zip") || mimeType.includes("archive"))
      return "\u{1F4E6}";
    if (mimeType.includes("text"))
      return "\u{1F4C4}";
    return "\u{1F4C1}";
  }
  onSave() {
    if (this.canConfirm) {
      const formData = {
        documents: this.uploadedDocuments
      };
      if (this.showApplicationSelection) {
        formData.applicationId = this.documentationForm.get("applicationId")?.value;
      }
      this.saved.emit(formData);
    }
  }
  onClose() {
    this.closed.emit();
  }
  static \u0275fac = function SharedDocumentationModalComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _SharedDocumentationModalComponent)(\u0275\u0275directiveInject(FormBuilder), \u0275\u0275directiveInject(ApplicationSelectionService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _SharedDocumentationModalComponent, selectors: [["app-shared-documentation-modal"]], inputs: { isOpen: "isOpen", editMode: "editMode", initialData: "initialData", loading: "loading", showApplicationSelection: "showApplicationSelection" }, outputs: { closed: "closed", saved: "saved" }, features: [\u0275\u0275NgOnChangesFeature], decls: 9, vars: 10, consts: [["confirmText", "Save Documentation", 3, "closed", "confirmed", "cancelled", "isOpen", "title", "subtitle", "loading", "canConfirm"], [1, "documentation-form", 3, "formGroup"], ["class", "form-group", 4, "ngIf"], [1, "form-section"], [1, "section-title"], [3, "documentAdded", "showDescription"], ["class", "form-section", 4, "ngIf"], ["class", "empty-state", 4, "ngIf"], [1, "form-group"], [1, "form-label"], ["formControlName", "applicationId", 1, "form-select"], ["value", ""], [3, "value", 4, "ngFor", "ngForOf"], ["class", "field-error", 4, "ngIf"], [3, "value"], [1, "field-error"], [1, "documents-list"], ["class", "document-item", 4, "ngFor", "ngForOf"], [1, "document-item"], [1, "document-icon"], [1, "document-info"], [1, "document-header"], [1, "document-name"], ["type", "button", 1, "remove-button", 3, "click"], ["viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor"], ["points", "3,6 5,6 21,6"], ["d", "m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"], [1, "document-meta"], [1, "document-type"], [1, "document-size"], [1, "document-mime"], [1, "document-upload-info"], [1, "upload-date"], [1, "uploaded-by"], ["class", "document-description", 4, "ngIf"], [1, "document-description"], [1, "empty-state"]], template: function SharedDocumentationModalComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "app-slide-modal", 0);
      \u0275\u0275listener("closed", function SharedDocumentationModalComponent_Template_app_slide_modal_closed_0_listener() {
        return ctx.onClose();
      })("confirmed", function SharedDocumentationModalComponent_Template_app_slide_modal_confirmed_0_listener() {
        return ctx.onSave();
      })("cancelled", function SharedDocumentationModalComponent_Template_app_slide_modal_cancelled_0_listener() {
        return ctx.onClose();
      });
      \u0275\u0275elementStart(1, "form", 1);
      \u0275\u0275template(2, SharedDocumentationModalComponent_div_2_Template, 9, 4, "div", 2);
      \u0275\u0275elementStart(3, "div", 3)(4, "h4", 4);
      \u0275\u0275text(5, "Upload Documents");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(6, "app-file-upload", 5);
      \u0275\u0275listener("documentAdded", function SharedDocumentationModalComponent_Template_app_file_upload_documentAdded_6_listener($event) {
        return ctx.onDocumentAdded($event);
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275template(7, SharedDocumentationModalComponent_div_7_Template, 5, 2, "div", 6)(8, SharedDocumentationModalComponent_div_8_Template, 3, 0, "div", 7);
      \u0275\u0275elementEnd()();
    }
    if (rf & 2) {
      \u0275\u0275property("isOpen", ctx.isOpen)("title", ctx.editMode ? "Edit Documentation" : "Add Documentation")("subtitle", ctx.showApplicationSelection ? "Upload documents and select application" : "Upload documents")("loading", ctx.loading)("canConfirm", ctx.canConfirm);
      \u0275\u0275advance();
      \u0275\u0275property("formGroup", ctx.documentationForm);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.showApplicationSelection);
      \u0275\u0275advance(4);
      \u0275\u0275property("showDescription", true);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.uploadedDocuments.length > 0);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.uploadedDocuments.length === 0);
    }
  }, dependencies: [CommonModule, NgForOf, NgIf, AsyncPipe, ReactiveFormsModule, \u0275NgNoValidate, NgSelectOption, \u0275NgSelectMultipleOption, SelectControlValueAccessor, NgControlStatus, NgControlStatusGroup, FormGroupDirective, FormControlName, SlideModalComponent, FileUploadComponent], styles: ["\n\n.documentation-form[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-lg);\n}\n.form-group[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-sm);\n}\n.form-label[_ngcontent-%COMP%] {\n  font-size: var(--text-sm);\n  font-weight: 500;\n  color: var(--secondary-700);\n}\n.form-select[_ngcontent-%COMP%] {\n  height: 40px;\n  padding: 0 var(--spacing-md);\n  border: 1px solid var(--secondary-300);\n  border-radius: var(--radius-md);\n  background: white;\n  font-size: var(--text-sm);\n  color: var(--secondary-800);\n  transition: all 0.2s ease;\n}\n.form-select[_ngcontent-%COMP%]:focus {\n  outline: none;\n  border-color: var(--primary-500);\n  box-shadow: 0 0 0 3px var(--primary-100);\n}\n.field-error[_ngcontent-%COMP%] {\n  font-size: var(--text-sm);\n  color: var(--error-600);\n}\n.form-section[_ngcontent-%COMP%] {\n  padding: var(--spacing-lg);\n  background: var(--secondary-50);\n  border-radius: var(--radius-md);\n  border: 1px solid var(--secondary-200);\n}\n.section-title[_ngcontent-%COMP%] {\n  margin: 0 0 var(--spacing-md) 0;\n  font-size: var(--text-lg);\n  font-weight: 600;\n  color: var(--secondary-900);\n}\n.documents-list[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-md);\n}\n.document-item[_ngcontent-%COMP%] {\n  display: flex;\n  gap: var(--spacing-md);\n  padding: var(--spacing-md);\n  background: white;\n  border-radius: var(--radius-md);\n  border: 1px solid var(--secondary-200);\n}\n.document-icon[_ngcontent-%COMP%] {\n  font-size: 24px;\n  width: 40px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: var(--primary-100);\n  border-radius: var(--radius-md);\n}\n.document-info[_ngcontent-%COMP%] {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-xs);\n}\n.document-header[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n.document-name[_ngcontent-%COMP%] {\n  font-weight: 500;\n  color: var(--secondary-900);\n}\n.remove-button[_ngcontent-%COMP%] {\n  padding: var(--spacing-xs);\n  background: none;\n  border: none;\n  color: var(--error-600);\n  cursor: pointer;\n  border-radius: var(--radius-sm);\n  transition: all 0.2s ease;\n}\n.remove-button[_ngcontent-%COMP%]:hover {\n  background: var(--error-100);\n}\n.remove-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\n  width: 16px;\n  height: 16px;\n  stroke-width: 2;\n}\n.document-meta[_ngcontent-%COMP%] {\n  display: flex;\n  gap: var(--spacing-md);\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n}\n.document-type[_ngcontent-%COMP%] {\n  padding: 2px 8px;\n  background: var(--primary-100);\n  color: var(--primary-700);\n  border-radius: var(--radius-sm);\n  font-size: var(--text-xs);\n  font-weight: 500;\n}\n.document-upload-info[_ngcontent-%COMP%] {\n  display: flex;\n  gap: var(--spacing-sm);\n  font-size: var(--text-xs);\n  color: var(--secondary-500);\n}\n.document-description[_ngcontent-%COMP%] {\n  font-size: var(--text-sm);\n  color: var(--secondary-700);\n  font-style: italic;\n}\n.empty-state[_ngcontent-%COMP%] {\n  text-align: center;\n  padding: var(--spacing-xl);\n  color: var(--secondary-600);\n}\n@media (max-width: 768px) {\n  .document-item[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: var(--spacing-sm);\n  }\n  .document-meta[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: var(--spacing-xs);\n  }\n}\n/*# sourceMappingURL=shared-documentation-modal.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(SharedDocumentationModalComponent, [{
    type: Component,
    args: [{ selector: "app-shared-documentation-modal", standalone: true, imports: [CommonModule, ReactiveFormsModule, SlideModalComponent, FileUploadComponent], template: `
    <app-slide-modal
      [isOpen]="isOpen"
      [title]="editMode ? 'Edit Documentation' : 'Add Documentation'"
      [subtitle]="showApplicationSelection ? 'Upload documents and select application' : 'Upload documents'"
      [loading]="loading"
      [canConfirm]="canConfirm"
      confirmText="Save Documentation"
      (closed)="onClose()"
      (confirmed)="onSave()"
      (cancelled)="onClose()"
    >
      <form [formGroup]="documentationForm" class="documentation-form">
        <!-- Application Selection (only when not in applications module) -->
        <div class="form-group" *ngIf="showApplicationSelection">
          <label class="form-label">Application *</label>
          <select formControlName="applicationId" class="form-select">
            <option value="">Select an application</option>
            <option *ngFor="let app of applicationOptions$ | async" [value]="app.id">
              {{ app.name }} - {{ app.department }}
            </option>
          </select>
          <div class="field-error" *ngIf="getFieldError('applicationId')">
            {{ getFieldError('applicationId') }}
          </div>
        </div>

        <!-- File Upload Section -->
        <div class="form-section">
          <h4 class="section-title">Upload Documents</h4>
          <app-file-upload
            [showDescription]="true"
            (documentAdded)="onDocumentAdded($event)"
          ></app-file-upload>
        </div>

        <!-- Uploaded Documents List -->
        <div class="form-section" *ngIf="uploadedDocuments.length > 0">
          <h4 class="section-title">Uploaded Documents ({{ uploadedDocuments.length }})</h4>
          <div class="documents-list">
            <div class="document-item" *ngFor="let doc of uploadedDocuments; let i = index">
              <div class="document-icon">{{ doc.icon }}</div>
              <div class="document-info">
                <div class="document-header">
                  <span class="document-name">{{ doc.fileName }}</span>
                  <button class="remove-button" (click)="removeDocument(i)" type="button">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <polyline points="3,6 5,6 21,6"></polyline>
                      <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
                    </svg>
                  </button>
                </div>
                <div class="document-meta">
                  <span class="document-type">{{ getDocumentTypeLabel(doc.type) }}</span>
                  <span class="document-size">{{ formatFileSize(doc.fileSize) }}</span>
                  <span class="document-mime">{{ doc.mimeType }}</span>
                </div>
                <div class="document-upload-info">
                  <span class="upload-date">{{ formatDate(doc.uploadedAt) }}</span>
                  <span class="uploaded-by">by {{ doc.uploadedBy }}</span>
                </div>
                <div class="document-description" *ngIf="doc.description">
                  {{ doc.description }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="empty-state" *ngIf="uploadedDocuments.length === 0">
          <p>No documents uploaded yet. Use the upload section above to add documents.</p>
        </div>
      </form>
    </app-slide-modal>
  `, styles: ["/* angular:styles/component:scss;9142145e5181851e229535883282c4070082610990bb03afbe0f114a85d40aa9;C:/Users/<USER>/Documents/VibeCoding/AppCatalog/app-catalog/frontend/src/app/shared/components/modals/shared-documentation-modal/shared-documentation-modal.component.ts */\n.documentation-form {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-lg);\n}\n.form-group {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-sm);\n}\n.form-label {\n  font-size: var(--text-sm);\n  font-weight: 500;\n  color: var(--secondary-700);\n}\n.form-select {\n  height: 40px;\n  padding: 0 var(--spacing-md);\n  border: 1px solid var(--secondary-300);\n  border-radius: var(--radius-md);\n  background: white;\n  font-size: var(--text-sm);\n  color: var(--secondary-800);\n  transition: all 0.2s ease;\n}\n.form-select:focus {\n  outline: none;\n  border-color: var(--primary-500);\n  box-shadow: 0 0 0 3px var(--primary-100);\n}\n.field-error {\n  font-size: var(--text-sm);\n  color: var(--error-600);\n}\n.form-section {\n  padding: var(--spacing-lg);\n  background: var(--secondary-50);\n  border-radius: var(--radius-md);\n  border: 1px solid var(--secondary-200);\n}\n.section-title {\n  margin: 0 0 var(--spacing-md) 0;\n  font-size: var(--text-lg);\n  font-weight: 600;\n  color: var(--secondary-900);\n}\n.documents-list {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-md);\n}\n.document-item {\n  display: flex;\n  gap: var(--spacing-md);\n  padding: var(--spacing-md);\n  background: white;\n  border-radius: var(--radius-md);\n  border: 1px solid var(--secondary-200);\n}\n.document-icon {\n  font-size: 24px;\n  width: 40px;\n  height: 40px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background: var(--primary-100);\n  border-radius: var(--radius-md);\n}\n.document-info {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-xs);\n}\n.document-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n.document-name {\n  font-weight: 500;\n  color: var(--secondary-900);\n}\n.remove-button {\n  padding: var(--spacing-xs);\n  background: none;\n  border: none;\n  color: var(--error-600);\n  cursor: pointer;\n  border-radius: var(--radius-sm);\n  transition: all 0.2s ease;\n}\n.remove-button:hover {\n  background: var(--error-100);\n}\n.remove-button svg {\n  width: 16px;\n  height: 16px;\n  stroke-width: 2;\n}\n.document-meta {\n  display: flex;\n  gap: var(--spacing-md);\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n}\n.document-type {\n  padding: 2px 8px;\n  background: var(--primary-100);\n  color: var(--primary-700);\n  border-radius: var(--radius-sm);\n  font-size: var(--text-xs);\n  font-weight: 500;\n}\n.document-upload-info {\n  display: flex;\n  gap: var(--spacing-sm);\n  font-size: var(--text-xs);\n  color: var(--secondary-500);\n}\n.document-description {\n  font-size: var(--text-sm);\n  color: var(--secondary-700);\n  font-style: italic;\n}\n.empty-state {\n  text-align: center;\n  padding: var(--spacing-xl);\n  color: var(--secondary-600);\n}\n@media (max-width: 768px) {\n  .document-item {\n    flex-direction: column;\n    gap: var(--spacing-sm);\n  }\n  .document-meta {\n    flex-direction: column;\n    gap: var(--spacing-xs);\n  }\n}\n/*# sourceMappingURL=shared-documentation-modal.component.css.map */\n"] }]
  }], () => [{ type: FormBuilder }, { type: ApplicationSelectionService }], { isOpen: [{
    type: Input
  }], editMode: [{
    type: Input
  }], initialData: [{
    type: Input
  }], loading: [{
    type: Input
  }], showApplicationSelection: [{
    type: Input
  }], closed: [{
    type: Output
  }], saved: [{
    type: Output
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(SharedDocumentationModalComponent, { className: "SharedDocumentationModalComponent", filePath: "src/app/shared/components/modals/shared-documentation-modal/shared-documentation-modal.component.ts", lineNumber: 260 });
})();

// src/app/modules/documentation/documentation.component.ts
var DocumentationComponent = class _DocumentationComponent {
  fb;
  documentation = [];
  filteredDocumentation = [];
  loading = false;
  // Modal state
  showDocumentationModal = false;
  editMode = false;
  editData = null;
  modalLoading = false;
  // Statistics
  stats = {
    total: 0,
    recentUpdates: 0,
    outdated: 0,
    categories: 0,
    externalLinks: 0
  };
  // Chart data
  categoryChartData = null;
  typeChartData = null;
  chartOptions = {
    responsive: true,
    maintainAspectRatio: false
  };
  // Table configuration
  tableColumns = [
    { key: "title", label: "Title", sortable: true },
    { key: "type", label: "Type", type: "badge", sortable: true },
    { key: "category", label: "Category", type: "badge", sortable: true },
    { key: "status", label: "Status", type: "badge", sortable: true },
    { key: "author", label: "Author", sortable: true },
    { key: "version", label: "Version", sortable: true },
    { key: "lastModified", label: "Last Modified", type: "date", sortable: true }
  ];
  tableActions = [
    {
      label: "View",
      icon: "M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7Z",
      variant: "ghost",
      action: (item) => this.viewDocument(item)
    },
    {
      label: "Edit",
      icon: "M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",
      variant: "ghost",
      action: (item) => this.editDocument(item)
    },
    {
      label: "Delete",
      icon: "M3 6h18m-2 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",
      variant: "error",
      action: (item) => this.deleteDocument(item)
    }
  ];
  constructor(fb) {
    this.fb = fb;
  }
  ngOnInit() {
    this.loadDocumentation();
  }
  loadDocumentation() {
    this.loading = true;
    setTimeout(() => {
      this.documentation = this.generateMockDocumentation();
      this.filteredDocumentation = [...this.documentation];
      this.updateStats();
      this.updateChartData();
      this.loading = false;
    }, 1e3);
  }
  generateMockDocumentation() {
    const types = ["document", "link", "wiki", "video", "tutorial"];
    const categories = ["api", "user_guide", "technical", "architecture", "deployment", "troubleshooting"];
    const statuses = ["current", "outdated", "draft", "archived"];
    const applications = ["App 1", "App 2", "App 3", "App 4", "App 5"];
    return Array.from({ length: 35 }, (_, i) => ({
      id: i + 1,
      title: `Documentation ${i + 1}`,
      type: types[Math.floor(Math.random() * types.length)],
      category: categories[Math.floor(Math.random() * categories.length)],
      status: statuses[Math.floor(Math.random() * statuses.length)],
      application: applications[Math.floor(Math.random() * applications.length)],
      author: `Author ${Math.floor(Math.random() * 10) + 1}`,
      createdDate: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1e3),
      lastModified: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1e3),
      version: `${Math.floor(Math.random() * 3) + 1}.${Math.floor(Math.random() * 10)}`,
      url: Math.random() > 0.5 ? `https://docs.example.com/doc-${i + 1}` : void 0,
      description: `Description for documentation ${i + 1}`,
      tags: [`tag${Math.floor(Math.random() * 5) + 1}`, `tag${Math.floor(Math.random() * 5) + 6}`]
    }));
  }
  updateStats() {
    this.stats.total = this.documentation.length;
    this.stats.recentUpdates = this.documentation.filter((d) => new Date(d.lastModified).getTime() > Date.now() - 7 * 24 * 60 * 60 * 1e3).length;
    this.stats.outdated = this.documentation.filter((d) => d.status === "outdated").length;
    this.stats.categories = new Set(this.documentation.map((d) => d.category)).size;
    this.stats.externalLinks = this.documentation.filter((d) => d.type === "link").length;
  }
  updateChartData() {
    const categoryCount = this.documentation.reduce((acc, doc) => {
      acc[doc.category] = (acc[doc.category] || 0) + 1;
      return acc;
    }, {});
    this.categoryChartData = {
      labels: Object.keys(categoryCount),
      datasets: [{
        data: Object.values(categoryCount),
        backgroundColor: [
          "#0ea5e9",
          "#22c55e",
          "#f59e0b",
          "#ef4444",
          "#8b5cf6",
          "#06b6d4"
        ]
      }]
    };
    const typeCount = this.documentation.reduce((acc, doc) => {
      acc[doc.type] = (acc[doc.type] || 0) + 1;
      return acc;
    }, {});
    this.typeChartData = {
      labels: Object.keys(typeCount),
      datasets: [{
        label: "Documents",
        data: Object.values(typeCount),
        backgroundColor: ["#22c55e", "#0ea5e9", "#f59e0b", "#ef4444", "#8b5cf6"]
      }]
    };
  }
  openAddModal() {
    this.editMode = false;
    this.editData = null;
    this.showDocumentationModal = true;
  }
  closeModal() {
    this.showDocumentationModal = false;
    this.editMode = false;
    this.editData = null;
  }
  onDocumentationSaved(data) {
    this.modalLoading = true;
    setTimeout(() => {
      console.log("Documentation saved:", data);
      this.modalLoading = false;
      this.closeModal();
      this.loadDocumentation();
    }, 1e3);
  }
  viewDocument(item) {
    if (item.url) {
      window.open(item.url, "_blank");
    } else {
      console.log("View document:", item);
    }
  }
  editDocument(item) {
    console.log("Edit document:", item);
  }
  deleteDocument(item) {
    if (confirm(`Are you sure you want to delete ${item.title}?`)) {
      this.documentation = this.documentation.filter((d) => d.id !== item.id);
      this.filteredDocumentation = this.filteredDocumentation.filter((d) => d.id !== item.id);
      this.updateStats();
      this.updateChartData();
    }
  }
  onSearchInput(event) {
    const target = event.target;
    this.onSearchChanged(target.value);
  }
  onSearchChanged(searchTerm) {
    this.applyFilters();
  }
  onFilterChanged(filterType, event) {
    const target = event.target;
    this.applyFilters();
  }
  onSortChanged(sort) {
    console.log("Sort changed:", sort);
  }
  applyFilters() {
    this.filteredDocumentation = [...this.documentation];
  }
  static \u0275fac = function DocumentationComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _DocumentationComponent)(\u0275\u0275directiveInject(FormBuilder));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _DocumentationComponent, selectors: [["app-documentation"]], decls: 86, vars: 19, consts: [[1, "documentation-page"], [1, "page-content"], [1, "page-header"], [1, "header-content"], [1, "page-subtitle"], [1, "header-actions"], ["variant", "primary", "leftIcon", "M12 4v16m8-8H4", 3, "clicked"], [1, "stats-grid"], ["title", "Total Documents"], [1, "stat-content"], [1, "stat-number"], [1, "stat-change"], ["title", "Recent Updates"], [1, "stat-number", "recent"], ["title", "Outdated Docs"], [1, "stat-number", "warning"], ["title", "External Links"], [1, "charts-section"], ["title", "Documents by Category", "subtitle", "Distribution across categories"], ["type", "doughnut", "height", "300px", 3, "data", "options"], ["title", "Document Types", "subtitle", "Distribution by document type"], ["type", "bar", "height", "300px", 3, "data", "options"], ["title", "Documentation Library", "subtitle", "All project documentation"], [1, "table-controls"], [1, "search-controls"], ["type", "text", "placeholder", "Search documentation...", 1, "search-input", 3, "input"], [1, "filter-select", 3, "change"], ["value", ""], ["value", "api"], ["value", "user_guide"], ["value", "technical"], ["value", "architecture"], ["value", "deployment"], ["value", "troubleshooting"], ["value", "document"], ["value", "link"], ["value", "wiki"], ["value", "video"], ["value", "tutorial"], ["value", "current"], ["value", "outdated"], ["value", "draft"], ["value", "archived"], [3, "sortChanged", "columns", "data", "actions", "loading", "sortable"], [3, "closed", "saved", "isOpen", "editMode", "initialData", "loading", "showApplicationSelection"]], template: function DocumentationComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "div", 3)(4, "h1");
      \u0275\u0275text(5, "Documentation");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(6, "p", 4);
      \u0275\u0275text(7, "Manage project documentation and resources");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(8, "div", 5)(9, "app-button", 6);
      \u0275\u0275listener("clicked", function DocumentationComponent_Template_app_button_clicked_9_listener() {
        return ctx.openAddModal();
      });
      \u0275\u0275text(10, " Add Document ");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(11, "div", 7)(12, "app-card", 8)(13, "div", 9)(14, "div", 10);
      \u0275\u0275text(15);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(16, "div", 11);
      \u0275\u0275text(17);
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(18, "app-card", 12)(19, "div", 9)(20, "div", 13);
      \u0275\u0275text(21);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(22, "div", 11);
      \u0275\u0275text(23, "Updated this week");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(24, "app-card", 14)(25, "div", 9)(26, "div", 15);
      \u0275\u0275text(27);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(28, "div", 11);
      \u0275\u0275text(29, "Need review");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(30, "app-card", 16)(31, "div", 9)(32, "div", 10);
      \u0275\u0275text(33);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(34, "div", 11);
      \u0275\u0275text(35, "External resources");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(36, "div", 17)(37, "app-card", 18);
      \u0275\u0275element(38, "app-chart", 19);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(39, "app-card", 20);
      \u0275\u0275element(40, "app-chart", 21);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(41, "app-card", 22)(42, "div", 23)(43, "div", 24)(44, "input", 25);
      \u0275\u0275listener("input", function DocumentationComponent_Template_input_input_44_listener($event) {
        return ctx.onSearchInput($event);
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(45, "select", 26);
      \u0275\u0275listener("change", function DocumentationComponent_Template_select_change_45_listener($event) {
        return ctx.onFilterChanged("category", $event);
      });
      \u0275\u0275elementStart(46, "option", 27);
      \u0275\u0275text(47, "All Categories");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(48, "option", 28);
      \u0275\u0275text(49, "API Documentation");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(50, "option", 29);
      \u0275\u0275text(51, "User Guide");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(52, "option", 30);
      \u0275\u0275text(53, "Technical Specs");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(54, "option", 31);
      \u0275\u0275text(55, "Architecture");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(56, "option", 32);
      \u0275\u0275text(57, "Deployment");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(58, "option", 33);
      \u0275\u0275text(59, "Troubleshooting");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(60, "select", 26);
      \u0275\u0275listener("change", function DocumentationComponent_Template_select_change_60_listener($event) {
        return ctx.onFilterChanged("type", $event);
      });
      \u0275\u0275elementStart(61, "option", 27);
      \u0275\u0275text(62, "All Types");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(63, "option", 34);
      \u0275\u0275text(64, "Document");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(65, "option", 35);
      \u0275\u0275text(66, "External Link");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(67, "option", 36);
      \u0275\u0275text(68, "Wiki Page");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(69, "option", 37);
      \u0275\u0275text(70, "Video");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(71, "option", 38);
      \u0275\u0275text(72, "Tutorial");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(73, "select", 26);
      \u0275\u0275listener("change", function DocumentationComponent_Template_select_change_73_listener($event) {
        return ctx.onFilterChanged("status", $event);
      });
      \u0275\u0275elementStart(74, "option", 27);
      \u0275\u0275text(75, "All Statuses");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(76, "option", 39);
      \u0275\u0275text(77, "Current");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(78, "option", 40);
      \u0275\u0275text(79, "Outdated");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(80, "option", 41);
      \u0275\u0275text(81, "Draft");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(82, "option", 42);
      \u0275\u0275text(83, "Archived");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(84, "app-table", 43);
      \u0275\u0275listener("sortChanged", function DocumentationComponent_Template_app_table_sortChanged_84_listener($event) {
        return ctx.onSortChanged($event);
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(85, "app-shared-documentation-modal", 44);
      \u0275\u0275listener("closed", function DocumentationComponent_Template_app_shared_documentation_modal_closed_85_listener() {
        return ctx.closeModal();
      })("saved", function DocumentationComponent_Template_app_shared_documentation_modal_saved_85_listener($event) {
        return ctx.onDocumentationSaved($event);
      });
      \u0275\u0275elementEnd()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(15);
      \u0275\u0275textInterpolate(ctx.stats.total);
      \u0275\u0275advance(2);
      \u0275\u0275textInterpolate1("Across ", ctx.stats.categories, " categories");
      \u0275\u0275advance(4);
      \u0275\u0275textInterpolate(ctx.stats.recentUpdates);
      \u0275\u0275advance(6);
      \u0275\u0275textInterpolate(ctx.stats.outdated);
      \u0275\u0275advance(6);
      \u0275\u0275textInterpolate(ctx.stats.externalLinks);
      \u0275\u0275advance(5);
      \u0275\u0275property("data", ctx.categoryChartData)("options", ctx.chartOptions);
      \u0275\u0275advance(2);
      \u0275\u0275property("data", ctx.typeChartData)("options", ctx.chartOptions);
      \u0275\u0275advance(44);
      \u0275\u0275property("columns", ctx.tableColumns)("data", ctx.filteredDocumentation)("actions", ctx.tableActions)("loading", ctx.loading)("sortable", true);
      \u0275\u0275advance();
      \u0275\u0275property("isOpen", ctx.showDocumentationModal)("editMode", ctx.editMode)("initialData", ctx.editData)("loading", ctx.modalLoading)("showApplicationSelection", true);
    }
  }, dependencies: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    NgSelectOption,
    \u0275NgSelectMultipleOption,
    CardComponent,
    ButtonComponent,
    TableComponent,
    ChartComponent,
    SharedDocumentationModalComponent
  ], styles: [`

.documentation-page[_ngcontent-%COMP%] {
  min-height: 100%;
}
.page-content[_ngcontent-%COMP%] {
  padding: var(--spacing-xl);
}
.page-header[_ngcontent-%COMP%] {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-xl);
}
.header-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--secondary-900);
}
.page-subtitle[_ngcontent-%COMP%] {
  margin: 0;
  font-size: var(--text-lg);
  color: var(--secondary-600);
}
.stats-grid[_ngcontent-%COMP%] {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}
.stat-content[_ngcontent-%COMP%] {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
}
.stat-number[_ngcontent-%COMP%] {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--secondary-900);
}
.stat-number.recent[_ngcontent-%COMP%] {
  color: var(--primary-600);
}
.stat-number.warning[_ngcontent-%COMP%] {
  color: var(--warning-600);
}
.stat-change[_ngcontent-%COMP%] {
  font-size: var(--text-sm);
  color: var(--secondary-600);
}
.charts-section[_ngcontent-%COMP%] {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}
.table-controls[_ngcontent-%COMP%] {
  margin-bottom: var(--spacing-lg);
}
.search-controls[_ngcontent-%COMP%] {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
  flex-wrap: wrap;
}
.search-input[_ngcontent-%COMP%], 
.filter-select[_ngcontent-%COMP%] {
  height: 40px;
  padding: 0 var(--spacing-md);
  border: 1px solid var(--secondary-300);
  border-radius: var(--radius-md);
  background: white;
  font-size: var(--text-sm);
  color: var(--secondary-800);
  transition: all 0.2s ease;
}
.search-input[_ngcontent-%COMP%]:focus, 
.filter-select[_ngcontent-%COMP%]:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}
.search-input[_ngcontent-%COMP%] {
  flex: 1;
  min-width: 200px;
}
.filter-select[_ngcontent-%COMP%] {
  min-width: 120px;
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-sm) center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
  appearance: none;
}
@media (max-width: 768px) {
  .page-header[_ngcontent-%COMP%] {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }
  .stats-grid[_ngcontent-%COMP%] {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  .charts-section[_ngcontent-%COMP%] {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  .search-controls[_ngcontent-%COMP%] {
    flex-direction: column;
    align-items: stretch;
  }
  .search-input[_ngcontent-%COMP%], 
   .filter-select[_ngcontent-%COMP%] {
    min-width: auto;
  }
}
/*# sourceMappingURL=documentation.component.css.map */`] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(DocumentationComponent, [{
    type: Component,
    args: [{ selector: "app-documentation", standalone: true, imports: [
      CommonModule,
      RouterModule,
      ReactiveFormsModule,
      CardComponent,
      ButtonComponent,
      TableComponent,
      ChartComponent,
      SharedDocumentationModalComponent
    ], template: `
    <div class="documentation-page">
      <div class="page-content">
        <div class="page-header">
          <div class="header-content">
            <h1>Documentation</h1>
            <p class="page-subtitle">Manage project documentation and resources</p>
          </div>
          <div class="header-actions">
            <app-button
              variant="primary"
              leftIcon="M12 4v16m8-8H4"
              (clicked)="openAddModal()"
            >
              Add Document
            </app-button>
          </div>
        </div>

      <!-- Statistics Cards -->
      <div class="stats-grid">
        <app-card title="Total Documents">
          <div class="stat-content">
            <div class="stat-number">{{ stats.total }}</div>
            <div class="stat-change">Across {{ stats.categories }} categories</div>
          </div>
        </app-card>

        <app-card title="Recent Updates">
          <div class="stat-content">
            <div class="stat-number recent">{{ stats.recentUpdates }}</div>
            <div class="stat-change">Updated this week</div>
          </div>
        </app-card>

        <app-card title="Outdated Docs">
          <div class="stat-content">
            <div class="stat-number warning">{{ stats.outdated }}</div>
            <div class="stat-change">Need review</div>
          </div>
        </app-card>

        <app-card title="External Links">
          <div class="stat-content">
            <div class="stat-number">{{ stats.externalLinks }}</div>
            <div class="stat-change">External resources</div>
          </div>
        </app-card>
      </div>

      <!-- Charts Section -->
      <div class="charts-section">
        <app-card title="Documents by Category" subtitle="Distribution across categories">
          <app-chart
            type="doughnut"
            [data]="categoryChartData"
            [options]="chartOptions"
            height="300px"
          ></app-chart>
        </app-card>

        <app-card title="Document Types" subtitle="Distribution by document type">
          <app-chart
            type="bar"
            [data]="typeChartData"
            [options]="chartOptions"
            height="300px"
          ></app-chart>
        </app-card>
      </div>

      <!-- Documentation Table -->
      <app-card title="Documentation Library" subtitle="All project documentation">
        <div class="table-controls">
          <div class="search-controls">
            <input
              type="text"
              placeholder="Search documentation..."
              class="search-input"
              (input)="onSearchInput($event)"
            >
            <select class="filter-select" (change)="onFilterChanged('category', $event)">
              <option value="">All Categories</option>
              <option value="api">API Documentation</option>
              <option value="user_guide">User Guide</option>
              <option value="technical">Technical Specs</option>
              <option value="architecture">Architecture</option>
              <option value="deployment">Deployment</option>
              <option value="troubleshooting">Troubleshooting</option>
            </select>
            <select class="filter-select" (change)="onFilterChanged('type', $event)">
              <option value="">All Types</option>
              <option value="document">Document</option>
              <option value="link">External Link</option>
              <option value="wiki">Wiki Page</option>
              <option value="video">Video</option>
              <option value="tutorial">Tutorial</option>
            </select>
            <select class="filter-select" (change)="onFilterChanged('status', $event)">
              <option value="">All Statuses</option>
              <option value="current">Current</option>
              <option value="outdated">Outdated</option>
              <option value="draft">Draft</option>
              <option value="archived">Archived</option>
            </select>
          </div>
        </div>

        <app-table
          [columns]="tableColumns"
          [data]="filteredDocumentation"
          [actions]="tableActions"
          [loading]="loading"
          [sortable]="true"
          (sortChanged)="onSortChanged($event)"
        ></app-table>
      </app-card>

      <!-- Documentation Modal -->
      <app-shared-documentation-modal
        [isOpen]="showDocumentationModal"
        [editMode]="editMode"
        [initialData]="editData"
        [loading]="modalLoading"
        [showApplicationSelection]="true"
        (closed)="closeModal()"
        (saved)="onDocumentationSaved($event)"
      ></app-shared-documentation-modal>
      </div>
    </div>
  `, styles: [`/* angular:styles/component:scss;233a67261a89751d644f82ce5ecfcd1df14a6e6ac29d5a64f232097ae38bf84b;C:/Users/<USER>/Documents/VibeCoding/AppCatalog/app-catalog/frontend/src/app/modules/documentation/documentation.component.ts */
.documentation-page {
  min-height: 100%;
}
.page-content {
  padding: var(--spacing-xl);
}
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-xl);
}
.header-content h1 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--secondary-900);
}
.page-subtitle {
  margin: 0;
  font-size: var(--text-lg);
  color: var(--secondary-600);
}
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}
.stat-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
}
.stat-number {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--secondary-900);
}
.stat-number.recent {
  color: var(--primary-600);
}
.stat-number.warning {
  color: var(--warning-600);
}
.stat-change {
  font-size: var(--text-sm);
  color: var(--secondary-600);
}
.charts-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}
.table-controls {
  margin-bottom: var(--spacing-lg);
}
.search-controls {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
  flex-wrap: wrap;
}
.search-input,
.filter-select {
  height: 40px;
  padding: 0 var(--spacing-md);
  border: 1px solid var(--secondary-300);
  border-radius: var(--radius-md);
  background: white;
  font-size: var(--text-sm);
  color: var(--secondary-800);
  transition: all 0.2s ease;
}
.search-input:focus,
.filter-select:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}
.search-input {
  flex: 1;
  min-width: 200px;
}
.filter-select {
  min-width: 120px;
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-sm) center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
  appearance: none;
}
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }
  .stats-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  .charts-section {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  .search-controls {
    flex-direction: column;
    align-items: stretch;
  }
  .search-input,
  .filter-select {
    min-width: auto;
  }
}
/*# sourceMappingURL=documentation.component.css.map */
`] }]
  }], () => [{ type: FormBuilder }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(DocumentationComponent, { className: "DocumentationComponent", filePath: "src/app/modules/documentation/documentation.component.ts", lineNumber: 314 });
})();
export {
  DocumentationComponent
};
//# sourceMappingURL=chunk-TS3XSHER.js.map
