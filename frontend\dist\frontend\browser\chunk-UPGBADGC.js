import {
  TableComponent
} from "./chunk-QQYNLSVO.js";
import {
  ChartComponent
} from "./chunk-HCVU2C6O.js";
import {
  StakeholderModalComponent
} from "./chunk-2GSYAZRY.js";
import "./chunk-WQFVHIJL.js";
import "./chunk-6DLBFET6.js";
import {
  FormBuilder,
  NgSelectOption,
  ReactiveFormsModule,
  ɵNgSelectMultipleOption
} from "./chunk-SM75SJZE.js";
import {
  CardComponent
} from "./chunk-SLWZQAXJ.js";
import {
  ButtonComponent
} from "./chunk-QMVBAXS7.js";
import {
  RouterModule
} from "./chunk-IKU57TF7.js";
import {
  CommonModule,
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵproperty,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1
} from "./chunk-3JQLQ36P.js";
import {
  __spreadProps,
  __spreadValues
} from "./chunk-WDMUDEB6.js";

// src/app/modules/stakeholders/stakeholders.component.ts
var StakeholdersComponent = class _StakeholdersComponent {
  fb;
  stakeholders = [];
  filteredStakeholders = [];
  loading = false;
  // Modal state
  showModal = false;
  editMode = false;
  editData = null;
  modalLoading = false;
  // Statistics
  stats = {
    total: 0,
    active: 0,
    owners: 0,
    departments: 0,
    recentActivity: 0,
    activePercentage: 0
  };
  // Chart data
  departmentChartData = null;
  roleChartData = null;
  chartOptions = {
    responsive: true,
    maintainAspectRatio: false
  };
  // Table configuration
  tableColumns = [
    { key: "name", label: "Name", sortable: true },
    { key: "email", label: "Email", sortable: true },
    { key: "role", label: "Role", type: "badge", sortable: true },
    { key: "department", label: "Department", type: "badge", sortable: true },
    { key: "applications", label: "Apps", type: "number", sortable: true },
    { key: "isActive", label: "Status", type: "boolean", sortable: true },
    { key: "lastActivity", label: "Last Activity", type: "date", sortable: true }
  ];
  tableActions = [
    {
      label: "Edit",
      icon: "M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",
      variant: "ghost",
      action: (item) => this.editStakeholder(item)
    },
    {
      label: "Delete",
      icon: "M3 6h18m-2 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",
      variant: "error",
      action: (item) => this.deleteStakeholder(item)
    }
  ];
  constructor(fb) {
    this.fb = fb;
  }
  ngOnInit() {
    this.loadStakeholders();
  }
  loadStakeholders() {
    this.loading = true;
    setTimeout(() => {
      this.stakeholders = this.generateMockStakeholders();
      this.filteredStakeholders = [...this.stakeholders];
      this.updateStats();
      this.updateChartData();
      this.loading = false;
    }, 1e3);
  }
  generateMockStakeholders() {
    const departments = ["engineering", "product", "design", "qa", "devops", "management"];
    const roles = ["owner", "developer", "architect", "manager", "stakeholder"];
    return Array.from({ length: 25 }, (_, i) => ({
      id: i + 1,
      name: `Stakeholder ${i + 1}`,
      email: `stakeholder${i + 1}@company.com`,
      role: roles[Math.floor(Math.random() * roles.length)],
      department: departments[Math.floor(Math.random() * departments.length)],
      isActive: Math.random() > 0.2,
      applications: Math.floor(Math.random() * 10) + 1,
      lastActivity: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1e3),
      phone: `******-${Math.floor(Math.random() * 900) + 100}-${Math.floor(Math.random() * 9e3) + 1e3}`,
      responsibilities: `Responsibilities for stakeholder ${i + 1}`
    }));
  }
  updateStats() {
    this.stats.total = this.stakeholders.length;
    this.stats.active = this.stakeholders.filter((s) => s.isActive).length;
    this.stats.owners = this.stakeholders.filter((s) => s.role === "owner").length;
    this.stats.departments = new Set(this.stakeholders.map((s) => s.department)).size;
    this.stats.recentActivity = this.stakeholders.filter((s) => new Date(s.lastActivity).getTime() > Date.now() - 7 * 24 * 60 * 60 * 1e3).length;
    this.stats.activePercentage = Math.round(this.stats.active / this.stats.total * 100);
  }
  updateChartData() {
    const departmentCount = this.stakeholders.reduce((acc, stakeholder) => {
      acc[stakeholder.department] = (acc[stakeholder.department] || 0) + 1;
      return acc;
    }, {});
    this.departmentChartData = {
      labels: Object.keys(departmentCount),
      datasets: [{
        data: Object.values(departmentCount),
        backgroundColor: [
          "#0ea5e9",
          "#22c55e",
          "#f59e0b",
          "#ef4444",
          "#8b5cf6",
          "#06b6d4"
        ]
      }]
    };
    const roleCount = this.stakeholders.reduce((acc, stakeholder) => {
      acc[stakeholder.role] = (acc[stakeholder.role] || 0) + 1;
      return acc;
    }, {});
    this.roleChartData = {
      labels: Object.keys(roleCount),
      datasets: [{
        label: "Stakeholders",
        data: Object.values(roleCount),
        backgroundColor: ["#22c55e", "#0ea5e9", "#f59e0b", "#ef4444", "#8b5cf6"]
      }]
    };
  }
  openAddModal() {
    this.editMode = false;
    this.editData = null;
    this.showModal = true;
  }
  editStakeholder(stakeholder) {
    this.editMode = true;
    this.editData = __spreadValues({}, stakeholder);
    this.showModal = true;
  }
  deleteStakeholder(stakeholder) {
    if (confirm(`Are you sure you want to delete ${stakeholder.name}?`)) {
      this.stakeholders = this.stakeholders.filter((s) => s.id !== stakeholder.id);
      this.filteredStakeholders = this.filteredStakeholders.filter((s) => s.id !== stakeholder.id);
      this.updateStats();
      this.updateChartData();
    }
  }
  closeModal() {
    this.showModal = false;
    this.editMode = false;
    this.editData = null;
  }
  onStakeholderSaved(stakeholderData) {
    this.modalLoading = true;
    setTimeout(() => {
      if (this.editMode) {
        const index = this.stakeholders.findIndex((s) => s.id === this.editData.id);
        if (index !== -1) {
          this.stakeholders[index] = __spreadValues(__spreadValues({}, this.stakeholders[index]), stakeholderData);
        }
      } else {
        const newStakeholder = __spreadProps(__spreadValues({
          id: Math.max(...this.stakeholders.map((s) => s.id)) + 1
        }, stakeholderData), {
          applications: 0,
          lastActivity: /* @__PURE__ */ new Date()
        });
        this.stakeholders.push(newStakeholder);
      }
      this.filteredStakeholders = [...this.stakeholders];
      this.updateStats();
      this.updateChartData();
      this.modalLoading = false;
      this.closeModal();
    }, 1e3);
  }
  onSearchInput(event) {
    const target = event.target;
    this.onSearchChanged(target.value);
  }
  onSearchChanged(searchTerm) {
    this.applyFilters();
  }
  onFilterChanged(filterType, event) {
    const target = event.target;
    this.applyFilters();
  }
  onSortChanged(sort) {
    console.log("Sort changed:", sort);
  }
  applyFilters() {
    this.filteredStakeholders = [...this.stakeholders];
  }
  static \u0275fac = function StakeholdersComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _StakeholdersComponent)(\u0275\u0275directiveInject(FormBuilder));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _StakeholdersComponent, selectors: [["app-stakeholders"]], decls: 75, vars: 19, consts: [[1, "stakeholders-page"], [1, "page-content"], [1, "page-header"], [1, "header-content"], [1, "page-subtitle"], [1, "header-actions"], ["variant", "primary", "leftIcon", "M12 4v16m8-8H4", 3, "clicked"], [1, "stats-grid"], ["title", "Total Stakeholders"], [1, "stat-content"], [1, "stat-number"], [1, "stat-change"], ["title", "Active Members"], [1, "stat-number", "active"], ["title", "Project Owners"], [1, "stat-number", "owner"], ["title", "Recent Activity"], [1, "charts-section"], ["title", "Stakeholders by Department", "subtitle", "Distribution across departments"], ["type", "doughnut", "height", "300px", 3, "data", "options"], ["title", "Roles Distribution", "subtitle", "Stakeholders by role"], ["type", "bar", "height", "300px", 3, "data", "options"], ["title", "Team Members", "subtitle", "All project stakeholders"], [1, "table-controls"], [1, "search-controls"], ["type", "text", "placeholder", "Search stakeholders...", 1, "search-input", 3, "input"], [1, "filter-select", 3, "change"], ["value", ""], ["value", "engineering"], ["value", "product"], ["value", "design"], ["value", "qa"], ["value", "devops"], ["value", "management"], ["value", "owner"], ["value", "developer"], ["value", "architect"], ["value", "manager"], ["value", "stakeholder"], [3, "sortChanged", "columns", "data", "actions", "loading", "sortable"], [3, "closed", "saved", "isOpen", "editMode", "initialData", "loading"]], template: function StakeholdersComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "div", 3)(4, "h1");
      \u0275\u0275text(5, "Stakeholders");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(6, "p", 4);
      \u0275\u0275text(7, "Manage team members and project stakeholders");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(8, "div", 5)(9, "app-button", 6);
      \u0275\u0275listener("clicked", function StakeholdersComponent_Template_app_button_clicked_9_listener() {
        return ctx.openAddModal();
      });
      \u0275\u0275text(10, " Add Stakeholder ");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(11, "div", 7)(12, "app-card", 8)(13, "div", 9)(14, "div", 10);
      \u0275\u0275text(15);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(16, "div", 11);
      \u0275\u0275text(17);
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(18, "app-card", 12)(19, "div", 9)(20, "div", 13);
      \u0275\u0275text(21);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(22, "div", 11);
      \u0275\u0275text(23);
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(24, "app-card", 14)(25, "div", 9)(26, "div", 15);
      \u0275\u0275text(27);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(28, "div", 11);
      \u0275\u0275text(29, "Leading projects");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(30, "app-card", 16)(31, "div", 9)(32, "div", 10);
      \u0275\u0275text(33);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(34, "div", 11);
      \u0275\u0275text(35, "Active this week");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(36, "div", 17)(37, "app-card", 18);
      \u0275\u0275element(38, "app-chart", 19);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(39, "app-card", 20);
      \u0275\u0275element(40, "app-chart", 21);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(41, "app-card", 22)(42, "div", 23)(43, "div", 24)(44, "input", 25);
      \u0275\u0275listener("input", function StakeholdersComponent_Template_input_input_44_listener($event) {
        return ctx.onSearchInput($event);
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(45, "select", 26);
      \u0275\u0275listener("change", function StakeholdersComponent_Template_select_change_45_listener($event) {
        return ctx.onFilterChanged("department", $event);
      });
      \u0275\u0275elementStart(46, "option", 27);
      \u0275\u0275text(47, "All Departments");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(48, "option", 28);
      \u0275\u0275text(49, "Engineering");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(50, "option", 29);
      \u0275\u0275text(51, "Product");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(52, "option", 30);
      \u0275\u0275text(53, "Design");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(54, "option", 31);
      \u0275\u0275text(55, "QA");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(56, "option", 32);
      \u0275\u0275text(57, "DevOps");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(58, "option", 33);
      \u0275\u0275text(59, "Management");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(60, "select", 26);
      \u0275\u0275listener("change", function StakeholdersComponent_Template_select_change_60_listener($event) {
        return ctx.onFilterChanged("role", $event);
      });
      \u0275\u0275elementStart(61, "option", 27);
      \u0275\u0275text(62, "All Roles");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(63, "option", 34);
      \u0275\u0275text(64, "Owner");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(65, "option", 35);
      \u0275\u0275text(66, "Developer");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(67, "option", 36);
      \u0275\u0275text(68, "Architect");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(69, "option", 37);
      \u0275\u0275text(70, "Manager");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(71, "option", 38);
      \u0275\u0275text(72, "Stakeholder");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(73, "app-table", 39);
      \u0275\u0275listener("sortChanged", function StakeholdersComponent_Template_app_table_sortChanged_73_listener($event) {
        return ctx.onSortChanged($event);
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(74, "app-stakeholder-modal", 40);
      \u0275\u0275listener("closed", function StakeholdersComponent_Template_app_stakeholder_modal_closed_74_listener() {
        return ctx.closeModal();
      })("saved", function StakeholdersComponent_Template_app_stakeholder_modal_saved_74_listener($event) {
        return ctx.onStakeholderSaved($event);
      });
      \u0275\u0275elementEnd()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(15);
      \u0275\u0275textInterpolate(ctx.stats.total);
      \u0275\u0275advance(2);
      \u0275\u0275textInterpolate1("Across ", ctx.stats.departments, " departments");
      \u0275\u0275advance(4);
      \u0275\u0275textInterpolate(ctx.stats.active);
      \u0275\u0275advance(2);
      \u0275\u0275textInterpolate1("", ctx.stats.activePercentage, "% of total");
      \u0275\u0275advance(4);
      \u0275\u0275textInterpolate(ctx.stats.owners);
      \u0275\u0275advance(6);
      \u0275\u0275textInterpolate(ctx.stats.recentActivity);
      \u0275\u0275advance(5);
      \u0275\u0275property("data", ctx.departmentChartData)("options", ctx.chartOptions);
      \u0275\u0275advance(2);
      \u0275\u0275property("data", ctx.roleChartData)("options", ctx.chartOptions);
      \u0275\u0275advance(33);
      \u0275\u0275property("columns", ctx.tableColumns)("data", ctx.filteredStakeholders)("actions", ctx.tableActions)("loading", ctx.loading)("sortable", true);
      \u0275\u0275advance();
      \u0275\u0275property("isOpen", ctx.showModal)("editMode", ctx.editMode)("initialData", ctx.editData)("loading", ctx.modalLoading);
    }
  }, dependencies: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    NgSelectOption,
    \u0275NgSelectMultipleOption,
    CardComponent,
    ButtonComponent,
    TableComponent,
    ChartComponent,
    StakeholderModalComponent
  ], styles: [`

.stakeholders-page[_ngcontent-%COMP%] {
  min-height: 100%;
}
.page-content[_ngcontent-%COMP%] {
  padding: var(--spacing-xl);
}
.page-header[_ngcontent-%COMP%] {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-xl);
}
.header-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--secondary-900);
}
.page-subtitle[_ngcontent-%COMP%] {
  margin: 0;
  font-size: var(--text-lg);
  color: var(--secondary-600);
}
.stats-grid[_ngcontent-%COMP%] {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}
.stat-content[_ngcontent-%COMP%] {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
}
.stat-number[_ngcontent-%COMP%] {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--secondary-900);
}
.stat-number.active[_ngcontent-%COMP%] {
  color: var(--success-600);
}
.stat-number.owner[_ngcontent-%COMP%] {
  color: var(--primary-600);
}
.stat-change[_ngcontent-%COMP%] {
  font-size: var(--text-sm);
  color: var(--secondary-600);
}
.charts-section[_ngcontent-%COMP%] {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}
.table-controls[_ngcontent-%COMP%] {
  margin-bottom: var(--spacing-lg);
}
.search-controls[_ngcontent-%COMP%] {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}
.search-input[_ngcontent-%COMP%], 
.filter-select[_ngcontent-%COMP%] {
  height: 40px;
  padding: 0 var(--spacing-md);
  border: 1px solid var(--secondary-300);
  border-radius: var(--radius-md);
  background: white;
  font-size: var(--text-sm);
  color: var(--secondary-800);
  transition: all 0.2s ease;
}
.search-input[_ngcontent-%COMP%]:focus, 
.filter-select[_ngcontent-%COMP%]:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}
.search-input[_ngcontent-%COMP%] {
  flex: 1;
  min-width: 200px;
}
.filter-select[_ngcontent-%COMP%] {
  min-width: 150px;
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-sm) center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
  appearance: none;
}
@media (max-width: 768px) {
  .page-header[_ngcontent-%COMP%] {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }
  .stats-grid[_ngcontent-%COMP%] {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  .charts-section[_ngcontent-%COMP%] {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  .search-controls[_ngcontent-%COMP%] {
    flex-direction: column;
    align-items: stretch;
  }
  .search-input[_ngcontent-%COMP%], 
   .filter-select[_ngcontent-%COMP%] {
    min-width: auto;
  }
}
/*# sourceMappingURL=stakeholders.component.css.map */`] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(StakeholdersComponent, [{
    type: Component,
    args: [{ selector: "app-stakeholders", standalone: true, imports: [
      CommonModule,
      RouterModule,
      ReactiveFormsModule,
      CardComponent,
      ButtonComponent,
      TableComponent,
      ChartComponent,
      StakeholderModalComponent
    ], template: `
    <div class="stakeholders-page">
      <div class="page-content">
        <div class="page-header">
          <div class="header-content">
            <h1>Stakeholders</h1>
            <p class="page-subtitle">Manage team members and project stakeholders</p>
          </div>
          <div class="header-actions">
            <app-button
              variant="primary"
              leftIcon="M12 4v16m8-8H4"
              (clicked)="openAddModal()"
            >
              Add Stakeholder
            </app-button>
          </div>
        </div>

      <!-- Statistics Cards -->
      <div class="stats-grid">
        <app-card title="Total Stakeholders">
          <div class="stat-content">
            <div class="stat-number">{{ stats.total }}</div>
            <div class="stat-change">Across {{ stats.departments }} departments</div>
          </div>
        </app-card>

        <app-card title="Active Members">
          <div class="stat-content">
            <div class="stat-number active">{{ stats.active }}</div>
            <div class="stat-change">{{ stats.activePercentage }}% of total</div>
          </div>
        </app-card>

        <app-card title="Project Owners">
          <div class="stat-content">
            <div class="stat-number owner">{{ stats.owners }}</div>
            <div class="stat-change">Leading projects</div>
          </div>
        </app-card>

        <app-card title="Recent Activity">
          <div class="stat-content">
            <div class="stat-number">{{ stats.recentActivity }}</div>
            <div class="stat-change">Active this week</div>
          </div>
        </app-card>
      </div>

      <!-- Charts Section -->
      <div class="charts-section">
        <app-card title="Stakeholders by Department" subtitle="Distribution across departments">
          <app-chart
            type="doughnut"
            [data]="departmentChartData"
            [options]="chartOptions"
            height="300px"
          ></app-chart>
        </app-card>

        <app-card title="Roles Distribution" subtitle="Stakeholders by role">
          <app-chart
            type="bar"
            [data]="roleChartData"
            [options]="chartOptions"
            height="300px"
          ></app-chart>
        </app-card>
      </div>

      <!-- Stakeholders Table -->
      <app-card title="Team Members" subtitle="All project stakeholders">
        <div class="table-controls">
          <div class="search-controls">
            <input
              type="text"
              placeholder="Search stakeholders..."
              class="search-input"
              (input)="onSearchInput($event)"
            >
            <select class="filter-select" (change)="onFilterChanged('department', $event)">
              <option value="">All Departments</option>
              <option value="engineering">Engineering</option>
              <option value="product">Product</option>
              <option value="design">Design</option>
              <option value="qa">QA</option>
              <option value="devops">DevOps</option>
              <option value="management">Management</option>
            </select>
            <select class="filter-select" (change)="onFilterChanged('role', $event)">
              <option value="">All Roles</option>
              <option value="owner">Owner</option>
              <option value="developer">Developer</option>
              <option value="architect">Architect</option>
              <option value="manager">Manager</option>
              <option value="stakeholder">Stakeholder</option>
            </select>
          </div>
        </div>

        <app-table
          [columns]="tableColumns"
          [data]="filteredStakeholders"
          [actions]="tableActions"
          [loading]="loading"
          [sortable]="true"
          (sortChanged)="onSortChanged($event)"
        ></app-table>
      </app-card>

      <!-- Add/Edit Modal -->
      <app-stakeholder-modal
        [isOpen]="showModal"
        [editMode]="editMode"
        [initialData]="editData"
        [loading]="modalLoading"
        (closed)="closeModal()"
        (saved)="onStakeholderSaved($event)"
      ></app-stakeholder-modal>
      </div>
    </div>
  `, styles: [`/* angular:styles/component:scss;98a5e8c8d020a404156a3121df184cce3650ad4c04b72d894550148587c7f89a;C:/Users/<USER>/Documents/VibeCoding/AppCatalog/app-catalog/frontend/src/app/modules/stakeholders/stakeholders.component.ts */
.stakeholders-page {
  min-height: 100%;
}
.page-content {
  padding: var(--spacing-xl);
}
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-xl);
}
.header-content h1 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--secondary-900);
}
.page-subtitle {
  margin: 0;
  font-size: var(--text-lg);
  color: var(--secondary-600);
}
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}
.stat-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
}
.stat-number {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--secondary-900);
}
.stat-number.active {
  color: var(--success-600);
}
.stat-number.owner {
  color: var(--primary-600);
}
.stat-change {
  font-size: var(--text-sm);
  color: var(--secondary-600);
}
.charts-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}
.table-controls {
  margin-bottom: var(--spacing-lg);
}
.search-controls {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}
.search-input,
.filter-select {
  height: 40px;
  padding: 0 var(--spacing-md);
  border: 1px solid var(--secondary-300);
  border-radius: var(--radius-md);
  background: white;
  font-size: var(--text-sm);
  color: var(--secondary-800);
  transition: all 0.2s ease;
}
.search-input:focus,
.filter-select:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}
.search-input {
  flex: 1;
  min-width: 200px;
}
.filter-select {
  min-width: 150px;
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-sm) center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
  appearance: none;
}
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }
  .stats-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  .charts-section {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  .search-controls {
    flex-direction: column;
    align-items: stretch;
  }
  .search-input,
  .filter-select {
    min-width: auto;
  }
}
/*# sourceMappingURL=stakeholders.component.css.map */
`] }]
  }], () => [{ type: FormBuilder }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(StakeholdersComponent, { className: "StakeholdersComponent", filePath: "src/app/modules/stakeholders/stakeholders.component.ts", lineNumber: 302 });
})();
export {
  StakeholdersComponent
};
//# sourceMappingURL=chunk-UPGBADGC.js.map
