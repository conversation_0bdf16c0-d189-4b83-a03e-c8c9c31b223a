{"version": 3, "sources": ["src/app/shared/models/application.model.ts", "src/app/modules/applications/applications.service.ts"], "sourcesContent": ["// Enums (defined first to be used in interfaces)\nexport enum ApplicationStatus {\n  DEVELOPMENT = 'Development',\n  TESTING = 'Testing',\n  STAGING = 'Staging',\n  PRODUCTION = 'Production',\n  MAINTENANCE = 'Maintenance',\n  DEPRECATED = 'Deprecated'\n}\n\nexport enum ApplicationCriticality {\n  LOW = 'Low',\n  MEDIUM = 'Medium',\n  HIGH = 'High',\n  CRITICAL = 'Critical'\n}\n\nexport interface Application {\n  id?: number;\n  name: string;\n  description: string;\n  owner: string;\n  department: string;\n  status: ApplicationStatus;\n  criticality: ApplicationCriticality;\n  version: string;\n  lastUpdated: Date;\n  createdDate: Date;\n  url?: string;\n  repository?: string;\n  documentation?: string;\n\n  // Scores\n  healthScore: number;\n  securityScore: number;\n  performanceScore: number;\n\n  // Additional properties\n  tags: string[];\n\n  // Related entities\n  dependencies?: Dependency[];\n  techStack?: TechStackItem[];\n  stakeholders?: Stakeholder[];\n  documents?: Documentation[];\n  securityAssessments?: SecurityAssessment[];\n  vulnerabilities?: Vulnerability[];\n}\n\n// For backward compatibility, keep the old interface as well\nexport interface LegacyApplication {\n  id?: number;\n  name: string;\n  description: string;\n  owner: string;\n  lifecycleStatus: LifecycleStatus;\n  businessCriticality: BusinessCriticality;\n  version: string;\n  repository?: string;\n  deploymentUrl?: string;\n  createdAt?: Date;\n  updatedAt?: Date;\n  createdBy?: string;\n  updatedBy?: string;\n\n  // Related entities\n  dependencies?: Dependency[];\n  techStack?: TechStack[];\n  stakeholders?: Stakeholder[];\n  documentation?: Documentation[];\n  securityAssessments?: SecurityAssessment[];\n  vulnerabilities?: Vulnerability[];\n}\n\nexport interface TechStackItem {\n  name: string;\n  version: string;\n  category: string;\n}\n\nexport interface Dependency {\n  id?: number;\n  applicationId: number;\n  name: string;\n  type: DependencyType;\n  version: string;\n  isInternal: boolean;\n  description?: string;\n  criticality: DependencyCriticality;\n  status: DependencyStatus;\n  lastUpdated?: Date;\n  maintainer?: string;\n  repository?: string;\n  documentation?: string;\n}\n\nexport interface TechStack {\n  id?: number;\n  applicationId: number;\n  category: TechStackCategory;\n  technology: string;\n  version: string;\n  purpose: string;\n  isCore: boolean;\n  supportLevel: SupportLevel;\n  endOfLife?: Date;\n  licenseType?: string;\n  notes?: string;\n}\n\nexport interface Stakeholder {\n  id?: number;\n  applicationId: number;\n  name: string;\n  email: string;\n  role: StakeholderRole;\n  department: string;\n  responsibility: string;\n  isPrimary: boolean;\n  contactPreference: ContactPreference;\n  phone?: string;\n  slackHandle?: string;\n}\n\nexport interface Documentation {\n  id?: number;\n  applicationId: number;\n  title: string;\n  type: DocumentationType;\n  description?: string;\n  url?: string;\n  filePath?: string;\n  fileName?: string;\n  fileSize?: number;\n  mimeType?: string;\n  version: string;\n  isPublic: boolean;\n  uploadedAt?: Date;\n  uploadedBy?: string;\n  lastModified?: Date;\n}\n\nexport interface SecurityAssessment {\n  id?: number;\n  applicationId: number;\n  assessmentType: AssessmentType;\n  score: number;\n  maxScore: number;\n  status: AssessmentStatus;\n  assessedBy: string;\n  assessedAt: Date;\n  nextAssessmentDue?: Date;\n  findings?: string;\n  recommendations?: string;\n  complianceFramework?: string;\n}\n\nexport interface Vulnerability {\n  id?: number;\n  applicationId: number;\n  cveId?: string;\n  title: string;\n  description: string;\n  severity: VulnerabilitySeverity;\n  cvssScore?: number;\n  status: VulnerabilityStatus;\n  discoveredAt: Date;\n  discoveredBy: string;\n  resolvedAt?: Date;\n  resolvedBy?: string;\n  mitigation?: string;\n  affectedComponent?: string;\n  patchAvailable: boolean;\n  patchVersion?: string;\n}\n\n// Additional Enums\nexport enum LifecycleStatus {\n  PLANNING = 'planning',\n  DEVELOPMENT = 'development',\n  TESTING = 'testing',\n  STAGING = 'staging',\n  PRODUCTION = 'production',\n  MAINTENANCE = 'maintenance',\n  DEPRECATED = 'deprecated',\n  RETIRED = 'retired'\n}\n\nexport enum BusinessCriticality {\n  LOW = 'low',\n  MEDIUM = 'medium',\n  HIGH = 'high',\n  CRITICAL = 'critical'\n}\n\nexport enum DependencyType {\n  LIBRARY = 'library',\n  FRAMEWORK = 'framework',\n  SERVICE = 'service',\n  DATABASE = 'database',\n  API = 'api',\n  TOOL = 'tool',\n  INFRASTRUCTURE = 'infrastructure'\n}\n\nexport enum DependencyCriticality {\n  LOW = 'low',\n  MEDIUM = 'medium',\n  HIGH = 'high',\n  CRITICAL = 'critical'\n}\n\nexport enum DependencyStatus {\n  ACTIVE = 'active',\n  DEPRECATED = 'deprecated',\n  END_OF_LIFE = 'end_of_life',\n  SECURITY_RISK = 'security_risk',\n  UPDATE_REQUIRED = 'update_required'\n}\n\nexport enum TechStackCategory {\n  FRONTEND = 'frontend',\n  BACKEND = 'backend',\n  DATABASE = 'database',\n  INFRASTRUCTURE = 'infrastructure',\n  MONITORING = 'monitoring',\n  SECURITY = 'security',\n  TESTING = 'testing',\n  DEPLOYMENT = 'deployment',\n  COMMUNICATION = 'communication'\n}\n\nexport enum SupportLevel {\n  FULL = 'full',\n  LIMITED = 'limited',\n  COMMUNITY = 'community',\n  DEPRECATED = 'deprecated',\n  NONE = 'none'\n}\n\nexport enum StakeholderRole {\n  PRODUCT_OWNER = 'product_owner',\n  TECH_LEAD = 'tech_lead',\n  DEVELOPER = 'developer',\n  DEVOPS_ENGINEER = 'devops_engineer',\n  SECURITY_OFFICER = 'security_officer',\n  BUSINESS_ANALYST = 'business_analyst',\n  PROJECT_MANAGER = 'project_manager',\n  ARCHITECT = 'architect',\n  QA_ENGINEER = 'qa_engineer',\n  SUPPORT_ENGINEER = 'support_engineer'\n}\n\nexport enum ContactPreference {\n  EMAIL = 'email',\n  SLACK = 'slack',\n  PHONE = 'phone',\n  TEAMS = 'teams'\n}\n\nexport enum DocumentationType {\n  TECHNICAL_SPEC = 'technical_spec',\n  API_DOCUMENTATION = 'api_documentation',\n  USER_MANUAL = 'user_manual',\n  DEPLOYMENT_GUIDE = 'deployment_guide',\n  ARCHITECTURE_DIAGRAM = 'architecture_diagram',\n  SECURITY_POLICY = 'security_policy',\n  COMPLIANCE_REPORT = 'compliance_report',\n  RUNBOOK = 'runbook',\n  TROUBLESHOOTING = 'troubleshooting',\n  CHANGELOG = 'changelog'\n}\n\nexport enum AssessmentType {\n  SECURITY_AUDIT = 'security_audit',\n  PENETRATION_TEST = 'penetration_test',\n  CODE_REVIEW = 'code_review',\n  COMPLIANCE_CHECK = 'compliance_check',\n  VULNERABILITY_SCAN = 'vulnerability_scan',\n  RISK_ASSESSMENT = 'risk_assessment'\n}\n\nexport enum AssessmentStatus {\n  SCHEDULED = 'scheduled',\n  IN_PROGRESS = 'in_progress',\n  COMPLETED = 'completed',\n  OVERDUE = 'overdue',\n  CANCELLED = 'cancelled'\n}\n\nexport enum VulnerabilitySeverity {\n  CRITICAL = 'critical',\n  HIGH = 'high',\n  MEDIUM = 'medium',\n  LOW = 'low',\n  INFO = 'info'\n}\n\nexport enum VulnerabilityStatus {\n  OPEN = 'open',\n  IN_PROGRESS = 'in_progress',\n  RESOLVED = 'resolved',\n  ACCEPTED_RISK = 'accepted_risk',\n  FALSE_POSITIVE = 'false_positive'\n}\n", "import { Injectable } from '@angular/core';\nimport { Observable, of, delay, map } from 'rxjs';\nimport {\n  Application,\n  ApplicationStatus,\n  ApplicationCriticality,\n  DependencyType,\n  DependencyCriticality,\n  DependencyStatus,\n  StakeholderRole,\n  ContactPreference,\n  DocumentationType,\n  VulnerabilitySeverity,\n  VulnerabilityStatus\n} from '../../shared/models/application.model';\n\nexport interface ApplicationsFilter {\n  search?: string;\n  status?: ApplicationStatus[];\n  criticality?: ApplicationCriticality[];\n  owner?: string[];\n  department?: string[];\n}\n\nexport interface ApplicationsResponse {\n  applications: Application[];\n  total: number;\n  page: number;\n  pageSize: number;\n  totalPages: number;\n}\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ApplicationsService {\n\n  constructor() { }\n\n  /**\n   * Get applications with filtering and pagination\n   */\n  getApplications(\n    page: number = 1,\n    pageSize: number = 12,\n    filter: ApplicationsFilter = {}\n  ): Observable<ApplicationsResponse> {\n    return this.getMockApplications().pipe(\n      delay(500),\n      map(applications => {\n        // Apply filters\n        let filteredApps = this.applyFilters(applications, filter);\n\n        // Calculate pagination\n        const total = filteredApps.length;\n        const totalPages = Math.ceil(total / pageSize);\n        const startIndex = (page - 1) * pageSize;\n        const endIndex = startIndex + pageSize;\n\n        // Apply pagination\n        const paginatedApps = filteredApps.slice(startIndex, endIndex);\n\n        return {\n          applications: paginatedApps,\n          total,\n          page,\n          pageSize,\n          totalPages\n        };\n      })\n    );\n  }\n\n  /**\n   * Get single application by ID\n   */\n  getApplication(id: number): Observable<Application | null> {\n    return this.getMockApplications().pipe(\n      delay(300),\n      map(applications => applications.find(app => app.id === id) || null)\n    );\n  }\n\n  /**\n   * Create new application\n   */\n  createApplication(application: Partial<Application>): Observable<Application> {\n    const newApp: Application = {\n      id: Date.now(), // Mock ID generation\n      name: application.name || '',\n      description: application.description || '',\n      owner: application.owner || '',\n      department: application.department || '',\n      status: application.status || ApplicationStatus.DEVELOPMENT,\n      criticality: application.criticality || ApplicationCriticality.MEDIUM,\n      version: application.version || '1.0.0',\n      lastUpdated: new Date(),\n      createdDate: new Date(),\n      url: application.url || '',\n      repository: application.repository || '',\n      documentation: application.documentation || '',\n      healthScore: 85,\n      securityScore: 80,\n      performanceScore: 90,\n      tags: application.tags || [],\n      techStack: application.techStack || [],\n      dependencies: application.dependencies || [],\n      vulnerabilities: application.vulnerabilities || [],\n      documents: application.documents || []\n    };\n\n    // In a real app, this would make an API call\n    return of(newApp).pipe(delay(1000));\n  }\n\n  /**\n   * Update existing application\n   */\n  updateApplication(id: number, updates: Partial<Application>): Observable<Application> {\n    return this.getApplication(id).pipe(\n      delay(800),\n      map(app => {\n        if (!app) {\n          throw new Error('Application not found');\n        }\n        return {\n          ...app,\n          ...updates,\n          lastUpdated: new Date()\n        };\n      })\n    );\n  }\n\n  /**\n   * Delete application\n   */\n  deleteApplication(id: number): Observable<boolean> {\n    return of(true).pipe(delay(500));\n  }\n\n  /**\n   * Get application statistics\n   */\n  getApplicationStats(): Observable<any> {\n    return this.getMockApplications().pipe(\n      delay(300),\n      map(applications => {\n        const stats = {\n          total: applications.length,\n          byStatus: this.groupBy(applications, 'status'),\n          byCriticality: this.groupBy(applications, 'criticality'),\n          byDepartment: this.groupBy(applications, 'department'),\n          averageHealthScore: this.calculateAverage(applications, 'healthScore'),\n          averageSecurityScore: this.calculateAverage(applications, 'securityScore'),\n          averagePerformanceScore: this.calculateAverage(applications, 'performanceScore')\n        };\n        return stats;\n      })\n    );\n  }\n\n  /**\n   * Get filter options for dropdowns\n   */\n  getFilterOptions(): Observable<any> {\n    return this.getMockApplications().pipe(\n      delay(200),\n      map(applications => ({\n        owners: [...new Set(applications.map(app => app.owner))].sort(),\n        departments: [...new Set(applications.map(app => app.department))].sort(),\n        statuses: Object.values(ApplicationStatus),\n        criticalities: Object.values(ApplicationCriticality)\n      }))\n    );\n  }\n\n  /**\n   * Search applications by name or description\n   */\n  searchApplications(query: string): Observable<Application[]> {\n    return this.getMockApplications().pipe(\n      delay(300),\n      map(applications =>\n        applications.filter(app =>\n          app.name.toLowerCase().includes(query.toLowerCase()) ||\n          app.description.toLowerCase().includes(query.toLowerCase())\n        ).slice(0, 10) // Limit to 10 results for autocomplete\n      )\n    );\n  }\n\n  private applyFilters(applications: Application[], filter: ApplicationsFilter): Application[] {\n    let filtered = [...applications];\n\n    if (filter.search) {\n      const searchTerm = filter.search.toLowerCase();\n      filtered = filtered.filter(app =>\n        app.name.toLowerCase().includes(searchTerm) ||\n        app.description.toLowerCase().includes(searchTerm) ||\n        app.owner.toLowerCase().includes(searchTerm)\n      );\n    }\n\n    if (filter.status && filter.status.length > 0) {\n      filtered = filtered.filter(app => filter.status!.includes(app.status));\n    }\n\n    if (filter.criticality && filter.criticality.length > 0) {\n      filtered = filtered.filter(app => filter.criticality!.includes(app.criticality));\n    }\n\n    if (filter.owner && filter.owner.length > 0) {\n      filtered = filtered.filter(app => filter.owner!.includes(app.owner));\n    }\n\n    if (filter.department && filter.department.length > 0) {\n      filtered = filtered.filter(app => filter.department!.includes(app.department));\n    }\n\n    return filtered;\n  }\n\n  private groupBy(array: any[], key: string): any {\n    return array.reduce((groups, item) => {\n      const value = item[key];\n      groups[value] = (groups[value] || 0) + 1;\n      return groups;\n    }, {});\n  }\n\n  private calculateAverage(array: any[], key: string): number {\n    const sum = array.reduce((total, item) => total + (item[key] || 0), 0);\n    return Math.round((sum / array.length) * 10) / 10;\n  }\n\n  private getMockApplications(): Observable<Application[]> {\n    const mockApps: Application[] = [\n      {\n        id: 1,\n        name: 'E-commerce Platform',\n        description: 'Main customer-facing e-commerce application with shopping cart, payment processing, and order management.',\n        owner: 'John Doe',\n        department: 'Engineering',\n        status: ApplicationStatus.PRODUCTION,\n        criticality: ApplicationCriticality.CRITICAL,\n        version: '2.1.0',\n        lastUpdated: new Date('2024-01-15'),\n        createdDate: new Date('2022-03-10'),\n        url: 'https://shop.company.com',\n        repository: 'https://github.com/company/ecommerce',\n        documentation: 'https://docs.company.com/ecommerce',\n        healthScore: 92,\n        securityScore: 88,\n        performanceScore: 95,\n        tags: ['customer-facing', 'revenue-critical', 'high-traffic'],\n        techStack: [\n          { name: 'Angular', version: '16.2.0', category: 'Frontend' },\n          { name: 'Node.js', version: '18.17.0', category: 'Backend' },\n          { name: 'PostgreSQL', version: '15.0', category: 'Database' },\n          { name: 'Redis', version: '7.0', category: 'Cache' },\n          { name: 'Docker', version: '24.0', category: 'Infrastructure' }\n        ],\n        dependencies: [\n          {\n            id: 1,\n            applicationId: 1,\n            name: 'Angular',\n            type: DependencyType.FRAMEWORK,\n            version: '16.2.0',\n            isInternal: false,\n            description: 'Frontend framework for building the user interface',\n            criticality: DependencyCriticality.HIGH,\n            status: DependencyStatus.ACTIVE,\n            lastUpdated: new Date('2024-01-10'),\n            maintainer: 'Google',\n            repository: 'https://github.com/angular/angular'\n          },\n          {\n            id: 2,\n            applicationId: 1,\n            name: 'Express.js',\n            type: DependencyType.FRAMEWORK,\n            version: '4.18.2',\n            isInternal: false,\n            description: 'Backend web framework for Node.js',\n            criticality: DependencyCriticality.CRITICAL,\n            status: DependencyStatus.ACTIVE,\n            lastUpdated: new Date('2024-01-05'),\n            maintainer: 'Express Team',\n            repository: 'https://github.com/expressjs/express'\n          },\n          {\n            id: 3,\n            applicationId: 1,\n            name: 'Payment Service',\n            type: DependencyType.SERVICE,\n            version: '1.5.3',\n            isInternal: true,\n            description: 'Internal payment processing service',\n            criticality: DependencyCriticality.CRITICAL,\n            status: DependencyStatus.ACTIVE,\n            lastUpdated: new Date('2024-01-14'),\n            maintainer: 'Finance Team'\n          }\n        ],\n        stakeholders: [\n          {\n            id: 1,\n            applicationId: 1,\n            name: 'John Doe',\n            email: '<EMAIL>',\n            role: StakeholderRole.PRODUCT_OWNER,\n            department: 'Engineering',\n            responsibility: 'Overall product strategy and technical direction',\n            isPrimary: true,\n            contactPreference: ContactPreference.EMAIL,\n            phone: '******-0123'\n          },\n          {\n            id: 2,\n            applicationId: 1,\n            name: 'Sarah Wilson',\n            email: '<EMAIL>',\n            role: StakeholderRole.DEVELOPER,\n            department: 'Engineering',\n            responsibility: 'Frontend development and user experience',\n            isPrimary: false,\n            contactPreference: ContactPreference.SLACK,\n            slackHandle: '@sarah.wilson'\n          },\n          {\n            id: 3,\n            applicationId: 1,\n            name: 'Mike Chen',\n            email: '<EMAIL>',\n            role: StakeholderRole.BUSINESS_ANALYST,\n            department: 'Product',\n            responsibility: 'Business requirements and user acceptance testing',\n            isPrimary: false,\n            contactPreference: ContactPreference.EMAIL\n          }\n        ],\n        documents: [\n          {\n            id: 1,\n            applicationId: 1,\n            title: 'API Documentation',\n            type: DocumentationType.API_DOCUMENTATION,\n            description: 'Complete API documentation for all endpoints',\n            url: 'https://docs.company.com/ecommerce/api',\n            version: '2.1.0',\n            isPublic: false,\n            uploadedAt: new Date('2024-01-10'),\n            uploadedBy: 'John Doe'\n          },\n          {\n            id: 2,\n            applicationId: 1,\n            title: 'User Manual',\n            type: DocumentationType.USER_MANUAL,\n            description: 'End-user guide for the e-commerce platform',\n            fileName: 'user-manual-v2.1.pdf',\n            fileSize: 2048576,\n            mimeType: 'application/pdf',\n            version: '2.1.0',\n            isPublic: true,\n            uploadedAt: new Date('2024-01-12'),\n            uploadedBy: 'Sarah Wilson'\n          },\n          {\n            id: 3,\n            applicationId: 1,\n            title: 'Architecture Overview',\n            type: DocumentationType.TECHNICAL_SPEC,\n            description: 'High-level system architecture and design decisions',\n            url: 'https://docs.company.com/ecommerce/architecture',\n            version: '2.0.0',\n            isPublic: false,\n            uploadedAt: new Date('2023-12-15'),\n            uploadedBy: 'John Doe'\n          }\n        ],\n        vulnerabilities: [\n          {\n            id: 1,\n            applicationId: 1,\n            cveId: 'CVE-2024-0001',\n            title: 'SQL Injection in User Search',\n            description: 'Potential SQL injection vulnerability in the user search functionality that could allow unauthorized data access.',\n            severity: VulnerabilitySeverity.HIGH,\n            cvssScore: 7.5,\n            status: VulnerabilityStatus.IN_PROGRESS,\n            discoveredAt: new Date('2024-01-08'),\n            discoveredBy: 'Security Team',\n            affectedComponent: 'User Search API',\n            patchAvailable: true,\n            patchVersion: '2.1.1'\n          },\n          {\n            id: 2,\n            applicationId: 1,\n            title: 'Outdated Dependencies',\n            description: 'Several npm packages are using outdated versions with known security vulnerabilities.',\n            severity: VulnerabilitySeverity.MEDIUM,\n            cvssScore: 5.3,\n            status: VulnerabilityStatus.OPEN,\n            discoveredAt: new Date('2024-01-12'),\n            discoveredBy: 'Automated Security Scan',\n            affectedComponent: 'Frontend Dependencies',\n            patchAvailable: true\n          },\n          {\n            id: 3,\n            applicationId: 1,\n            title: 'Cross-Site Scripting (XSS)',\n            description: 'Potential XSS vulnerability in product review comments.',\n            severity: VulnerabilitySeverity.CRITICAL,\n            cvssScore: 9.1,\n            status: VulnerabilityStatus.RESOLVED,\n            discoveredAt: new Date('2023-12-20'),\n            discoveredBy: 'Penetration Testing',\n            resolvedAt: new Date('2024-01-05'),\n            resolvedBy: 'Sarah Wilson',\n            affectedComponent: 'Product Reviews',\n            patchAvailable: false\n          }\n        ],\n        securityAssessments: []\n      },\n      {\n        id: 2,\n        name: 'User Management System',\n        description: 'Centralized user authentication and authorization service for all company applications.',\n        owner: 'Jane Smith',\n        department: 'Security',\n        status: ApplicationStatus.PRODUCTION,\n        criticality: ApplicationCriticality.HIGH,\n        version: '1.8.2',\n        lastUpdated: new Date('2024-01-12'),\n        createdDate: new Date('2021-11-05'),\n        url: 'https://auth.company.com',\n        repository: 'https://github.com/company/auth-service',\n        documentation: 'https://docs.company.com/auth',\n        healthScore: 96,\n        securityScore: 98,\n        performanceScore: 89,\n        tags: ['security', 'authentication', 'microservice'],\n        techStack: [\n          { name: 'Node.js', version: '18.17.0', category: 'Backend' },\n          { name: 'PostgreSQL', version: '15.0', category: 'Database' },\n          { name: 'Redis', version: '7.0', category: 'Cache' }\n        ],\n        dependencies: [],\n        vulnerabilities: [],\n        documents: []\n      },\n      {\n        id: 3,\n        name: 'Analytics Dashboard',\n        description: 'Business intelligence dashboard providing insights into sales, user behavior, and system performance.',\n        owner: 'Mike Johnson',\n        department: 'Product',\n        status: ApplicationStatus.PRODUCTION,\n        criticality: ApplicationCriticality.MEDIUM,\n        version: '3.2.1',\n        lastUpdated: new Date('2024-01-10'),\n        createdDate: new Date('2022-08-15'),\n        url: 'https://analytics.company.com',\n        repository: 'https://github.com/company/analytics',\n        documentation: 'https://docs.company.com/analytics',\n        healthScore: 87,\n        securityScore: 85,\n        performanceScore: 92,\n        tags: ['analytics', 'business-intelligence', 'reporting'],\n        techStack: [\n          { name: 'React', version: '18.2.0', category: 'Frontend' },\n          { name: 'Python', version: '3.11', category: 'Backend' },\n          { name: 'MongoDB', version: '6.0', category: 'Database' }\n        ],\n        dependencies: [],\n        vulnerabilities: [],\n        documents: []\n      },\n      {\n        id: 4,\n        name: 'Payment Service',\n        description: 'Microservice handling payment processing, refunds, and financial transaction management.',\n        owner: 'Sarah Wilson',\n        department: 'Finance',\n        status: ApplicationStatus.PRODUCTION,\n        criticality: ApplicationCriticality.CRITICAL,\n        version: '1.5.3',\n        lastUpdated: new Date('2024-01-14'),\n        createdDate: new Date('2022-01-20'),\n        url: 'https://payments.company.com',\n        repository: 'https://github.com/company/payments',\n        documentation: 'https://docs.company.com/payments',\n        healthScore: 94,\n        securityScore: 96,\n        performanceScore: 88,\n        tags: ['payments', 'financial', 'pci-compliant'],\n        techStack: [\n          { name: 'Java', version: '17', category: 'Backend' },\n          { name: 'Spring Boot', version: '3.0', category: 'Framework' },\n          { name: 'PostgreSQL', version: '15.0', category: 'Database' }\n        ],\n        dependencies: [],\n        vulnerabilities: [],\n        documents: []\n      },\n      {\n        id: 5,\n        name: 'Notification Service',\n        description: 'Service responsible for sending emails, SMS, and push notifications to users.',\n        owner: 'David Brown',\n        department: 'Engineering',\n        status: ApplicationStatus.DEVELOPMENT,\n        criticality: ApplicationCriticality.MEDIUM,\n        version: '0.8.0',\n        lastUpdated: new Date('2024-01-16'),\n        createdDate: new Date('2023-09-01'),\n        url: 'https://notifications.company.com',\n        repository: 'https://github.com/company/notifications',\n        documentation: 'https://docs.company.com/notifications',\n        healthScore: 78,\n        securityScore: 82,\n        performanceScore: 85,\n        tags: ['notifications', 'messaging', 'microservice'],\n        techStack: [\n          { name: 'Node.js', version: '18.17.0', category: 'Backend' },\n          { name: 'RabbitMQ', version: '3.12', category: 'Message Queue' },\n          { name: 'Redis', version: '7.0', category: 'Cache' }\n        ],\n        dependencies: [],\n        vulnerabilities: [],\n        documents: []\n      },\n      {\n        id: 6,\n        name: 'Inventory Management',\n        description: 'System for tracking product inventory, stock levels, and warehouse management.',\n        owner: 'Lisa Davis',\n        department: 'Operations',\n        status: ApplicationStatus.TESTING,\n        criticality: ApplicationCriticality.HIGH,\n        version: '2.0.0-beta',\n        lastUpdated: new Date('2024-01-13'),\n        createdDate: new Date('2023-05-10'),\n        url: 'https://inventory.company.com',\n        repository: 'https://github.com/company/inventory',\n        documentation: 'https://docs.company.com/inventory',\n        healthScore: 83,\n        securityScore: 87,\n        performanceScore: 91,\n        tags: ['inventory', 'warehouse', 'supply-chain'],\n        techStack: [\n          { name: 'Vue.js', version: '3.3.0', category: 'Frontend' },\n          { name: '.NET Core', version: '7.0', category: 'Backend' },\n          { name: 'MySQL', version: '8.0', category: 'Database' }\n        ],\n        dependencies: [],\n        vulnerabilities: [],\n        documents: []\n      },\n      {\n        id: 7,\n        name: 'Customer Support Portal',\n        description: 'Self-service portal for customers to submit tickets, track issues, and access knowledge base.',\n        owner: 'Tom Anderson',\n        department: 'Customer Success',\n        status: ApplicationStatus.PRODUCTION,\n        criticality: ApplicationCriticality.MEDIUM,\n        version: '1.9.1',\n        lastUpdated: new Date('2024-01-11'),\n        createdDate: new Date('2022-06-15'),\n        url: 'https://support.company.com',\n        repository: 'https://github.com/company/support-portal',\n        documentation: 'https://docs.company.com/support',\n        healthScore: 89,\n        securityScore: 91,\n        performanceScore: 86,\n        tags: ['customer-support', 'self-service', 'knowledge-base'],\n        techStack: [\n          { name: 'Angular', version: '16.2.0', category: 'Frontend' },\n          { name: 'PHP', version: '8.2', category: 'Backend' },\n          { name: 'MySQL', version: '8.0', category: 'Database' }\n        ],\n        dependencies: [],\n        vulnerabilities: [],\n        documents: []\n      },\n      {\n        id: 8,\n        name: 'Mobile API Gateway',\n        description: 'API gateway providing unified access to backend services for mobile applications.',\n        owner: 'Emma Thompson',\n        department: 'Mobile',\n        status: ApplicationStatus.PRODUCTION,\n        criticality: ApplicationCriticality.HIGH,\n        version: '2.3.0',\n        lastUpdated: new Date('2024-01-09'),\n        createdDate: new Date('2022-11-20'),\n        url: 'https://api-mobile.company.com',\n        repository: 'https://github.com/company/mobile-gateway',\n        documentation: 'https://docs.company.com/mobile-api',\n        healthScore: 91,\n        securityScore: 93,\n        performanceScore: 94,\n        tags: ['api-gateway', 'mobile', 'microservices'],\n        techStack: [\n          { name: 'Kong', version: '3.4', category: 'Infrastructure' },\n          { name: 'Lua', version: '5.4', category: 'Backend' },\n          { name: 'PostgreSQL', version: '15.0', category: 'Database' }\n        ],\n        dependencies: [],\n        vulnerabilities: [],\n        documents: []\n      }\n    ];\n\n    return of(mockApps);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;AACA,IAAY;CAAZ,SAAYA,oBAAiB;AAC3B,EAAAA,mBAAA,aAAA,IAAA;AACA,EAAAA,mBAAA,SAAA,IAAA;AACA,EAAAA,mBAAA,SAAA,IAAA;AACA,EAAAA,mBAAA,YAAA,IAAA;AACA,EAAAA,mBAAA,aAAA,IAAA;AACA,EAAAA,mBAAA,YAAA,IAAA;AACF,GAPY,sBAAA,oBAAiB,CAAA,EAAA;AAS7B,IAAY;CAAZ,SAAYC,yBAAsB;AAChC,EAAAA,wBAAA,KAAA,IAAA;AACA,EAAAA,wBAAA,QAAA,IAAA;AACA,EAAAA,wBAAA,MAAA,IAAA;AACA,EAAAA,wBAAA,UAAA,IAAA;AACF,GALY,2BAAA,yBAAsB,CAAA,EAAA;AAuKlC,IAAY;CAAZ,SAAYC,kBAAe;AACzB,EAAAA,iBAAA,UAAA,IAAA;AACA,EAAAA,iBAAA,aAAA,IAAA;AACA,EAAAA,iBAAA,SAAA,IAAA;AACA,EAAAA,iBAAA,SAAA,IAAA;AACA,EAAAA,iBAAA,YAAA,IAAA;AACA,EAAAA,iBAAA,aAAA,IAAA;AACA,EAAAA,iBAAA,YAAA,IAAA;AACA,EAAAA,iBAAA,SAAA,IAAA;AACF,GATY,oBAAA,kBAAe,CAAA,EAAA;AAW3B,IAAY;CAAZ,SAAYC,sBAAmB;AAC7B,EAAAA,qBAAA,KAAA,IAAA;AACA,EAAAA,qBAAA,QAAA,IAAA;AACA,EAAAA,qBAAA,MAAA,IAAA;AACA,EAAAA,qBAAA,UAAA,IAAA;AACF,GALY,wBAAA,sBAAmB,CAAA,EAAA;AAO/B,IAAY;CAAZ,SAAYC,iBAAc;AACxB,EAAAA,gBAAA,SAAA,IAAA;AACA,EAAAA,gBAAA,WAAA,IAAA;AACA,EAAAA,gBAAA,SAAA,IAAA;AACA,EAAAA,gBAAA,UAAA,IAAA;AACA,EAAAA,gBAAA,KAAA,IAAA;AACA,EAAAA,gBAAA,MAAA,IAAA;AACA,EAAAA,gBAAA,gBAAA,IAAA;AACF,GARY,mBAAA,iBAAc,CAAA,EAAA;AAU1B,IAAY;CAAZ,SAAYC,wBAAqB;AAC/B,EAAAA,uBAAA,KAAA,IAAA;AACA,EAAAA,uBAAA,QAAA,IAAA;AACA,EAAAA,uBAAA,MAAA,IAAA;AACA,EAAAA,uBAAA,UAAA,IAAA;AACF,GALY,0BAAA,wBAAqB,CAAA,EAAA;AAOjC,IAAY;CAAZ,SAAYC,mBAAgB;AAC1B,EAAAA,kBAAA,QAAA,IAAA;AACA,EAAAA,kBAAA,YAAA,IAAA;AACA,EAAAA,kBAAA,aAAA,IAAA;AACA,EAAAA,kBAAA,eAAA,IAAA;AACA,EAAAA,kBAAA,iBAAA,IAAA;AACF,GANY,qBAAA,mBAAgB,CAAA,EAAA;AAQ5B,IAAY;CAAZ,SAAYC,oBAAiB;AAC3B,EAAAA,mBAAA,UAAA,IAAA;AACA,EAAAA,mBAAA,SAAA,IAAA;AACA,EAAAA,mBAAA,UAAA,IAAA;AACA,EAAAA,mBAAA,gBAAA,IAAA;AACA,EAAAA,mBAAA,YAAA,IAAA;AACA,EAAAA,mBAAA,UAAA,IAAA;AACA,EAAAA,mBAAA,SAAA,IAAA;AACA,EAAAA,mBAAA,YAAA,IAAA;AACA,EAAAA,mBAAA,eAAA,IAAA;AACF,GAVY,sBAAA,oBAAiB,CAAA,EAAA;AAY7B,IAAY;CAAZ,SAAYC,eAAY;AACtB,EAAAA,cAAA,MAAA,IAAA;AACA,EAAAA,cAAA,SAAA,IAAA;AACA,EAAAA,cAAA,WAAA,IAAA;AACA,EAAAA,cAAA,YAAA,IAAA;AACA,EAAAA,cAAA,MAAA,IAAA;AACF,GANY,iBAAA,eAAY,CAAA,EAAA;AAQxB,IAAY;CAAZ,SAAYC,kBAAe;AACzB,EAAAA,iBAAA,eAAA,IAAA;AACA,EAAAA,iBAAA,WAAA,IAAA;AACA,EAAAA,iBAAA,WAAA,IAAA;AACA,EAAAA,iBAAA,iBAAA,IAAA;AACA,EAAAA,iBAAA,kBAAA,IAAA;AACA,EAAAA,iBAAA,kBAAA,IAAA;AACA,EAAAA,iBAAA,iBAAA,IAAA;AACA,EAAAA,iBAAA,WAAA,IAAA;AACA,EAAAA,iBAAA,aAAA,IAAA;AACA,EAAAA,iBAAA,kBAAA,IAAA;AACF,GAXY,oBAAA,kBAAe,CAAA,EAAA;AAa3B,IAAY;CAAZ,SAAYC,oBAAiB;AAC3B,EAAAA,mBAAA,OAAA,IAAA;AACA,EAAAA,mBAAA,OAAA,IAAA;AACA,EAAAA,mBAAA,OAAA,IAAA;AACA,EAAAA,mBAAA,OAAA,IAAA;AACF,GALY,sBAAA,oBAAiB,CAAA,EAAA;AAO7B,IAAY;CAAZ,SAAYC,oBAAiB;AAC3B,EAAAA,mBAAA,gBAAA,IAAA;AACA,EAAAA,mBAAA,mBAAA,IAAA;AACA,EAAAA,mBAAA,aAAA,IAAA;AACA,EAAAA,mBAAA,kBAAA,IAAA;AACA,EAAAA,mBAAA,sBAAA,IAAA;AACA,EAAAA,mBAAA,iBAAA,IAAA;AACA,EAAAA,mBAAA,mBAAA,IAAA;AACA,EAAAA,mBAAA,SAAA,IAAA;AACA,EAAAA,mBAAA,iBAAA,IAAA;AACA,EAAAA,mBAAA,WAAA,IAAA;AACF,GAXY,sBAAA,oBAAiB,CAAA,EAAA;AAa7B,IAAY;CAAZ,SAAYC,iBAAc;AACxB,EAAAA,gBAAA,gBAAA,IAAA;AACA,EAAAA,gBAAA,kBAAA,IAAA;AACA,EAAAA,gBAAA,aAAA,IAAA;AACA,EAAAA,gBAAA,kBAAA,IAAA;AACA,EAAAA,gBAAA,oBAAA,IAAA;AACA,EAAAA,gBAAA,iBAAA,IAAA;AACF,GAPY,mBAAA,iBAAc,CAAA,EAAA;AAS1B,IAAY;CAAZ,SAAYC,mBAAgB;AAC1B,EAAAA,kBAAA,WAAA,IAAA;AACA,EAAAA,kBAAA,aAAA,IAAA;AACA,EAAAA,kBAAA,WAAA,IAAA;AACA,EAAAA,kBAAA,SAAA,IAAA;AACA,EAAAA,kBAAA,WAAA,IAAA;AACF,GANY,qBAAA,mBAAgB,CAAA,EAAA;AAQ5B,IAAY;CAAZ,SAAYC,wBAAqB;AAC/B,EAAAA,uBAAA,UAAA,IAAA;AACA,EAAAA,uBAAA,MAAA,IAAA;AACA,EAAAA,uBAAA,QAAA,IAAA;AACA,EAAAA,uBAAA,KAAA,IAAA;AACA,EAAAA,uBAAA,MAAA,IAAA;AACF,GANY,0BAAA,wBAAqB,CAAA,EAAA;AAQjC,IAAY;CAAZ,SAAYC,sBAAmB;AAC7B,EAAAA,qBAAA,MAAA,IAAA;AACA,EAAAA,qBAAA,aAAA,IAAA;AACA,EAAAA,qBAAA,UAAA,IAAA;AACA,EAAAA,qBAAA,eAAA,IAAA;AACA,EAAAA,qBAAA,gBAAA,IAAA;AACF,GANY,wBAAA,sBAAmB,CAAA,EAAA;;;ACvQzB,IAAO,sBAAP,MAAO,qBAAmB;EAE9B,cAAA;EAAgB;;;;EAKhB,gBACE,OAAe,GACf,WAAmB,IACnB,SAA6B,CAAA,GAAE;AAE/B,WAAO,KAAK,oBAAmB,EAAG,KAChC,MAAM,GAAG,GACT,IAAI,kBAAe;AAEjB,UAAI,eAAe,KAAK,aAAa,cAAc,MAAM;AAGzD,YAAM,QAAQ,aAAa;AAC3B,YAAM,aAAa,KAAK,KAAK,QAAQ,QAAQ;AAC7C,YAAM,cAAc,OAAO,KAAK;AAChC,YAAM,WAAW,aAAa;AAG9B,YAAM,gBAAgB,aAAa,MAAM,YAAY,QAAQ;AAE7D,aAAO;QACL,cAAc;QACd;QACA;QACA;QACA;;IAEJ,CAAC,CAAC;EAEN;;;;EAKA,eAAe,IAAU;AACvB,WAAO,KAAK,oBAAmB,EAAG,KAChC,MAAM,GAAG,GACT,IAAI,kBAAgB,aAAa,KAAK,SAAO,IAAI,OAAO,EAAE,KAAK,IAAI,CAAC;EAExE;;;;EAKA,kBAAkB,aAAiC;AACjD,UAAM,SAAsB;MAC1B,IAAI,KAAK,IAAG;;MACZ,MAAM,YAAY,QAAQ;MAC1B,aAAa,YAAY,eAAe;MACxC,OAAO,YAAY,SAAS;MAC5B,YAAY,YAAY,cAAc;MACtC,QAAQ,YAAY,UAAU,kBAAkB;MAChD,aAAa,YAAY,eAAe,uBAAuB;MAC/D,SAAS,YAAY,WAAW;MAChC,aAAa,oBAAI,KAAI;MACrB,aAAa,oBAAI,KAAI;MACrB,KAAK,YAAY,OAAO;MACxB,YAAY,YAAY,cAAc;MACtC,eAAe,YAAY,iBAAiB;MAC5C,aAAa;MACb,eAAe;MACf,kBAAkB;MAClB,MAAM,YAAY,QAAQ,CAAA;MAC1B,WAAW,YAAY,aAAa,CAAA;MACpC,cAAc,YAAY,gBAAgB,CAAA;MAC1C,iBAAiB,YAAY,mBAAmB,CAAA;MAChD,WAAW,YAAY,aAAa,CAAA;;AAItC,WAAO,GAAG,MAAM,EAAE,KAAK,MAAM,GAAI,CAAC;EACpC;;;;EAKA,kBAAkB,IAAY,SAA6B;AACzD,WAAO,KAAK,eAAe,EAAE,EAAE,KAC7B,MAAM,GAAG,GACT,IAAI,SAAM;AACR,UAAI,CAAC,KAAK;AACR,cAAM,IAAI,MAAM,uBAAuB;MACzC;AACA,aAAO,gDACF,MACA,UAFE;QAGL,aAAa,oBAAI,KAAI;;IAEzB,CAAC,CAAC;EAEN;;;;EAKA,kBAAkB,IAAU;AAC1B,WAAO,GAAG,IAAI,EAAE,KAAK,MAAM,GAAG,CAAC;EACjC;;;;EAKA,sBAAmB;AACjB,WAAO,KAAK,oBAAmB,EAAG,KAChC,MAAM,GAAG,GACT,IAAI,kBAAe;AACjB,YAAM,QAAQ;QACZ,OAAO,aAAa;QACpB,UAAU,KAAK,QAAQ,cAAc,QAAQ;QAC7C,eAAe,KAAK,QAAQ,cAAc,aAAa;QACvD,cAAc,KAAK,QAAQ,cAAc,YAAY;QACrD,oBAAoB,KAAK,iBAAiB,cAAc,aAAa;QACrE,sBAAsB,KAAK,iBAAiB,cAAc,eAAe;QACzE,yBAAyB,KAAK,iBAAiB,cAAc,kBAAkB;;AAEjF,aAAO;IACT,CAAC,CAAC;EAEN;;;;EAKA,mBAAgB;AACd,WAAO,KAAK,oBAAmB,EAAG,KAChC,MAAM,GAAG,GACT,IAAI,mBAAiB;MACnB,QAAQ,CAAC,GAAG,IAAI,IAAI,aAAa,IAAI,SAAO,IAAI,KAAK,CAAC,CAAC,EAAE,KAAI;MAC7D,aAAa,CAAC,GAAG,IAAI,IAAI,aAAa,IAAI,SAAO,IAAI,UAAU,CAAC,CAAC,EAAE,KAAI;MACvE,UAAU,OAAO,OAAO,iBAAiB;MACzC,eAAe,OAAO,OAAO,sBAAsB;MACnD,CAAC;EAEP;;;;EAKA,mBAAmB,OAAa;AAC9B,WAAO,KAAK,oBAAmB,EAAG,KAChC,MAAM,GAAG,GACT;MAAI,kBACF,aAAa,OAAO,SAClB,IAAI,KAAK,YAAW,EAAG,SAAS,MAAM,YAAW,CAAE,KACnD,IAAI,YAAY,YAAW,EAAG,SAAS,MAAM,YAAW,CAAE,CAAC,EAC3D,MAAM,GAAG,EAAE;;KACd;EAEL;EAEQ,aAAa,cAA6B,QAA0B;AAC1E,QAAI,WAAW,CAAC,GAAG,YAAY;AAE/B,QAAI,OAAO,QAAQ;AACjB,YAAM,aAAa,OAAO,OAAO,YAAW;AAC5C,iBAAW,SAAS,OAAO,SACzB,IAAI,KAAK,YAAW,EAAG,SAAS,UAAU,KAC1C,IAAI,YAAY,YAAW,EAAG,SAAS,UAAU,KACjD,IAAI,MAAM,YAAW,EAAG,SAAS,UAAU,CAAC;IAEhD;AAEA,QAAI,OAAO,UAAU,OAAO,OAAO,SAAS,GAAG;AAC7C,iBAAW,SAAS,OAAO,SAAO,OAAO,OAAQ,SAAS,IAAI,MAAM,CAAC;IACvE;AAEA,QAAI,OAAO,eAAe,OAAO,YAAY,SAAS,GAAG;AACvD,iBAAW,SAAS,OAAO,SAAO,OAAO,YAAa,SAAS,IAAI,WAAW,CAAC;IACjF;AAEA,QAAI,OAAO,SAAS,OAAO,MAAM,SAAS,GAAG;AAC3C,iBAAW,SAAS,OAAO,SAAO,OAAO,MAAO,SAAS,IAAI,KAAK,CAAC;IACrE;AAEA,QAAI,OAAO,cAAc,OAAO,WAAW,SAAS,GAAG;AACrD,iBAAW,SAAS,OAAO,SAAO,OAAO,WAAY,SAAS,IAAI,UAAU,CAAC;IAC/E;AAEA,WAAO;EACT;EAEQ,QAAQ,OAAc,KAAW;AACvC,WAAO,MAAM,OAAO,CAAC,QAAQ,SAAQ;AACnC,YAAM,QAAQ,KAAK,GAAG;AACtB,aAAO,KAAK,KAAK,OAAO,KAAK,KAAK,KAAK;AACvC,aAAO;IACT,GAAG,CAAA,CAAE;EACP;EAEQ,iBAAiB,OAAc,KAAW;AAChD,UAAM,MAAM,MAAM,OAAO,CAAC,OAAO,SAAS,SAAS,KAAK,GAAG,KAAK,IAAI,CAAC;AACrE,WAAO,KAAK,MAAO,MAAM,MAAM,SAAU,EAAE,IAAI;EACjD;EAEQ,sBAAmB;AACzB,UAAM,WAA0B;MAC9B;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,YAAY;QACZ,QAAQ,kBAAkB;QAC1B,aAAa,uBAAuB;QACpC,SAAS;QACT,aAAa,oBAAI,KAAK,YAAY;QAClC,aAAa,oBAAI,KAAK,YAAY;QAClC,KAAK;QACL,YAAY;QACZ,eAAe;QACf,aAAa;QACb,eAAe;QACf,kBAAkB;QAClB,MAAM,CAAC,mBAAmB,oBAAoB,cAAc;QAC5D,WAAW;UACT,EAAE,MAAM,WAAW,SAAS,UAAU,UAAU,WAAU;UAC1D,EAAE,MAAM,WAAW,SAAS,WAAW,UAAU,UAAS;UAC1D,EAAE,MAAM,cAAc,SAAS,QAAQ,UAAU,WAAU;UAC3D,EAAE,MAAM,SAAS,SAAS,OAAO,UAAU,QAAO;UAClD,EAAE,MAAM,UAAU,SAAS,QAAQ,UAAU,iBAAgB;;QAE/D,cAAc;UACZ;YACE,IAAI;YACJ,eAAe;YACf,MAAM;YACN,MAAM,eAAe;YACrB,SAAS;YACT,YAAY;YACZ,aAAa;YACb,aAAa,sBAAsB;YACnC,QAAQ,iBAAiB;YACzB,aAAa,oBAAI,KAAK,YAAY;YAClC,YAAY;YACZ,YAAY;;UAEd;YACE,IAAI;YACJ,eAAe;YACf,MAAM;YACN,MAAM,eAAe;YACrB,SAAS;YACT,YAAY;YACZ,aAAa;YACb,aAAa,sBAAsB;YACnC,QAAQ,iBAAiB;YACzB,aAAa,oBAAI,KAAK,YAAY;YAClC,YAAY;YACZ,YAAY;;UAEd;YACE,IAAI;YACJ,eAAe;YACf,MAAM;YACN,MAAM,eAAe;YACrB,SAAS;YACT,YAAY;YACZ,aAAa;YACb,aAAa,sBAAsB;YACnC,QAAQ,iBAAiB;YACzB,aAAa,oBAAI,KAAK,YAAY;YAClC,YAAY;;;QAGhB,cAAc;UACZ;YACE,IAAI;YACJ,eAAe;YACf,MAAM;YACN,OAAO;YACP,MAAM,gBAAgB;YACtB,YAAY;YACZ,gBAAgB;YAChB,WAAW;YACX,mBAAmB,kBAAkB;YACrC,OAAO;;UAET;YACE,IAAI;YACJ,eAAe;YACf,MAAM;YACN,OAAO;YACP,MAAM,gBAAgB;YACtB,YAAY;YACZ,gBAAgB;YAChB,WAAW;YACX,mBAAmB,kBAAkB;YACrC,aAAa;;UAEf;YACE,IAAI;YACJ,eAAe;YACf,MAAM;YACN,OAAO;YACP,MAAM,gBAAgB;YACtB,YAAY;YACZ,gBAAgB;YAChB,WAAW;YACX,mBAAmB,kBAAkB;;;QAGzC,WAAW;UACT;YACE,IAAI;YACJ,eAAe;YACf,OAAO;YACP,MAAM,kBAAkB;YACxB,aAAa;YACb,KAAK;YACL,SAAS;YACT,UAAU;YACV,YAAY,oBAAI,KAAK,YAAY;YACjC,YAAY;;UAEd;YACE,IAAI;YACJ,eAAe;YACf,OAAO;YACP,MAAM,kBAAkB;YACxB,aAAa;YACb,UAAU;YACV,UAAU;YACV,UAAU;YACV,SAAS;YACT,UAAU;YACV,YAAY,oBAAI,KAAK,YAAY;YACjC,YAAY;;UAEd;YACE,IAAI;YACJ,eAAe;YACf,OAAO;YACP,MAAM,kBAAkB;YACxB,aAAa;YACb,KAAK;YACL,SAAS;YACT,UAAU;YACV,YAAY,oBAAI,KAAK,YAAY;YACjC,YAAY;;;QAGhB,iBAAiB;UACf;YACE,IAAI;YACJ,eAAe;YACf,OAAO;YACP,OAAO;YACP,aAAa;YACb,UAAU,sBAAsB;YAChC,WAAW;YACX,QAAQ,oBAAoB;YAC5B,cAAc,oBAAI,KAAK,YAAY;YACnC,cAAc;YACd,mBAAmB;YACnB,gBAAgB;YAChB,cAAc;;UAEhB;YACE,IAAI;YACJ,eAAe;YACf,OAAO;YACP,aAAa;YACb,UAAU,sBAAsB;YAChC,WAAW;YACX,QAAQ,oBAAoB;YAC5B,cAAc,oBAAI,KAAK,YAAY;YACnC,cAAc;YACd,mBAAmB;YACnB,gBAAgB;;UAElB;YACE,IAAI;YACJ,eAAe;YACf,OAAO;YACP,aAAa;YACb,UAAU,sBAAsB;YAChC,WAAW;YACX,QAAQ,oBAAoB;YAC5B,cAAc,oBAAI,KAAK,YAAY;YACnC,cAAc;YACd,YAAY,oBAAI,KAAK,YAAY;YACjC,YAAY;YACZ,mBAAmB;YACnB,gBAAgB;;;QAGpB,qBAAqB,CAAA;;MAEvB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,YAAY;QACZ,QAAQ,kBAAkB;QAC1B,aAAa,uBAAuB;QACpC,SAAS;QACT,aAAa,oBAAI,KAAK,YAAY;QAClC,aAAa,oBAAI,KAAK,YAAY;QAClC,KAAK;QACL,YAAY;QACZ,eAAe;QACf,aAAa;QACb,eAAe;QACf,kBAAkB;QAClB,MAAM,CAAC,YAAY,kBAAkB,cAAc;QACnD,WAAW;UACT,EAAE,MAAM,WAAW,SAAS,WAAW,UAAU,UAAS;UAC1D,EAAE,MAAM,cAAc,SAAS,QAAQ,UAAU,WAAU;UAC3D,EAAE,MAAM,SAAS,SAAS,OAAO,UAAU,QAAO;;QAEpD,cAAc,CAAA;QACd,iBAAiB,CAAA;QACjB,WAAW,CAAA;;MAEb;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,YAAY;QACZ,QAAQ,kBAAkB;QAC1B,aAAa,uBAAuB;QACpC,SAAS;QACT,aAAa,oBAAI,KAAK,YAAY;QAClC,aAAa,oBAAI,KAAK,YAAY;QAClC,KAAK;QACL,YAAY;QACZ,eAAe;QACf,aAAa;QACb,eAAe;QACf,kBAAkB;QAClB,MAAM,CAAC,aAAa,yBAAyB,WAAW;QACxD,WAAW;UACT,EAAE,MAAM,SAAS,SAAS,UAAU,UAAU,WAAU;UACxD,EAAE,MAAM,UAAU,SAAS,QAAQ,UAAU,UAAS;UACtD,EAAE,MAAM,WAAW,SAAS,OAAO,UAAU,WAAU;;QAEzD,cAAc,CAAA;QACd,iBAAiB,CAAA;QACjB,WAAW,CAAA;;MAEb;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,YAAY;QACZ,QAAQ,kBAAkB;QAC1B,aAAa,uBAAuB;QACpC,SAAS;QACT,aAAa,oBAAI,KAAK,YAAY;QAClC,aAAa,oBAAI,KAAK,YAAY;QAClC,KAAK;QACL,YAAY;QACZ,eAAe;QACf,aAAa;QACb,eAAe;QACf,kBAAkB;QAClB,MAAM,CAAC,YAAY,aAAa,eAAe;QAC/C,WAAW;UACT,EAAE,MAAM,QAAQ,SAAS,MAAM,UAAU,UAAS;UAClD,EAAE,MAAM,eAAe,SAAS,OAAO,UAAU,YAAW;UAC5D,EAAE,MAAM,cAAc,SAAS,QAAQ,UAAU,WAAU;;QAE7D,cAAc,CAAA;QACd,iBAAiB,CAAA;QACjB,WAAW,CAAA;;MAEb;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,YAAY;QACZ,QAAQ,kBAAkB;QAC1B,aAAa,uBAAuB;QACpC,SAAS;QACT,aAAa,oBAAI,KAAK,YAAY;QAClC,aAAa,oBAAI,KAAK,YAAY;QAClC,KAAK;QACL,YAAY;QACZ,eAAe;QACf,aAAa;QACb,eAAe;QACf,kBAAkB;QAClB,MAAM,CAAC,iBAAiB,aAAa,cAAc;QACnD,WAAW;UACT,EAAE,MAAM,WAAW,SAAS,WAAW,UAAU,UAAS;UAC1D,EAAE,MAAM,YAAY,SAAS,QAAQ,UAAU,gBAAe;UAC9D,EAAE,MAAM,SAAS,SAAS,OAAO,UAAU,QAAO;;QAEpD,cAAc,CAAA;QACd,iBAAiB,CAAA;QACjB,WAAW,CAAA;;MAEb;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,YAAY;QACZ,QAAQ,kBAAkB;QAC1B,aAAa,uBAAuB;QACpC,SAAS;QACT,aAAa,oBAAI,KAAK,YAAY;QAClC,aAAa,oBAAI,KAAK,YAAY;QAClC,KAAK;QACL,YAAY;QACZ,eAAe;QACf,aAAa;QACb,eAAe;QACf,kBAAkB;QAClB,MAAM,CAAC,aAAa,aAAa,cAAc;QAC/C,WAAW;UACT,EAAE,MAAM,UAAU,SAAS,SAAS,UAAU,WAAU;UACxD,EAAE,MAAM,aAAa,SAAS,OAAO,UAAU,UAAS;UACxD,EAAE,MAAM,SAAS,SAAS,OAAO,UAAU,WAAU;;QAEvD,cAAc,CAAA;QACd,iBAAiB,CAAA;QACjB,WAAW,CAAA;;MAEb;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,YAAY;QACZ,QAAQ,kBAAkB;QAC1B,aAAa,uBAAuB;QACpC,SAAS;QACT,aAAa,oBAAI,KAAK,YAAY;QAClC,aAAa,oBAAI,KAAK,YAAY;QAClC,KAAK;QACL,YAAY;QACZ,eAAe;QACf,aAAa;QACb,eAAe;QACf,kBAAkB;QAClB,MAAM,CAAC,oBAAoB,gBAAgB,gBAAgB;QAC3D,WAAW;UACT,EAAE,MAAM,WAAW,SAAS,UAAU,UAAU,WAAU;UAC1D,EAAE,MAAM,OAAO,SAAS,OAAO,UAAU,UAAS;UAClD,EAAE,MAAM,SAAS,SAAS,OAAO,UAAU,WAAU;;QAEvD,cAAc,CAAA;QACd,iBAAiB,CAAA;QACjB,WAAW,CAAA;;MAEb;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,YAAY;QACZ,QAAQ,kBAAkB;QAC1B,aAAa,uBAAuB;QACpC,SAAS;QACT,aAAa,oBAAI,KAAK,YAAY;QAClC,aAAa,oBAAI,KAAK,YAAY;QAClC,KAAK;QACL,YAAY;QACZ,eAAe;QACf,aAAa;QACb,eAAe;QACf,kBAAkB;QAClB,MAAM,CAAC,eAAe,UAAU,eAAe;QAC/C,WAAW;UACT,EAAE,MAAM,QAAQ,SAAS,OAAO,UAAU,iBAAgB;UAC1D,EAAE,MAAM,OAAO,SAAS,OAAO,UAAU,UAAS;UAClD,EAAE,MAAM,cAAc,SAAS,QAAQ,UAAU,WAAU;;QAE7D,cAAc,CAAA;QACd,iBAAiB,CAAA;QACjB,WAAW,CAAA;;;AAIf,WAAO,GAAG,QAAQ;EACpB;;qCA3kBW,sBAAmB;EAAA;4EAAnB,sBAAmB,SAAnB,qBAAmB,WAAA,YAFlB,OAAM,CAAA;;;sEAEP,qBAAmB,CAAA;UAH/B;WAAW;MACV,YAAY;KACb;;;", "names": ["ApplicationStatus", "ApplicationCriticality", "LifecycleStatus", "BusinessCriticality", "DependencyType", "DependencyCriticality", "DependencyStatus", "TechStackCategory", "SupportLevel", "StakeholderRole", "ContactPreference", "DocumentationType", "AssessmentType", "AssessmentStatus", "VulnerabilitySeverity", "VulnerabilityStatus"]}