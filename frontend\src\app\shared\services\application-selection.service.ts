import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { delay, map } from 'rxjs/operators';

export interface ApplicationOption {
  id: number;
  name: string;
  description: string;
  owner: string;
  department: string;
}

@Injectable({
  providedIn: 'root'
})
export class ApplicationSelectionService {

  constructor() { }

  /**
   * Get applications for selection dropdown
   */
  getApplicationOptions(): Observable<ApplicationOption[]> {
    return this.getMockApplications().pipe(
      delay(300),
      map(apps => apps.map(app => ({
        id: app.id!,
        name: app.name,
        description: app.description,
        owner: app.owner,
        department: app.department
      })))
    );
  }

  /**
   * Search applications by name
   */
  searchApplications(query: string): Observable<ApplicationOption[]> {
    return this.getApplicationOptions().pipe(
      map(apps => 
        apps.filter(app => 
          app.name.toLowerCase().includes(query.toLowerCase()) ||
          app.description.toLowerCase().includes(query.toLowerCase())
        ).slice(0, 10)
      )
    );
  }

  private getMockApplications() {
    return of([
      {
        id: 1,
        name: 'E-Commerce Platform',
        description: 'Main customer-facing e-commerce application',
        owner: 'John Doe',
        department: 'Engineering'
      },
      {
        id: 2,
        name: 'Customer Portal',
        description: 'Self-service customer management portal',
        owner: 'Jane Smith',
        department: 'Product'
      },
      {
        id: 3,
        name: 'Admin Dashboard',
        description: 'Internal administration and reporting dashboard',
        owner: 'Mike Johnson',
        department: 'Operations'
      },
      {
        id: 4,
        name: 'Mobile App',
        description: 'iOS and Android mobile application',
        owner: 'Sarah Wilson',
        department: 'Mobile'
      },
      {
        id: 5,
        name: 'Analytics Service',
        description: 'Data analytics and reporting microservice',
        owner: 'David Brown',
        department: 'Data'
      },
      {
        id: 6,
        name: 'Payment Gateway',
        description: 'Payment processing and transaction management',
        owner: 'Lisa Davis',
        department: 'FinTech'
      },
      {
        id: 7,
        name: 'Inventory System',
        description: 'Warehouse and inventory management system',
        owner: 'Tom Wilson',
        department: 'Logistics'
      },
      {
        id: 8,
        name: 'CRM System',
        description: 'Customer relationship management platform',
        owner: 'Emily Johnson',
        department: 'Sales'
      }
    ]);
  }
}
