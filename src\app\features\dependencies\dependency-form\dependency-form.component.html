<div class="container-fluid">
  <form [formGroup]="dependencyForm" (ngSubmit)="onSubmit()">
    <div class="card mb-4">
      <div class="card-header">
        <h3>Basic Information</h3>
      </div>
      <div class="card-body">
        <div class="row mb-3">
          <div class="col-md-6">
            <div class="form-group">
              <label for="name" class="form-label">Name <span class="text-danger">*</span></label>
              <input type="text" id="name" formControlName="name" class="form-control" placeholder="Dependency name">
              <div *ngIf="dependencyForm.get('name')?.invalid && dependencyForm.get('name')?.touched" class="text-danger mt-1">
                Name is required
              </div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label for="type" class="form-label">Type <span class="text-danger">*</span></label>
              <select id="type" formControlName="type" class="form-select">
                <option value="Internal">Internal</option>
                <option value="External">External</option>
              </select>
            </div>
          </div>
        </div>
        
        <div class="row mb-3">
          <div class="col-md-12">
            <div class="form-group">
              <label for="description" class="form-label">Description <span class="text-danger">*</span></label>
              <textarea id="description" formControlName="description" class="form-control" rows="3" placeholder="Describe the dependency"></textarea>
              <div *ngIf="dependencyForm.get('description')?.invalid && dependencyForm.get('description')?.touched" class="text-danger mt-1">
                Description is required
              </div>
            </div>
          </div>
        </div>
        
        <div class="row mb-3">
          <div class="col-md-6">
            <div class="form-group">
              <label for="category" class="form-label">Category <span class="text-danger">*</span></label>
              <select id="category" formControlName="category" class="form-select">
                <option value="API">API</option>
                <option value="Library">Library</option>
                <option value="Service">Service</option>
                <option value="Application">Application</option>
                <option value="Other">Other</option>
              </select>
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
              <select id="status" formControlName="status" class="form-select">
                <option value="Active">Active</option>
                <option value="Planned">Planned</option>
                <option value="Deprecated">Deprecated</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="card mb-4">
      <div class="card-header">
        <h3>Technical Details</h3>
      </div>
      <div class="card-body">
        <div class="row mb-3">
          <div class="col-md-6">
            <div class="form-group">
              <label for="version" class="form-label">Version</label>
              <input type="text" id="version" formControlName="version" class="form-control" placeholder="e.g. 1.0.0">
            </div>
          </div>
          <div class="col-md-6">
            <div class="form-group">
              <label for="url" class="form-label">URL</label>
              <input type="url" id="url" formControlName="url" class="form-control" placeholder="https://example.com">
            </div>
          </div>
        </div>
        
        <div class="row mb-3">
          <div class="col-md-6">
            <div class="form-group">
              <label for="owner" class="form-label">Owner</label>
              <input type="text" id="owner" formControlName="owner" class="form-control" placeholder="Team or person responsible">
            </div>
          </div>
        </div>
        
        <div class="row">
          <div class="col-md-12">
            <div class="form-group">
              <label for="integrationDetails" class="form-label">Integration Details</label>
              <textarea id="integrationDetails" formControlName="integrationDetails" class="form-control" rows="3" placeholder="Details about how to integrate with this dependency"></textarea>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="d-flex justify-content-end mb-4 gap-2">
      <button type="button" class="btn btn-outline-secondary" (click)="onCancel()">
        Cancel
      </button>
      <button type="submit" class="btn btn-primary" [disabled]="dependencyForm.invalid">
        {{ isEditMode ? 'Update' : 'Create' }} Dependency
      </button>
    </div>
  </form>
</div>