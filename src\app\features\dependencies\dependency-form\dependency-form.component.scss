.card {
  border-radius: 0.5rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid rgba(0, 0, 0, 0.125);
  padding: 1rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-label {
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.form-control, .form-select {
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  border: 1px solid #ced4da;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  
  &:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
  }
}

textarea.form-control {
  resize: vertical;
}

.btn-primary {
  background-color: #0d6efd;
  border-color: #0d6efd;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  
  &:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
  }
  
  &:disabled {
    background-color: #6c757d;
    border-color: #6c757d;
    opacity: 0.65;
  }
}

.text-danger {
  color: #dc3545;
  font-size: 0.875rem;
}