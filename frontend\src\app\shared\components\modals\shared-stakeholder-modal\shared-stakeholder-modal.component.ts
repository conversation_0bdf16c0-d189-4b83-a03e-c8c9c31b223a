import { Component, Input, Output, EventEmitter, OnInit, OnChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { SlideModalComponent } from '../../slide-modal/slide-modal.component';
import { FormInputComponent } from '../../form-input/form-input.component';
import { ApplicationSelectionService, ApplicationOption } from '../../../services/application-selection.service';
import { Observable } from 'rxjs';

export interface SharedStakeholderFormData {
  applicationId?: number;
  name: string;
  email: string;
  role: string;
  department: string;
  responsibility: string;
  isPrimary: boolean;
  phone?: string;
  slackHandle?: string;
  contactPreference: string;
}

@Component({
  selector: 'app-shared-stakeholder-modal',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, SlideModalComponent, FormInputComponent],
  template: `
    <app-slide-modal
      [isOpen]="isOpen"
      [title]="editMode ? 'Edit Stakeholder' : 'Add Stakeholder'"
      [subtitle]="showApplicationSelection ? 'Configure team member details and select application' : 'Configure team member details'"
      [loading]="loading"
      [canConfirm]="stakeholderForm.valid"
      confirmText="Save Stakeholder"
      (closed)="onClose()"
      (confirmed)="onSave()"
      (cancelled)="onClose()"
    >
      <form [formGroup]="stakeholderForm" class="stakeholder-form">
        <!-- Application Selection (only when not in applications module) -->
        <div class="form-group" *ngIf="showApplicationSelection">
          <label class="form-label">Application *</label>
          <select formControlName="applicationId" class="form-select">
            <option value="">Select an application</option>
            <option *ngFor="let app of applicationOptions$ | async" [value]="app.id">
              {{ app.name }} - {{ app.department }}
            </option>
          </select>
          <div class="field-error" *ngIf="getFieldError('applicationId')">
            {{ getFieldError('applicationId') }}
          </div>
        </div>

        <div class="form-grid">
          <app-form-input
            label="Full Name"
            placeholder="e.g., John Doe"
            formControlName="name"
            [required]="true"
            [errorMessage]="getFieldError('name')"
          ></app-form-input>

          <app-form-input
            label="Email Address"
            placeholder="<EMAIL>"
            formControlName="email"
            type="email"
            [required]="true"
            [errorMessage]="getFieldError('email')"
          ></app-form-input>

          <div class="form-group">
            <label class="form-label">Role</label>
            <select formControlName="role" class="form-select">
              <option value="developer">Developer</option>
              <option value="tech_lead">Tech Lead</option>
              <option value="architect">Architect</option>
              <option value="product_manager">Product Manager</option>
              <option value="project_manager">Project Manager</option>
              <option value="devops_engineer">DevOps Engineer</option>
              <option value="qa_engineer">QA Engineer</option>
              <option value="designer">Designer</option>
              <option value="business_analyst">Business Analyst</option>
              <option value="stakeholder">Business Stakeholder</option>
              <option value="other">Other</option>
            </select>
          </div>

          <app-form-input
            label="Department"
            placeholder="e.g., Engineering, Product"
            formControlName="department"
            [required]="true"
            [errorMessage]="getFieldError('department')"
          ></app-form-input>

          <app-form-input
            label="Phone Number"
            placeholder="+****************"
            formControlName="phone"
            [errorMessage]="getFieldError('phone')"
          ></app-form-input>

          <app-form-input
            label="Slack Handle"
            placeholder="@john.doe"
            formControlName="slackHandle"
            [errorMessage]="getFieldError('slackHandle')"
          ></app-form-input>

          <div class="form-group">
            <label class="form-label">Contact Preference</label>
            <select formControlName="contactPreference" class="form-select">
              <option value="email">Email</option>
              <option value="slack">Slack</option>
              <option value="phone">Phone</option>
              <option value="teams">Microsoft Teams</option>
              <option value="in_person">In Person</option>
            </select>
          </div>

          <div class="form-group checkbox-group">
            <label class="checkbox-label">
              <input
                type="checkbox"
                formControlName="isPrimary"
                class="checkbox-input"
              >
              <span class="checkbox-text">Primary Contact</span>
            </label>
            <div class="checkbox-description">
              Mark as the primary contact for this application
            </div>
          </div>

          <div class="form-group full-width">
            <label class="form-label">Responsibility</label>
            <textarea 
              formControlName="responsibility"
              class="form-textarea"
              placeholder="Describe their role and responsibilities for this application..."
              rows="3"
            ></textarea>
          </div>
        </div>
      </form>
    </app-slide-modal>
  `,
  styles: [`
    .stakeholder-form {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-lg);
    }

    .form-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-lg);
    }

    .form-group {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-sm);
    }

    .form-group.full-width {
      grid-column: 1 / -1;
    }

    .form-label {
      font-size: var(--text-sm);
      font-weight: 500;
      color: var(--secondary-700);
    }

    .form-select,
    .form-textarea {
      height: 40px;
      padding: 0 var(--spacing-md);
      border: 1px solid var(--secondary-300);
      border-radius: var(--radius-md);
      background: white;
      font-size: var(--text-sm);
      color: var(--secondary-800);
      transition: all 0.2s ease;

      &:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px var(--primary-100);
      }
    }

    .form-textarea {
      height: auto;
      padding: var(--spacing-sm) var(--spacing-md);
      resize: vertical;
      min-height: 80px;
    }

    .field-error {
      font-size: var(--text-sm);
      color: var(--error-600);
    }

    .checkbox-group {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-xs);
    }

    .checkbox-label {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      cursor: pointer;
      font-size: var(--text-sm);
      color: var(--secondary-700);
    }

    .checkbox-input {
      width: 16px;
      height: 16px;
      border: 1px solid var(--secondary-300);
      border-radius: var(--radius-sm);
      cursor: pointer;

      &:checked {
        background-color: var(--primary-600);
        border-color: var(--primary-600);
      }

      &:focus {
        outline: none;
        box-shadow: 0 0 0 3px var(--primary-100);
      }
    }

    .checkbox-text {
      font-weight: 500;
    }

    .checkbox-description {
      font-size: var(--text-xs);
      color: var(--secondary-500);
      margin-left: 24px;
    }

    @media (max-width: 768px) {
      .form-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
      }

      .checkbox-description {
        margin-left: 0;
      }
    }
  `]
})
export class SharedStakeholderModalComponent implements OnInit, OnChanges {
  @Input() isOpen = false;
  @Input() editMode = false;
  @Input() initialData: SharedStakeholderFormData | null = null;
  @Input() loading = false;
  @Input() showApplicationSelection = true; // Hide when used within applications module

  @Output() closed = new EventEmitter<void>();
  @Output() saved = new EventEmitter<SharedStakeholderFormData>();

  stakeholderForm!: FormGroup;
  applicationOptions$: Observable<ApplicationOption[]>;

  constructor(
    private fb: FormBuilder,
    private applicationSelectionService: ApplicationSelectionService
  ) {
    this.applicationOptions$ = this.applicationSelectionService.getApplicationOptions();
  }

  ngOnInit(): void {
    this.initializeForm();
  }

  ngOnChanges(): void {
    if (this.stakeholderForm && this.initialData) {
      this.stakeholderForm.patchValue(this.initialData);
    }
  }

  private initializeForm(): void {
    const formConfig: any = {
      name: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      role: ['developer', [Validators.required]],
      department: ['', [Validators.required]],
      responsibility: [''],
      isPrimary: [false],
      phone: [''],
      slackHandle: [''],
      contactPreference: ['email', [Validators.required]]
    };

    if (this.showApplicationSelection) {
      formConfig.applicationId = ['', [Validators.required]];
    }

    this.stakeholderForm = this.fb.group(formConfig);

    if (this.initialData) {
      this.stakeholderForm.patchValue(this.initialData);
    }
  }

  getFieldError(fieldName: string): string {
    const field = this.stakeholderForm.get(fieldName);
    if (field && field.invalid && field.touched) {
      if (field.errors?.['required']) {
        return `${fieldName} is required`;
      }
      if (field.errors?.['minlength']) {
        return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;
      }
      if (field.errors?.['email']) {
        return 'Please enter a valid email address';
      }
    }
    return '';
  }

  onSave(): void {
    if (this.stakeholderForm.valid) {
      const formData: SharedStakeholderFormData = this.stakeholderForm.value;
      this.saved.emit(formData);
    }
  }

  onClose(): void {
    this.closed.emit();
  }
}
