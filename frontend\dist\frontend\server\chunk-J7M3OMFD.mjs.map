{"version": 3, "sources": ["src/app/modules/auth/components/login-input/login-input.component.ts", "src/app/modules/auth/components/login/login.component.ts"], "sourcesContent": ["import { Component, Input, forwardRef } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { ControlValueAccessor, NG_VALUE_ACCESSOR, FormsModule } from '@angular/forms';\r\n\r\n@Component({\r\n  selector: 'app-login-input',\r\n  standalone: true,\r\n  imports: [CommonModule, FormsModule],\r\n  providers: [\r\n    {\r\n      provide: NG_VALUE_ACCESSOR,\r\n      useExisting: forwardRef(() => LoginInputComponent),\r\n      multi: true\r\n    }\r\n  ],\r\n  template: `\r\n    <div class=\"form-group\">\r\n      <label *ngIf=\"label\" [for]=\"inputId\" class=\"form-label\">\r\n        {{ label }}\r\n        <span class=\"required-indicator\" *ngIf=\"required\">*</span>\r\n      </label>\r\n\r\n      <div class=\"input-wrapper\">\r\n        <input\r\n          [id]=\"inputId\"\r\n          [type]=\"type\"\r\n          [placeholder]=\"placeholder\"\r\n          [disabled]=\"disabled\"\r\n          [(ngModel)]=\"value\"\r\n          (input)=\"onInput($event)\"\r\n          (blur)=\"onBlur()\"\r\n          class=\"form-input\"\r\n          [class.error]=\"!!errorMessage\"\r\n        >\r\n      </div>\r\n\r\n      @if (errorMessage) {\r\n        <p class=\"error-message\">{{ errorMessage }}</p>\r\n      }\r\n    </div>\r\n  `,\r\n  styles: [`\r\n    :host {\r\n      display: block;\r\n      width: 100%;\r\n    }\r\n\r\n    .form-group {\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: var(--spacing-sm);\r\n    }\r\n\r\n    .form-label {\r\n      font-size: var(--text-sm);\r\n      font-weight: 500;\r\n      color: var(--secondary-700);\r\n      display: flex;\r\n      align-items: center;\r\n      gap: var(--spacing-xs);\r\n    }\r\n\r\n    .required-indicator {\r\n      color: var(--error-500);\r\n      font-weight: 600;\r\n    }\r\n\r\n    .input-wrapper {\r\n      position: relative;\r\n    }\r\n\r\n    .form-input {\r\n      width: 100%;\r\n      height: 44px;\r\n      padding: 0 var(--spacing-md);\r\n      border: 1px solid var(--secondary-300);\r\n      border-radius: var(--radius-md);\r\n      background: white;\r\n      font-size: var(--text-sm);\r\n      color: var(--secondary-800);\r\n      transition: all 0.2s ease;\r\n      outline: none;\r\n\r\n      &::placeholder {\r\n        color: var(--secondary-400);\r\n      }\r\n\r\n      &:focus {\r\n        border-color: var(--primary-500);\r\n        box-shadow: 0 0 0 3px var(--primary-100);\r\n      }\r\n\r\n      &:disabled {\r\n        background: var(--secondary-50);\r\n        color: var(--secondary-500);\r\n        cursor: not-allowed;\r\n        border-color: var(--secondary-200);\r\n      }\r\n\r\n      &.error {\r\n        border-color: var(--error-500);\r\n        box-shadow: 0 0 0 3px var(--error-100);\r\n\r\n        &:focus {\r\n          border-color: var(--error-500);\r\n          box-shadow: 0 0 0 3px var(--error-100);\r\n        }\r\n      }\r\n    }\r\n\r\n    .error-message {\r\n      font-size: var(--text-sm);\r\n      color: var(--error-600);\r\n      margin: 0;\r\n      display: flex;\r\n      align-items: center;\r\n      gap: var(--spacing-xs);\r\n    }\r\n\r\n    /* High Contrast Mode Support */\r\n    @media (prefers-contrast: high) {\r\n      .form-input {\r\n        border-width: 2px;\r\n      }\r\n    }\r\n\r\n    /* Reduced Motion Support */\r\n    @media (prefers-reduced-motion: reduce) {\r\n      .form-input {\r\n        transition: none;\r\n      }\r\n    }\r\n  `]\r\n})\r\nexport class LoginInputComponent implements ControlValueAccessor {\r\n  @Input() label = '';\r\n  @Input() type = 'text';\r\n  @Input() inputId = '';\r\n  @Input() placeholder = '';\r\n  @Input() errorMessage = '';\r\n  @Input() disabled = false;\r\n  @Input() required = false;\r\n\r\n  value: any = '';\r\n  private touched = false;\r\n  private onChange = (value: any) => {};\r\n  private onTouched = () => {};\r\n\r\n  writeValue(value: any): void {\r\n    this.value = value;\r\n  }\r\n\r\n  registerOnChange(fn: any): void {\r\n    this.onChange = fn;\r\n  }\r\n\r\n  registerOnTouched(fn: any): void {\r\n    this.onTouched = fn;\r\n  }\r\n\r\n  setDisabledState(isDisabled: boolean): void {\r\n    this.disabled = isDisabled;\r\n  }\r\n\r\n  onInput(event: Event): void {\r\n    const target = event.target as HTMLInputElement;\r\n    this.value = target.value;\r\n    this.onChange(this.value);\r\n  }\r\n\r\n  onBlur(): void {\r\n    if (!this.touched) {\r\n      this.touched = true;\r\n      this.onTouched();\r\n    }\r\n  }\r\n}\r\n", "import { Component } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';\r\nimport { Router, ActivatedRoute } from '@angular/router';\r\nimport { AuthService } from '../../services/auth.service';\r\nimport { LoginInputComponent } from '../login-input/login-input.component';\r\nimport { ButtonComponent } from '../../../../shared/components/button/button.component';\r\n\r\n@Component({\r\n  selector: 'app-login',\r\n  standalone: true,\r\n  imports: [CommonModule, ReactiveFormsModule, LoginInputComponent, ButtonComponent],\r\n  template: `\r\n    <div class=\"login-container\">\r\n      <div class=\"login-card\">\r\n        <!-- Logo/Branding -->\r\n        <div class=\"login-header\">\r\n          <div class=\"logo-container\">\r\n            <svg class=\"logo-icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\"/>\r\n            </svg>\r\n            <h1 class=\"logo-text\">App Catalog</h1>\r\n          </div>\r\n          <h2 class=\"login-title\">\r\n            Welcome back\r\n          </h2>\r\n          <p class=\"login-subtitle\">\r\n            Sign in to your account to continue\r\n          </p>\r\n        </div>\r\n\r\n        <!-- Error Message -->\r\n        <div class=\"error-message\" *ngIf=\"error\">\r\n          <svg class=\"error-icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\r\n            <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\r\n            <line x1=\"15\" y1=\"9\" x2=\"9\" y2=\"15\"></line>\r\n            <line x1=\"9\" y1=\"9\" x2=\"15\" y2=\"15\"></line>\r\n          </svg>\r\n          <span>{{ error }}</span>\r\n        </div>\r\n\r\n        <!-- Login Form -->\r\n        <form class=\"login-form\" [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\">\r\n          <!-- Email Input -->\r\n          <app-login-input\r\n            formControlName=\"email\"\r\n            type=\"email\"\r\n            label=\"Email address\"\r\n            inputId=\"email\"\r\n            [errorMessage]=\"getFieldError('email')\"\r\n            [disabled]=\"loading\"\r\n            placeholder=\"Enter your email\"\r\n            [required]=\"true\"\r\n          ></app-login-input>\r\n\r\n          <!-- Password Input -->\r\n          <app-login-input\r\n            formControlName=\"password\"\r\n            type=\"password\"\r\n            label=\"Password\"\r\n            inputId=\"password\"\r\n            [errorMessage]=\"getFieldError('password')\"\r\n            [disabled]=\"loading\"\r\n            placeholder=\"Enter your password\"\r\n            [required]=\"true\"\r\n          ></app-login-input>\r\n\r\n          <!-- Remember Me & Forgot Password -->\r\n          <div class=\"form-options\">\r\n            <div class=\"remember-me\">\r\n              <input\r\n                id=\"remember-me\"\r\n                formControlName=\"rememberMe\"\r\n                type=\"checkbox\"\r\n                [disabled]=\"loading\"\r\n                class=\"checkbox-input\"\r\n              >\r\n              <label for=\"remember-me\" class=\"checkbox-label\">\r\n                Remember me\r\n              </label>\r\n            </div>\r\n\r\n            <div class=\"forgot-password\">\r\n              <a href=\"#\" class=\"forgot-link\">\r\n                Forgot your password?\r\n              </a>\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Submit Button -->\r\n          <app-button\r\n            type=\"submit\"\r\n            variant=\"primary\"\r\n            size=\"lg\"\r\n            [disabled]=\"loginForm.invalid\"\r\n            [loading]=\"loading\"\r\n            [fullWidth]=\"true\"\r\n            loadingText=\"Signing in...\"\r\n            leftIcon=\"M5 13l4 4L19 7\"\r\n            (clicked)=\"onSubmit()\"\r\n          >\r\n            Sign in\r\n          </app-button>\r\n        </form>\r\n\r\n        <!-- Additional Links -->\r\n        <div class=\"login-footer\">\r\n          <p class=\"footer-text\">\r\n            Don't have an account?\r\n            <a href=\"#\" class=\"footer-link\">Contact your administrator</a>\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  `,\r\n  styles: [`\r\n    :host {\r\n      display: block;\r\n      min-height: 100vh;\r\n    }\r\n\r\n    .login-container {\r\n      min-height: 100vh;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      padding: var(--spacing-lg);\r\n      background: linear-gradient(\r\n        135deg,\r\n        var(--neutral-50) 0%,\r\n        var(--neutral-100) 100%\r\n      );\r\n    }\r\n\r\n    .login-card {\r\n      width: 100%;\r\n      max-width: 420px;\r\n      background: white;\r\n      border-radius: var(--radius-xl);\r\n      box-shadow: var(--shadow-xl);\r\n      border: 1px solid var(--secondary-200);\r\n      overflow: hidden;\r\n    }\r\n\r\n    .login-header {\r\n      padding: var(--spacing-2xl) var(--spacing-xl) var(--spacing-xl);\r\n      text-align: center;\r\n      background: linear-gradient(\r\n        135deg,\r\n        var(--primary-50) 0%,\r\n        white 100%\r\n      );\r\n      border-bottom: 1px solid var(--secondary-100);\r\n    }\r\n\r\n    .logo-container {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      gap: var(--spacing-md);\r\n      margin-bottom: var(--spacing-lg);\r\n    }\r\n\r\n    .logo-icon {\r\n      width: 32px;\r\n      height: 32px;\r\n      color: var(--primary-600);\r\n      stroke-width: 2;\r\n    }\r\n\r\n    .logo-text {\r\n      font-size: var(--text-xl);\r\n      font-weight: 700;\r\n      color: var(--secondary-900);\r\n      margin: 0;\r\n    }\r\n\r\n    .login-title {\r\n      font-size: var(--text-2xl);\r\n      font-weight: 600;\r\n      color: var(--secondary-900);\r\n      margin: 0 0 var(--spacing-sm) 0;\r\n      line-height: var(--leading-tight);\r\n    }\r\n\r\n    .login-subtitle {\r\n      font-size: var(--text-sm);\r\n      color: var(--secondary-600);\r\n      margin: 0;\r\n      line-height: var(--leading-normal);\r\n    }\r\n\r\n    .error-message {\r\n      margin: var(--spacing-lg) var(--spacing-xl) 0;\r\n      padding: var(--spacing-md);\r\n      background: var(--error-50);\r\n      border: 1px solid var(--error-200);\r\n      border-radius: var(--radius-md);\r\n      display: flex;\r\n      align-items: center;\r\n      gap: var(--spacing-sm);\r\n      font-size: var(--text-sm);\r\n      color: var(--error-700);\r\n    }\r\n\r\n    .error-icon {\r\n      width: 16px;\r\n      height: 16px;\r\n      color: var(--error-500);\r\n      flex-shrink: 0;\r\n      stroke-width: 2;\r\n    }\r\n\r\n    .login-form {\r\n      padding: var(--spacing-xl);\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: var(--spacing-lg);\r\n    }\r\n\r\n    .form-options {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      gap: var(--spacing-md);\r\n    }\r\n\r\n    .remember-me {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: var(--spacing-sm);\r\n    }\r\n\r\n    .checkbox-input {\r\n      width: 16px;\r\n      height: 16px;\r\n      border: 1px solid var(--secondary-300);\r\n      border-radius: var(--radius-sm);\r\n      background: white;\r\n      cursor: pointer;\r\n      transition: all 0.2s ease;\r\n\r\n      &:checked {\r\n        background-color: var(--primary-600);\r\n        border-color: var(--primary-600);\r\n      }\r\n\r\n      &:focus {\r\n        outline: none;\r\n        box-shadow: 0 0 0 3px var(--primary-100);\r\n      }\r\n\r\n      &:disabled {\r\n        opacity: 0.5;\r\n        cursor: not-allowed;\r\n      }\r\n    }\r\n\r\n    .checkbox-label {\r\n      font-size: var(--text-sm);\r\n      color: var(--secondary-700);\r\n      cursor: pointer;\r\n      user-select: none;\r\n    }\r\n\r\n    .forgot-password {\r\n      flex-shrink: 0;\r\n    }\r\n\r\n    .forgot-link {\r\n      font-size: var(--text-sm);\r\n      color: var(--primary-600);\r\n      text-decoration: none;\r\n      font-weight: 500;\r\n      transition: color 0.2s ease;\r\n\r\n      &:hover {\r\n        color: var(--primary-700);\r\n        text-decoration: underline;\r\n      }\r\n\r\n      &:focus {\r\n        outline: none;\r\n        color: var(--primary-700);\r\n        text-decoration: underline;\r\n      }\r\n    }\r\n\r\n    .login-footer {\r\n      padding: var(--spacing-lg) var(--spacing-xl) var(--spacing-xl);\r\n      text-align: center;\r\n      background: var(--secondary-50);\r\n      border-top: 1px solid var(--secondary-100);\r\n    }\r\n\r\n    .footer-text {\r\n      font-size: var(--text-sm);\r\n      color: var(--secondary-600);\r\n      margin: 0;\r\n    }\r\n\r\n    .footer-link {\r\n      color: var(--primary-600);\r\n      text-decoration: none;\r\n      font-weight: 500;\r\n      transition: color 0.2s ease;\r\n\r\n      &:hover {\r\n        color: var(--primary-700);\r\n        text-decoration: underline;\r\n      }\r\n\r\n      &:focus {\r\n        outline: none;\r\n        color: var(--primary-700);\r\n        text-decoration: underline;\r\n      }\r\n    }\r\n\r\n    /* Responsive Design */\r\n    @media (max-width: 480px) {\r\n      .login-container {\r\n        padding: var(--spacing-md);\r\n      }\r\n\r\n      .login-card {\r\n        max-width: 100%;\r\n      }\r\n\r\n      .login-header {\r\n        padding: var(--spacing-xl) var(--spacing-lg) var(--spacing-lg);\r\n      }\r\n\r\n      .login-form {\r\n        padding: var(--spacing-lg);\r\n      }\r\n\r\n      .login-footer {\r\n        padding: var(--spacing-md) var(--spacing-lg) var(--spacing-lg);\r\n      }\r\n\r\n      .form-options {\r\n        flex-direction: column;\r\n        align-items: flex-start;\r\n        gap: var(--spacing-sm);\r\n      }\r\n\r\n      .forgot-password {\r\n        align-self: flex-end;\r\n      }\r\n    }\r\n\r\n    /* High Contrast Mode Support */\r\n    @media (prefers-contrast: high) {\r\n      .login-card {\r\n        border-width: 2px;\r\n      }\r\n\r\n      .checkbox-input {\r\n        border-width: 2px;\r\n      }\r\n    }\r\n\r\n    /* Reduced Motion Support */\r\n    @media (prefers-reduced-motion: reduce) {\r\n      .checkbox-input,\r\n      .forgot-link,\r\n      .footer-link {\r\n        transition: none;\r\n      }\r\n    }\r\n  `]\r\n})\r\nexport class LoginComponent {\r\n  loginForm: FormGroup;\r\n  loading = false;\r\n  error = '';\r\n  returnUrl: string = '/dashboard';\r\n\r\n  constructor(\r\n    private formBuilder: FormBuilder,\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private authService: AuthService\r\n  ) {\r\n    this.loginForm = this.formBuilder.group({\r\n      email: ['', [Validators.required, Validators.email]],\r\n      password: ['', [Validators.required]],\r\n      rememberMe: [false]\r\n    });\r\n\r\n    // Get return url from route parameters or default to '/'\r\n    this.route.queryParams.subscribe(params => {\r\n      this.returnUrl = params['returnUrl'] || '/dashboard';\r\n    });\r\n\r\n    // Redirect if already logged in\r\n    if (this.authService.isAuthenticated()) {\r\n      this.router.navigate([this.returnUrl]);\r\n    }\r\n  }\r\n\r\n  getFieldError(fieldName: string): string {\r\n    const field = this.loginForm.get(fieldName);\r\n    if (field && field.errors && field.touched) {\r\n      if (field.errors['required']) {\r\n        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;\r\n      }\r\n      if (field.errors['email']) {\r\n        return 'Please enter a valid email address';\r\n      }\r\n    }\r\n    return '';\r\n  }\r\n\r\n  onSubmit() {\r\n    if (this.loginForm.invalid) {\r\n      // Mark all fields as touched to trigger validation messages\r\n      Object.keys(this.loginForm.controls).forEach(key => {\r\n        const control = this.loginForm.get(key);\r\n        control?.markAsTouched();\r\n      });\r\n      return;\r\n    }\r\n\r\n    this.loading = true;\r\n    this.error = '';\r\n\r\n    this.authService.login(this.loginForm.value).subscribe({\r\n      next: () => {\r\n        this.router.navigate([this.returnUrl]);\r\n      },\r\n      error: (error: any) => {\r\n        this.error = error.message || 'An error occurred during login. Please try again.';\r\n        this.loading = false;\r\n      }\r\n    });\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBQ,IAAA,yBAAA,GAAA,QAAA,CAAA;AAAkD,IAAA,iBAAA,GAAA,GAAA;AAAC,IAAA,uBAAA;;;;;AAFrD,IAAA,yBAAA,GAAA,SAAA,CAAA;AACE,IAAA,iBAAA,CAAA;AACA,IAAA,qBAAA,GAAA,6CAAA,GAAA,GAAA,QAAA,CAAA;AACF,IAAA,uBAAA;;;;AAHqB,IAAA,qBAAA,OAAA,OAAA,OAAA;AACnB,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,OAAA,GAAA;AACkC,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,QAAA;;;;;AAkBlC,IAAA,yBAAA,GAAA,KAAA,CAAA;AAAyB,IAAA,iBAAA,CAAA;AAAkB,IAAA,uBAAA;;;;AAAlB,IAAA,oBAAA;AAAA,IAAA,4BAAA,OAAA,YAAA;;;AAiG3B,IAAO,sBAAP,MAAO,qBAAmB;EACrB,QAAQ;EACR,OAAO;EACP,UAAU;EACV,cAAc;EACd,eAAe;EACf,WAAW;EACX,WAAW;EAEpB,QAAa;EACL,UAAU;EACV,WAAW,CAAC,UAAc;EAAE;EAC5B,YAAY,MAAK;EAAE;EAE3B,WAAW,OAAU;AACnB,SAAK,QAAQ;EACf;EAEA,iBAAiB,IAAO;AACtB,SAAK,WAAW;EAClB;EAEA,kBAAkB,IAAO;AACvB,SAAK,YAAY;EACnB;EAEA,iBAAiB,YAAmB;AAClC,SAAK,WAAW;EAClB;EAEA,QAAQ,OAAY;AAClB,UAAM,SAAS,MAAM;AACrB,SAAK,QAAQ,OAAO;AACpB,SAAK,SAAS,KAAK,KAAK;EAC1B;EAEA,SAAM;AACJ,QAAI,CAAC,KAAK,SAAS;AACjB,WAAK,UAAU;AACf,WAAK,UAAS;IAChB;EACF;;qCAzCW,sBAAmB;EAAA;yEAAnB,sBAAmB,WAAA,CAAA,CAAA,iBAAA,CAAA,GAAA,QAAA,EAAA,OAAA,SAAA,MAAA,QAAA,SAAA,WAAA,aAAA,eAAA,cAAA,gBAAA,UAAA,YAAA,UAAA,WAAA,GAAA,UAAA,CAAA,6BA9HnB;IACT;MACE,SAAS;MACT,aAAa,WAAW,MAAM,oBAAmB;MACjD,OAAO;;GAEV,CAAA,GAAA,OAAA,GAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,YAAA,GAAA,CAAA,SAAA,cAAA,GAAA,OAAA,GAAA,MAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,cAAA,GAAA,iBAAA,SAAA,QAAA,MAAA,QAAA,eAAA,YAAA,SAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,cAAA,GAAA,KAAA,GAAA,CAAA,SAAA,sBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,oBAAA,CAAA,GAAA,UAAA,SAAA,6BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAEC,MAAA,yBAAA,GAAA,OAAA,CAAA;AACE,MAAA,qBAAA,GAAA,sCAAA,GAAA,GAAA,SAAA,CAAA;AAKA,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA2B,GAAA,SAAA,CAAA;AAMvB,MAAA,2BAAA,iBAAA,SAAA,4DAAA,QAAA;AAAA,QAAA,6BAAA,IAAA,OAAA,MAAA,MAAA,IAAA,QAAA;AAAA,eAAA;MAAA,CAAA;AACA,MAAA,qBAAA,SAAA,SAAA,oDAAA,QAAA;AAAA,eAAS,IAAA,QAAA,MAAA;MAAe,CAAA,EAAC,QAAA,SAAA,qDAAA;AAAA,eACjB,IAAA,OAAA;MAAQ,CAAA;AAPlB,MAAA,uBAAA,EAUC;AAGH,MAAA,qBAAA,GAAA,4CAAA,GAAA,GAAA,KAAA,CAAA;AAGF,MAAA,uBAAA;;;AAtBU,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,KAAA;AAeJ,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,SAAA,CAAA,CAAA,IAAA,YAAA;AARA,MAAA,qBAAA,MAAA,IAAA,OAAA,EAAc,QAAA,IAAA,IAAA,EACD,eAAA,IAAA,WAAA,EACc,YAAA,IAAA,QAAA;AAE3B,MAAA,2BAAA,WAAA,IAAA,KAAA;AAQJ,MAAA,oBAAA;AAAA,MAAA,wBAAA,IAAA,eAAA,IAAA,EAAA;;oBA7BM,cAAY,MAAE,aAAW,sBAAA,iBAAA,OAAA,GAAA,QAAA,CAAA,w3DAAA,EAAA,CAAA;;;sEA+HxB,qBAAmB,CAAA;UAlI/B;uBACW,mBAAiB,YACf,MAAI,SACP,CAAC,cAAc,WAAW,GAAC,WACzB;MACT;QACE,SAAS;QACT,aAAa,WAAW,MAAK,mBAAoB;QACjD,OAAO;;OAEV,UACS;;;;;;;;;;;;;;;;;;;;;;;;;KAyBT,QAAA,CAAA,o2DAAA,EAAA,CAAA;cA+FQ,OAAK,CAAA;UAAb;MACQ,MAAI,CAAA;UAAZ;MACQ,SAAO,CAAA;UAAf;MACQ,aAAW,CAAA;UAAnB;MACQ,cAAY,CAAA;UAApB;MACQ,UAAQ,CAAA;UAAhB;MACQ,UAAQ,CAAA;UAAhB;;;;6EAPU,qBAAmB,EAAA,WAAA,uBAAA,UAAA,wEAAA,YAAA,IAAA,CAAA;AAAA,GAAA;;;;;ACtGxB,IAAA,yBAAA,GAAA,OAAA,EAAA;;AACE,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,UAAA,EAAA,EAAwC,GAAA,QAAA,EAAA,EACG,GAAA,QAAA,EAAA;AAE7C,IAAA,uBAAA;;AACA,IAAA,yBAAA,GAAA,MAAA;AAAM,IAAA,iBAAA,CAAA;AAAW,IAAA,uBAAA,EAAO;;;;AAAlB,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,KAAA;;;AA+UV,IAAO,iBAAP,MAAO,gBAAc;EAOf;EACA;EACA;EACA;EATV;EACA,UAAU;EACV,QAAQ;EACR,YAAoB;EAEpB,YACU,aACA,OACA,QACA,aAAwB;AAHxB,SAAA,cAAA;AACA,SAAA,QAAA;AACA,SAAA,SAAA;AACA,SAAA,cAAA;AAER,SAAK,YAAY,KAAK,YAAY,MAAM;MACtC,OAAO,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,KAAK,CAAC;MACnD,UAAU,CAAC,IAAI,CAAC,WAAW,QAAQ,CAAC;MACpC,YAAY,CAAC,KAAK;KACnB;AAGD,SAAK,MAAM,YAAY,UAAU,YAAS;AACxC,WAAK,YAAY,OAAO,WAAW,KAAK;IAC1C,CAAC;AAGD,QAAI,KAAK,YAAY,gBAAe,GAAI;AACtC,WAAK,OAAO,SAAS,CAAC,KAAK,SAAS,CAAC;IACvC;EACF;EAEA,cAAc,WAAiB;AAC7B,UAAM,QAAQ,KAAK,UAAU,IAAI,SAAS;AAC1C,QAAI,SAAS,MAAM,UAAU,MAAM,SAAS;AAC1C,UAAI,MAAM,OAAO,UAAU,GAAG;AAC5B,eAAO,GAAG,UAAU,OAAO,CAAC,EAAE,YAAW,IAAK,UAAU,MAAM,CAAC,CAAC;MAClE;AACA,UAAI,MAAM,OAAO,OAAO,GAAG;AACzB,eAAO;MACT;IACF;AACA,WAAO;EACT;EAEA,WAAQ;AACN,QAAI,KAAK,UAAU,SAAS;AAE1B,aAAO,KAAK,KAAK,UAAU,QAAQ,EAAE,QAAQ,SAAM;AACjD,cAAM,UAAU,KAAK,UAAU,IAAI,GAAG;AACtC,iBAAS,cAAa;MACxB,CAAC;AACD;IACF;AAEA,SAAK,UAAU;AACf,SAAK,QAAQ;AAEb,SAAK,YAAY,MAAM,KAAK,UAAU,KAAK,EAAE,UAAU;MACrD,MAAM,MAAK;AACT,aAAK,OAAO,SAAS,CAAC,KAAK,SAAS,CAAC;MACvC;MACA,OAAO,CAAC,UAAc;AACpB,aAAK,QAAQ,MAAM,WAAW;AAC9B,aAAK,UAAU;MACjB;KACD;EACH;;qCAhEW,iBAAc,4BAAA,WAAA,GAAA,4BAAA,cAAA,GAAA,4BAAA,MAAA,GAAA,4BAAA,WAAA,CAAA;EAAA;yEAAd,iBAAc,WAAA,CAAA,CAAA,WAAA,CAAA,GAAA,OAAA,IAAA,MAAA,IAAA,QAAA,CAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,WAAA,aAAA,QAAA,QAAA,UAAA,gBAAA,GAAA,WAAA,GAAA,CAAA,kBAAA,SAAA,mBAAA,SAAA,gBAAA,KAAA,KAAA,wJAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,cAAA,GAAA,YAAA,WAAA,GAAA,CAAA,mBAAA,SAAA,QAAA,SAAA,SAAA,iBAAA,WAAA,SAAA,eAAA,oBAAA,GAAA,gBAAA,YAAA,UAAA,GAAA,CAAA,mBAAA,YAAA,QAAA,YAAA,SAAA,YAAA,WAAA,YAAA,eAAA,uBAAA,GAAA,gBAAA,YAAA,UAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,MAAA,eAAA,mBAAA,cAAA,QAAA,YAAA,GAAA,kBAAA,GAAA,UAAA,GAAA,CAAA,OAAA,eAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,QAAA,KAAA,GAAA,aAAA,GAAA,CAAA,QAAA,UAAA,WAAA,WAAA,QAAA,MAAA,eAAA,iBAAA,YAAA,kBAAA,GAAA,WAAA,YAAA,WAAA,WAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,QAAA,KAAA,GAAA,aAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,WAAA,aAAA,QAAA,QAAA,UAAA,gBAAA,GAAA,YAAA,GAAA,CAAA,MAAA,MAAA,MAAA,MAAA,KAAA,IAAA,GAAA,CAAA,MAAA,MAAA,MAAA,KAAA,MAAA,KAAA,MAAA,IAAA,GAAA,CAAA,MAAA,KAAA,MAAA,KAAA,MAAA,MAAA,MAAA,IAAA,CAAA,GAAA,UAAA,SAAA,wBAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAxWvB,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA6B,GAAA,OAAA,CAAA,EACH,GAAA,OAAA,CAAA,EAEI,GAAA,OAAA,CAAA;;AAEtB,MAAA,yBAAA,GAAA,OAAA,CAAA;AACE,MAAA,oBAAA,GAAA,QAAA,CAAA;AACF,MAAA,uBAAA;;AACA,MAAA,yBAAA,GAAA,MAAA,CAAA;AAAsB,MAAA,iBAAA,GAAA,aAAA;AAAW,MAAA,uBAAA,EAAK;AAExC,MAAA,yBAAA,GAAA,MAAA,CAAA;AACE,MAAA,iBAAA,GAAA,gBAAA;AACF,MAAA,uBAAA;AACA,MAAA,yBAAA,IAAA,KAAA,CAAA;AACE,MAAA,iBAAA,IAAA,uCAAA;AACF,MAAA,uBAAA,EAAI;AAIN,MAAA,qBAAA,IAAA,gCAAA,GAAA,GAAA,OAAA,CAAA;AAUA,MAAA,yBAAA,IAAA,QAAA,EAAA;AAAiD,MAAA,qBAAA,YAAA,SAAA,oDAAA;AAAA,eAAY,IAAA,SAAA;MAAU,CAAA;AAErE,MAAA,oBAAA,IAAA,mBAAA,EAAA,EASmB,IAAA,mBAAA,EAAA;AAenB,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA0B,IAAA,OAAA,EAAA;AAEtB,MAAA,oBAAA,IAAA,SAAA,EAAA;AAOA,MAAA,yBAAA,IAAA,SAAA,EAAA;AACE,MAAA,iBAAA,IAAA,eAAA;AACF,MAAA,uBAAA,EAAQ;AAGV,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA6B,IAAA,KAAA,EAAA;AAEzB,MAAA,iBAAA,IAAA,yBAAA;AACF,MAAA,uBAAA,EAAI,EACA;AAIR,MAAA,yBAAA,IAAA,cAAA,EAAA;AASE,MAAA,qBAAA,WAAA,SAAA,yDAAA;AAAA,eAAW,IAAA,SAAA;MAAU,CAAA;AAErB,MAAA,iBAAA,IAAA,WAAA;AACF,MAAA,uBAAA,EAAa;AAIf,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA0B,IAAA,KAAA,EAAA;AAEtB,MAAA,iBAAA,IAAA,0BAAA;AACA,MAAA,yBAAA,IAAA,KAAA,EAAA;AAAgC,MAAA,iBAAA,IAAA,4BAAA;AAA0B,MAAA,uBAAA,EAAI,EAC5D,EACA,EACF;;;AAhFwB,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,KAAA;AAUH,MAAA,oBAAA;AAAA,MAAA,qBAAA,aAAA,IAAA,SAAA;AAOrB,MAAA,oBAAA;AAAA,MAAA,qBAAA,gBAAA,IAAA,cAAA,OAAA,CAAA,EAAuC,YAAA,IAAA,OAAA,EACnB,YAAA,IAAA;AAWpB,MAAA,oBAAA;AAAA,MAAA,qBAAA,gBAAA,IAAA,cAAA,UAAA,CAAA,EAA0C,YAAA,IAAA,OAAA,EACtB,YAAA,IAAA;AAYhB,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,YAAA,IAAA,OAAA;AAoBJ,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,YAAA,IAAA,UAAA,OAAA,EAA8B,WAAA,IAAA,OAAA,EACX,aAAA,IAAA;;oBApFnB,cAAY,MAAE,qBAAmB,oBAAA,8BAAA,iBAAA,sBAAA,mBAAA,oBAAA,iBAAE,qBAAqB,eAAe,GAAA,QAAA,CAAA,43KAAA,EAAA,CAAA;;;sEA0WtE,gBAAc,CAAA;UA7W1B;uBACW,aAAW,YACT,MAAI,SACP,CAAC,cAAc,qBAAqB,qBAAqB,eAAe,GAAC,UACxE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAsGT,QAAA,CAAA,02JAAA,EAAA,CAAA;;;;6EAmQU,gBAAc,EAAA,WAAA,kBAAA,UAAA,4DAAA,YAAA,IAAA,CAAA;AAAA,GAAA;", "names": []}