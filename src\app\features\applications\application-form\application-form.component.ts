import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, FormArray, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { Application } from '../models/application.model';
import { Dependency } from '../../dependencies/models/dependency.model';
import { Vulnerability } from '../../security/models/vulnerability.model';
import { DependencyService } from '../../dependencies/services/dependency.service';
import { VulnerabilityService } from '../../security/services/vulnerability.service';
import { Observable } from 'rxjs';
import { DocumentUploadComponent } from '../../../shared/components/document-upload/document-upload.component';
import { DocumentFile } from '../../../shared/models/document.model';

@Component({
  selector: 'app-application-form',
  templateUrl: './application-form.component.html',
  styleUrls: ['./application-form.component.scss'],
  imports: [CommonModule, ReactiveFormsModule, RouterModule, FormsModule, DocumentUploadComponent],
  standalone: true
})
export class ApplicationFormComponent implements OnChanges, OnInit {
  @Input() application?: Application;
  @Output() formSubmit = new EventEmitter<Omit<Application, 'id'>>();
  @Output() cancel = new EventEmitter<void>();

  applicationForm: FormGroup;
  isEditMode = false;
  uploadedDocuments: DocumentFile[] = [];

  // For dependencies and vulnerabilities
  allDependencies$: Observable<Dependency[]>;
  allVulnerabilities$: Observable<Vulnerability[]>;
  selectedDependencies: string[] = [];
  selectedVulnerabilities: string[] = [];

  // For UI state
  activeTab = 'basic';

  constructor(
    private fb: FormBuilder,
    private dependencyService: DependencyService,
    private vulnerabilityService: VulnerabilityService,
    private router: Router
  ) {
    this.applicationForm = this.fb.group({
      name: ['', Validators.required],
      description: ['', Validators.required],
      type: ['Web Application', Validators.required],
      businessTeam: ['', Validators.required],
      status: ['Active', Validators.required],
      tags: this.fb.array([]),
      contactEmail: ['', Validators.email],
      supportUrl: [''],
      frontend: this.fb.group({
        framework: ['', Validators.required],
        version: ['', Validators.required],
        deploymentUrl: ['', Validators.required]
      }),
      backend: this.fb.group({
        framework: ['', Validators.required],
        version: ['', Validators.required],
        deploymentUrl: ['', Validators.required]
      }),
      database: this.fb.group({
        type: ['', Validators.required],
        version: ['', Validators.required],
        server: ['', Validators.required],
        dbName: ['', Validators.required],
        authType: ['Windows Authentication', Validators.required],
        username: ['', Validators.required]
      }),
      serverInfo: this.fb.array([]),
      teamInfo: this.fb.group({
        developers: this.fb.array([]),
        businessAnalysts: this.fb.array([]),
        businessUnit: [''],
        productOwner: [''],
        technicalLead: ['']
      }),
      repositoryUrl: [''],
      deploymentInfo: this.fb.array([]),
      documents: this.fb.array([]),
      dependencies: this.fb.array([]),
      vulnerabilities: this.fb.array([])
    });

    // Initialize observables
    this.allDependencies$ = this.dependencyService.getDependencies();
    this.allVulnerabilities$ = this.vulnerabilityService.getVulnerabilities();
  }

  ngOnInit(): void {
    // Add at least one deployment environment by default
    if (this.getDeploymentInfoArray().length === 0) {
      this.addDeploymentInfo();
    }

    // Restore form state if returning from dependency creation
    const savedFormState = sessionStorage.getItem('applicationFormState');
    const savedSelectedDependencies = sessionStorage.getItem('selectedDependencies');
    const savedSelectedVulnerabilities = sessionStorage.getItem('selectedVulnerabilities');
    const savedActiveTab = sessionStorage.getItem('activeTab');

    if (savedFormState && !this.application) {
      const formState = JSON.parse(savedFormState);
      this.applicationForm.patchValue(formState);
      
      // Restore arrays
      if (formState.tags && formState.tags.length > 0) {
        this.getTagsArray().clear();
        formState.tags.forEach((tag: string) => this.addTag(tag));
      }
      
      if (formState.serverInfo && formState.serverInfo.length > 0) {
        this.getServerInfoArray().clear();
        formState.serverInfo.forEach((server: string) => this.addServerInfo(server));
      }
      
      if (formState.deploymentInfo && formState.deploymentInfo.length > 0) {
        this.getDeploymentInfoArray().clear();
        formState.deploymentInfo.forEach((deployment: any) => this.addDeploymentInfo(deployment));
      }
      
      if (formState.teamInfo) {
        if (formState.teamInfo.developers && formState.teamInfo.developers.length > 0) {
          this.getDevelopersArray().clear();
          formState.teamInfo.developers.forEach((dev: string) => this.addDeveloper(dev));
        }
        
        if (formState.teamInfo.businessAnalysts && formState.teamInfo.businessAnalysts.length > 0) {
          this.getBusinessAnalystsArray().clear();
          formState.teamInfo.businessAnalysts.forEach((ba: string) => this.addBusinessAnalyst(ba));
        }
      }
    }

    if (savedSelectedDependencies && !this.application) {
      this.selectedDependencies = JSON.parse(savedSelectedDependencies);
    }

    if (savedSelectedVulnerabilities && !this.application) {
      this.selectedVulnerabilities = JSON.parse(savedSelectedVulnerabilities);
    }

    if (savedActiveTab && !this.application) {
      this.activeTab = savedActiveTab;
    }

    // Clear session storage after restoring
    sessionStorage.removeItem('applicationFormState');
    sessionStorage.removeItem('selectedDependencies');
    sessionStorage.removeItem('selectedVulnerabilities');
    sessionStorage.removeItem('activeTab');
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['application'] && this.application) {
      this.isEditMode = true;
      this.patchFormValues();
    }
  }

  patchFormValues() {
    if (!this.application) return;

    // Reset form arrays before patching
    this.getServerInfoArray().clear();
    this.getDevelopersArray().clear();
    this.getBusinessAnalystsArray().clear();
    this.getTagsArray().clear();
    this.getDeploymentInfoArray().clear();

    // Patch basic fields
    this.applicationForm.patchValue({
      name: this.application.name,
      description: this.application.description,
      type: this.application.type,
      businessTeam: this.application.businessTeam,
      status: this.application.status,
      contactEmail: this.application.contactEmail || '',
      supportUrl: this.application.supportUrl || '',
      frontend: this.application.frontend,
      backend: this.application.backend,
      database: this.application.database,
      repositoryUrl: this.application.repositoryUrl
    });

    // Patch server info array
    if (this.application.serverInfo && this.application.serverInfo.length > 0) {
      this.application.serverInfo.forEach(server => {
        this.addServerInfo(server);
      });
    }

    // Patch team info arrays
    if (this.application.teamInfo) {
      if (this.application.teamInfo.businessUnit) {
        this.applicationForm.get('teamInfo.businessUnit')?.setValue(this.application.teamInfo.businessUnit);
      }

      if (this.application.teamInfo.productOwner) {
        this.applicationForm.get('teamInfo.productOwner')?.setValue(this.application.teamInfo.productOwner);
      }

      if (this.application.teamInfo.technicalLead) {
        this.applicationForm.get('teamInfo.technicalLead')?.setValue(this.application.teamInfo.technicalLead);
      }

      if (this.application.teamInfo.developers && this.application.teamInfo.developers.length > 0) {
        this.application.teamInfo.developers.forEach(dev => {
          this.addDeveloper(dev);
        });
      }

      if (this.application.teamInfo.businessAnalysts && this.application.teamInfo.businessAnalysts.length > 0) {
        this.application.teamInfo.businessAnalysts.forEach(ba => {
          this.addBusinessAnalyst(ba);
        });
      }
    }

    // Patch tags
    if (this.application.tags && this.application.tags.length > 0) {
      this.application.tags.forEach(tag => {
        this.addTag(tag);
      });
    }

    // Patch deployment info
    if (this.application.deploymentInfo && this.application.deploymentInfo.length > 0) {
      this.application.deploymentInfo.forEach(deployment => {
        this.addDeploymentInfo(deployment);
      });
    } else {
      this.addDeploymentInfo();
    }

    // Set selected dependencies and vulnerabilities
    if (this.application.dependencies) {
      this.selectedDependencies = [...this.application.dependencies];
    }

    if (this.application.vulnerabilities) {
      this.selectedVulnerabilities = [...this.application.vulnerabilities];
    }

    // Handle documents
    if (this.application.documents && this.application.documents.length > 0) {
      // In a real app, we would load the actual files or file metadata
      // For now, we'll just create placeholder objects
      this.application.documents.forEach(doc => {
        const documentFile: DocumentFile = {
          id: doc.id,
          name: doc.title,
          title: doc.title,
          description: doc.description,
          type: doc.type,
          url: doc.url,
          size: 0,
          lastModified: doc.createdAt?.getTime() || Date.now()
        };
        this.uploadedDocuments.push(documentFile);
      });
    }
  }

  // Form array getters
  getServerInfoArray() {
    return this.applicationForm.get('serverInfo') as FormArray;
  }

  getDevelopersArray() {
    return this.applicationForm.get('teamInfo.developers') as FormArray;
  }

  getBusinessAnalystsArray() {
    return this.applicationForm.get('teamInfo.businessAnalysts') as FormArray;
  }

  getTagsArray() {
    return this.applicationForm.get('tags') as FormArray;
  }

  getDeploymentInfoArray() {
    return this.applicationForm.get('deploymentInfo') as FormArray;
  }

  // Server info methods
  addServerInfo(server: string = '') {
    this.getServerInfoArray().push(this.fb.control(server, Validators.required));
  }

  removeServerInfo(index: number) {
    this.getServerInfoArray().removeAt(index);
  }

  // Developer methods
  addDeveloper(developer: string = '') {
    this.getDevelopersArray().push(this.fb.control(developer, Validators.required));
  }

  removeDeveloper(index: number) {
    this.getDevelopersArray().removeAt(index);
  }

  // Business analyst methods
  addBusinessAnalyst(analyst: string = '') {
    this.getBusinessAnalystsArray().push(this.fb.control(analyst, Validators.required));
  }

  removeBusinessAnalyst(index: number) {
    this.getBusinessAnalystsArray().removeAt(index);
  }

  // Tag methods
  addTag(tag: string = '') {
    this.getTagsArray().push(this.fb.control(tag, Validators.required));
  }

  removeTag(index: number) {
    this.getTagsArray().removeAt(index);
  }

  // Deployment info methods
  addDeploymentInfo(deploymentInfo?: { environment: string, url: string, deploymentDate: Date, version: string }) {
    this.getDeploymentInfoArray().push(
      this.fb.group({
        environment: [deploymentInfo?.environment || '', Validators.required],
        url: [deploymentInfo?.url || ''],
        deploymentDate: [deploymentInfo?.deploymentDate || new Date()],
        version: [deploymentInfo?.version || '']
      })
    );
  }

  removeDeploymentInfo(index: number) {
    this.getDeploymentInfoArray().removeAt(index);
  }



  // Dependency methods
  toggleDependency(dependencyId: string): void {
    const index = this.selectedDependencies.indexOf(dependencyId);
    if (index === -1) {
      this.selectedDependencies.push(dependencyId);
    } else {
      this.selectedDependencies.splice(index, 1);
    }
  }

  isDependencySelected(dependencyId: string): boolean {
    return this.selectedDependencies.includes(dependencyId);
  }

  // Navigate to create new dependency
  createNewDependency(): void {
    // Store current form state in session storage to restore when returning
    sessionStorage.setItem('applicationFormState', JSON.stringify(this.applicationForm.value));
    sessionStorage.setItem('selectedDependencies', JSON.stringify(this.selectedDependencies));
    sessionStorage.setItem('selectedVulnerabilities', JSON.stringify(this.selectedVulnerabilities));
    sessionStorage.setItem('activeTab', this.activeTab);
    
    // Navigate to dependency creation page
    this.router.navigate(['/dependencies/new']);
  }

  // Vulnerability methods
  toggleVulnerability(vulnerabilityId: string): void {
    const index = this.selectedVulnerabilities.indexOf(vulnerabilityId);
    if (index === -1) {
      this.selectedVulnerabilities.push(vulnerabilityId);
    } else {
      this.selectedVulnerabilities.splice(index, 1);
    }
  }

  isVulnerabilitySelected(vulnerabilityId: string): boolean {
    return this.selectedVulnerabilities.includes(vulnerabilityId);
  }

  // Tab navigation
  setActiveTab(tab: string): void {
    this.activeTab = tab;
  }

  onSubmit() {
    if (this.applicationForm.valid) {
      // Add the selected dependencies and vulnerabilities to the form value
      const formValue = this.applicationForm.value;
      formValue.dependencies = this.selectedDependencies;
      formValue.vulnerabilities = this.selectedVulnerabilities;

      // In a real app, we would process the uploaded documents here
      // For now, we'll just create placeholder document objects
      formValue.documents = this.uploadedDocuments.map((doc, index) => {
        return {
          id: doc.id || `doc-${index}`,
          title: doc.name, // Use filename as title
          description: '', // Empty description
          type: doc.type || 'Other',
          url: doc.url || '#', // In a real app, we would upload the file and get a URL
          createdAt: new Date()
        };
      });

      this.formSubmit.emit(formValue);
    }
  }

  onCancel() {
    this.cancel.emit();
  }


}