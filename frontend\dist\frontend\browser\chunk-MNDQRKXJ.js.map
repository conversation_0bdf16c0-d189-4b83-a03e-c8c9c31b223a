{"version": 3, "sources": ["src/app/modules/tech-stack/tech-stack.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\n\nexport const routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./tech-stack.component').then(m => m.TechStackComponent)\n  }\n];\n"], "mappings": ";;;;;AAEO,IAAM,SAAiB;EAC5B;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAAwB,EAAE,KAAK,OAAK,EAAE,kBAAkB;;;", "names": []}