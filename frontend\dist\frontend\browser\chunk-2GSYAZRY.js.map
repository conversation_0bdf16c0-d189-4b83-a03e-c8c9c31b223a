{"version": 3, "sources": ["src/app/modules/applications/components/stakeholder-modal/stakeholder-modal.component.ts"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';\nimport { SlideModalComponent } from '../../../../shared/components/slide-modal/slide-modal.component';\nimport { FormInputComponent } from '../../../../shared/components/form-input/form-input.component';\n\nexport interface StakeholderFormData {\n  name: string;\n  email: string;\n  role: string;\n  department: string;\n  responsibility: string;\n  isPrimary: boolean;\n}\n\n@Component({\n  selector: 'app-stakeholder-modal',\n  standalone: true,\n  imports: [CommonModule, ReactiveFormsModule, SlideModalComponent, FormInputComponent],\n  template: `\n    <app-slide-modal\n      [isOpen]=\"isOpen\"\n      [title]=\"editMode ? 'Edit Stakeholder' : 'Add Stakeholder'\"\n      subtitle=\"Configure team member details\"\n      [loading]=\"loading\"\n      [canConfirm]=\"stakeholderForm.valid\"\n      confirmText=\"Save Stakeholder\"\n      (closed)=\"onClose()\"\n      (confirmed)=\"onSave()\"\n      (cancelled)=\"onClose()\"\n    >\n      <form [formGroup]=\"stakeholderForm\" class=\"stakeholder-form\">\n        <div class=\"form-grid\">\n          <app-form-input\n            label=\"Full Name\"\n            placeholder=\"e.g., John Doe\"\n            formControlName=\"name\"\n            [required]=\"true\"\n            [errorMessage]=\"getFieldError('name')\"\n          ></app-form-input>\n\n          <app-form-input\n            label=\"Email Address\"\n            placeholder=\"e.g., <EMAIL>\"\n            formControlName=\"email\"\n            type=\"email\"\n            [required]=\"true\"\n            [errorMessage]=\"getFieldError('email')\"\n          ></app-form-input>\n\n          <div class=\"form-group\">\n            <label class=\"form-label\">Role</label>\n            <select formControlName=\"role\" class=\"form-select\">\n              <option value=\"product_owner\">Product Owner</option>\n              <option value=\"developer\">Developer</option>\n              <option value=\"tech_lead\">Tech Lead</option>\n              <option value=\"architect\">Architect</option>\n              <option value=\"business_analyst\">Business Analyst</option>\n              <option value=\"project_manager\">Project Manager</option>\n              <option value=\"qa_engineer\">QA Engineer</option>\n              <option value=\"devops_engineer\">DevOps Engineer</option>\n              <option value=\"security_engineer\">Security Engineer</option>\n              <option value=\"stakeholder\">Stakeholder</option>\n            </select>\n          </div>\n\n          <app-form-input\n            label=\"Department\"\n            placeholder=\"e.g., Engineering, Product\"\n            formControlName=\"department\"\n            [required]=\"true\"\n            [errorMessage]=\"getFieldError('department')\"\n          ></app-form-input>\n\n          <div class=\"form-group full-width\">\n            <label class=\"checkbox-label\">\n              <input\n                type=\"checkbox\"\n                formControlName=\"isPrimary\"\n                class=\"checkbox-input\"\n              >\n              <span class=\"checkbox-text\">Primary Contact</span>\n              <span class=\"checkbox-description\">This person is the primary contact for this application</span>\n            </label>\n          </div>\n\n          <div class=\"form-group full-width\">\n            <label class=\"form-label\">Responsibility</label>\n            <textarea\n              formControlName=\"responsibility\"\n              class=\"form-textarea\"\n              placeholder=\"Describe this person's role and responsibilities for the application...\"\n              rows=\"3\"\n            ></textarea>\n          </div>\n        </div>\n      </form>\n    </app-slide-modal>\n  `,\n  styles: [`\n    .stakeholder-form {\n      display: flex;\n      flex-direction: column;\n      gap: var(--spacing-lg);\n    }\n\n    .form-grid {\n      display: grid;\n      grid-template-columns: 1fr 1fr;\n      gap: var(--spacing-lg);\n    }\n\n    .form-group {\n      display: flex;\n      flex-direction: column;\n      gap: var(--spacing-sm);\n    }\n\n    .form-group.full-width {\n      grid-column: 1 / -1;\n    }\n\n    .form-label {\n      font-size: var(--text-sm);\n      font-weight: 500;\n      color: var(--secondary-700);\n    }\n\n    .form-select,\n    .form-textarea {\n      border: 1px solid var(--secondary-300);\n      border-radius: var(--radius-md);\n      background: white;\n      font-size: var(--text-sm);\n      color: var(--secondary-800);\n      transition: all 0.2s ease;\n\n      &:focus {\n        outline: none;\n        border-color: var(--primary-500);\n        box-shadow: 0 0 0 3px var(--primary-100);\n      }\n    }\n\n    .form-select {\n      height: 40px;\n      padding: 0 var(--spacing-md);\n      cursor: pointer;\n      background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e\");\n      background-position: right var(--spacing-sm) center;\n      background-repeat: no-repeat;\n      background-size: 16px;\n      padding-right: 40px;\n      appearance: none;\n    }\n\n    .form-textarea {\n      padding: var(--spacing-md);\n      resize: vertical;\n      min-height: 80px;\n      font-family: inherit;\n    }\n\n    .checkbox-label {\n      display: flex;\n      align-items: flex-start;\n      gap: var(--spacing-sm);\n      cursor: pointer;\n      padding: var(--spacing-md);\n      border: 1px solid var(--secondary-200);\n      border-radius: var(--radius-md);\n      transition: all 0.2s ease;\n\n      &:hover {\n        border-color: var(--primary-300);\n        background: var(--primary-25);\n      }\n    }\n\n    .checkbox-input {\n      margin: 0;\n      margin-top: 2px;\n    }\n\n    .checkbox-text {\n      font-size: var(--text-sm);\n      font-weight: 500;\n      color: var(--secondary-900);\n      margin-bottom: var(--spacing-xs);\n    }\n\n    .checkbox-description {\n      font-size: var(--text-xs);\n      color: var(--secondary-600);\n      display: block;\n    }\n\n    @media (max-width: 768px) {\n      .form-grid {\n        grid-template-columns: 1fr;\n        gap: var(--spacing-md);\n      }\n    }\n  `]\n})\nexport class StakeholderModalComponent implements OnInit {\n  @Input() isOpen = false;\n  @Input() editMode = false;\n  @Input() initialData: StakeholderFormData | null = null;\n  @Input() loading = false;\n\n  @Output() closed = new EventEmitter<void>();\n  @Output() saved = new EventEmitter<StakeholderFormData>();\n\n  stakeholderForm!: FormGroup;\n\n  constructor(private fb: FormBuilder) {}\n\n  ngOnInit(): void {\n    this.initializeForm();\n  }\n\n  ngOnChanges(): void {\n    if (this.stakeholderForm && this.initialData) {\n      this.stakeholderForm.patchValue(this.initialData);\n    }\n  }\n\n  private initializeForm(): void {\n    this.stakeholderForm = this.fb.group({\n      name: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      role: ['developer', [Validators.required]],\n      department: ['', [Validators.required]],\n      responsibility: [''],\n      isPrimary: [false]\n    });\n\n    if (this.initialData) {\n      this.stakeholderForm.patchValue(this.initialData);\n    }\n  }\n\n  getFieldError(fieldName: string): string {\n    const field = this.stakeholderForm.get(fieldName);\n    if (field && field.invalid && field.touched) {\n      if (field.errors?.['required']) {\n        return `${fieldName} is required`;\n      }\n      if (field.errors?.['minlength']) {\n        return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;\n      }\n      if (field.errors?.['email']) {\n        return 'Please enter a valid email address';\n      }\n    }\n    return '';\n  }\n\n  onClose(): void {\n    this.stakeholderForm.reset();\n    this.closed.emit();\n  }\n\n  onSave(): void {\n    if (this.stakeholderForm.valid) {\n      this.saved.emit(this.stakeholderForm.value);\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6MM,IAAO,4BAAP,MAAO,2BAAyB;EAWhB;EAVX,SAAS;EACT,WAAW;EACX,cAA0C;EAC1C,UAAU;EAET,SAAS,IAAI,aAAY;EACzB,QAAQ,IAAI,aAAY;EAElC;EAEA,YAAoB,IAAe;AAAf,SAAA,KAAA;EAAkB;EAEtC,WAAQ;AACN,SAAK,eAAc;EACrB;EAEA,cAAW;AACT,QAAI,KAAK,mBAAmB,KAAK,aAAa;AAC5C,WAAK,gBAAgB,WAAW,KAAK,WAAW;IAClD;EACF;EAEQ,iBAAc;AACpB,SAAK,kBAAkB,KAAK,GAAG,MAAM;MACnC,MAAM,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,UAAU,CAAC,CAAC,CAAC;MACzD,OAAO,CAAC,IAAI,CAAC,WAAW,UAAU,WAAW,KAAK,CAAC;MACnD,MAAM,CAAC,aAAa,CAAC,WAAW,QAAQ,CAAC;MACzC,YAAY,CAAC,IAAI,CAAC,WAAW,QAAQ,CAAC;MACtC,gBAAgB,CAAC,EAAE;MACnB,WAAW,CAAC,KAAK;KAClB;AAED,QAAI,KAAK,aAAa;AACpB,WAAK,gBAAgB,WAAW,KAAK,WAAW;IAClD;EACF;EAEA,cAAc,WAAiB;AAC7B,UAAM,QAAQ,KAAK,gBAAgB,IAAI,SAAS;AAChD,QAAI,SAAS,MAAM,WAAW,MAAM,SAAS;AAC3C,UAAI,MAAM,SAAS,UAAU,GAAG;AAC9B,eAAO,GAAG,SAAS;MACrB;AACA,UAAI,MAAM,SAAS,WAAW,GAAG;AAC/B,eAAO,GAAG,SAAS,qBAAqB,MAAM,OAAO,WAAW,EAAE,cAAc;MAClF;AACA,UAAI,MAAM,SAAS,OAAO,GAAG;AAC3B,eAAO;MACT;IACF;AACA,WAAO;EACT;EAEA,UAAO;AACL,SAAK,gBAAgB,MAAK;AAC1B,SAAK,OAAO,KAAI;EAClB;EAEA,SAAM;AACJ,QAAI,KAAK,gBAAgB,OAAO;AAC9B,WAAK,MAAM,KAAK,KAAK,gBAAgB,KAAK;IAC5C;EACF;;qCA/DW,4BAAyB,4BAAA,WAAA,CAAA;EAAA;yEAAzB,4BAAyB,WAAA,CAAA,CAAA,uBAAA,CAAA,GAAA,QAAA,EAAA,QAAA,UAAA,UAAA,YAAA,aAAA,eAAA,SAAA,UAAA,GAAA,SAAA,EAAA,QAAA,UAAA,OAAA,QAAA,GAAA,UAAA,CAAA,8BAAA,GAAA,OAAA,IAAA,MAAA,IAAA,QAAA,CAAA,CAAA,YAAA,iCAAA,eAAA,oBAAA,GAAA,UAAA,aAAA,aAAA,UAAA,SAAA,WAAA,YAAA,GAAA,CAAA,GAAA,oBAAA,GAAA,WAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,SAAA,aAAA,eAAA,kBAAA,mBAAA,QAAA,GAAA,YAAA,cAAA,GAAA,CAAA,SAAA,iBAAA,eAAA,8BAAA,mBAAA,SAAA,QAAA,SAAA,GAAA,YAAA,cAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,mBAAA,QAAA,GAAA,aAAA,GAAA,CAAA,SAAA,eAAA,GAAA,CAAA,SAAA,WAAA,GAAA,CAAA,SAAA,WAAA,GAAA,CAAA,SAAA,WAAA,GAAA,CAAA,SAAA,kBAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,CAAA,SAAA,aAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,CAAA,SAAA,mBAAA,GAAA,CAAA,SAAA,aAAA,GAAA,CAAA,SAAA,cAAA,eAAA,8BAAA,mBAAA,cAAA,GAAA,YAAA,cAAA,GAAA,CAAA,GAAA,cAAA,YAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,QAAA,YAAA,mBAAA,aAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,sBAAA,GAAA,CAAA,mBAAA,kBAAA,eAAA,2EAAA,QAAA,KAAA,GAAA,eAAA,CAAA,GAAA,UAAA,SAAA,mCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAzLlC,MAAA,yBAAA,GAAA,mBAAA,CAAA;AAOE,MAAA,qBAAA,UAAA,SAAA,uEAAA;AAAA,eAAU,IAAA,QAAA;MAAS,CAAA,EAAC,aAAA,SAAA,0EAAA;AAAA,eACP,IAAA,OAAA;MAAQ,CAAA,EAAC,aAAA,SAAA,0EAAA;AAAA,eACT,IAAA,QAAA;MAAS,CAAA;AAEtB,MAAA,yBAAA,GAAA,QAAA,CAAA,EAA6D,GAAA,OAAA,CAAA;AAEzD,MAAA,oBAAA,GAAA,kBAAA,CAAA,EAMkB,GAAA,kBAAA,CAAA;AAWlB,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAwB,GAAA,SAAA,CAAA;AACI,MAAA,iBAAA,GAAA,MAAA;AAAI,MAAA,uBAAA;AAC9B,MAAA,yBAAA,GAAA,UAAA,CAAA,EAAmD,GAAA,UAAA,CAAA;AACnB,MAAA,iBAAA,IAAA,eAAA;AAAa,MAAA,uBAAA;AAC3C,MAAA,yBAAA,IAAA,UAAA,CAAA;AAA0B,MAAA,iBAAA,IAAA,WAAA;AAAS,MAAA,uBAAA;AACnC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA0B,MAAA,iBAAA,IAAA,WAAA;AAAS,MAAA,uBAAA;AACnC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA0B,MAAA,iBAAA,IAAA,WAAA;AAAS,MAAA,uBAAA;AACnC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAiC,MAAA,iBAAA,IAAA,kBAAA;AAAgB,MAAA,uBAAA;AACjD,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAgC,MAAA,iBAAA,IAAA,iBAAA;AAAe,MAAA,uBAAA;AAC/C,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA4B,MAAA,iBAAA,IAAA,aAAA;AAAW,MAAA,uBAAA;AACvC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAgC,MAAA,iBAAA,IAAA,iBAAA;AAAe,MAAA,uBAAA;AAC/C,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAkC,MAAA,iBAAA,IAAA,mBAAA;AAAiB,MAAA,uBAAA;AACnD,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA4B,MAAA,iBAAA,IAAA,aAAA;AAAW,MAAA,uBAAA,EAAS,EACzC;AAGX,MAAA,oBAAA,IAAA,kBAAA,EAAA;AAQA,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAmC,IAAA,SAAA,EAAA;AAE/B,MAAA,oBAAA,IAAA,SAAA,EAAA;AAKA,MAAA,yBAAA,IAAA,QAAA,EAAA;AAA4B,MAAA,iBAAA,IAAA,iBAAA;AAAe,MAAA,uBAAA;AAC3C,MAAA,yBAAA,IAAA,QAAA,EAAA;AAAmC,MAAA,iBAAA,IAAA,yDAAA;AAAuD,MAAA,uBAAA,EAAO,EAC3F;AAGV,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAmC,IAAA,SAAA,CAAA;AACP,MAAA,iBAAA,IAAA,gBAAA;AAAc,MAAA,uBAAA;AACxC,MAAA,oBAAA,IAAA,YAAA,EAAA;AAMF,MAAA,uBAAA,EAAM,EACF,EACD;;;AA3EP,MAAA,qBAAA,UAAA,IAAA,MAAA,EAAiB,SAAA,IAAA,WAAA,qBAAA,iBAAA,EAC0C,WAAA,IAAA,OAAA,EAExC,cAAA,IAAA,gBAAA,KAAA;AAOb,MAAA,oBAAA;AAAA,MAAA,qBAAA,aAAA,IAAA,eAAA;AAMA,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,YAAA,IAAA,EAAiB,gBAAA,IAAA,cAAA,MAAA,CAAA;AASjB,MAAA,oBAAA;AAAA,MAAA,qBAAA,YAAA,IAAA,EAAiB,gBAAA,IAAA,cAAA,OAAA,CAAA;AAwBjB,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,YAAA,IAAA,EAAiB,gBAAA,IAAA,cAAA,YAAA,CAAA;;oBApDjB,cAAc,qBAAmB,oBAAA,gBAAA,8BAAA,sBAAA,8BAAA,4BAAA,iBAAA,sBAAA,mBAAA,oBAAA,iBAAE,qBAAqB,kBAAkB,GAAA,QAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;4DAAA,EAAA,CAAA;;;sEA2LzE,2BAAyB,CAAA;UA9LrC;uBACW,yBAAuB,YACrB,MAAI,SACP,CAAC,cAAc,qBAAqB,qBAAqB,kBAAkB,GAAC,UAC3E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA+ET,QAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAAA,EAAA,CAAA;uCA4GQ,QAAM,CAAA;UAAd;MACQ,UAAQ,CAAA;UAAhB;MACQ,aAAW,CAAA;UAAnB;MACQ,SAAO,CAAA;UAAf;MAES,QAAM,CAAA;UAAf;MACS,OAAK,CAAA;UAAd;;;;6EAPU,2BAAyB,EAAA,WAAA,6BAAA,UAAA,4FAAA,YAAA,IAAA,CAAA;AAAA,GAAA;", "names": []}