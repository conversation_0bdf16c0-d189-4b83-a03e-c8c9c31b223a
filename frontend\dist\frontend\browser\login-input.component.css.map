{"version": 3, "sources": ["src/app/modules/auth/components/login-input/login-input.component.ts"], "sourcesContent": ["\n    :host {\n      display: block;\n      width: 100%;\n    }\n\n    .form-group {\n      display: flex;\n      flex-direction: column;\n      gap: var(--spacing-sm);\n    }\n\n    .form-label {\n      font-size: var(--text-sm);\n      font-weight: 500;\n      color: var(--secondary-700);\n      display: flex;\n      align-items: center;\n      gap: var(--spacing-xs);\n    }\n\n    .required-indicator {\n      color: var(--error-500);\n      font-weight: 600;\n    }\n\n    .input-wrapper {\n      position: relative;\n    }\n\n    .form-input {\n      width: 100%;\n      height: 44px;\n      padding: 0 var(--spacing-md);\n      border: 1px solid var(--secondary-300);\n      border-radius: var(--radius-md);\n      background: white;\n      font-size: var(--text-sm);\n      color: var(--secondary-800);\n      transition: all 0.2s ease;\n      outline: none;\n\n      &::placeholder {\n        color: var(--secondary-400);\n      }\n\n      &:focus {\n        border-color: var(--primary-500);\n        box-shadow: 0 0 0 3px var(--primary-100);\n      }\n\n      &:disabled {\n        background: var(--secondary-50);\n        color: var(--secondary-500);\n        cursor: not-allowed;\n        border-color: var(--secondary-200);\n      }\n\n      &.error {\n        border-color: var(--error-500);\n        box-shadow: 0 0 0 3px var(--error-100);\n\n        &:focus {\n          border-color: var(--error-500);\n          box-shadow: 0 0 0 3px var(--error-100);\n        }\n      }\n    }\n\n    .error-message {\n      font-size: var(--text-sm);\n      color: var(--error-600);\n      margin: 0;\n      display: flex;\n      align-items: center;\n      gap: var(--spacing-xs);\n    }\n\n    /* High Contrast Mode Support */\n    @media (prefers-contrast: high) {\n      .form-input {\n        border-width: 2px;\n      }\n    }\n\n    /* Reduced Motion Support */\n    @media (prefers-reduced-motion: reduce) {\n      .form-input {\n        transition: none;\n      }\n    }\n  "], "mappings": ";AACI;AACE,WAAA;AACA,SAAA;;AAGF,CAAA;AACE,WAAA;AACA,kBAAA;AACA,OAAA,IAAA;;AAGF,CAAA;AACE,aAAA,IAAA;AACA,eAAA;AACA,SAAA,IAAA;AACA,WAAA;AACA,eAAA;AACA,OAAA,IAAA;;AAGF,CAAA;AACE,SAAA,IAAA;AACA,eAAA;;AAGF,CAAA;AACE,YAAA;;AAGF,CAAA;AACE,SAAA;AACA,UAAA;AACA,WAAA,EAAA,IAAA;AACA,UAAA,IAAA,MAAA,IAAA;AACA,iBAAA,IAAA;AACA,cAAA;AACA,aAAA,IAAA;AACA,SAAA,IAAA;AACA,cAAA,IAAA,KAAA;AACA,WAAA;;AAEA,CAZF,UAYE;AACE,SAAA,IAAA;;AAGF,CAhBF,UAgBE;AACE,gBAAA,IAAA;AACA,cAAA,EAAA,EAAA,EAAA,IAAA,IAAA;;AAGF,CArBF,UAqBE;AACE,cAAA,IAAA;AACA,SAAA,IAAA;AACA,UAAA;AACA,gBAAA,IAAA;;AAGF,CA5BF,UA4BE,CAAA;AACE,gBAAA,IAAA;AACA,cAAA,EAAA,EAAA,EAAA,IAAA,IAAA;;AAEA,CAhCJ,UAgCI,CAJF,KAIE;AACE,gBAAA,IAAA;AACA,cAAA,EAAA,EAAA,EAAA,IAAA,IAAA;;AAKN,CAAA;AACE,aAAA,IAAA;AACA,SAAA,IAAA;AACA,UAAA;AACA,WAAA;AACA,eAAA;AACA,OAAA,IAAA;;AAIF,OAAA,CAAA,gBAAA,EAAA;AACE,GAlDF;AAmDI,kBAAA;;;AAKJ,OAAA,CAAA,sBAAA,EAAA;AACE,GAzDF;AA0DI,gBAAA;;;", "names": []}