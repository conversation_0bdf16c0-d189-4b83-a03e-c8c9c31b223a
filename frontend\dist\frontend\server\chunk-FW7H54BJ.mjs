import './polyfills.server.mjs';
import {
  ApplicationsService
} from "./chunk-BBFG7BOY.mjs";
import {
  CardComponent
} from "./chunk-UJ4MSIYR.mjs";
import {
  DefaultValueAccessor,
  FormsModule,
  NgControlStatus,
  NgModel
} from "./chunk-22WSPHJT.mjs";
import {
  ButtonComponent
} from "./chunk-NVGHB2VF.mjs";
import {
  RouterLink,
  RouterModule
} from "./chunk-MUYKQ5QZ.mjs";
import {
  CommonModule,
  Component,
  NgForOf,
  NgIf,
  SlicePipe,
  Subject,
  debounceTime,
  distinctUntilChanged,
  setClassMetadata,
  takeUntil,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassMap,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnamespaceHTML,
  ɵɵnamespaceSVG,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind3,
  ɵɵproperty,
  ɵɵpureFunction0,
  ɵɵpureFunction1,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtextInterpolate2,
  ɵɵtwoWayBindingSet,
  ɵɵtwoWayListener,
  ɵɵtwoWayProperty
} from "./chunk-IPMSWJNG.mjs";
import "./chunk-5WKMABBB.mjs";

// src/app/modules/applications/applications-list/applications-list.component.ts
var _c0 = () => [1, 2, 3, 4, 5, 6];
var _c1 = (a0) => ["/applications", a0];
var _c2 = (a0) => ["/applications", a0, "edit"];
function ApplicationsListComponent_span_22_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 30);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.getActiveFilterCount());
  }
}
function ApplicationsListComponent_div_28_div_10_div_3_label_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "label", 49)(1, "input", 50);
    \u0275\u0275listener("change", function ApplicationsListComponent_div_28_div_10_div_3_label_2_Template_input_change_1_listener($event) {
      const option_r4 = \u0275\u0275restoreView(_r3).$implicit;
      const config_r5 = \u0275\u0275nextContext(2).$implicit;
      const ctx_r0 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r0.onMultiSelectChange(config_r5.key, option_r4.value, $event));
    });
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(2, "span", 51);
    \u0275\u0275text(3);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const option_r4 = ctx.$implicit;
    const config_r5 = \u0275\u0275nextContext(2).$implicit;
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275property("value", option_r4.value)("checked", ctx_r0.isOptionSelected(config_r5.key, option_r4.value));
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(option_r4.label);
  }
}
function ApplicationsListComponent_div_28_div_10_div_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 46)(1, "div", 47);
    \u0275\u0275template(2, ApplicationsListComponent_div_28_div_10_div_3_label_2_Template, 4, 3, "label", 48);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const config_r5 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance(2);
    \u0275\u0275property("ngForOf", config_r5.options);
  }
}
function ApplicationsListComponent_div_28_div_10_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 43)(1, "label", 44);
    \u0275\u0275text(2);
    \u0275\u0275elementEnd();
    \u0275\u0275template(3, ApplicationsListComponent_div_28_div_10_div_3_Template, 3, 1, "div", 45);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const config_r5 = ctx.$implicit;
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(config_r5.label);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", config_r5.type === "multiselect");
  }
}
function ApplicationsListComponent_div_28_Template(rf, ctx) {
  if (rf & 1) {
    const _r2 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 31)(1, "div", 32)(2, "div", 33)(3, "h3");
    \u0275\u0275text(4, "Filter Applications");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "button", 34);
    \u0275\u0275listener("click", function ApplicationsListComponent_div_28_Template_button_click_5_listener() {
      \u0275\u0275restoreView(_r2);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.closeFiltersOverlay());
    });
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(6, "svg", 35);
    \u0275\u0275element(7, "line", 36)(8, "line", 37);
    \u0275\u0275elementEnd()()();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(9, "div", 38);
    \u0275\u0275template(10, ApplicationsListComponent_div_28_div_10_Template, 4, 2, "div", 39);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(11, "div", 40)(12, "app-button", 41);
    \u0275\u0275listener("click", function ApplicationsListComponent_div_28_Template_app_button_click_12_listener() {
      \u0275\u0275restoreView(_r2);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.clearFilters());
    });
    \u0275\u0275text(13, " Clear All ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(14, "app-button", 42);
    \u0275\u0275listener("click", function ApplicationsListComponent_div_28_Template_app_button_click_14_listener() {
      \u0275\u0275restoreView(_r2);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.closeFiltersOverlay());
    });
    \u0275\u0275text(15, " Apply Filters ");
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275classProp("show", ctx_r0.showFiltersOverlay);
    \u0275\u0275advance(10);
    \u0275\u0275property("ngForOf", ctx_r0.filterConfigs);
  }
}
function ApplicationsListComponent_div_30_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275element(0, "div", 55);
  }
}
function ApplicationsListComponent_div_30_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 52)(1, "div", 53);
    \u0275\u0275template(2, ApplicationsListComponent_div_30_div_2_Template, 1, 0, "div", 54);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    \u0275\u0275advance(2);
    \u0275\u0275property("ngForOf", \u0275\u0275pureFunction0(1, _c0));
  }
}
function ApplicationsListComponent_div_31_Template(rf, ctx) {
  if (rf & 1) {
    const _r6 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 56)(1, "div", 57);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(2, "svg", 58);
    \u0275\u0275element(3, "circle", 59)(4, "line", 60)(5, "line", 61);
    \u0275\u0275elementEnd();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(6, "h3");
    \u0275\u0275text(7, "Failed to load applications");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(8, "p");
    \u0275\u0275text(9, "There was an error loading the applications list. Please try again.");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "app-button", 62);
    \u0275\u0275listener("click", function ApplicationsListComponent_div_31_Template_app_button_click_10_listener() {
      \u0275\u0275restoreView(_r6);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.loadApplications());
    });
    \u0275\u0275text(11, " Retry ");
    \u0275\u0275elementEnd()()();
  }
}
function ApplicationsListComponent_div_32_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 65)(1, "div", 66)(2, "h3", 67);
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "span", 68);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(6, "p", 69);
    \u0275\u0275text(7);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(8, "div", 70)(9, "span", 71);
    \u0275\u0275text(10);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(11, "span", 72);
    \u0275\u0275text(12);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(13, "span", 73);
    \u0275\u0275text(14);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(15, "div", 74)(16, "div", 75)(17, "span", 76);
    \u0275\u0275text(18, "Health");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(19, "span", 77);
    \u0275\u0275text(20);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(21, "div", 75)(22, "span", 76);
    \u0275\u0275text(23, "Security");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(24, "span", 77);
    \u0275\u0275text(25);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(26, "div", 78)(27, "app-button", 79);
    \u0275\u0275text(28, " View Details ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(29, "app-button", 80);
    \u0275\u0275text(30, " Edit ");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const app_r7 = ctx.$implicit;
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(app_r7.name);
    \u0275\u0275advance();
    \u0275\u0275classMap("status-" + app_r7.status.toLowerCase());
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", app_r7.status, " ");
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(app_r7.description);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(app_r7.owner);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(app_r7.department);
    \u0275\u0275advance();
    \u0275\u0275classMap("criticality-" + app_r7.criticality.toLowerCase());
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", app_r7.criticality, " ");
    \u0275\u0275advance(5);
    \u0275\u0275classMap(ctx_r0.getScoreClass(app_r7.healthScore));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1("", app_r7.healthScore, "%");
    \u0275\u0275advance(4);
    \u0275\u0275classMap(ctx_r0.getScoreClass(app_r7.securityScore));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1("", app_r7.securityScore, "%");
    \u0275\u0275advance(2);
    \u0275\u0275property("routerLink", \u0275\u0275pureFunction1(18, _c1, app_r7.id));
    \u0275\u0275advance(2);
    \u0275\u0275property("routerLink", \u0275\u0275pureFunction1(20, _c2, app_r7.id));
  }
}
function ApplicationsListComponent_div_32_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 63);
    \u0275\u0275template(1, ApplicationsListComponent_div_32_div_1_Template, 31, 22, "div", 64);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("ngForOf", ctx_r0.applicationsResponse == null ? null : ctx_r0.applicationsResponse.applications);
  }
}
function ApplicationsListComponent_div_33_tr_21_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "tr", 84)(1, "td", 85)(2, "div", 86)(3, "h4", 67);
    \u0275\u0275text(4);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "p", 87);
    \u0275\u0275text(6);
    \u0275\u0275pipe(7, "slice");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(8, "td");
    \u0275\u0275text(9);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "td");
    \u0275\u0275text(11);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(12, "td")(13, "span", 68);
    \u0275\u0275text(14);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(15, "td")(16, "span", 73);
    \u0275\u0275text(17);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(18, "td")(19, "span", 77);
    \u0275\u0275text(20);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(21, "td")(22, "span", 77);
    \u0275\u0275text(23);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(24, "td", 88)(25, "div", 89)(26, "app-button", 79);
    \u0275\u0275text(27, " View ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(28, "app-button", 80);
    \u0275\u0275text(29, " Edit ");
    \u0275\u0275elementEnd()()()();
  }
  if (rf & 2) {
    const app_r8 = ctx.$implicit;
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(app_r8.name);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate2("", \u0275\u0275pipeBind3(7, 19, app_r8.description, 0, 80), "", app_r8.description.length > 80 ? "..." : "", "");
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(app_r8.owner);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(app_r8.department);
    \u0275\u0275advance(2);
    \u0275\u0275classMap("status-" + app_r8.status.toLowerCase());
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", app_r8.status, " ");
    \u0275\u0275advance(2);
    \u0275\u0275classMap("criticality-" + app_r8.criticality.toLowerCase());
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", app_r8.criticality, " ");
    \u0275\u0275advance(2);
    \u0275\u0275classMap(ctx_r0.getScoreClass(app_r8.healthScore));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1("", app_r8.healthScore, "%");
    \u0275\u0275advance(2);
    \u0275\u0275classMap(ctx_r0.getScoreClass(app_r8.securityScore));
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1("", app_r8.securityScore, "%");
    \u0275\u0275advance(3);
    \u0275\u0275property("routerLink", \u0275\u0275pureFunction1(23, _c1, app_r8.id));
    \u0275\u0275advance(2);
    \u0275\u0275property("routerLink", \u0275\u0275pureFunction1(25, _c2, app_r8.id));
  }
}
function ApplicationsListComponent_div_33_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 81)(1, "table", 82)(2, "thead")(3, "tr")(4, "th");
    \u0275\u0275text(5, "Name");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "th");
    \u0275\u0275text(7, "Owner");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(8, "th");
    \u0275\u0275text(9, "Department");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "th");
    \u0275\u0275text(11, "Status");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(12, "th");
    \u0275\u0275text(13, "Criticality");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(14, "th");
    \u0275\u0275text(15, "Health");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(16, "th");
    \u0275\u0275text(17, "Security");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(18, "th");
    \u0275\u0275text(19, "Actions");
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(20, "tbody");
    \u0275\u0275template(21, ApplicationsListComponent_div_33_tr_21_Template, 30, 27, "tr", 83);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(21);
    \u0275\u0275property("ngForOf", ctx_r0.applicationsResponse == null ? null : ctx_r0.applicationsResponse.applications);
  }
}
function ApplicationsListComponent_div_34_Template(rf, ctx) {
  if (rf & 1) {
    const _r9 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 90)(1, "div", 91);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(2, "svg", 92);
    \u0275\u0275element(3, "rect", 93)(4, "circle", 94)(5, "path", 95);
    \u0275\u0275elementEnd();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(6, "h3");
    \u0275\u0275text(7, "No applications found");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(8, "p");
    \u0275\u0275text(9, "No applications match your current search and filter criteria.");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(10, "app-button", 96);
    \u0275\u0275listener("click", function ApplicationsListComponent_div_34_Template_app_button_click_10_listener() {
      \u0275\u0275restoreView(_r9);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.clearFilters());
    });
    \u0275\u0275text(11, " Clear Filters ");
    \u0275\u0275elementEnd()()();
  }
}
function ApplicationsListComponent_div_35_button_4_Template(rf, ctx) {
  if (rf & 1) {
    const _r11 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 101);
    \u0275\u0275listener("click", function ApplicationsListComponent_div_35_button_4_Template_button_click_0_listener() {
      const page_r12 = \u0275\u0275restoreView(_r11).$implicit;
      const ctx_r0 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r0.goToPage(page_r12));
    });
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const page_r12 = ctx.$implicit;
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275classProp("active", page_r12 === ctx_r0.currentPage);
    \u0275\u0275property("disabled", page_r12 === "...");
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", page_r12, " ");
  }
}
function ApplicationsListComponent_div_35_Template(rf, ctx) {
  if (rf & 1) {
    const _r10 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 97)(1, "app-button", 98);
    \u0275\u0275listener("click", function ApplicationsListComponent_div_35_Template_app_button_click_1_listener() {
      \u0275\u0275restoreView(_r10);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.goToPage(ctx_r0.currentPage - 1));
    });
    \u0275\u0275text(2, " Previous ");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "div", 99);
    \u0275\u0275template(4, ApplicationsListComponent_div_35_button_4_Template, 2, 4, "button", 100);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(5, "app-button", 98);
    \u0275\u0275listener("click", function ApplicationsListComponent_div_35_Template_app_button_click_5_listener() {
      \u0275\u0275restoreView(_r10);
      const ctx_r0 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r0.goToPage(ctx_r0.currentPage + 1));
    });
    \u0275\u0275text(6, " Next ");
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275property("disabled", ctx_r0.currentPage === 1);
    \u0275\u0275advance(3);
    \u0275\u0275property("ngForOf", ctx_r0.getPageNumbers());
    \u0275\u0275advance();
    \u0275\u0275property("disabled", ctx_r0.currentPage === ctx_r0.applicationsResponse.totalPages);
  }
}
var ApplicationsListComponent = class _ApplicationsListComponent {
  applicationsService;
  // Data properties
  applicationsResponse = null;
  isLoading = true;
  hasError = false;
  // View mode
  viewMode = "card";
  // Pagination
  currentPage = 1;
  pageSize = 12;
  // Search and filtering
  searchFilterState = { search: "", filters: {} };
  filterConfigs = [];
  showFiltersOverlay = false;
  // Reactive streams
  destroy$ = new Subject();
  searchFilter$ = new Subject();
  constructor(applicationsService) {
    this.applicationsService = applicationsService;
  }
  ngOnInit() {
    this.initializeFilters();
    this.setupSearchFilterStream();
    this.loadApplications();
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  initializeFilters() {
    this.applicationsService.getFilterOptions().pipe(takeUntil(this.destroy$)).subscribe((options) => {
      this.filterConfigs = [
        {
          key: "status",
          label: "Status",
          type: "multiselect",
          options: options.statuses.map((status) => ({
            value: status,
            label: status
          }))
        },
        {
          key: "criticality",
          label: "Criticality",
          type: "multiselect",
          options: options.criticalities.map((criticality) => ({
            value: criticality,
            label: criticality
          }))
        },
        {
          key: "owner",
          label: "Owner",
          type: "multiselect",
          options: options.owners.map((owner) => ({
            value: owner,
            label: owner
          }))
        },
        {
          key: "department",
          label: "Department",
          type: "multiselect",
          options: options.departments.map((dept) => ({
            value: dept,
            label: dept
          }))
        }
      ];
    });
  }
  setupSearchFilterStream() {
    this.searchFilter$.pipe(debounceTime(300), distinctUntilChanged((prev, curr) => JSON.stringify(prev) === JSON.stringify(curr)), takeUntil(this.destroy$)).subscribe((state) => {
      this.searchFilterState = state;
      this.currentPage = 1;
      this.loadApplications();
    });
  }
  loadApplications() {
    this.isLoading = true;
    this.hasError = false;
    const filter = {
      search: this.searchFilterState.search,
      status: this.searchFilterState.filters["status"],
      criticality: this.searchFilterState.filters["criticality"],
      owner: this.searchFilterState.filters["owner"],
      department: this.searchFilterState.filters["department"]
    };
    this.applicationsService.getApplications(this.currentPage, this.pageSize, filter).pipe(takeUntil(this.destroy$)).subscribe({
      next: (response) => {
        this.applicationsResponse = response;
        this.isLoading = false;
      },
      error: (error) => {
        console.error("Error loading applications:", error);
        this.hasError = true;
        this.isLoading = false;
      }
    });
  }
  onSearchFilterChange(state) {
    this.searchFilter$.next(state);
  }
  goToPage(page) {
    if (typeof page === "number" && page !== this.currentPage) {
      this.currentPage = page;
      this.loadApplications();
    }
  }
  getPageNumbers() {
    if (!this.applicationsResponse)
      return [];
    const totalPages = this.applicationsResponse.totalPages;
    const current = this.currentPage;
    const pages = [];
    if (totalPages <= 7) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      pages.push(1);
      if (current > 4) {
        pages.push("...");
      }
      const start = Math.max(2, current - 1);
      const end = Math.min(totalPages - 1, current + 1);
      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
      if (current < totalPages - 3) {
        pages.push("...");
      }
      if (totalPages > 1) {
        pages.push(totalPages);
      }
    }
    return pages;
  }
  getSubtitle() {
    if (!this.applicationsResponse)
      return "Loading...";
    const { total, applications } = this.applicationsResponse;
    const showing = applications.length;
    if (total === showing) {
      return `${total} application${total !== 1 ? "s" : ""}`;
    } else {
      return `Showing ${showing} of ${total} applications`;
    }
  }
  getScoreClass(score) {
    if (score >= 90)
      return "score-excellent";
    if (score >= 80)
      return "score-good";
    if (score >= 70)
      return "score-fair";
    return "score-poor";
  }
  toggleViewMode() {
    this.viewMode = this.viewMode === "card" ? "grid" : "card";
  }
  toggleFiltersOverlay() {
    this.showFiltersOverlay = !this.showFiltersOverlay;
  }
  closeFiltersOverlay() {
    this.showFiltersOverlay = false;
  }
  getActiveFilterCount() {
    let count = 0;
    Object.keys(this.searchFilterState.filters).forEach((key) => {
      const value = this.searchFilterState.filters[key];
      if (value && (Array.isArray(value) ? value.length > 0 : value !== "")) {
        count += Array.isArray(value) ? value.length : 1;
      }
    });
    return count;
  }
  isOptionSelected(key, value) {
    const filterValue = this.searchFilterState.filters[key];
    return filterValue && Array.isArray(filterValue) && filterValue.includes(value);
  }
  onMultiSelectChange(key, value, event) {
    if (!this.searchFilterState.filters[key]) {
      this.searchFilterState.filters[key] = [];
    }
    if (event.target.checked) {
      this.searchFilterState.filters[key] = [...this.searchFilterState.filters[key], value];
    } else {
      this.searchFilterState.filters[key] = this.searchFilterState.filters[key].filter((v) => v !== value);
    }
    this.onSearchFilterChange(this.searchFilterState);
  }
  clearFilters() {
    this.searchFilterState = { search: "", filters: {} };
    this.onSearchFilterChange(this.searchFilterState);
  }
  static \u0275fac = function ApplicationsListComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ApplicationsListComponent)(\u0275\u0275directiveInject(ApplicationsService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _ApplicationsListComponent, selectors: [["app-applications-list"]], decls: 36, vars: 17, consts: [[1, "applications-page"], [1, "page-header"], [1, "header-content"], [1, "header-info"], [1, "page-title"], [1, "page-subtitle"], [1, "header-actions"], ["variant", "primary", "leftIcon", "M12 4v16m8-8H4", "routerLink", "/applications/new"], [1, "applications-content"], [1, "controls-section"], [1, "search-controls"], [1, "search-input"], ["viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", 1, "search-icon"], ["cx", "11", "cy", "11", "r", "8"], ["d", "m21 21-4.35-4.35"], ["type", "text", "placeholder", "Search applications...", 1, "search-field", 3, "ngModelChange", "input", "ngModel"], [1, "control-buttons"], ["variant", "outline", "leftIcon", "M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z", 3, "click"], ["class", "filter-count", 4, "ngIf"], [1, "view-toggle"], ["variant", "ghost", "size", "sm", "leftIcon", "M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z", 3, "click"], ["variant", "ghost", "size", "sm", "leftIcon", "M3 4h18v2H3V4zm0 7h18v2H3v-2zm0 7h18v2H3v-2z", 3, "click"], ["class", "filters-overlay", 3, "show", 4, "ngIf"], [1, "applications-card", 3, "title", "subtitle"], ["class", "loading-state", 4, "ngIf"], ["class", "error-state", 4, "ngIf"], ["class", "applications-grid", 4, "ngIf"], ["class", "applications-table", 4, "ngIf"], ["class", "empty-state", 4, "ngIf"], ["class", "pagination", 4, "ngIf"], [1, "filter-count"], [1, "filters-overlay"], [1, "filters-content"], [1, "filters-header"], [1, "close-filters", 3, "click"], ["viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor"], ["x1", "18", "y1", "6", "x2", "6", "y2", "18"], ["x1", "6", "y1", "6", "x2", "18", "y2", "18"], [1, "filters-grid"], ["class", "filter-group", 4, "ngFor", "ngForOf"], [1, "filters-actions"], ["variant", "ghost", "size", "sm", 3, "click"], ["variant", "primary", "size", "sm", 3, "click"], [1, "filter-group"], [1, "filter-label"], ["class", "multiselect", 4, "ngIf"], [1, "multiselect"], [1, "multiselect-options"], ["class", "checkbox-option", 4, "ngFor", "ngForOf"], [1, "checkbox-option"], ["type", "checkbox", 3, "change", "value", "checked"], [1, "checkbox-label"], [1, "loading-state"], [1, "loading-grid"], ["class", "loading-item", 4, "ngFor", "ngForOf"], [1, "loading-item"], [1, "error-state"], [1, "error-content"], ["viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", 1, "error-icon"], ["cx", "12", "cy", "12", "r", "10"], ["x1", "12", "y1", "8", "x2", "12", "y2", "12"], ["x1", "12", "y1", "16", "x2", "12.01", "y2", "16"], ["variant", "primary", 3, "click"], [1, "applications-grid"], ["class", "application-item", 4, "ngFor", "ngForOf"], [1, "application-item"], [1, "app-header"], [1, "app-name"], [1, "app-status"], [1, "app-description"], [1, "app-meta"], [1, "app-owner"], [1, "app-department"], [1, "app-criticality"], [1, "app-scores"], [1, "score-item"], [1, "score-label"], [1, "score-value"], [1, "app-actions"], ["variant", "ghost", "size", "sm", 3, "routerLink"], ["variant", "outline", "size", "sm", 3, "routerLink"], [1, "applications-table"], [1, "table"], ["class", "table-row", 4, "ngFor", "ngForOf"], [1, "table-row"], [1, "app-name-cell"], [1, "app-name-content"], [1, "app-description-short"], [1, "actions-cell"], [1, "table-actions"], [1, "empty-state"], [1, "empty-content"], ["viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", 1, "empty-icon"], ["x", "3", "y", "3", "width", "18", "height", "18", "rx", "2", "ry", "2"], ["cx", "9", "cy", "9", "r", "2"], ["d", "M21 15l-3.086-3.086a2 2 0 00-2.828 0L6 21"], ["variant", "outline", 3, "click"], [1, "pagination"], ["variant", "ghost", "size", "sm", 3, "click", "disabled"], [1, "page-numbers"], ["class", "page-number", 3, "active", "disabled", "click", 4, "ngFor", "ngForOf"], [1, "page-number", 3, "click", "disabled"]], template: function ApplicationsListComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "div", 3)(4, "h1", 4);
      \u0275\u0275text(5, "Applications");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(6, "p", 5);
      \u0275\u0275text(7, " Manage and monitor all your applications in one place. ");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(8, "div", 6)(9, "app-button", 7);
      \u0275\u0275text(10, " Add Application ");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(11, "div", 8)(12, "div", 9)(13, "div", 10)(14, "div", 11);
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(15, "svg", 12);
      \u0275\u0275element(16, "circle", 13)(17, "path", 14);
      \u0275\u0275elementEnd();
      \u0275\u0275namespaceHTML();
      \u0275\u0275elementStart(18, "input", 15);
      \u0275\u0275twoWayListener("ngModelChange", function ApplicationsListComponent_Template_input_ngModelChange_18_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.searchFilterState.search, $event) || (ctx.searchFilterState.search = $event);
        return $event;
      });
      \u0275\u0275listener("input", function ApplicationsListComponent_Template_input_input_18_listener() {
        return ctx.onSearchFilterChange(ctx.searchFilterState);
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(19, "div", 16)(20, "app-button", 17);
      \u0275\u0275listener("click", function ApplicationsListComponent_Template_app_button_click_20_listener() {
        return ctx.toggleFiltersOverlay();
      });
      \u0275\u0275text(21, " Filters ");
      \u0275\u0275template(22, ApplicationsListComponent_span_22_Template, 2, 1, "span", 18);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(23, "div", 19)(24, "app-button", 20);
      \u0275\u0275listener("click", function ApplicationsListComponent_Template_app_button_click_24_listener() {
        return ctx.viewMode = "card";
      });
      \u0275\u0275text(25, " Cards ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(26, "app-button", 21);
      \u0275\u0275listener("click", function ApplicationsListComponent_Template_app_button_click_26_listener() {
        return ctx.viewMode = "grid";
      });
      \u0275\u0275text(27, " Grid ");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275template(28, ApplicationsListComponent_div_28_Template, 16, 3, "div", 22);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(29, "app-card", 23);
      \u0275\u0275template(30, ApplicationsListComponent_div_30_Template, 3, 2, "div", 24)(31, ApplicationsListComponent_div_31_Template, 12, 0, "div", 25)(32, ApplicationsListComponent_div_32_Template, 2, 1, "div", 26)(33, ApplicationsListComponent_div_33_Template, 22, 1, "div", 27)(34, ApplicationsListComponent_div_34_Template, 12, 0, "div", 28)(35, ApplicationsListComponent_div_35_Template, 7, 3, "div", 29);
      \u0275\u0275elementEnd()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(18);
      \u0275\u0275twoWayProperty("ngModel", ctx.searchFilterState.search);
      \u0275\u0275advance(2);
      \u0275\u0275classProp("active", ctx.showFiltersOverlay);
      \u0275\u0275advance(2);
      \u0275\u0275property("ngIf", ctx.getActiveFilterCount() > 0);
      \u0275\u0275advance(2);
      \u0275\u0275classProp("active", ctx.viewMode === "card");
      \u0275\u0275advance(2);
      \u0275\u0275classProp("active", ctx.viewMode === "grid");
      \u0275\u0275advance(2);
      \u0275\u0275property("ngIf", ctx.filterConfigs.length > 0);
      \u0275\u0275advance();
      \u0275\u0275property("title", "Applications List")("subtitle", ctx.getSubtitle());
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.isLoading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.hasError && !ctx.isLoading);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.isLoading && !ctx.hasError && ctx.viewMode === "card");
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.isLoading && !ctx.hasError && ctx.viewMode === "grid");
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.isLoading && !ctx.hasError && ctx.applicationsResponse && ctx.applicationsResponse.applications && ctx.applicationsResponse.applications.length === 0);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.applicationsResponse && ctx.applicationsResponse.totalPages > 1);
    }
  }, dependencies: [CommonModule, NgForOf, NgIf, SlicePipe, RouterModule, RouterLink, FormsModule, DefaultValueAccessor, NgControlStatus, NgModel, CardComponent, ButtonComponent], styles: ['\n\n.applications-page[_ngcontent-%COMP%] {\n  min-height: 100%;\n}\n.page-header[_ngcontent-%COMP%] {\n  margin-bottom: var(--spacing-xl);\n}\n.header-content[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: flex-start;\n  justify-content: space-between;\n  gap: var(--spacing-lg);\n}\n.header-info[_ngcontent-%COMP%] {\n  flex: 1;\n}\n.page-title[_ngcontent-%COMP%] {\n  margin: 0 0 var(--spacing-xs) 0;\n  font-size: var(--text-3xl);\n  font-weight: 700;\n  color: var(--secondary-900);\n}\n.page-subtitle[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: var(--text-base);\n  color: var(--secondary-600);\n  line-height: var(--leading-relaxed);\n}\n.header-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: var(--spacing-sm);\n  flex-shrink: 0;\n}\n.applications-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\n  gap: var(--spacing-lg);\n  padding: var(--spacing-lg);\n}\n.application-item[_ngcontent-%COMP%] {\n  padding: var(--spacing-lg);\n  border: 1px solid var(--secondary-200);\n  border-radius: var(--radius-lg);\n  background: white;\n  transition: all 0.2s ease;\n}\n.application-item[_ngcontent-%COMP%]:hover {\n  transform: translateY(-2px);\n  box-shadow: var(--shadow-lg);\n  border-color: var(--secondary-300);\n}\n.app-header[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: flex-start;\n  justify-content: space-between;\n  gap: var(--spacing-md);\n  margin-bottom: var(--spacing-md);\n}\n.app-name[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: var(--text-lg);\n  font-weight: 600;\n  color: var(--secondary-900);\n  flex: 1;\n}\n.app-status[_ngcontent-%COMP%] {\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border-radius: var(--radius-sm);\n  font-size: var(--text-xs);\n  font-weight: 500;\n  text-transform: uppercase;\n}\n.app-status.status-production[_ngcontent-%COMP%] {\n  background: var(--success-100);\n  color: var(--success-700);\n}\n.app-status.status-development[_ngcontent-%COMP%] {\n  background: var(--primary-100);\n  color: var(--primary-700);\n}\n.app-status.status-testing[_ngcontent-%COMP%] {\n  background: var(--warning-100);\n  color: var(--warning-700);\n}\n.app-status.status-deprecated[_ngcontent-%COMP%] {\n  background: var(--error-100);\n  color: var(--error-700);\n}\n.app-description[_ngcontent-%COMP%] {\n  margin: 0 0 var(--spacing-md) 0;\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n  line-height: var(--leading-relaxed);\n}\n.app-meta[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  gap: var(--spacing-md);\n  margin-bottom: var(--spacing-lg);\n  font-size: var(--text-sm);\n}\n.app-owner[_ngcontent-%COMP%] {\n  color: var(--secondary-700);\n  font-weight: 500;\n}\n.app-criticality[_ngcontent-%COMP%] {\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border-radius: var(--radius-sm);\n  font-size: var(--text-xs);\n  font-weight: 500;\n}\n.app-criticality.criticality-critical[_ngcontent-%COMP%] {\n  background: var(--error-100);\n  color: var(--error-700);\n}\n.app-criticality.criticality-high[_ngcontent-%COMP%] {\n  background: var(--warning-100);\n  color: var(--warning-700);\n}\n.app-criticality.criticality-medium[_ngcontent-%COMP%] {\n  background: var(--primary-100);\n  color: var(--primary-700);\n}\n.app-criticality.criticality-low[_ngcontent-%COMP%] {\n  background: var(--success-100);\n  color: var(--success-700);\n}\n.app-department[_ngcontent-%COMP%] {\n  color: var(--secondary-500);\n  font-size: var(--text-xs);\n  margin: 0 var(--spacing-sm);\n}\n.app-scores[_ngcontent-%COMP%] {\n  display: flex;\n  gap: var(--spacing-md);\n  margin-bottom: var(--spacing-md);\n}\n.score-item[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: var(--spacing-xs);\n}\n.score-label[_ngcontent-%COMP%] {\n  font-size: var(--text-xs);\n  color: var(--secondary-500);\n  font-weight: 500;\n}\n.score-value[_ngcontent-%COMP%] {\n  font-size: var(--text-sm);\n  font-weight: 600;\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border-radius: var(--radius-sm);\n}\n.score-value.score-excellent[_ngcontent-%COMP%] {\n  background: var(--success-100);\n  color: var(--success-700);\n}\n.score-value.score-good[_ngcontent-%COMP%] {\n  background: var(--primary-100);\n  color: var(--primary-700);\n}\n.score-value.score-fair[_ngcontent-%COMP%] {\n  background: var(--warning-100);\n  color: var(--warning-700);\n}\n.score-value.score-poor[_ngcontent-%COMP%] {\n  background: var(--error-100);\n  color: var(--error-700);\n}\n.app-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: var(--spacing-sm);\n}\n.controls-section[_ngcontent-%COMP%] {\n  margin-bottom: var(--spacing-lg);\n  position: relative;\n}\n.search-controls[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-md);\n  margin-bottom: var(--spacing-md);\n}\n.search-input[_ngcontent-%COMP%] {\n  position: relative;\n  flex: 1;\n  max-width: 400px;\n}\n.search-icon[_ngcontent-%COMP%] {\n  position: absolute;\n  left: var(--spacing-md);\n  top: 50%;\n  transform: translateY(-50%);\n  width: 16px;\n  height: 16px;\n  color: var(--secondary-400);\n  z-index: 1;\n}\n.search-field[_ngcontent-%COMP%] {\n  width: 100%;\n  height: 40px;\n  padding: 0 var(--spacing-md) 0 44px;\n  border: 1px solid var(--secondary-200);\n  border-radius: var(--radius-lg);\n  background: white;\n  font-size: var(--text-sm);\n  color: var(--secondary-800);\n  transition: all 0.2s ease;\n}\n.search-field[_ngcontent-%COMP%]::placeholder {\n  color: var(--secondary-400);\n}\n.search-field[_ngcontent-%COMP%]:focus {\n  outline: none;\n  border-color: var(--primary-500);\n  box-shadow: 0 0 0 3px var(--primary-100);\n}\n.control-buttons[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-md);\n}\n.filter-count[_ngcontent-%COMP%] {\n  background: var(--primary-500);\n  color: white;\n  border-radius: 50%;\n  width: 18px;\n  height: 18px;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 10px;\n  font-weight: 600;\n  margin-left: var(--spacing-xs);\n}\n.view-toggle[_ngcontent-%COMP%] {\n  display: flex;\n  border: 1px solid var(--secondary-200);\n  border-radius: var(--radius-md);\n  overflow: hidden;\n}\n.view-toggle[_ngcontent-%COMP%]   app-button[_ngcontent-%COMP%] {\n  border-radius: 0;\n  border: none;\n}\n.view-toggle[_ngcontent-%COMP%]   app-button[_ngcontent-%COMP%]:first-child {\n  border-right: 1px solid var(--secondary-200);\n}\n.view-toggle[_ngcontent-%COMP%]   app-button.active[_ngcontent-%COMP%] {\n  background: var(--primary-100);\n  color: var(--primary-700);\n}\n.filters-overlay[_ngcontent-%COMP%] {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  right: 0;\n  background: white;\n  border: 1px solid var(--secondary-200);\n  border-radius: var(--radius-lg);\n  box-shadow: var(--shadow-lg);\n  z-index: 50;\n  display: none;\n}\n.filters-overlay.show[_ngcontent-%COMP%] {\n  display: block;\n}\n.filters-content[_ngcontent-%COMP%] {\n  padding: var(--spacing-lg);\n}\n.filters-header[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: var(--spacing-lg);\n  padding-bottom: var(--spacing-md);\n  border-bottom: 1px solid var(--secondary-200);\n}\n.filters-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: var(--text-lg);\n  font-weight: 600;\n  color: var(--secondary-900);\n}\n.close-filters[_ngcontent-%COMP%] {\n  background: none;\n  border: none;\n  color: var(--secondary-400);\n  cursor: pointer;\n  padding: var(--spacing-xs);\n  border-radius: var(--radius-sm);\n  transition: color 0.2s ease;\n}\n.close-filters[_ngcontent-%COMP%]:hover {\n  color: var(--secondary-600);\n}\n.close-filters[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\n  width: 20px;\n  height: 20px;\n}\n.filters-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: var(--spacing-lg);\n  margin-bottom: var(--spacing-lg);\n}\n.filter-group[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-sm);\n}\n.filter-label[_ngcontent-%COMP%] {\n  font-size: var(--text-sm);\n  font-weight: 500;\n  color: var(--secondary-700);\n}\n.multiselect-options[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-xs);\n  max-height: 150px;\n  overflow-y: auto;\n  padding: var(--spacing-sm);\n  border: 1px solid var(--secondary-300);\n  border-radius: var(--radius-md);\n  background: white;\n}\n.checkbox-option[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n  cursor: pointer;\n  padding: var(--spacing-xs);\n  border-radius: var(--radius-sm);\n  transition: background-color 0.2s ease;\n}\n.checkbox-option[_ngcontent-%COMP%]:hover {\n  background: var(--neutral-100);\n}\n.checkbox-option[_ngcontent-%COMP%]   input[type=checkbox][_ngcontent-%COMP%] {\n  margin: 0;\n}\n.checkbox-label[_ngcontent-%COMP%] {\n  font-size: var(--text-sm);\n  color: var(--secondary-700);\n  flex: 1;\n}\n.filters-actions[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: flex-end;\n  gap: var(--spacing-sm);\n  padding-top: var(--spacing-md);\n  border-top: 1px solid var(--secondary-200);\n}\n.applications-table[_ngcontent-%COMP%] {\n  padding: var(--spacing-lg);\n  overflow-x: auto;\n}\n.table[_ngcontent-%COMP%] {\n  width: 100%;\n  border-collapse: collapse;\n  font-size: var(--text-sm);\n}\n.table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\n  text-align: left;\n  padding: var(--spacing-md);\n  border-bottom: 2px solid var(--secondary-200);\n  background: var(--neutral-50);\n  font-weight: 600;\n  color: var(--secondary-700);\n  white-space: nowrap;\n}\n.table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\n  padding: var(--spacing-md);\n  border-bottom: 1px solid var(--secondary-100);\n  vertical-align: top;\n}\n.table-row[_ngcontent-%COMP%] {\n  transition: background-color 0.2s ease;\n}\n.table-row[_ngcontent-%COMP%]:hover {\n  background: var(--neutral-50);\n}\n.app-name-cell[_ngcontent-%COMP%] {\n  min-width: 250px;\n}\n.app-name-content[_ngcontent-%COMP%]   .app-name[_ngcontent-%COMP%] {\n  margin: 0 0 var(--spacing-xs) 0;\n  font-size: var(--text-sm);\n  font-weight: 600;\n  color: var(--secondary-900);\n}\n.app-description-short[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: var(--text-xs);\n  color: var(--secondary-600);\n  line-height: var(--leading-relaxed);\n}\n.actions-cell[_ngcontent-%COMP%] {\n  width: 140px;\n}\n.table-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: var(--spacing-xs);\n}\n.loading-state[_ngcontent-%COMP%] {\n  padding: var(--spacing-lg);\n}\n.loading-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\n  gap: var(--spacing-lg);\n}\n.loading-item[_ngcontent-%COMP%] {\n  height: 200px;\n  background: var(--neutral-100);\n  border-radius: var(--radius-lg);\n  position: relative;\n  overflow: hidden;\n}\n.loading-item[_ngcontent-%COMP%]::after {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background:\n    linear-gradient(\n      90deg,\n      transparent,\n      rgba(255, 255, 255, 0.4),\n      transparent);\n  animation: _ngcontent-%COMP%_shimmer 1.5s infinite;\n}\n@keyframes _ngcontent-%COMP%_shimmer {\n  0% {\n    left: -100%;\n  }\n  100% {\n    left: 100%;\n  }\n}\n.error-state[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 300px;\n  padding: var(--spacing-xl);\n}\n.error-content[_ngcontent-%COMP%] {\n  text-align: center;\n  max-width: 400px;\n}\n.error-icon[_ngcontent-%COMP%] {\n  width: 64px;\n  height: 64px;\n  color: var(--error-500);\n  margin: 0 auto var(--spacing-lg);\n}\n.error-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin-bottom: var(--spacing-sm);\n  color: var(--secondary-900);\n}\n.error-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin-bottom: var(--spacing-lg);\n  color: var(--secondary-600);\n}\n.empty-state[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 300px;\n  padding: var(--spacing-xl);\n  grid-column: 1/-1;\n}\n.empty-content[_ngcontent-%COMP%] {\n  text-align: center;\n  max-width: 400px;\n}\n.empty-icon[_ngcontent-%COMP%] {\n  width: 64px;\n  height: 64px;\n  color: var(--secondary-400);\n  margin: 0 auto var(--spacing-lg);\n}\n.empty-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin-bottom: var(--spacing-sm);\n  color: var(--secondary-700);\n}\n.empty-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\n  margin-bottom: var(--spacing-lg);\n  color: var(--secondary-500);\n}\n.pagination[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: var(--spacing-md);\n  padding: var(--spacing-lg);\n  border-top: 1px solid var(--secondary-200);\n  margin-top: var(--spacing-lg);\n}\n.page-numbers[_ngcontent-%COMP%] {\n  display: flex;\n  gap: var(--spacing-xs);\n}\n.page-number[_ngcontent-%COMP%] {\n  padding: var(--spacing-sm) var(--spacing-md);\n  border: 1px solid var(--secondary-300);\n  background: white;\n  color: var(--secondary-700);\n  border-radius: var(--radius-md);\n  cursor: pointer;\n  font-size: var(--text-sm);\n  font-weight: 500;\n  transition: all 0.2s ease;\n}\n.page-number[_ngcontent-%COMP%]:hover:not(:disabled) {\n  background: var(--primary-50);\n  border-color: var(--primary-300);\n  color: var(--primary-700);\n}\n.page-number.active[_ngcontent-%COMP%] {\n  background: var(--primary-500);\n  border-color: var(--primary-500);\n  color: white;\n}\n.page-number[_ngcontent-%COMP%]:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n@media (max-width: 768px) {\n  .header-content[_ngcontent-%COMP%] {\n    flex-direction: column;\n    align-items: stretch;\n    gap: var(--spacing-md);\n  }\n  .header-actions[_ngcontent-%COMP%] {\n    justify-content: flex-end;\n  }\n  .search-controls[_ngcontent-%COMP%] {\n    flex-direction: column;\n    align-items: stretch;\n    gap: var(--spacing-md);\n  }\n  .search-input[_ngcontent-%COMP%] {\n    max-width: none;\n  }\n  .control-buttons[_ngcontent-%COMP%] {\n    justify-content: space-between;\n  }\n  .applications-grid[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-md);\n  }\n  .filters-grid[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-md);\n  }\n  .table[_ngcontent-%COMP%] {\n    font-size: var(--text-xs);\n  }\n  .table[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], \n   .table[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\n    padding: var(--spacing-sm);\n  }\n  .app-name-cell[_ngcontent-%COMP%] {\n    min-width: 200px;\n  }\n  .table-actions[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: var(--spacing-xs);\n  }\n}\n/*# sourceMappingURL=applications-list.component.css.map */'] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ApplicationsListComponent, [{
    type: Component,
    args: [{ selector: "app-applications-list", standalone: true, imports: [CommonModule, RouterModule, FormsModule, CardComponent, ButtonComponent], template: `
    <div class="applications-page">
      <div class="page-header">
        <div class="header-content">
          <div class="header-info">
            <h1 class="page-title">Applications</h1>
            <p class="page-subtitle">
              Manage and monitor all your applications in one place.
            </p>
          </div>
          <div class="header-actions">
            <app-button
              variant="primary"
              leftIcon="M12 4v16m8-8H4"
              routerLink="/applications/new"
            >
              Add Application
            </app-button>
          </div>
        </div>
      </div>

      <div class="applications-content">
        <!-- Search and Controls -->
        <div class="controls-section">
          <div class="search-controls">
            <div class="search-input">
              <svg class="search-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <circle cx="11" cy="11" r="8"></circle>
                <path d="m21 21-4.35-4.35"></path>
              </svg>
              <input
                type="text"
                placeholder="Search applications..."
                [(ngModel)]="searchFilterState.search"
                (input)="onSearchFilterChange(searchFilterState)"
                class="search-field"
              >
            </div>
            <div class="control-buttons">
              <app-button
                variant="outline"
                leftIcon="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.414A1 1 0 013 6.707V4z"
                (click)="toggleFiltersOverlay()"
                [class.active]="showFiltersOverlay"
              >
                Filters
                <span class="filter-count" *ngIf="getActiveFilterCount() > 0">{{ getActiveFilterCount() }}</span>
              </app-button>
              <div class="view-toggle">
                <app-button
                  variant="ghost"
                  size="sm"
                  [class.active]="viewMode === 'card'"
                  (click)="viewMode = 'card'"
                  leftIcon="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"
                >
                  Cards
                </app-button>
                <app-button
                  variant="ghost"
                  size="sm"
                  [class.active]="viewMode === 'grid'"
                  (click)="viewMode = 'grid'"
                  leftIcon="M3 4h18v2H3V4zm0 7h18v2H3v-2zm0 7h18v2H3v-2z"
                >
                  Grid
                </app-button>
              </div>
            </div>
          </div>

          <!-- Filters Overlay -->
          <div class="filters-overlay" [class.show]="showFiltersOverlay" *ngIf="filterConfigs.length > 0">
            <div class="filters-content">
              <div class="filters-header">
                <h3>Filter Applications</h3>
                <button class="close-filters" (click)="closeFiltersOverlay()">
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                  </svg>
                </button>
              </div>
              <div class="filters-grid">
                <div class="filter-group" *ngFor="let config of filterConfigs">
                  <label class="filter-label">{{ config.label }}</label>
                  <div *ngIf="config.type === 'multiselect'" class="multiselect">
                    <div class="multiselect-options">
                      <label class="checkbox-option" *ngFor="let option of config.options">
                        <input
                          type="checkbox"
                          [value]="option.value"
                          [checked]="isOptionSelected(config.key, option.value)"
                          (change)="onMultiSelectChange(config.key, option.value, $event)"
                        >
                        <span class="checkbox-label">{{ option.label }}</span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
              <div class="filters-actions">
                <app-button variant="ghost" size="sm" (click)="clearFilters()">
                  Clear All
                </app-button>
                <app-button variant="primary" size="sm" (click)="closeFiltersOverlay()">
                  Apply Filters
                </app-button>
              </div>
            </div>
          </div>
        </div>

        <!-- Applications List -->
        <app-card
          [title]="'Applications List'"
          [subtitle]="getSubtitle()"
          class="applications-card"
        >
          <!-- Loading State -->
          <div *ngIf="isLoading" class="loading-state">
            <div class="loading-grid">
              <div class="loading-item" *ngFor="let item of [1,2,3,4,5,6]"></div>
            </div>
          </div>

          <!-- Error State -->
          <div *ngIf="hasError && !isLoading" class="error-state">
            <div class="error-content">
              <svg class="error-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="8" x2="12" y2="12"></line>
                <line x1="12" y1="16" x2="12.01" y2="16"></line>
              </svg>
              <h3>Failed to load applications</h3>
              <p>There was an error loading the applications list. Please try again.</p>
              <app-button variant="primary" (click)="loadApplications()">
                Retry
              </app-button>
            </div>
          </div>

          <!-- Applications Card View -->
          <div *ngIf="!isLoading && !hasError && viewMode === 'card'" class="applications-grid">
            <div class="application-item" *ngFor="let app of applicationsResponse?.applications">
              <div class="app-header">
                <h3 class="app-name">{{ app.name }}</h3>
                <span class="app-status" [class]="'status-' + app.status.toLowerCase()">
                  {{ app.status }}
                </span>
              </div>
              <p class="app-description">{{ app.description }}</p>
              <div class="app-meta">
                <span class="app-owner">{{ app.owner }}</span>
                <span class="app-department">{{ app.department }}</span>
                <span class="app-criticality" [class]="'criticality-' + app.criticality.toLowerCase()">
                  {{ app.criticality }}
                </span>
              </div>
              <div class="app-scores">
                <div class="score-item">
                  <span class="score-label">Health</span>
                  <span class="score-value" [class]="getScoreClass(app.healthScore)">{{ app.healthScore }}%</span>
                </div>
                <div class="score-item">
                  <span class="score-label">Security</span>
                  <span class="score-value" [class]="getScoreClass(app.securityScore)">{{ app.securityScore }}%</span>
                </div>
              </div>
              <div class="app-actions">
                <app-button variant="ghost" size="sm" [routerLink]="['/applications', app.id]">
                  View Details
                </app-button>
                <app-button variant="outline" size="sm" [routerLink]="['/applications', app.id, 'edit']">
                  Edit
                </app-button>
              </div>
            </div>
          </div>

          <!-- Applications Grid View -->
          <div *ngIf="!isLoading && !hasError && viewMode === 'grid'" class="applications-table">
            <table class="table">
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Owner</th>
                  <th>Department</th>
                  <th>Status</th>
                  <th>Criticality</th>
                  <th>Health</th>
                  <th>Security</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let app of applicationsResponse?.applications" class="table-row">
                  <td class="app-name-cell">
                    <div class="app-name-content">
                      <h4 class="app-name">{{ app.name }}</h4>
                      <p class="app-description-short">{{ app.description | slice:0:80 }}{{ app.description.length > 80 ? '...' : '' }}</p>
                    </div>
                  </td>
                  <td>{{ app.owner }}</td>
                  <td>{{ app.department }}</td>
                  <td>
                    <span class="app-status" [class]="'status-' + app.status.toLowerCase()">
                      {{ app.status }}
                    </span>
                  </td>
                  <td>
                    <span class="app-criticality" [class]="'criticality-' + app.criticality.toLowerCase()">
                      {{ app.criticality }}
                    </span>
                  </td>
                  <td>
                    <span class="score-value" [class]="getScoreClass(app.healthScore)">{{ app.healthScore }}%</span>
                  </td>
                  <td>
                    <span class="score-value" [class]="getScoreClass(app.securityScore)">{{ app.securityScore }}%</span>
                  </td>
                  <td class="actions-cell">
                    <div class="table-actions">
                      <app-button variant="ghost" size="sm" [routerLink]="['/applications', app.id]">
                        View
                      </app-button>
                      <app-button variant="outline" size="sm" [routerLink]="['/applications', app.id, 'edit']">
                        Edit
                      </app-button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- Empty State -->
          <div *ngIf="!isLoading && !hasError && applicationsResponse && applicationsResponse.applications && applicationsResponse.applications.length === 0" class="empty-state">
            <div class="empty-content">
              <svg class="empty-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                <circle cx="9" cy="9" r="2"></circle>
                <path d="M21 15l-3.086-3.086a2 2 0 00-2.828 0L6 21"></path>
              </svg>
              <h3>No applications found</h3>
              <p>No applications match your current search and filter criteria.</p>
              <app-button variant="outline" (click)="clearFilters()">
                Clear Filters
              </app-button>
            </div>
          </div>

          <!-- Pagination -->
          <div *ngIf="applicationsResponse && applicationsResponse.totalPages > 1" class="pagination">
            <app-button
              variant="ghost"
              size="sm"
              [disabled]="currentPage === 1"
              (click)="goToPage(currentPage - 1)"
            >
              Previous
            </app-button>

            <div class="page-numbers">
              <button
                *ngFor="let page of getPageNumbers()"
                class="page-number"
                [class.active]="page === currentPage"
                [disabled]="page === '...'"
                (click)="goToPage(page)"
              >
                {{ page }}
              </button>
            </div>

            <app-button
              variant="ghost"
              size="sm"
              [disabled]="currentPage === applicationsResponse.totalPages"
              (click)="goToPage(currentPage + 1)"
            >
              Next
            </app-button>
          </div>
        </app-card>
      </div>
    </div>
  `, styles: ['/* angular:styles/component:scss;3d112ff58e61b5c77b27051781487cf258ed15f54bdcf0ff86da4eb64b37d661;C:/Users/<USER>/Documents/VibeCoding/AppCatalog/app-catalog/frontend/src/app/modules/applications/applications-list/applications-list.component.ts */\n.applications-page {\n  min-height: 100%;\n}\n.page-header {\n  margin-bottom: var(--spacing-xl);\n}\n.header-content {\n  display: flex;\n  align-items: flex-start;\n  justify-content: space-between;\n  gap: var(--spacing-lg);\n}\n.header-info {\n  flex: 1;\n}\n.page-title {\n  margin: 0 0 var(--spacing-xs) 0;\n  font-size: var(--text-3xl);\n  font-weight: 700;\n  color: var(--secondary-900);\n}\n.page-subtitle {\n  margin: 0;\n  font-size: var(--text-base);\n  color: var(--secondary-600);\n  line-height: var(--leading-relaxed);\n}\n.header-actions {\n  display: flex;\n  gap: var(--spacing-sm);\n  flex-shrink: 0;\n}\n.applications-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\n  gap: var(--spacing-lg);\n  padding: var(--spacing-lg);\n}\n.application-item {\n  padding: var(--spacing-lg);\n  border: 1px solid var(--secondary-200);\n  border-radius: var(--radius-lg);\n  background: white;\n  transition: all 0.2s ease;\n}\n.application-item:hover {\n  transform: translateY(-2px);\n  box-shadow: var(--shadow-lg);\n  border-color: var(--secondary-300);\n}\n.app-header {\n  display: flex;\n  align-items: flex-start;\n  justify-content: space-between;\n  gap: var(--spacing-md);\n  margin-bottom: var(--spacing-md);\n}\n.app-name {\n  margin: 0;\n  font-size: var(--text-lg);\n  font-weight: 600;\n  color: var(--secondary-900);\n  flex: 1;\n}\n.app-status {\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border-radius: var(--radius-sm);\n  font-size: var(--text-xs);\n  font-weight: 500;\n  text-transform: uppercase;\n}\n.app-status.status-production {\n  background: var(--success-100);\n  color: var(--success-700);\n}\n.app-status.status-development {\n  background: var(--primary-100);\n  color: var(--primary-700);\n}\n.app-status.status-testing {\n  background: var(--warning-100);\n  color: var(--warning-700);\n}\n.app-status.status-deprecated {\n  background: var(--error-100);\n  color: var(--error-700);\n}\n.app-description {\n  margin: 0 0 var(--spacing-md) 0;\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n  line-height: var(--leading-relaxed);\n}\n.app-meta {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  gap: var(--spacing-md);\n  margin-bottom: var(--spacing-lg);\n  font-size: var(--text-sm);\n}\n.app-owner {\n  color: var(--secondary-700);\n  font-weight: 500;\n}\n.app-criticality {\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border-radius: var(--radius-sm);\n  font-size: var(--text-xs);\n  font-weight: 500;\n}\n.app-criticality.criticality-critical {\n  background: var(--error-100);\n  color: var(--error-700);\n}\n.app-criticality.criticality-high {\n  background: var(--warning-100);\n  color: var(--warning-700);\n}\n.app-criticality.criticality-medium {\n  background: var(--primary-100);\n  color: var(--primary-700);\n}\n.app-criticality.criticality-low {\n  background: var(--success-100);\n  color: var(--success-700);\n}\n.app-department {\n  color: var(--secondary-500);\n  font-size: var(--text-xs);\n  margin: 0 var(--spacing-sm);\n}\n.app-scores {\n  display: flex;\n  gap: var(--spacing-md);\n  margin-bottom: var(--spacing-md);\n}\n.score-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: var(--spacing-xs);\n}\n.score-label {\n  font-size: var(--text-xs);\n  color: var(--secondary-500);\n  font-weight: 500;\n}\n.score-value {\n  font-size: var(--text-sm);\n  font-weight: 600;\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border-radius: var(--radius-sm);\n}\n.score-value.score-excellent {\n  background: var(--success-100);\n  color: var(--success-700);\n}\n.score-value.score-good {\n  background: var(--primary-100);\n  color: var(--primary-700);\n}\n.score-value.score-fair {\n  background: var(--warning-100);\n  color: var(--warning-700);\n}\n.score-value.score-poor {\n  background: var(--error-100);\n  color: var(--error-700);\n}\n.app-actions {\n  display: flex;\n  gap: var(--spacing-sm);\n}\n.controls-section {\n  margin-bottom: var(--spacing-lg);\n  position: relative;\n}\n.search-controls {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-md);\n  margin-bottom: var(--spacing-md);\n}\n.search-input {\n  position: relative;\n  flex: 1;\n  max-width: 400px;\n}\n.search-icon {\n  position: absolute;\n  left: var(--spacing-md);\n  top: 50%;\n  transform: translateY(-50%);\n  width: 16px;\n  height: 16px;\n  color: var(--secondary-400);\n  z-index: 1;\n}\n.search-field {\n  width: 100%;\n  height: 40px;\n  padding: 0 var(--spacing-md) 0 44px;\n  border: 1px solid var(--secondary-200);\n  border-radius: var(--radius-lg);\n  background: white;\n  font-size: var(--text-sm);\n  color: var(--secondary-800);\n  transition: all 0.2s ease;\n}\n.search-field::placeholder {\n  color: var(--secondary-400);\n}\n.search-field:focus {\n  outline: none;\n  border-color: var(--primary-500);\n  box-shadow: 0 0 0 3px var(--primary-100);\n}\n.control-buttons {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-md);\n}\n.filter-count {\n  background: var(--primary-500);\n  color: white;\n  border-radius: 50%;\n  width: 18px;\n  height: 18px;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 10px;\n  font-weight: 600;\n  margin-left: var(--spacing-xs);\n}\n.view-toggle {\n  display: flex;\n  border: 1px solid var(--secondary-200);\n  border-radius: var(--radius-md);\n  overflow: hidden;\n}\n.view-toggle app-button {\n  border-radius: 0;\n  border: none;\n}\n.view-toggle app-button:first-child {\n  border-right: 1px solid var(--secondary-200);\n}\n.view-toggle app-button.active {\n  background: var(--primary-100);\n  color: var(--primary-700);\n}\n.filters-overlay {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  right: 0;\n  background: white;\n  border: 1px solid var(--secondary-200);\n  border-radius: var(--radius-lg);\n  box-shadow: var(--shadow-lg);\n  z-index: 50;\n  display: none;\n}\n.filters-overlay.show {\n  display: block;\n}\n.filters-content {\n  padding: var(--spacing-lg);\n}\n.filters-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: var(--spacing-lg);\n  padding-bottom: var(--spacing-md);\n  border-bottom: 1px solid var(--secondary-200);\n}\n.filters-header h3 {\n  margin: 0;\n  font-size: var(--text-lg);\n  font-weight: 600;\n  color: var(--secondary-900);\n}\n.close-filters {\n  background: none;\n  border: none;\n  color: var(--secondary-400);\n  cursor: pointer;\n  padding: var(--spacing-xs);\n  border-radius: var(--radius-sm);\n  transition: color 0.2s ease;\n}\n.close-filters:hover {\n  color: var(--secondary-600);\n}\n.close-filters svg {\n  width: 20px;\n  height: 20px;\n}\n.filters-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: var(--spacing-lg);\n  margin-bottom: var(--spacing-lg);\n}\n.filter-group {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-sm);\n}\n.filter-label {\n  font-size: var(--text-sm);\n  font-weight: 500;\n  color: var(--secondary-700);\n}\n.multiselect-options {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-xs);\n  max-height: 150px;\n  overflow-y: auto;\n  padding: var(--spacing-sm);\n  border: 1px solid var(--secondary-300);\n  border-radius: var(--radius-md);\n  background: white;\n}\n.checkbox-option {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n  cursor: pointer;\n  padding: var(--spacing-xs);\n  border-radius: var(--radius-sm);\n  transition: background-color 0.2s ease;\n}\n.checkbox-option:hover {\n  background: var(--neutral-100);\n}\n.checkbox-option input[type=checkbox] {\n  margin: 0;\n}\n.checkbox-label {\n  font-size: var(--text-sm);\n  color: var(--secondary-700);\n  flex: 1;\n}\n.filters-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: var(--spacing-sm);\n  padding-top: var(--spacing-md);\n  border-top: 1px solid var(--secondary-200);\n}\n.applications-table {\n  padding: var(--spacing-lg);\n  overflow-x: auto;\n}\n.table {\n  width: 100%;\n  border-collapse: collapse;\n  font-size: var(--text-sm);\n}\n.table th {\n  text-align: left;\n  padding: var(--spacing-md);\n  border-bottom: 2px solid var(--secondary-200);\n  background: var(--neutral-50);\n  font-weight: 600;\n  color: var(--secondary-700);\n  white-space: nowrap;\n}\n.table td {\n  padding: var(--spacing-md);\n  border-bottom: 1px solid var(--secondary-100);\n  vertical-align: top;\n}\n.table-row {\n  transition: background-color 0.2s ease;\n}\n.table-row:hover {\n  background: var(--neutral-50);\n}\n.app-name-cell {\n  min-width: 250px;\n}\n.app-name-content .app-name {\n  margin: 0 0 var(--spacing-xs) 0;\n  font-size: var(--text-sm);\n  font-weight: 600;\n  color: var(--secondary-900);\n}\n.app-description-short {\n  margin: 0;\n  font-size: var(--text-xs);\n  color: var(--secondary-600);\n  line-height: var(--leading-relaxed);\n}\n.actions-cell {\n  width: 140px;\n}\n.table-actions {\n  display: flex;\n  gap: var(--spacing-xs);\n}\n.loading-state {\n  padding: var(--spacing-lg);\n}\n.loading-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\n  gap: var(--spacing-lg);\n}\n.loading-item {\n  height: 200px;\n  background: var(--neutral-100);\n  border-radius: var(--radius-lg);\n  position: relative;\n  overflow: hidden;\n}\n.loading-item::after {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background:\n    linear-gradient(\n      90deg,\n      transparent,\n      rgba(255, 255, 255, 0.4),\n      transparent);\n  animation: shimmer 1.5s infinite;\n}\n@keyframes shimmer {\n  0% {\n    left: -100%;\n  }\n  100% {\n    left: 100%;\n  }\n}\n.error-state {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 300px;\n  padding: var(--spacing-xl);\n}\n.error-content {\n  text-align: center;\n  max-width: 400px;\n}\n.error-icon {\n  width: 64px;\n  height: 64px;\n  color: var(--error-500);\n  margin: 0 auto var(--spacing-lg);\n}\n.error-content h3 {\n  margin-bottom: var(--spacing-sm);\n  color: var(--secondary-900);\n}\n.error-content p {\n  margin-bottom: var(--spacing-lg);\n  color: var(--secondary-600);\n}\n.empty-state {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  min-height: 300px;\n  padding: var(--spacing-xl);\n  grid-column: 1/-1;\n}\n.empty-content {\n  text-align: center;\n  max-width: 400px;\n}\n.empty-icon {\n  width: 64px;\n  height: 64px;\n  color: var(--secondary-400);\n  margin: 0 auto var(--spacing-lg);\n}\n.empty-content h3 {\n  margin-bottom: var(--spacing-sm);\n  color: var(--secondary-700);\n}\n.empty-content p {\n  margin-bottom: var(--spacing-lg);\n  color: var(--secondary-500);\n}\n.pagination {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: var(--spacing-md);\n  padding: var(--spacing-lg);\n  border-top: 1px solid var(--secondary-200);\n  margin-top: var(--spacing-lg);\n}\n.page-numbers {\n  display: flex;\n  gap: var(--spacing-xs);\n}\n.page-number {\n  padding: var(--spacing-sm) var(--spacing-md);\n  border: 1px solid var(--secondary-300);\n  background: white;\n  color: var(--secondary-700);\n  border-radius: var(--radius-md);\n  cursor: pointer;\n  font-size: var(--text-sm);\n  font-weight: 500;\n  transition: all 0.2s ease;\n}\n.page-number:hover:not(:disabled) {\n  background: var(--primary-50);\n  border-color: var(--primary-300);\n  color: var(--primary-700);\n}\n.page-number.active {\n  background: var(--primary-500);\n  border-color: var(--primary-500);\n  color: white;\n}\n.page-number:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n@media (max-width: 768px) {\n  .header-content {\n    flex-direction: column;\n    align-items: stretch;\n    gap: var(--spacing-md);\n  }\n  .header-actions {\n    justify-content: flex-end;\n  }\n  .search-controls {\n    flex-direction: column;\n    align-items: stretch;\n    gap: var(--spacing-md);\n  }\n  .search-input {\n    max-width: none;\n  }\n  .control-buttons {\n    justify-content: space-between;\n  }\n  .applications-grid {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-md);\n  }\n  .filters-grid {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-md);\n  }\n  .table {\n    font-size: var(--text-xs);\n  }\n  .table th,\n  .table td {\n    padding: var(--spacing-sm);\n  }\n  .app-name-cell {\n    min-width: 200px;\n  }\n  .table-actions {\n    flex-direction: column;\n    gap: var(--spacing-xs);\n  }\n}\n/*# sourceMappingURL=applications-list.component.css.map */\n'] }]
  }], () => [{ type: ApplicationsService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(ApplicationsListComponent, { className: "ApplicationsListComponent", filePath: "src/app/modules/applications/applications-list/applications-list.component.ts", lineNumber: 994 });
})();
export {
  ApplicationsListComponent
};
//# sourceMappingURL=chunk-FW7H54BJ.mjs.map
