import './polyfills.server.mjs';
import {
  destroyAngularServerApp,
  extractRoutesAndCreateRouteTree,
  getOrCreateAngularServerApp,
  main_server_default,
  setAngularAppManifest
} from "./chunk-TD6JD4J2.mjs";
import "./chunk-7KSDAUOT.mjs";
import "./chunk-EQ3AZOB3.mjs";
import "./chunk-22WSPHJT.mjs";
import "./chunk-MUYKQ5QZ.mjs";
import {
  resetCompiledComponents
} from "./chunk-IPMSWJNG.mjs";
import "./chunk-5WKMABBB.mjs";

// angular:main-server-inject-manifest:angular:main-server-inject-manifest
import manifest from "./angular-app-manifest.mjs";
setAngularAppManifest(manifest);
export {
  main_server_default as default,
  destroyAngularServerApp as \u0275destroyAngularServerApp,
  extractRoutesAndCreateRouteTree as \u0275extractRoutesAndCreateRouteTree,
  getOrCreateAngularServerApp as \u0275getOrCreateAngularServerApp,
  resetCompiledComponents as \u0275resetCompiledComponents
};
//# sourceMappingURL=main.server.mjs.map
