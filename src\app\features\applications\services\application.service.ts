import { Injectable } from '@angular/core';
import { Observable, of, switchMap } from 'rxjs';
import { Application } from '../models/application.model';
import { ApprovalService, ApprovalRequest } from '../../../core/services/approval.service';

@Injectable({
  providedIn: 'root'
})
export class ApplicationService {
  // Mock data for development - would be replaced with API calls
  private applications: Application[] = [
    {
      id: '1',
      name: 'HR Portal',
      description: 'Employee management system for HR department',
      type: 'Web Application',
      businessTeam: 'Human Resources',
      status: 'Active',
      frontend: {
        framework: 'Angular',
        version: '19.2.0',
        deploymentUrl: 'https://hr-portal.internal.company.com'
      },
      backend: {
        framework: '.NET Core',
        version: '8.0',
        deploymentUrl: 'https://hr-api.internal.company.com'
      },
      database: {
        type: 'SQL Server',
        version: '2022',
        server: 'SQLDB01',
        dbName: 'HRPortalDB',
        authType: 'Windows Authentication',
        username: 'hr_service_account'
      },
      serverInfo: ['APP-SERVER-01', 'APP-SERVER-02'],
      teamInfo: {
        developers: ['<PERSON> <PERSON>e', '<PERSON> <PERSON>'],
        businessAnalysts: ['Bob <PERSON>'],
        businessUnit: 'HR Technology'
      },
      repositoryUrl: 'https://dev.azure.com/company/hr-portal',
      createdDate: new Date('2023-01-15'),
      updatedDate: new Date('2023-06-20')
    },
    {
      id: '2',
      name: 'Finance Dashboard',
      description: 'Financial reporting and analytics dashboard',
      type: 'Web Application',
      businessTeam: 'Finance',
      status: 'Active',
      frontend: {
        framework: 'React',
        version: '18.2.0',
        deploymentUrl: 'https://finance.internal.company.com'
      },
      backend: {
        framework: '.NET Core',
        version: '8.0',
        deploymentUrl: 'https://finance-api.internal.company.com'
      },
      database: {
        type: 'SQL Server',
        version: '2022',
        server: 'SQLDB02',
        dbName: 'FinanceDB',
        authType: 'SQL Authentication',
        username: 'finance_service_account'
      },
      serverInfo: ['APP-SERVER-03'],
      teamInfo: {
        developers: ['Alice Williams', 'Charlie Brown'],
        businessAnalysts: ['Diana Prince'],
        businessUnit: 'Finance Technology'
      },
      repositoryUrl: 'https://dev.azure.com/company/finance-dashboard',
      createdDate: new Date('2023-03-10'),
      updatedDate: new Date('2023-07-05')
    }
  ];

  constructor(private approvalService: ApprovalService) { }

  getApplications(): Observable<Application[]> {
    return of(this.applications);
  }

  getApplicationById(id: string): Observable<Application | undefined> {
    const application = this.applications.find(app => app.id === id);
    return of(application);
  }

  createApplication(application: Omit<Application, 'id'>): Observable<Application> {
    // Generate a simple ID for now
    const newApp: Application = {
      ...application,
      id: (this.applications.length + 1).toString(),
      createdDate: new Date(),
      updatedDate: new Date()
    };

    if (!newApp.id) {
      throw new Error('Failed to generate application ID');
    }

    // Create an approval request for the new application
    return this.approvalService.createApprovalRequest(
      newApp.id,
      newApp.name,
      'CREATE',
      newApp
    ).pipe(
      switchMap(() => {
        // Store the application in pending state
        this.applications.push(newApp);
        return of(newApp);
      })
    );
  }

  updateApplication(application: Application): Observable<Application> {
    if (!application.id) {
      throw new Error('Application ID is required for updates');
    }

    const applicationId = application.id;

    return this.approvalService.hasActiveRequest(applicationId).pipe(
      switchMap(hasActive => {
        if (hasActive) {
          throw new Error('An approval request is already pending for this application');
        }

        return this.approvalService.createApprovalRequest(
          applicationId,
          application.name,
          'UPDATE',
          application
        ).pipe(
          switchMap(() => {
            const index = this.applications.findIndex(app => app.id === application.id);
            if (index !== -1) {
              application.updatedDate = new Date();
              this.applications[index] = application;
            }
            return of(application);
          })
        );
      })
    );
  }

  deleteApplication(id: string): Observable<boolean> {
    const index = this.applications.findIndex(app => app.id === id);
    if (index !== -1) {
      this.applications.splice(index, 1);
      return of(true);
    }
    return of(false);
  }

  applyApprovedChanges(approvalRequest: ApprovalRequest): Observable<Application> {
    const { applicationId, requestType, changes } = approvalRequest;
    
    if (!changes) {
      throw new Error('Approval request changes are required');
    }
    
    const applicationChanges = changes as unknown as Application;
    
    if (requestType === 'CREATE') {
      // For creation, the application is already stored in pending state
      return of(applicationChanges);
    } else {
      // For updates, apply the changes
      const index = this.applications.findIndex(app => app.id === applicationId);
      if (index !== -1) {
        this.applications[index] = {
          ...applicationChanges,
          updatedDate: new Date()
        };
        return of(this.applications[index]);
      }
      throw new Error('Application not found');
    }
  }
}