import { Component, Input, Output, EventEmitter, OnInit, ViewChild, ElementRef, Inject, PLATFORM_ID } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';

export interface FileUploadData {
  file: File;
  type: string;
  description?: string;
}

export interface UploadedDocument {
  id?: number;
  fileName: string;
  fileSize: number;
  mimeType: string;
  type: string;
  description?: string;
  uploadedAt: Date;
  uploadedBy: string;
  icon: string;
}

@Component({
  selector: 'app-file-upload',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  template: `
    <div class="file-upload-container">
      <form [formGroup]="uploadForm" class="upload-form">
        <div class="form-row">
          <div class="form-group">
            <label class="form-label">Document Type</label>
            <select formControlName="type" class="form-select">
              <option value="">Select document type</option>
              <option value="technical_spec">Technical Specification</option>
              <option value="api_documentation">API Documentation</option>
              <option value="user_manual">User Manual</option>
              <option value="deployment_guide">Deployment Guide</option>
              <option value="architecture_diagram">Architecture Diagram</option>
              <option value="security_policy">Security Policy</option>
              <option value="compliance_report">Compliance Report</option>
              <option value="runbook">Runbook</option>
              <option value="troubleshooting">Troubleshooting Guide</option>
              <option value="changelog">Changelog</option>
            </select>
          </div>

          <div class="form-group">
            <label class="form-label">File</label>
            <div class="file-input-wrapper">
              <input
                #fileInput
                type="file"
                (change)="onFileSelected($event)"
                class="file-input"
                id="file-input"
                accept=".pdf,.doc,.docx,.txt,.md,.png,.jpg,.jpeg,.gif,.svg,.zip,.tar,.gz"
              >
              <label for="file-input" class="file-input-label">
                <svg class="upload-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                  <polyline points="7,10 12,15 17,10"></polyline>
                  <line x1="12" y1="15" x2="12" y2="3"></line>
                </svg>
                {{ selectedFile ? selectedFile.name : 'Choose file' }}
              </label>
            </div>
          </div>

          <div class="form-group">
            <button
              type="button"
              class="add-button"
              [disabled]="!canAdd"
              (click)="addDocument()"
            >
              Add Document
            </button>
          </div>
        </div>

        <div class="form-group full-width" *ngIf="showDescription">
          <label class="form-label">Description (Optional)</label>
          <textarea
            formControlName="description"
            class="form-textarea"
            placeholder="Brief description of the document..."
            rows="2"
          ></textarea>
        </div>
      </form>

      <!-- File info display -->
      <div class="file-info" *ngIf="selectedFile">
        <div class="file-details">
          <span class="file-name">{{ selectedFile.name }}</span>
          <span class="file-size">{{ formatFileSize(selectedFile.size) }}</span>
          <span class="file-type">{{ selectedFile.type || 'Unknown type' }}</span>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .file-upload-container {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-md);
    }

    .upload-form {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-md);
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr auto;
      gap: var(--spacing-md);
      align-items: end;
    }

    .form-group {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-sm);
    }

    .form-group.full-width {
      grid-column: 1 / -1;
    }

    .form-label {
      font-size: var(--text-sm);
      font-weight: 500;
      color: var(--secondary-700);
    }

    .form-select,
    .form-textarea {
      height: 40px;
      padding: 0 var(--spacing-md);
      border: 1px solid var(--secondary-300);
      border-radius: var(--radius-md);
      background: white;
      font-size: var(--text-sm);
      color: var(--secondary-800);
      transition: all 0.2s ease;

      &:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px var(--primary-100);
      }
    }

    .form-textarea {
      height: auto;
      padding: var(--spacing-sm) var(--spacing-md);
      resize: vertical;
      min-height: 60px;
    }

    .file-input-wrapper {
      position: relative;
    }

    .file-input {
      position: absolute;
      opacity: 0;
      width: 100%;
      height: 100%;
      cursor: pointer;
    }

    .file-input-label {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      height: 40px;
      padding: 0 var(--spacing-md);
      border: 1px solid var(--secondary-300);
      border-radius: var(--radius-md);
      background: white;
      font-size: var(--text-sm);
      color: var(--secondary-600);
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        border-color: var(--primary-400);
        background: var(--primary-50);
      }
    }

    .upload-icon {
      width: 16px;
      height: 16px;
      stroke-width: 2;
    }

    .add-button {
      height: 40px;
      padding: 0 var(--spacing-lg);
      background: var(--primary-600);
      color: white;
      border: none;
      border-radius: var(--radius-md);
      font-size: var(--text-sm);
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      white-space: nowrap;

      &:hover:not(:disabled) {
        background: var(--primary-700);
      }

      &:disabled {
        background: var(--secondary-300);
        cursor: not-allowed;
      }
    }

    .file-info {
      padding: var(--spacing-md);
      background: var(--secondary-50);
      border-radius: var(--radius-md);
      border: 1px solid var(--secondary-200);
    }

    .file-details {
      display: flex;
      gap: var(--spacing-md);
      align-items: center;
      flex-wrap: wrap;
    }

    .file-name {
      font-weight: 500;
      color: var(--secondary-900);
    }

    .file-size,
    .file-type {
      font-size: var(--text-sm);
      color: var(--secondary-600);
    }

    @media (max-width: 768px) {
      .form-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
      }

      .file-details {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
      }
    }
  `]
})
export class FileUploadComponent implements OnInit {
  @Input() showDescription = false;
  @Output() documentAdded = new EventEmitter<FileUploadData>();
  @ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>;

  uploadForm!: FormGroup;
  selectedFile: File | null = null;

  constructor(
    private fb: FormBuilder,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {}

  ngOnInit(): void {
    this.initializeForm();
  }

  private initializeForm(): void {
    this.uploadForm = this.fb.group({
      type: ['', [Validators.required]],
      description: ['']
    });
  }

  onFileSelected(event: Event): void {
    const target = event.target as HTMLInputElement;
    if (target.files && target.files.length > 0) {
      this.selectedFile = target.files[0];
    }
  }

  get canAdd(): boolean {
    return (this.uploadForm.get('type')?.valid ?? false) && this.selectedFile !== null;
  }

  addDocument(): void {
    if (this.canAdd && this.selectedFile) {
      const formData: FileUploadData = {
        file: this.selectedFile,
        type: this.uploadForm.get('type')?.value,
        description: this.uploadForm.get('description')?.value || undefined
      };

      this.documentAdded.emit(formData);
      this.resetForm();
    }
  }

  private resetForm(): void {
    this.uploadForm.reset();
    this.selectedFile = null;

    // Reset file input - only in browser environment
    if (isPlatformBrowser(this.platformId) && this.fileInput) {
      this.fileInput.nativeElement.value = '';
    }
  }

  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  getFileIcon(mimeType: string): string {
    if (mimeType.includes('pdf')) return '📄';
    if (mimeType.includes('word') || mimeType.includes('document')) return '📝';
    if (mimeType.includes('image')) return '🖼️';
    if (mimeType.includes('video')) return '🎥';
    if (mimeType.includes('audio')) return '🎵';
    if (mimeType.includes('zip') || mimeType.includes('archive')) return '📦';
    if (mimeType.includes('text')) return '📄';
    return '📁';
  }
}
