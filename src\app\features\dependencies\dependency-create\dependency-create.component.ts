import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { Dependency } from '../models/dependency.model';
import { DependencyService } from '../services/dependency.service';
import { DependencyFormComponent } from '../dependency-form/dependency-form.component';

@Component({
  selector: 'app-dependency-create',
  templateUrl: './dependency-create.component.html',
  styleUrls: ['./dependency-create.component.scss'],
  imports: [CommonModule, DependencyFormComponent],
  standalone: true
})
export class DependencyCreateComponent implements OnInit {
  cameFromApplicationForm: boolean = false;

  constructor(
    private dependencyService: DependencyService,
    private router: Router,
    private route: ActivatedRoute
  ) { }

  ngOnInit(): void {
    // Check if there's application form state in session storage
    this.cameFromApplicationForm = !!sessionStorage.getItem('applicationFormState');
  }
  
  onFormSubmit(dependencyData: Omit<Dependency, 'id'>): void {
    this.dependencyService.createDependency(dependencyData).subscribe({
      next: (createdDependency) => {
        if (this.cameFromApplicationForm) {
          // If came from application form, return to it
          this.router.navigate(['/applications/new']);
        } else {
          // Otherwise go to dependency detail
          this.router.navigate(['/dependencies', createdDependency.id]);
        }
      },
      error: (err) => {
        console.error('Failed to create dependency:', err);
      }
    });
  }
  
  onCancel(): void {
    if (this.cameFromApplicationForm) {
      // If came from application form, return to it
      this.router.navigate(['/applications/new']);
    } else {
      // Otherwise go back to dependencies list
      this.router.navigate(['/dependencies']);
    }
  }
}