import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ReactiveFormsModule, FormBuilder, FormGroup } from '@angular/forms';
import { CardComponent } from '../../shared/components/card/card.component';
import { ButtonComponent } from '../../shared/components/button/button.component';
import { TableComponent, TableColumn, TableAction } from '../../shared/components/table/table.component';

import { ChartComponent } from '../../shared/components/chart/chart.component';
import { SharedDependencyModalComponent, SharedDependencyFormData } from '../../shared/components/modals/shared-dependency-modal/shared-dependency-modal.component';

interface Dependency {
  id: number;
  name: string;
  type: string;
  version: string;
  criticality: string;
  status: string;
  isInternal: boolean;
  lastUpdated: Date;
  applications: number;
  vulnerabilities: number;
  description?: string;
}

@Component({
  selector: 'app-dependencies',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    CardComponent,
    ButtonComponent,
    TableComponent,
    ChartComponent,
    SharedDependencyModalComponent
  ],
  template: `
    <div class="dependencies-page">
      <div class="page-content">
        <div class="page-header">
          <div class="header-content">
            <h1>Dependencies</h1>
            <p class="page-subtitle">Manage and monitor application dependencies</p>
          </div>
          <div class="header-actions">
            <app-button
              variant="primary"
              leftIcon="M12 4v16m8-8H4"
              (clicked)="openAddModal()"
            >
              Add Dependency
            </app-button>
          </div>
        </div>

      <!-- Statistics Cards -->
      <div class="stats-grid">
        <app-card title="Total Dependencies">
          <div class="stat-content">
            <div class="stat-number">{{ stats.total }}</div>
            <div class="stat-change positive">+{{ stats.newThisMonth }} this month</div>
          </div>
        </app-card>

        <app-card title="Critical Dependencies">
          <div class="stat-content">
            <div class="stat-number critical">{{ stats.critical }}</div>
            <div class="stat-change">{{ stats.criticalPercentage }}% of total</div>
          </div>
        </app-card>

        <app-card title="Outdated">
          <div class="stat-content">
            <div class="stat-number warning">{{ stats.outdated }}</div>
            <div class="stat-change">Need updates</div>
          </div>
        </app-card>

        <app-card title="Vulnerabilities">
          <div class="stat-content">
            <div class="stat-number error">{{ stats.vulnerabilities }}</div>
            <div class="stat-change">Security issues</div>
          </div>
        </app-card>
      </div>

      <!-- Charts Section -->
      <div class="charts-section">
        <app-card title="Dependencies by Type" subtitle="Distribution of dependency types">
          <app-chart
            type="doughnut"
            [data]="typeChartData"
            [options]="chartOptions"
            height="300px"
          ></app-chart>
        </app-card>

        <app-card title="Criticality Distribution" subtitle="Dependencies by criticality level">
          <app-chart
            type="bar"
            [data]="criticalityChartData"
            [options]="chartOptions"
            height="300px"
          ></app-chart>
        </app-card>
      </div>

      <!-- Dependencies Table -->
      <app-card title="Dependencies" subtitle="All application dependencies">
        <div class="table-controls">
          <div class="search-controls">
            <input
              type="text"
              placeholder="Search dependencies..."
              class="search-input"
              (input)="onSearchInput($event)"
            >
            <select class="filter-select" (change)="onFilterChanged('type', $event)">
              <option value="">All Types</option>
              <option value="library">Library</option>
              <option value="framework">Framework</option>
              <option value="service">Service</option>
              <option value="database">Database</option>
              <option value="api">API</option>
              <option value="tool">Tool</option>
              <option value="infrastructure">Infrastructure</option>
            </select>
            <select class="filter-select" (change)="onFilterChanged('criticality', $event)">
              <option value="">All Criticality</option>
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="critical">Critical</option>
            </select>
          </div>
        </div>

        <app-table
          [columns]="tableColumns"
          [data]="filteredDependencies"
          [actions]="tableActions"
          [loading]="loading"
          [sortable]="true"
          (sortChanged)="onSortChanged($event)"
        ></app-table>
      </app-card>

      <!-- Add/Edit Modal -->
      <app-shared-dependency-modal
        [isOpen]="showModal"
        [editMode]="editMode"
        [initialData]="editData"
        [loading]="modalLoading"
        [showApplicationSelection]="true"
        (closed)="closeModal()"
        (saved)="onDependencySaved($event)"
      ></app-shared-dependency-modal>
      </div>
    </div>
  `,
  styles: [`
    .dependencies-page {
      min-height: 100%;
    }

    .page-content {
      padding: var(--spacing-xl);
    }

    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: var(--spacing-xl);
    }

    .header-content h1 {
      margin: 0 0 var(--spacing-sm) 0;
      font-size: var(--text-3xl);
      font-weight: 700;
      color: var(--secondary-900);
    }

    .page-subtitle {
      margin: 0;
      font-size: var(--text-lg);
      color: var(--secondary-600);
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: var(--spacing-lg);
      margin-bottom: var(--spacing-xl);
    }

    .stat-content {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-sm);
      padding: var(--spacing-lg);
    }

    .stat-number {
      font-size: var(--text-3xl);
      font-weight: 700;
      color: var(--secondary-900);

      &.critical {
        color: var(--error-600);
      }

      &.warning {
        color: var(--warning-600);
      }

      &.error {
        color: var(--error-600);
      }
    }

    .stat-change {
      font-size: var(--text-sm);
      color: var(--secondary-600);

      &.positive {
        color: var(--success-600);
      }
    }

    .charts-section {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: var(--spacing-lg);
      margin-bottom: var(--spacing-xl);
    }

    .table-controls {
      margin-bottom: var(--spacing-lg);
    }

    .search-controls {
      display: flex;
      gap: var(--spacing-md);
      align-items: center;
    }

    .search-input,
    .filter-select {
      height: 40px;
      padding: 0 var(--spacing-md);
      border: 1px solid var(--secondary-300);
      border-radius: var(--radius-md);
      background: white;
      font-size: var(--text-sm);
      color: var(--secondary-800);
      transition: all 0.2s ease;

      &:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px var(--primary-100);
      }
    }

    .search-input {
      flex: 1;
      min-width: 200px;
    }

    .filter-select {
      min-width: 150px;
      cursor: pointer;
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
      background-position: right var(--spacing-sm) center;
      background-repeat: no-repeat;
      background-size: 16px;
      padding-right: 40px;
      appearance: none;
    }

    @media (max-width: 768px) {
      .page-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
      }

      .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
      }

      .charts-section {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
      }

      .search-controls {
        flex-direction: column;
        align-items: stretch;
      }

      .search-input,
      .filter-select {
        min-width: auto;
      }
    }
  `]
})
export class DependenciesComponent implements OnInit {
  dependencies: Dependency[] = [];
  filteredDependencies: Dependency[] = [];
  loading = false;

  // Modal state
  showModal = false;
  editMode = false;
  editData: SharedDependencyFormData | null = null;
  modalLoading = false;

  // Statistics
  stats = {
    total: 0,
    critical: 0,
    outdated: 0,
    vulnerabilities: 0,
    newThisMonth: 0,
    criticalPercentage: 0
  };

  // Chart data
  typeChartData: any = null;
  criticalityChartData: any = null;
  chartOptions = {
    responsive: true,
    maintainAspectRatio: false
  };

  // Table configuration
  tableColumns: TableColumn[] = [
    { key: 'name', label: 'Name', sortable: true },
    { key: 'type', label: 'Type', type: 'badge', sortable: true },
    { key: 'version', label: 'Version', sortable: true },
    { key: 'criticality', label: 'Criticality', type: 'badge', sortable: true },
    { key: 'status', label: 'Status', type: 'badge', sortable: true },
    { key: 'applications', label: 'Apps', type: 'number', sortable: true },
    { key: 'vulnerabilities', label: 'Vulnerabilities', type: 'number', sortable: true },
    { key: 'lastUpdated', label: 'Last Updated', type: 'date', sortable: true }
  ];

  tableActions: TableAction[] = [
    {
      label: 'Edit',
      icon: 'M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7',
      variant: 'ghost',
      action: (item) => this.editDependency(item)
    },
    {
      label: 'Delete',
      icon: 'M3 6h18m-2 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2',
      variant: 'error',
      action: (item) => this.deleteDependency(item)
    }
  ];



  constructor(private fb: FormBuilder) {}

  ngOnInit(): void {
    this.loadDependencies();
  }

  private loadDependencies(): void {
    this.loading = true;

    // Mock data - replace with actual API call
    setTimeout(() => {
      this.dependencies = this.generateMockDependencies();
      this.filteredDependencies = [...this.dependencies];
      this.updateStats();
      this.updateChartData();
      this.loading = false;
    }, 1000);
  }

  private generateMockDependencies(): Dependency[] {
    const types = ['library', 'framework', 'service', 'database', 'api', 'tool', 'infrastructure'];
    const criticalities = ['low', 'medium', 'high', 'critical'];
    const statuses = ['active', 'deprecated', 'end_of_life', 'security_risk', 'update_required'];

    return Array.from({ length: 50 }, (_, i) => ({
      id: i + 1,
      name: `Dependency ${i + 1}`,
      type: types[Math.floor(Math.random() * types.length)],
      version: `${Math.floor(Math.random() * 5) + 1}.${Math.floor(Math.random() * 10)}.${Math.floor(Math.random() * 10)}`,
      criticality: criticalities[Math.floor(Math.random() * criticalities.length)],
      status: statuses[Math.floor(Math.random() * statuses.length)],
      isInternal: Math.random() > 0.7,
      lastUpdated: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000),
      applications: Math.floor(Math.random() * 20) + 1,
      vulnerabilities: Math.floor(Math.random() * 5),
      description: `Description for dependency ${i + 1}`
    }));
  }

  private updateStats(): void {
    this.stats.total = this.dependencies.length;
    this.stats.critical = this.dependencies.filter(d => d.criticality === 'critical').length;
    this.stats.outdated = this.dependencies.filter(d => d.status === 'update_required').length;
    this.stats.vulnerabilities = this.dependencies.reduce((sum, d) => sum + d.vulnerabilities, 0);
    this.stats.newThisMonth = this.dependencies.filter(d =>
      new Date(d.lastUpdated).getMonth() === new Date().getMonth()
    ).length;
    this.stats.criticalPercentage = Math.round((this.stats.critical / this.stats.total) * 100);
  }

  private updateChartData(): void {
    // Type distribution chart
    const typeCount = this.dependencies.reduce((acc, dep) => {
      acc[dep.type] = (acc[dep.type] || 0) + 1;
      return acc;
    }, {} as any);

    this.typeChartData = {
      labels: Object.keys(typeCount),
      datasets: [{
        data: Object.values(typeCount),
        backgroundColor: [
          '#0ea5e9', '#22c55e', '#f59e0b', '#ef4444', '#8b5cf6',
          '#06b6d4', '#84cc16'
        ]
      }]
    };

    // Criticality distribution chart
    const criticalityCount = this.dependencies.reduce((acc, dep) => {
      acc[dep.criticality] = (acc[dep.criticality] || 0) + 1;
      return acc;
    }, {} as any);

    this.criticalityChartData = {
      labels: Object.keys(criticalityCount),
      datasets: [{
        label: 'Dependencies',
        data: Object.values(criticalityCount),
        backgroundColor: ['#22c55e', '#f59e0b', '#f97316', '#ef4444']
      }]
    };
  }

  openAddModal(): void {
    this.editMode = false;
    this.editData = null;
    this.showModal = true;
  }

  editDependency(dependency: Dependency): void {
    this.editMode = true;
    this.editData = {
      name: dependency.name,
      type: dependency.type,
      version: dependency.version,
      description: dependency.description || '',
      criticality: dependency.criticality,
      status: dependency.status,
      maintainer: 'Unknown', // Default value since not in original interface
      licenseType: 'MIT' // Default value since not in original interface
    };
    this.showModal = true;
  }

  deleteDependency(dependency: Dependency): void {
    if (confirm(`Are you sure you want to delete ${dependency.name}?`)) {
      this.dependencies = this.dependencies.filter(d => d.id !== dependency.id);
      this.filteredDependencies = this.filteredDependencies.filter(d => d.id !== dependency.id);
      this.updateStats();
      this.updateChartData();
    }
  }

  closeModal(): void {
    this.showModal = false;
    this.editMode = false;
    this.editData = null;
  }

  onDependencySaved(dependencyData: SharedDependencyFormData): void {
    this.modalLoading = true;

    setTimeout(() => {
      console.log('Dependency saved:', dependencyData);

      // In a real app, you would make an API call here and refresh the data
      this.modalLoading = false;
      this.closeModal();
      this.loadDependencies(); // Refresh the list
    }, 1000);
  }

  onSearchInput(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.onSearchChanged(target.value);
  }

  onSearchChanged(searchTerm: string): void {
    this.applyFilters();
  }

  onFilterChanged(filterType: string, event: Event): void {
    const target = event.target as HTMLSelectElement;
    // Apply filter logic here
    this.applyFilters();
  }

  onFiltersChanged(filters: any): void {
    this.applyFilters();
  }

  onSortChanged(sort: any): void {
    // Implement sorting logic
    console.log('Sort changed:', sort);
  }

  private applyFilters(): void {
    // Implement filtering logic
    this.filteredDependencies = [...this.dependencies];
  }
}
