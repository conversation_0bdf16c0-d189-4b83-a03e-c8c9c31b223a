import './polyfills.server.mjs';
import {
  ButtonComponent
} from "./chunk-NVGHB2VF.mjs";
import {
  CommonModule,
  Component,
  DOCUMENT,
  EventEmitter,
  Inject,
  Input,
  NgIf,
  Output,
  PLATFORM_ID,
  isPlatformBrowser,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵNgOnChangesFeature,
  ɵɵadvance,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnamespaceHTML,
  ɵɵnamespaceSVG,
  ɵɵnextContext,
  ɵɵprojection,
  ɵɵprojectionDef,
  ɵɵproperty,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1
} from "./chunk-IPMSWJNG.mjs";

// src/app/shared/components/slide-modal/slide-modal.component.ts
var _c0 = ["*"];
function SlideModalComponent_div_0_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 2);
    \u0275\u0275listener("click", function SlideModalComponent_div_0_Template_div_click_0_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.onBackdropClick());
    });
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275classProp("show", ctx_r1.isVisible && ctx_r1.isAnimating);
  }
}
function SlideModalComponent_div_1_p_5_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p", 14);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r1.subtitle);
  }
}
function SlideModalComponent_div_1_div_12_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 15)(1, "div", 16)(2, "app-button", 17);
    \u0275\u0275listener("clicked", function SlideModalComponent_div_1_div_12_Template_app_button_clicked_2_listener() {
      \u0275\u0275restoreView(_r4);
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.cancel());
    });
    \u0275\u0275text(3);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "app-button", 18);
    \u0275\u0275listener("clicked", function SlideModalComponent_div_1_div_12_Template_app_button_clicked_4_listener() {
      \u0275\u0275restoreView(_r4);
      const ctx_r1 = \u0275\u0275nextContext(2);
      return \u0275\u0275resetView(ctx_r1.confirm());
    });
    \u0275\u0275text(5);
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance(2);
    \u0275\u0275property("disabled", ctx_r1.loading);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r1.cancelText, " ");
    \u0275\u0275advance();
    \u0275\u0275property("loading", ctx_r1.loading)("disabled", !ctx_r1.canConfirm);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r1.confirmText, " ");
  }
}
function SlideModalComponent_div_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 3)(1, "div", 4)(2, "div", 5)(3, "h2", 6);
    \u0275\u0275text(4);
    \u0275\u0275elementEnd();
    \u0275\u0275template(5, SlideModalComponent_div_1_p_5_Template, 2, 1, "p", 7);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(6, "button", 8);
    \u0275\u0275listener("click", function SlideModalComponent_div_1_Template_button_click_6_listener() {
      \u0275\u0275restoreView(_r3);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.close());
    });
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(7, "svg", 9);
    \u0275\u0275element(8, "line", 10)(9, "line", 11);
    \u0275\u0275elementEnd()()();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(10, "div", 12);
    \u0275\u0275projection(11);
    \u0275\u0275elementEnd();
    \u0275\u0275template(12, SlideModalComponent_div_1_div_12_Template, 6, 5, "div", 13);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275classProp("show", ctx_r1.isVisible && ctx_r1.isAnimating);
    \u0275\u0275advance(4);
    \u0275\u0275textInterpolate(ctx_r1.title);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.subtitle);
    \u0275\u0275advance(7);
    \u0275\u0275property("ngIf", ctx_r1.showFooter);
  }
}
var SlideModalComponent = class _SlideModalComponent {
  platformId;
  document;
  isOpen = false;
  title = "";
  subtitle = "";
  showFooter = true;
  confirmText = "Save";
  cancelText = "Cancel";
  loading = false;
  canConfirm = true;
  closeOnBackdrop = true;
  opened = new EventEmitter();
  closed = new EventEmitter();
  confirmed = new EventEmitter();
  cancelled = new EventEmitter();
  isVisible = false;
  isAnimating = false;
  animationTimeout;
  constructor(platformId, document) {
    this.platformId = platformId;
    this.document = document;
  }
  ngOnInit() {
    if (this.isOpen) {
      this.handleOpen();
    }
  }
  ngOnDestroy() {
    this.handleClose();
    if (isPlatformBrowser(this.platformId) && this.animationTimeout) {
      clearTimeout(this.animationTimeout);
    }
  }
  ngOnChanges() {
    if (this.isOpen && !this.isVisible) {
      this.handleOpen();
    } else if (!this.isOpen && this.isVisible) {
      this.handleClose();
    }
  }
  handleOpen() {
    this.isVisible = true;
    if (isPlatformBrowser(this.platformId)) {
      this.document.body.style.overflow = "hidden";
      setTimeout(() => {
        this.isAnimating = true;
      }, 10);
    }
    this.opened.emit();
  }
  handleClose() {
    this.isAnimating = false;
    if (isPlatformBrowser(this.platformId)) {
      this.animationTimeout = window.setTimeout(() => {
        this.isVisible = false;
        this.document.body.style.overflow = "";
        this.closed.emit();
      }, 300);
    } else {
      this.isVisible = false;
      this.closed.emit();
    }
  }
  onBackdropClick() {
    if (this.closeOnBackdrop) {
      this.close();
    }
  }
  close() {
    this.closed.emit();
  }
  confirm() {
    this.confirmed.emit();
  }
  cancel() {
    this.cancelled.emit();
  }
  static \u0275fac = function SlideModalComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _SlideModalComponent)(\u0275\u0275directiveInject(PLATFORM_ID), \u0275\u0275directiveInject(DOCUMENT));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _SlideModalComponent, selectors: [["app-slide-modal"]], inputs: { isOpen: "isOpen", title: "title", subtitle: "subtitle", showFooter: "showFooter", confirmText: "confirmText", cancelText: "cancelText", loading: "loading", canConfirm: "canConfirm", closeOnBackdrop: "closeOnBackdrop" }, outputs: { opened: "opened", closed: "closed", confirmed: "confirmed", cancelled: "cancelled" }, features: [\u0275\u0275NgOnChangesFeature], ngContentSelectors: _c0, decls: 2, vars: 2, consts: [["class", "modal-backdrop", 3, "show", "click", 4, "ngIf"], ["class", "modal-panel", 3, "show", 4, "ngIf"], [1, "modal-backdrop", 3, "click"], [1, "modal-panel"], [1, "modal-header"], [1, "modal-title-section"], [1, "modal-title"], ["class", "modal-subtitle", 4, "ngIf"], ["type", "button", "aria-label", "Close modal", 1, "close-button", 3, "click"], ["viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor"], ["x1", "18", "y1", "6", "x2", "6", "y2", "18"], ["x1", "6", "y1", "6", "x2", "18", "y2", "18"], [1, "modal-content"], ["class", "modal-footer", 4, "ngIf"], [1, "modal-subtitle"], [1, "modal-footer"], [1, "footer-actions"], ["variant", "ghost", 3, "clicked", "disabled"], ["variant", "primary", 3, "clicked", "loading", "disabled"]], template: function SlideModalComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275projectionDef();
      \u0275\u0275template(0, SlideModalComponent_div_0_Template, 1, 2, "div", 0)(1, SlideModalComponent_div_1_Template, 13, 5, "div", 1);
    }
    if (rf & 2) {
      \u0275\u0275property("ngIf", ctx.isVisible);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.isVisible);
    }
  }, dependencies: [CommonModule, NgIf, ButtonComponent], styles: ["\n\n.modal-backdrop[_ngcontent-%COMP%] {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  z-index: 9998;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n.modal-backdrop.show[_ngcontent-%COMP%] {\n  opacity: 1;\n}\n.modal-panel[_ngcontent-%COMP%] {\n  position: fixed;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  width: 600px;\n  max-width: 90vw;\n  background: white;\n  box-shadow: var(--shadow-xl);\n  z-index: 9999;\n  display: flex;\n  flex-direction: column;\n  transform: translateX(100%);\n  transition: transform 0.3s ease;\n}\n.modal-panel.show[_ngcontent-%COMP%] {\n  transform: translateX(0);\n}\n.modal-header[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: flex-start;\n  justify-content: space-between;\n  padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg);\n  border-bottom: 1px solid var(--secondary-200);\n  flex-shrink: 0;\n}\n.modal-title-section[_ngcontent-%COMP%] {\n  flex: 1;\n  margin-right: var(--spacing-md);\n}\n.modal-title[_ngcontent-%COMP%] {\n  margin: 0 0 var(--spacing-xs) 0;\n  font-size: var(--text-xl);\n  font-weight: 600;\n  color: var(--secondary-900);\n  line-height: var(--leading-tight);\n}\n.modal-subtitle[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n  line-height: var(--leading-relaxed);\n}\n.close-button[_ngcontent-%COMP%] {\n  background: none;\n  border: none;\n  color: var(--secondary-400);\n  cursor: pointer;\n  padding: var(--spacing-sm);\n  border-radius: var(--radius-md);\n  transition: all 0.2s ease;\n  flex-shrink: 0;\n}\n.close-button[_ngcontent-%COMP%]:hover {\n  color: var(--secondary-600);\n  background: var(--secondary-100);\n}\n.close-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\n  width: 20px;\n  height: 20px;\n}\n.modal-content[_ngcontent-%COMP%] {\n  flex: 1;\n  overflow-y: auto;\n  padding: var(--spacing-lg) var(--spacing-xl);\n}\n.modal-footer[_ngcontent-%COMP%] {\n  padding: var(--spacing-lg) var(--spacing-xl);\n  border-top: 1px solid var(--secondary-200);\n  flex-shrink: 0;\n}\n.footer-actions[_ngcontent-%COMP%] {\n  display: flex;\n  justify-content: flex-end;\n  gap: var(--spacing-md);\n}\n@media (max-width: 768px) {\n  .modal-panel[_ngcontent-%COMP%] {\n    width: 100vw;\n    max-width: none;\n  }\n  .modal-header[_ngcontent-%COMP%] {\n    padding: var(--spacing-lg);\n  }\n  .modal-content[_ngcontent-%COMP%] {\n    padding: var(--spacing-md) var(--spacing-lg);\n  }\n  .modal-footer[_ngcontent-%COMP%] {\n    padding: var(--spacing-lg);\n  }\n  .footer-actions[_ngcontent-%COMP%] {\n    flex-direction: column-reverse;\n    gap: var(--spacing-sm);\n  }\n  .footer-actions[_ngcontent-%COMP%]   app-button[_ngcontent-%COMP%] {\n    width: 100%;\n  }\n}\n/*# sourceMappingURL=slide-modal.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(SlideModalComponent, [{
    type: Component,
    args: [{ selector: "app-slide-modal", standalone: true, imports: [CommonModule, ButtonComponent], template: `
    <!-- Modal Backdrop -->
    <div
      class="modal-backdrop"
      [class.show]="isVisible && isAnimating"
      (click)="onBackdropClick()"
      *ngIf="isVisible"
    ></div>

    <!-- Modal Panel -->
    <div
      class="modal-panel"
      [class.show]="isVisible && isAnimating"
      *ngIf="isVisible"
    >
      <!-- Modal Header -->
      <div class="modal-header">
        <div class="modal-title-section">
          <h2 class="modal-title">{{ title }}</h2>
          <p class="modal-subtitle" *ngIf="subtitle">{{ subtitle }}</p>
        </div>
        <button
          class="close-button"
          (click)="close()"
          type="button"
          aria-label="Close modal"
        >
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>

      <!-- Modal Content -->
      <div class="modal-content">
        <ng-content></ng-content>
      </div>

      <!-- Modal Footer -->
      <div class="modal-footer" *ngIf="showFooter">
        <div class="footer-actions">
          <app-button
            variant="ghost"
            (clicked)="cancel()"
            [disabled]="loading"
          >
            {{ cancelText }}
          </app-button>
          <app-button
            variant="primary"
            (clicked)="confirm()"
            [loading]="loading"
            [disabled]="!canConfirm"
          >
            {{ confirmText }}
          </app-button>
        </div>
      </div>
    </div>
  `, styles: ["/* angular:styles/component:scss;724fdfcaf3d708fab5f7d7995230b225b15d56efea90b6d8f0dba322a322db1e;C:/Users/<USER>/Documents/VibeCoding/AppCatalog/app-catalog/frontend/src/app/shared/components/slide-modal/slide-modal.component.ts */\n.modal-backdrop {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  z-index: 9998;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n.modal-backdrop.show {\n  opacity: 1;\n}\n.modal-panel {\n  position: fixed;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  width: 600px;\n  max-width: 90vw;\n  background: white;\n  box-shadow: var(--shadow-xl);\n  z-index: 9999;\n  display: flex;\n  flex-direction: column;\n  transform: translateX(100%);\n  transition: transform 0.3s ease;\n}\n.modal-panel.show {\n  transform: translateX(0);\n}\n.modal-header {\n  display: flex;\n  align-items: flex-start;\n  justify-content: space-between;\n  padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg);\n  border-bottom: 1px solid var(--secondary-200);\n  flex-shrink: 0;\n}\n.modal-title-section {\n  flex: 1;\n  margin-right: var(--spacing-md);\n}\n.modal-title {\n  margin: 0 0 var(--spacing-xs) 0;\n  font-size: var(--text-xl);\n  font-weight: 600;\n  color: var(--secondary-900);\n  line-height: var(--leading-tight);\n}\n.modal-subtitle {\n  margin: 0;\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n  line-height: var(--leading-relaxed);\n}\n.close-button {\n  background: none;\n  border: none;\n  color: var(--secondary-400);\n  cursor: pointer;\n  padding: var(--spacing-sm);\n  border-radius: var(--radius-md);\n  transition: all 0.2s ease;\n  flex-shrink: 0;\n}\n.close-button:hover {\n  color: var(--secondary-600);\n  background: var(--secondary-100);\n}\n.close-button svg {\n  width: 20px;\n  height: 20px;\n}\n.modal-content {\n  flex: 1;\n  overflow-y: auto;\n  padding: var(--spacing-lg) var(--spacing-xl);\n}\n.modal-footer {\n  padding: var(--spacing-lg) var(--spacing-xl);\n  border-top: 1px solid var(--secondary-200);\n  flex-shrink: 0;\n}\n.footer-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: var(--spacing-md);\n}\n@media (max-width: 768px) {\n  .modal-panel {\n    width: 100vw;\n    max-width: none;\n  }\n  .modal-header {\n    padding: var(--spacing-lg);\n  }\n  .modal-content {\n    padding: var(--spacing-md) var(--spacing-lg);\n  }\n  .modal-footer {\n    padding: var(--spacing-lg);\n  }\n  .footer-actions {\n    flex-direction: column-reverse;\n    gap: var(--spacing-sm);\n  }\n  .footer-actions app-button {\n    width: 100%;\n  }\n}\n/*# sourceMappingURL=slide-modal.component.css.map */\n"] }]
  }], () => [{ type: Object, decorators: [{
    type: Inject,
    args: [PLATFORM_ID]
  }] }, { type: Document, decorators: [{
    type: Inject,
    args: [DOCUMENT]
  }] }], { isOpen: [{
    type: Input
  }], title: [{
    type: Input
  }], subtitle: [{
    type: Input
  }], showFooter: [{
    type: Input
  }], confirmText: [{
    type: Input
  }], cancelText: [{
    type: Input
  }], loading: [{
    type: Input
  }], canConfirm: [{
    type: Input
  }], closeOnBackdrop: [{
    type: Input
  }], opened: [{
    type: Output
  }], closed: [{
    type: Output
  }], confirmed: [{
    type: Output
  }], cancelled: [{
    type: Output
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(SlideModalComponent, { className: "SlideModalComponent", filePath: "src/app/shared/components/slide-modal/slide-modal.component.ts", lineNumber: 210 });
})();

export {
  SlideModalComponent
};
//# sourceMappingURL=chunk-V4LU676I.mjs.map
