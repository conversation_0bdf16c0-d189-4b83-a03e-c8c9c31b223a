{"version": 3, "sources": ["src/app/modules/stakeholders/stakeholders.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { ReactiveFormsModule, FormBuilder } from '@angular/forms';\nimport { CardComponent } from '../../shared/components/card/card.component';\nimport { ButtonComponent } from '../../shared/components/button/button.component';\nimport { TableComponent, TableColumn, TableAction } from '../../shared/components/table/table.component';\nimport { ChartComponent } from '../../shared/components/chart/chart.component';\nimport { StakeholderModalComponent } from '../applications/components/stakeholder-modal/stakeholder-modal.component';\n\ninterface Stakeholder {\n  id: number;\n  name: string;\n  email: string;\n  role: string;\n  department: string;\n  isActive: boolean;\n  applications: number;\n  lastActivity: Date;\n  phone?: string;\n  responsibilities?: string;\n}\n\n@Component({\n  selector: 'app-stakeholders',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    ReactiveFormsModule,\n    CardComponent,\n    ButtonComponent,\n    TableComponent,\n    ChartComponent,\n    StakeholderModalComponent\n  ],\n  template: `\n    <div class=\"stakeholders-page\">\n      <div class=\"page-content\">\n        <div class=\"page-header\">\n          <div class=\"header-content\">\n            <h1>Stakeholders</h1>\n            <p class=\"page-subtitle\">Manage team members and project stakeholders</p>\n          </div>\n          <div class=\"header-actions\">\n            <app-button\n              variant=\"primary\"\n              leftIcon=\"M12 4v16m8-8H4\"\n              (clicked)=\"openAddModal()\"\n            >\n              Add Stakeholder\n            </app-button>\n          </div>\n        </div>\n\n      <!-- Statistics Cards -->\n      <div class=\"stats-grid\">\n        <app-card title=\"Total Stakeholders\">\n          <div class=\"stat-content\">\n            <div class=\"stat-number\">{{ stats.total }}</div>\n            <div class=\"stat-change\">Across {{ stats.departments }} departments</div>\n          </div>\n        </app-card>\n\n        <app-card title=\"Active Members\">\n          <div class=\"stat-content\">\n            <div class=\"stat-number active\">{{ stats.active }}</div>\n            <div class=\"stat-change\">{{ stats.activePercentage }}% of total</div>\n          </div>\n        </app-card>\n\n        <app-card title=\"Project Owners\">\n          <div class=\"stat-content\">\n            <div class=\"stat-number owner\">{{ stats.owners }}</div>\n            <div class=\"stat-change\">Leading projects</div>\n          </div>\n        </app-card>\n\n        <app-card title=\"Recent Activity\">\n          <div class=\"stat-content\">\n            <div class=\"stat-number\">{{ stats.recentActivity }}</div>\n            <div class=\"stat-change\">Active this week</div>\n          </div>\n        </app-card>\n      </div>\n\n      <!-- Charts Section -->\n      <div class=\"charts-section\">\n        <app-card title=\"Stakeholders by Department\" subtitle=\"Distribution across departments\">\n          <app-chart\n            type=\"doughnut\"\n            [data]=\"departmentChartData\"\n            [options]=\"chartOptions\"\n            height=\"300px\"\n          ></app-chart>\n        </app-card>\n\n        <app-card title=\"Roles Distribution\" subtitle=\"Stakeholders by role\">\n          <app-chart\n            type=\"bar\"\n            [data]=\"roleChartData\"\n            [options]=\"chartOptions\"\n            height=\"300px\"\n          ></app-chart>\n        </app-card>\n      </div>\n\n      <!-- Stakeholders Table -->\n      <app-card title=\"Team Members\" subtitle=\"All project stakeholders\">\n        <div class=\"table-controls\">\n          <div class=\"search-controls\">\n            <input\n              type=\"text\"\n              placeholder=\"Search stakeholders...\"\n              class=\"search-input\"\n              (input)=\"onSearchInput($event)\"\n            >\n            <select class=\"filter-select\" (change)=\"onFilterChanged('department', $event)\">\n              <option value=\"\">All Departments</option>\n              <option value=\"engineering\">Engineering</option>\n              <option value=\"product\">Product</option>\n              <option value=\"design\">Design</option>\n              <option value=\"qa\">QA</option>\n              <option value=\"devops\">DevOps</option>\n              <option value=\"management\">Management</option>\n            </select>\n            <select class=\"filter-select\" (change)=\"onFilterChanged('role', $event)\">\n              <option value=\"\">All Roles</option>\n              <option value=\"owner\">Owner</option>\n              <option value=\"developer\">Developer</option>\n              <option value=\"architect\">Architect</option>\n              <option value=\"manager\">Manager</option>\n              <option value=\"stakeholder\">Stakeholder</option>\n            </select>\n          </div>\n        </div>\n\n        <app-table\n          [columns]=\"tableColumns\"\n          [data]=\"filteredStakeholders\"\n          [actions]=\"tableActions\"\n          [loading]=\"loading\"\n          [sortable]=\"true\"\n          (sortChanged)=\"onSortChanged($event)\"\n        ></app-table>\n      </app-card>\n\n      <!-- Add/Edit Modal -->\n      <app-stakeholder-modal\n        [isOpen]=\"showModal\"\n        [editMode]=\"editMode\"\n        [initialData]=\"editData\"\n        [loading]=\"modalLoading\"\n        (closed)=\"closeModal()\"\n        (saved)=\"onStakeholderSaved($event)\"\n      ></app-stakeholder-modal>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .stakeholders-page {\n      min-height: 100%;\n    }\n\n    .page-content {\n      padding: var(--spacing-xl);\n    }\n\n    .page-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: flex-start;\n      margin-bottom: var(--spacing-xl);\n    }\n\n    .header-content h1 {\n      margin: 0 0 var(--spacing-sm) 0;\n      font-size: var(--text-3xl);\n      font-weight: 700;\n      color: var(--secondary-900);\n    }\n\n    .page-subtitle {\n      margin: 0;\n      font-size: var(--text-lg);\n      color: var(--secondary-600);\n    }\n\n    .stats-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n      gap: var(--spacing-lg);\n      margin-bottom: var(--spacing-xl);\n    }\n\n    .stat-content {\n      display: flex;\n      flex-direction: column;\n      gap: var(--spacing-sm);\n      padding: var(--spacing-lg);\n    }\n\n    .stat-number {\n      font-size: var(--text-3xl);\n      font-weight: 700;\n      color: var(--secondary-900);\n\n      &.active {\n        color: var(--success-600);\n      }\n\n      &.owner {\n        color: var(--primary-600);\n      }\n    }\n\n    .stat-change {\n      font-size: var(--text-sm);\n      color: var(--secondary-600);\n    }\n\n    .charts-section {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n      gap: var(--spacing-lg);\n      margin-bottom: var(--spacing-xl);\n    }\n\n    .table-controls {\n      margin-bottom: var(--spacing-lg);\n    }\n\n    .search-controls {\n      display: flex;\n      gap: var(--spacing-md);\n      align-items: center;\n    }\n\n    .search-input,\n    .filter-select {\n      height: 40px;\n      padding: 0 var(--spacing-md);\n      border: 1px solid var(--secondary-300);\n      border-radius: var(--radius-md);\n      background: white;\n      font-size: var(--text-sm);\n      color: var(--secondary-800);\n      transition: all 0.2s ease;\n\n      &:focus {\n        outline: none;\n        border-color: var(--primary-500);\n        box-shadow: 0 0 0 3px var(--primary-100);\n      }\n    }\n\n    .search-input {\n      flex: 1;\n      min-width: 200px;\n    }\n\n    .filter-select {\n      min-width: 150px;\n      cursor: pointer;\n      background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e\");\n      background-position: right var(--spacing-sm) center;\n      background-repeat: no-repeat;\n      background-size: 16px;\n      padding-right: 40px;\n      appearance: none;\n    }\n\n    @media (max-width: 768px) {\n      .page-header {\n        flex-direction: column;\n        gap: var(--spacing-md);\n        align-items: stretch;\n      }\n\n      .stats-grid {\n        grid-template-columns: 1fr;\n        gap: var(--spacing-md);\n      }\n\n      .charts-section {\n        grid-template-columns: 1fr;\n        gap: var(--spacing-md);\n      }\n\n      .search-controls {\n        flex-direction: column;\n        align-items: stretch;\n      }\n\n      .search-input,\n      .filter-select {\n        min-width: auto;\n      }\n    }\n  `]\n})\nexport class StakeholdersComponent implements OnInit {\n  stakeholders: Stakeholder[] = [];\n  filteredStakeholders: Stakeholder[] = [];\n  loading = false;\n\n  // Modal state\n  showModal = false;\n  editMode = false;\n  editData: any = null;\n  modalLoading = false;\n\n  // Statistics\n  stats = {\n    total: 0,\n    active: 0,\n    owners: 0,\n    departments: 0,\n    recentActivity: 0,\n    activePercentage: 0\n  };\n\n  // Chart data\n  departmentChartData: any = null;\n  roleChartData: any = null;\n  chartOptions = {\n    responsive: true,\n    maintainAspectRatio: false\n  };\n\n  // Table configuration\n  tableColumns: TableColumn[] = [\n    { key: 'name', label: 'Name', sortable: true },\n    { key: 'email', label: 'Email', sortable: true },\n    { key: 'role', label: 'Role', type: 'badge', sortable: true },\n    { key: 'department', label: 'Department', type: 'badge', sortable: true },\n    { key: 'applications', label: 'Apps', type: 'number', sortable: true },\n    { key: 'isActive', label: 'Status', type: 'boolean', sortable: true },\n    { key: 'lastActivity', label: 'Last Activity', type: 'date', sortable: true }\n  ];\n\n  tableActions: TableAction[] = [\n    {\n      label: 'Edit',\n      icon: 'M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7',\n      variant: 'ghost',\n      action: (item) => this.editStakeholder(item)\n    },\n    {\n      label: 'Delete',\n      icon: 'M3 6h18m-2 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2',\n      variant: 'error',\n      action: (item) => this.deleteStakeholder(item)\n    }\n  ];\n\n  constructor(private fb: FormBuilder) {}\n\n  ngOnInit(): void {\n    this.loadStakeholders();\n  }\n\n  private loadStakeholders(): void {\n    this.loading = true;\n\n    // Mock data - replace with actual API call\n    setTimeout(() => {\n      this.stakeholders = this.generateMockStakeholders();\n      this.filteredStakeholders = [...this.stakeholders];\n      this.updateStats();\n      this.updateChartData();\n      this.loading = false;\n    }, 1000);\n  }\n\n  private generateMockStakeholders(): Stakeholder[] {\n    const departments = ['engineering', 'product', 'design', 'qa', 'devops', 'management'];\n    const roles = ['owner', 'developer', 'architect', 'manager', 'stakeholder'];\n\n    return Array.from({ length: 25 }, (_, i) => ({\n      id: i + 1,\n      name: `Stakeholder ${i + 1}`,\n      email: `stakeholder${i + 1}@company.com`,\n      role: roles[Math.floor(Math.random() * roles.length)],\n      department: departments[Math.floor(Math.random() * departments.length)],\n      isActive: Math.random() > 0.2,\n      applications: Math.floor(Math.random() * 10) + 1,\n      lastActivity: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),\n      phone: `******-${Math.floor(Math.random() * 900) + 100}-${Math.floor(Math.random() * 9000) + 1000}`,\n      responsibilities: `Responsibilities for stakeholder ${i + 1}`\n    }));\n  }\n\n  private updateStats(): void {\n    this.stats.total = this.stakeholders.length;\n    this.stats.active = this.stakeholders.filter(s => s.isActive).length;\n    this.stats.owners = this.stakeholders.filter(s => s.role === 'owner').length;\n    this.stats.departments = new Set(this.stakeholders.map(s => s.department)).size;\n    this.stats.recentActivity = this.stakeholders.filter(s =>\n      new Date(s.lastActivity).getTime() > Date.now() - 7 * 24 * 60 * 60 * 1000\n    ).length;\n    this.stats.activePercentage = Math.round((this.stats.active / this.stats.total) * 100);\n  }\n\n  private updateChartData(): void {\n    // Department distribution chart\n    const departmentCount = this.stakeholders.reduce((acc, stakeholder) => {\n      acc[stakeholder.department] = (acc[stakeholder.department] || 0) + 1;\n      return acc;\n    }, {} as any);\n\n    this.departmentChartData = {\n      labels: Object.keys(departmentCount),\n      datasets: [{\n        data: Object.values(departmentCount),\n        backgroundColor: [\n          '#0ea5e9', '#22c55e', '#f59e0b', '#ef4444', '#8b5cf6', '#06b6d4'\n        ]\n      }]\n    };\n\n    // Role distribution chart\n    const roleCount = this.stakeholders.reduce((acc, stakeholder) => {\n      acc[stakeholder.role] = (acc[stakeholder.role] || 0) + 1;\n      return acc;\n    }, {} as any);\n\n    this.roleChartData = {\n      labels: Object.keys(roleCount),\n      datasets: [{\n        label: 'Stakeholders',\n        data: Object.values(roleCount),\n        backgroundColor: ['#22c55e', '#0ea5e9', '#f59e0b', '#ef4444', '#8b5cf6']\n      }]\n    };\n  }\n\n  openAddModal(): void {\n    this.editMode = false;\n    this.editData = null;\n    this.showModal = true;\n  }\n\n  editStakeholder(stakeholder: Stakeholder): void {\n    this.editMode = true;\n    this.editData = { ...stakeholder };\n    this.showModal = true;\n  }\n\n  deleteStakeholder(stakeholder: Stakeholder): void {\n    if (confirm(`Are you sure you want to delete ${stakeholder.name}?`)) {\n      this.stakeholders = this.stakeholders.filter(s => s.id !== stakeholder.id);\n      this.filteredStakeholders = this.filteredStakeholders.filter(s => s.id !== stakeholder.id);\n      this.updateStats();\n      this.updateChartData();\n    }\n  }\n\n  closeModal(): void {\n    this.showModal = false;\n    this.editMode = false;\n    this.editData = null;\n  }\n\n  onStakeholderSaved(stakeholderData: any): void {\n    this.modalLoading = true;\n\n    setTimeout(() => {\n      if (this.editMode) {\n        const index = this.stakeholders.findIndex(s => s.id === this.editData.id);\n        if (index !== -1) {\n          this.stakeholders[index] = { ...this.stakeholders[index], ...stakeholderData };\n        }\n      } else {\n        const newStakeholder: Stakeholder = {\n          id: Math.max(...this.stakeholders.map(s => s.id)) + 1,\n          ...stakeholderData,\n          applications: 0,\n          lastActivity: new Date()\n        };\n        this.stakeholders.push(newStakeholder);\n      }\n\n      this.filteredStakeholders = [...this.stakeholders];\n      this.updateStats();\n      this.updateChartData();\n      this.modalLoading = false;\n      this.closeModal();\n    }, 1000);\n  }\n\n  onSearchInput(event: Event): void {\n    const target = event.target as HTMLInputElement;\n    this.onSearchChanged(target.value);\n  }\n\n  onSearchChanged(searchTerm: string): void {\n    this.applyFilters();\n  }\n\n  onFilterChanged(filterType: string, event: Event): void {\n    const target = event.target as HTMLSelectElement;\n    // Apply filter logic here\n    this.applyFilters();\n  }\n\n  onSortChanged(sort: any): void {\n    // Implement sorting logic\n    console.log('Sort changed:', sort);\n  }\n\n  private applyFilters(): void {\n    // Implement filtering logic\n    this.filteredStakeholders = [...this.stakeholders];\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6SM,IAAO,wBAAP,MAAO,uBAAqB;EAuDZ;EAtDpB,eAA8B,CAAA;EAC9B,uBAAsC,CAAA;EACtC,UAAU;;EAGV,YAAY;EACZ,WAAW;EACX,WAAgB;EAChB,eAAe;;EAGf,QAAQ;IACN,OAAO;IACP,QAAQ;IACR,QAAQ;IACR,aAAa;IACb,gBAAgB;IAChB,kBAAkB;;;EAIpB,sBAA2B;EAC3B,gBAAqB;EACrB,eAAe;IACb,YAAY;IACZ,qBAAqB;;;EAIvB,eAA8B;IAC5B,EAAE,KAAK,QAAQ,OAAO,QAAQ,UAAU,KAAI;IAC5C,EAAE,KAAK,SAAS,OAAO,SAAS,UAAU,KAAI;IAC9C,EAAE,KAAK,QAAQ,OAAO,QAAQ,MAAM,SAAS,UAAU,KAAI;IAC3D,EAAE,KAAK,cAAc,OAAO,cAAc,MAAM,SAAS,UAAU,KAAI;IACvE,EAAE,KAAK,gBAAgB,OAAO,QAAQ,MAAM,UAAU,UAAU,KAAI;IACpE,EAAE,KAAK,YAAY,OAAO,UAAU,MAAM,WAAW,UAAU,KAAI;IACnE,EAAE,KAAK,gBAAgB,OAAO,iBAAiB,MAAM,QAAQ,UAAU,KAAI;;EAG7E,eAA8B;IAC5B;MACE,OAAO;MACP,MAAM;MACN,SAAS;MACT,QAAQ,CAAC,SAAS,KAAK,gBAAgB,IAAI;;IAE7C;MACE,OAAO;MACP,MAAM;MACN,SAAS;MACT,QAAQ,CAAC,SAAS,KAAK,kBAAkB,IAAI;;;EAIjD,YAAoB,IAAe;AAAf,SAAA,KAAA;EAAkB;EAEtC,WAAQ;AACN,SAAK,iBAAgB;EACvB;EAEQ,mBAAgB;AACtB,SAAK,UAAU;AAGf,eAAW,MAAK;AACd,WAAK,eAAe,KAAK,yBAAwB;AACjD,WAAK,uBAAuB,CAAC,GAAG,KAAK,YAAY;AACjD,WAAK,YAAW;AAChB,WAAK,gBAAe;AACpB,WAAK,UAAU;IACjB,GAAG,GAAI;EACT;EAEQ,2BAAwB;AAC9B,UAAM,cAAc,CAAC,eAAe,WAAW,UAAU,MAAM,UAAU,YAAY;AACrF,UAAM,QAAQ,CAAC,SAAS,aAAa,aAAa,WAAW,aAAa;AAE1E,WAAO,MAAM,KAAK,EAAE,QAAQ,GAAE,GAAI,CAAC,GAAG,OAAO;MAC3C,IAAI,IAAI;MACR,MAAM,eAAe,IAAI,CAAC;MAC1B,OAAO,cAAc,IAAI,CAAC;MAC1B,MAAM,MAAM,KAAK,MAAM,KAAK,OAAM,IAAK,MAAM,MAAM,CAAC;MACpD,YAAY,YAAY,KAAK,MAAM,KAAK,OAAM,IAAK,YAAY,MAAM,CAAC;MACtE,UAAU,KAAK,OAAM,IAAK;MAC1B,cAAc,KAAK,MAAM,KAAK,OAAM,IAAK,EAAE,IAAI;MAC/C,cAAc,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,OAAM,IAAK,KAAK,KAAK,KAAK,KAAK,GAAI;MAC5E,OAAO,UAAU,KAAK,MAAM,KAAK,OAAM,IAAK,GAAG,IAAI,GAAG,IAAI,KAAK,MAAM,KAAK,OAAM,IAAK,GAAI,IAAI,GAAI;MACjG,kBAAkB,oCAAoC,IAAI,CAAC;MAC3D;EACJ;EAEQ,cAAW;AACjB,SAAK,MAAM,QAAQ,KAAK,aAAa;AACrC,SAAK,MAAM,SAAS,KAAK,aAAa,OAAO,OAAK,EAAE,QAAQ,EAAE;AAC9D,SAAK,MAAM,SAAS,KAAK,aAAa,OAAO,OAAK,EAAE,SAAS,OAAO,EAAE;AACtE,SAAK,MAAM,cAAc,IAAI,IAAI,KAAK,aAAa,IAAI,OAAK,EAAE,UAAU,CAAC,EAAE;AAC3E,SAAK,MAAM,iBAAiB,KAAK,aAAa,OAAO,OACnD,IAAI,KAAK,EAAE,YAAY,EAAE,QAAO,IAAK,KAAK,IAAG,IAAK,IAAI,KAAK,KAAK,KAAK,GAAI,EACzE;AACF,SAAK,MAAM,mBAAmB,KAAK,MAAO,KAAK,MAAM,SAAS,KAAK,MAAM,QAAS,GAAG;EACvF;EAEQ,kBAAe;AAErB,UAAM,kBAAkB,KAAK,aAAa,OAAO,CAAC,KAAK,gBAAe;AACpE,UAAI,YAAY,UAAU,KAAK,IAAI,YAAY,UAAU,KAAK,KAAK;AACnE,aAAO;IACT,GAAG,CAAA,CAAS;AAEZ,SAAK,sBAAsB;MACzB,QAAQ,OAAO,KAAK,eAAe;MACnC,UAAU,CAAC;QACT,MAAM,OAAO,OAAO,eAAe;QACnC,iBAAiB;UACf;UAAW;UAAW;UAAW;UAAW;UAAW;;OAE1D;;AAIH,UAAM,YAAY,KAAK,aAAa,OAAO,CAAC,KAAK,gBAAe;AAC9D,UAAI,YAAY,IAAI,KAAK,IAAI,YAAY,IAAI,KAAK,KAAK;AACvD,aAAO;IACT,GAAG,CAAA,CAAS;AAEZ,SAAK,gBAAgB;MACnB,QAAQ,OAAO,KAAK,SAAS;MAC7B,UAAU,CAAC;QACT,OAAO;QACP,MAAM,OAAO,OAAO,SAAS;QAC7B,iBAAiB,CAAC,WAAW,WAAW,WAAW,WAAW,SAAS;OACxE;;EAEL;EAEA,eAAY;AACV,SAAK,WAAW;AAChB,SAAK,WAAW;AAChB,SAAK,YAAY;EACnB;EAEA,gBAAgB,aAAwB;AACtC,SAAK,WAAW;AAChB,SAAK,WAAW,mBAAK;AACrB,SAAK,YAAY;EACnB;EAEA,kBAAkB,aAAwB;AACxC,QAAI,QAAQ,mCAAmC,YAAY,IAAI,GAAG,GAAG;AACnE,WAAK,eAAe,KAAK,aAAa,OAAO,OAAK,EAAE,OAAO,YAAY,EAAE;AACzE,WAAK,uBAAuB,KAAK,qBAAqB,OAAO,OAAK,EAAE,OAAO,YAAY,EAAE;AACzF,WAAK,YAAW;AAChB,WAAK,gBAAe;IACtB;EACF;EAEA,aAAU;AACR,SAAK,YAAY;AACjB,SAAK,WAAW;AAChB,SAAK,WAAW;EAClB;EAEA,mBAAmB,iBAAoB;AACrC,SAAK,eAAe;AAEpB,eAAW,MAAK;AACd,UAAI,KAAK,UAAU;AACjB,cAAM,QAAQ,KAAK,aAAa,UAAU,OAAK,EAAE,OAAO,KAAK,SAAS,EAAE;AACxE,YAAI,UAAU,IAAI;AAChB,eAAK,aAAa,KAAK,IAAI,kCAAK,KAAK,aAAa,KAAK,IAAM;QAC/D;MACF,OAAO;AACL,cAAM,iBAA8B;UAClC,IAAI,KAAK,IAAI,GAAG,KAAK,aAAa,IAAI,OAAK,EAAE,EAAE,CAAC,IAAI;WACjD,kBAF+B;UAGlC,cAAc;UACd,cAAc,oBAAI,KAAI;;AAExB,aAAK,aAAa,KAAK,cAAc;MACvC;AAEA,WAAK,uBAAuB,CAAC,GAAG,KAAK,YAAY;AACjD,WAAK,YAAW;AAChB,WAAK,gBAAe;AACpB,WAAK,eAAe;AACpB,WAAK,WAAU;IACjB,GAAG,GAAI;EACT;EAEA,cAAc,OAAY;AACxB,UAAM,SAAS,MAAM;AACrB,SAAK,gBAAgB,OAAO,KAAK;EACnC;EAEA,gBAAgB,YAAkB;AAChC,SAAK,aAAY;EACnB;EAEA,gBAAgB,YAAoB,OAAY;AAC9C,UAAM,SAAS,MAAM;AAErB,SAAK,aAAY;EACnB;EAEA,cAAc,MAAS;AAErB,YAAQ,IAAI,iBAAiB,IAAI;EACnC;EAEQ,eAAY;AAElB,SAAK,uBAAuB,CAAC,GAAG,KAAK,YAAY;EACnD;;qCArNW,wBAAqB,4BAAA,WAAA,CAAA;EAAA;yEAArB,wBAAqB,WAAA,CAAA,CAAA,kBAAA,CAAA,GAAA,OAAA,IAAA,MAAA,IAAA,QAAA,CAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,WAAA,WAAA,YAAA,kBAAA,GAAA,SAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,SAAA,oBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,SAAA,gBAAA,GAAA,CAAA,GAAA,eAAA,QAAA,GAAA,CAAA,SAAA,gBAAA,GAAA,CAAA,GAAA,eAAA,OAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,SAAA,8BAAA,YAAA,iCAAA,GAAA,CAAA,QAAA,YAAA,UAAA,SAAA,GAAA,QAAA,SAAA,GAAA,CAAA,SAAA,sBAAA,YAAA,sBAAA,GAAA,CAAA,QAAA,OAAA,UAAA,SAAA,GAAA,QAAA,SAAA,GAAA,CAAA,SAAA,gBAAA,YAAA,0BAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,QAAA,QAAA,eAAA,0BAAA,GAAA,gBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,QAAA,GAAA,CAAA,SAAA,EAAA,GAAA,CAAA,SAAA,aAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,SAAA,QAAA,GAAA,CAAA,SAAA,IAAA,GAAA,CAAA,SAAA,QAAA,GAAA,CAAA,SAAA,YAAA,GAAA,CAAA,SAAA,OAAA,GAAA,CAAA,SAAA,WAAA,GAAA,CAAA,SAAA,WAAA,GAAA,CAAA,SAAA,SAAA,GAAA,CAAA,SAAA,aAAA,GAAA,CAAA,GAAA,eAAA,WAAA,QAAA,WAAA,WAAA,UAAA,GAAA,CAAA,GAAA,UAAA,SAAA,UAAA,YAAA,eAAA,SAAA,CAAA,GAAA,UAAA,SAAA,+BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAxQ9B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA+B,GAAA,OAAA,CAAA,EACH,GAAA,OAAA,CAAA,EACC,GAAA,OAAA,CAAA,EACK,GAAA,IAAA;AACtB,MAAA,iBAAA,GAAA,cAAA;AAAY,MAAA,uBAAA;AAChB,MAAA,yBAAA,GAAA,KAAA,CAAA;AAAyB,MAAA,iBAAA,GAAA,8CAAA;AAA4C,MAAA,uBAAA,EAAI;AAE3E,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA4B,GAAA,cAAA,CAAA;AAIxB,MAAA,qBAAA,WAAA,SAAA,+DAAA;AAAA,eAAW,IAAA,aAAA;MAAc,CAAA;AAEzB,MAAA,iBAAA,IAAA,mBAAA;AACF,MAAA,uBAAA,EAAa,EACT;AAIV,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,YAAA,CAAA,EACe,IAAA,OAAA,CAAA,EACT,IAAA,OAAA,EAAA;AACC,MAAA,iBAAA,EAAA;AAAiB,MAAA,uBAAA;AAC1C,MAAA,yBAAA,IAAA,OAAA,EAAA;AAAyB,MAAA,iBAAA,EAAA;AAA0C,MAAA,uBAAA,EAAM,EACrE;AAGR,MAAA,yBAAA,IAAA,YAAA,EAAA,EAAiC,IAAA,OAAA,CAAA,EACL,IAAA,OAAA,EAAA;AACQ,MAAA,iBAAA,EAAA;AAAkB,MAAA,uBAAA;AAClD,MAAA,yBAAA,IAAA,OAAA,EAAA;AAAyB,MAAA,iBAAA,EAAA;AAAsC,MAAA,uBAAA,EAAM,EACjE;AAGR,MAAA,yBAAA,IAAA,YAAA,EAAA,EAAiC,IAAA,OAAA,CAAA,EACL,IAAA,OAAA,EAAA;AACO,MAAA,iBAAA,EAAA;AAAkB,MAAA,uBAAA;AACjD,MAAA,yBAAA,IAAA,OAAA,EAAA;AAAyB,MAAA,iBAAA,IAAA,kBAAA;AAAgB,MAAA,uBAAA,EAAM,EAC3C;AAGR,MAAA,yBAAA,IAAA,YAAA,EAAA,EAAkC,IAAA,OAAA,CAAA,EACN,IAAA,OAAA,EAAA;AACC,MAAA,iBAAA,EAAA;AAA0B,MAAA,uBAAA;AACnD,MAAA,yBAAA,IAAA,OAAA,EAAA;AAAyB,MAAA,iBAAA,IAAA,kBAAA;AAAgB,MAAA,uBAAA,EAAM,EAC3C,EACG;AAIb,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA4B,IAAA,YAAA,EAAA;AAExB,MAAA,oBAAA,IAAA,aAAA,EAAA;AAMF,MAAA,uBAAA;AAEA,MAAA,yBAAA,IAAA,YAAA,EAAA;AACE,MAAA,oBAAA,IAAA,aAAA,EAAA;AAMF,MAAA,uBAAA,EAAW;AAIb,MAAA,yBAAA,IAAA,YAAA,EAAA,EAAmE,IAAA,OAAA,EAAA,EACrC,IAAA,OAAA,EAAA,EACG,IAAA,SAAA,EAAA;AAKzB,MAAA,qBAAA,SAAA,SAAA,uDAAA,QAAA;AAAA,eAAS,IAAA,cAAA,MAAA;MAAqB,CAAA;AAJhC,MAAA,uBAAA;AAMA,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA8B,MAAA,qBAAA,UAAA,SAAA,yDAAA,QAAA;AAAA,eAAU,IAAA,gBAAgB,cAAY,MAAA;MAAS,CAAA;AAC3E,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAiB,MAAA,iBAAA,IAAA,iBAAA;AAAe,MAAA,uBAAA;AAChC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA4B,MAAA,iBAAA,IAAA,aAAA;AAAW,MAAA,uBAAA;AACvC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAwB,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA;AAC/B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAuB,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA;AAC7B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAmB,MAAA,iBAAA,IAAA,IAAA;AAAE,MAAA,uBAAA;AACrB,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAuB,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA;AAC7B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA2B,MAAA,iBAAA,IAAA,YAAA;AAAU,MAAA,uBAAA,EAAS;AAEhD,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA8B,MAAA,qBAAA,UAAA,SAAA,yDAAA,QAAA;AAAA,eAAU,IAAA,gBAAgB,QAAM,MAAA;MAAS,CAAA;AACrE,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAiB,MAAA,iBAAA,IAAA,WAAA;AAAS,MAAA,uBAAA;AAC1B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAsB,MAAA,iBAAA,IAAA,OAAA;AAAK,MAAA,uBAAA;AAC3B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA0B,MAAA,iBAAA,IAAA,WAAA;AAAS,MAAA,uBAAA;AACnC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA0B,MAAA,iBAAA,IAAA,WAAA;AAAS,MAAA,uBAAA;AACnC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAwB,MAAA,iBAAA,IAAA,SAAA;AAAO,MAAA,uBAAA;AAC/B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA4B,MAAA,iBAAA,IAAA,aAAA;AAAW,MAAA,uBAAA,EAAS,EACzC,EACL;AAGR,MAAA,yBAAA,IAAA,aAAA,EAAA;AAME,MAAA,qBAAA,eAAA,SAAA,iEAAA,QAAA;AAAA,eAAe,IAAA,cAAA,MAAA;MAAqB,CAAA;AACrC,MAAA,uBAAA,EAAY;AAIf,MAAA,yBAAA,IAAA,yBAAA,EAAA;AAKE,MAAA,qBAAA,UAAA,SAAA,0EAAA;AAAA,eAAU,IAAA,WAAA;MAAY,CAAA,EAAC,SAAA,SAAA,uEAAA,QAAA;AAAA,eACd,IAAA,mBAAA,MAAA;MAA0B,CAAA;AACpC,MAAA,uBAAA,EAAwB,EACnB;;;AAjGyB,MAAA,oBAAA,EAAA;AAAA,MAAA,4BAAA,IAAA,MAAA,KAAA;AACA,MAAA,oBAAA,CAAA;AAAA,MAAA,6BAAA,WAAA,IAAA,MAAA,aAAA,cAAA;AAMO,MAAA,oBAAA,CAAA;AAAA,MAAA,4BAAA,IAAA,MAAA,MAAA;AACP,MAAA,oBAAA,CAAA;AAAA,MAAA,6BAAA,IAAA,IAAA,MAAA,kBAAA,YAAA;AAMM,MAAA,oBAAA,CAAA;AAAA,MAAA,4BAAA,IAAA,MAAA,MAAA;AAON,MAAA,oBAAA,CAAA;AAAA,MAAA,4BAAA,IAAA,MAAA,cAAA;AAWzB,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,mBAAA,EAA4B,WAAA,IAAA,YAAA;AAS5B,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,aAAA,EAAsB,WAAA,IAAA,YAAA;AAsCxB,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,WAAA,IAAA,YAAA,EAAwB,QAAA,IAAA,oBAAA,EACK,WAAA,IAAA,YAAA,EACL,WAAA,IAAA,OAAA,EACL,YAAA,IAAA;AAQrB,MAAA,oBAAA;AAAA,MAAA,qBAAA,UAAA,IAAA,SAAA,EAAoB,YAAA,IAAA,QAAA,EACC,eAAA,IAAA,QAAA,EACG,WAAA,IAAA,YAAA;;;IA5H5B;IACA;IACA;IAAmB;IAAA;IACnB;IACA;IACA;IACA;IACA;EAAyB,GAAA,QAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uDAAA,EAAA,CAAA;;;sEA2QhB,uBAAqB,CAAA;UAtRjC;uBACW,oBAAkB,YAChB,MAAI,SACP;MACP;MACA;MACA;MACA;MACA;MACA;MACA;MACA;OACD,UACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA0HT,QAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAAA,EAAA,CAAA;;;;6EA+IU,uBAAqB,EAAA,WAAA,yBAAA,UAAA,0DAAA,YAAA,IAAA,CAAA;AAAA,GAAA;", "names": []}