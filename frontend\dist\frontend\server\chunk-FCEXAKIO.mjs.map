{"version": 3, "sources": ["src/app/shared/components/table/table.component.ts"], "sourcesContent": ["import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { ButtonComponent } from '../button/button.component';\n\nexport interface TableColumn {\n  key: string;\n  label: string;\n  sortable?: boolean;\n  width?: string;\n  type?: 'text' | 'number' | 'date' | 'badge' | 'boolean' | 'actions';\n  format?: (value: any) => string;\n  badgeConfig?: {\n    [key: string]: { color: string; label?: string };\n  };\n}\n\nexport interface TableAction {\n  label: string;\n  icon?: string;\n  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'ghost' | 'outline';\n  action: (item: any) => void;\n  visible?: (item: any) => boolean;\n}\n\nexport interface SortConfig {\n  column: string;\n  direction: 'asc' | 'desc';\n}\n\n@Component({\n  selector: 'app-table',\n  standalone: true,\n  imports: [CommonModule, FormsModule, ButtonComponent],\n  template: `\n    <div class=\"table-container\">\n      <!-- Table Header -->\n      <div class=\"table-header\" *ngIf=\"showHeader\">\n        <div class=\"table-title-section\">\n          <h3 class=\"table-title\" *ngIf=\"title\">{{ title }}</h3>\n          <p class=\"table-subtitle\" *ngIf=\"subtitle\">{{ subtitle }}</p>\n        </div>\n\n        <div class=\"table-actions\">\n          <!-- Search -->\n          <div class=\"search-container\" *ngIf=\"searchable\">\n            <svg class=\"search-icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n              <circle cx=\"11\" cy=\"11\" r=\"8\"></circle>\n              <path d=\"m21 21-4.35-4.35\"></path>\n            </svg>\n            <input\n              type=\"text\"\n              class=\"search-input\"\n              placeholder=\"Search...\"\n              [(ngModel)]=\"searchTerm\"\n              (input)=\"onSearch()\"\n            >\n          </div>\n\n          <!-- Custom Actions -->\n          <ng-content select=\"[slot=actions]\"></ng-content>\n        </div>\n      </div>\n\n      <!-- Table -->\n      <div class=\"table-wrapper\">\n        <table class=\"table\" [class.table-striped]=\"striped\" [class.table-hover]=\"hoverable\">\n          <!-- Table Head -->\n          <thead class=\"table-head\">\n            <tr>\n              <th\n                *ngFor=\"let column of columns\"\n                class=\"table-header-cell\"\n                [class.sortable]=\"column.sortable\"\n                [style.width]=\"column.width\"\n                (click)=\"onSort(column)\"\n              >\n                <div class=\"header-content\">\n                  <span>{{ column.label }}</span>\n                  <div class=\"sort-indicator\" *ngIf=\"column.sortable\">\n                    <svg\n                      class=\"sort-icon\"\n                      [class.sort-active]=\"sortConfig?.column === column.key\"\n                      [class.sort-desc]=\"sortConfig?.column === column.key && sortConfig?.direction === 'desc'\"\n                      viewBox=\"0 0 24 24\"\n                      fill=\"none\"\n                      stroke=\"currentColor\"\n                    >\n                      <polyline points=\"6,9 12,15 18,9\"></polyline>\n                    </svg>\n                  </div>\n                </div>\n              </th>\n            </tr>\n          </thead>\n\n          <!-- Table Body -->\n          <tbody class=\"table-body\">\n            <!-- Loading State -->\n            <tr *ngIf=\"loading\" class=\"loading-row\">\n              <td [attr.colspan]=\"columns.length\" class=\"loading-cell\">\n                <div class=\"loading-content\">\n                  <div class=\"spinner\"></div>\n                  <span>Loading...</span>\n                </div>\n              </td>\n            </tr>\n\n            <!-- Empty State -->\n            <tr *ngIf=\"!loading && (!data || data.length === 0)\" class=\"empty-row\">\n              <td [attr.colspan]=\"columns.length\" class=\"empty-cell\">\n                <div class=\"empty-content\">\n                  <svg class=\"empty-icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n                    <circle cx=\"11\" cy=\"11\" r=\"8\"></circle>\n                    <path d=\"m21 21-4.35-4.35\"></path>\n                  </svg>\n                  <h4>{{ emptyMessage || 'No data available' }}</h4>\n                  <p>{{ emptyDescription || 'There are no items to display.' }}</p>\n                </div>\n              </td>\n            </tr>\n\n            <!-- Data Rows -->\n            <tr\n              *ngFor=\"let item of paginatedData; let i = index; trackBy: trackByFn\"\n              class=\"table-row\"\n              [class.selected]=\"isSelected(item)\"\n              (click)=\"onRowClick(item)\"\n            >\n              <td\n                *ngFor=\"let column of columns\"\n                class=\"table-cell\"\n                [class]=\"'cell-' + column.type\"\n              >\n                <!-- Text Content -->\n                <span *ngIf=\"column.type === 'text' || !column.type\" class=\"cell-text\">\n                  {{ column.format ? column.format(getColumnValue(item, column.key)) : getColumnValue(item, column.key) }}\n                </span>\n\n                <!-- Number Content -->\n                <span *ngIf=\"column.type === 'number'\" class=\"cell-number\">\n                  {{ column.format ? column.format(getColumnValue(item, column.key)) : (getColumnValue(item, column.key) | number) }}\n                </span>\n\n                <!-- Date Content -->\n                <span *ngIf=\"column.type === 'date'\" class=\"cell-date\">\n                  {{ column.format ? column.format(getColumnValue(item, column.key)) : (getColumnValue(item, column.key) | date:'medium') }}\n                </span>\n\n                <!-- Badge Content -->\n                <span\n                  *ngIf=\"column.type === 'badge'\"\n                  class=\"cell-badge\"\n                  [class]=\"'badge-' + getBadgeClass(getColumnValue(item, column.key), column.badgeConfig)\"\n                >\n                  {{ getBadgeLabel(getColumnValue(item, column.key), column.badgeConfig) }}\n                </span>\n\n                <!-- Boolean Content -->\n                <span *ngIf=\"column.type === 'boolean'\" class=\"cell-boolean\">\n                  <span\n                    class=\"boolean-indicator\"\n                    [class.boolean-true]=\"getColumnValue(item, column.key)\"\n                    [class.boolean-false]=\"!getColumnValue(item, column.key)\"\n                  >\n                    {{ getColumnValue(item, column.key) ? 'Active' : 'Inactive' }}\n                  </span>\n                </span>\n\n                <!-- Actions Content -->\n                <div *ngIf=\"column.type === 'actions'\" class=\"cell-actions\">\n                  <ng-container *ngFor=\"let action of getVisibleActions(item)\">\n                    <app-button\n                      [variant]=\"action.variant || 'ghost'\"\n                      size=\"sm\"\n                      [leftIcon]=\"action.icon || ''\"\n                      (clicked)=\"action.action(item)\"\n                    >\n                      {{ action.label }}\n                    </app-button>\n                  </ng-container>\n                </div>\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n\n      <!-- Table Footer -->\n      <div class=\"table-footer\" *ngIf=\"showFooter && !loading\">\n        <div class=\"footer-info\">\n          <span class=\"item-count\">\n            Showing {{ startIndex + 1 }}-{{ endIndex }} of {{ filteredData.length }} items\n          </span>\n        </div>\n\n        <!-- Pagination -->\n        <div class=\"pagination\" *ngIf=\"paginated && totalPages > 1\">\n          <app-button\n            variant=\"outline\"\n            size=\"sm\"\n            leftIcon=\"M15 19l-7-7 7-7\"\n            [disabled]=\"currentPage === 1\"\n            (clicked)=\"goToPage(currentPage - 1)\"\n          >\n            Previous\n          </app-button>\n\n          <div class=\"page-numbers\">\n            <button\n              *ngFor=\"let page of getPageNumbers()\"\n              class=\"page-button\"\n              [class.active]=\"page === currentPage\"\n              [disabled]=\"page === '...'\"\n              (click)=\"goToPage(page)\"\n            >\n              {{ page }}\n            </button>\n          </div>\n\n          <app-button\n            variant=\"outline\"\n            size=\"sm\"\n            rightIcon=\"M9 5l7 7-7 7\"\n            [disabled]=\"currentPage === totalPages\"\n            (clicked)=\"goToPage(currentPage + 1)\"\n          >\n            Next\n          </app-button>\n        </div>\n      </div>\n    </div>\n  `,\n  styleUrls: ['./table.component.scss']\n})\nexport class TableComponent implements OnInit {\n  @Input() data: any[] = [];\n  @Input() columns: TableColumn[] = [];\n  @Input() actions: TableAction[] = [];\n  @Input() title = '';\n  @Input() subtitle = '';\n  @Input() loading = false;\n  @Input() searchable = true;\n  @Input() sortable = true;\n  @Input() paginated = true;\n  @Input() pageSize = 10;\n  @Input() striped = true;\n  @Input() hoverable = true;\n  @Input() selectable = false;\n  @Input() showHeader = true;\n  @Input() showFooter = true;\n  @Input() emptyMessage = '';\n  @Input() emptyDescription = '';\n\n  @Output() rowClick = new EventEmitter<any>();\n  @Output() sortChange = new EventEmitter<SortConfig>();\n  @Output() searchChange = new EventEmitter<string>();\n  @Output() pageChange = new EventEmitter<number>();\n\n  searchTerm = '';\n  sortConfig: SortConfig | null = null;\n  currentPage = 1;\n  selectedItems: any[] = [];\n\n  filteredData: any[] = [];\n  paginatedData: any[] = [];\n\n  ngOnInit(): void {\n    this.updateData();\n  }\n\n  ngOnChanges(): void {\n    this.updateData();\n  }\n\n  private updateData(): void {\n    this.filterData();\n    this.sortData();\n    this.paginateData();\n  }\n\n  private filterData(): void {\n    if (!this.searchTerm.trim()) {\n      this.filteredData = [...this.data];\n      return;\n    }\n\n    const term = this.searchTerm.toLowerCase();\n    this.filteredData = this.data.filter(item => {\n      return this.columns.some(column => {\n        const value = this.getColumnValue(item, column.key);\n        return value && value.toString().toLowerCase().includes(term);\n      });\n    });\n\n    this.currentPage = 1; // Reset to first page when filtering\n  }\n\n  private sortData(): void {\n    if (!this.sortConfig) return;\n\n    this.filteredData.sort((a, b) => {\n      const aValue = this.getColumnValue(a, this.sortConfig!.column);\n      const bValue = this.getColumnValue(b, this.sortConfig!.column);\n\n      let comparison = 0;\n      if (aValue < bValue) comparison = -1;\n      if (aValue > bValue) comparison = 1;\n\n      return this.sortConfig!.direction === 'desc' ? -comparison : comparison;\n    });\n  }\n\n  private paginateData(): void {\n    if (!this.paginated) {\n      this.paginatedData = this.filteredData;\n      return;\n    }\n\n    const startIndex = (this.currentPage - 1) * this.pageSize;\n    const endIndex = startIndex + this.pageSize;\n    this.paginatedData = this.filteredData.slice(startIndex, endIndex);\n  }\n\n  onSearch(): void {\n    this.updateData();\n    this.searchChange.emit(this.searchTerm);\n  }\n\n  onSort(column: TableColumn): void {\n    if (!column.sortable) return;\n\n    if (this.sortConfig?.column === column.key) {\n      this.sortConfig.direction = this.sortConfig.direction === 'asc' ? 'desc' : 'asc';\n    } else {\n      this.sortConfig = { column: column.key, direction: 'asc' };\n    }\n\n    this.updateData();\n    this.sortChange.emit(this.sortConfig);\n  }\n\n  onRowClick(item: any): void {\n    if (this.selectable) {\n      this.toggleSelection(item);\n    }\n    this.rowClick.emit(item);\n  }\n\n  goToPage(page: number | string): void {\n    if (typeof page === 'string') return;\n\n    this.currentPage = Math.max(1, Math.min(page, this.totalPages));\n    this.paginateData();\n    this.pageChange.emit(this.currentPage);\n  }\n\n  getColumnValue(item: any, key: string): any {\n    return key.split('.').reduce((obj, prop) => obj?.[prop], item);\n  }\n\n  getBadgeClass(value: any, config?: any): string {\n    return config?.[value]?.color || 'default';\n  }\n\n  getBadgeLabel(value: any, config?: any): string {\n    return config?.[value]?.label || value;\n  }\n\n  getVisibleActions(item: any): TableAction[] {\n    return this.actions.filter(action => !action.visible || action.visible(item));\n  }\n\n  isSelected(item: any): boolean {\n    return this.selectedItems.includes(item);\n  }\n\n  toggleSelection(item: any): void {\n    const index = this.selectedItems.indexOf(item);\n    if (index > -1) {\n      this.selectedItems.splice(index, 1);\n    } else {\n      this.selectedItems.push(item);\n    }\n  }\n\n  getPageNumbers(): (number | string)[] {\n    const pages: (number | string)[] = [];\n    const total = this.totalPages;\n    const current = this.currentPage;\n\n    if (total <= 7) {\n      for (let i = 1; i <= total; i++) {\n        pages.push(i);\n      }\n    } else {\n      pages.push(1);\n\n      if (current > 4) {\n        pages.push('...');\n      }\n\n      const start = Math.max(2, current - 1);\n      const end = Math.min(total - 1, current + 1);\n\n      for (let i = start; i <= end; i++) {\n        pages.push(i);\n      }\n\n      if (current < total - 3) {\n        pages.push('...');\n      }\n\n      pages.push(total);\n    }\n\n    return pages;\n  }\n\n  trackByFn(index: number, item: any): any {\n    return item.id || index;\n  }\n\n  get totalPages(): number {\n    return Math.ceil(this.filteredData.length / this.pageSize);\n  }\n\n  get startIndex(): number {\n    return (this.currentPage - 1) * this.pageSize;\n  }\n\n  get endIndex(): number {\n    return Math.min(this.startIndex + this.pageSize, this.filteredData.length);\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCU,IAAA,yBAAA,GAAA,MAAA,EAAA;AAAsC,IAAA,iBAAA,CAAA;AAAW,IAAA,uBAAA;;;;AAAX,IAAA,oBAAA;AAAA,IAAA,4BAAA,OAAA,KAAA;;;;;AACtC,IAAA,yBAAA,GAAA,KAAA,EAAA;AAA2C,IAAA,iBAAA,CAAA;AAAc,IAAA,uBAAA;;;;AAAd,IAAA,oBAAA;AAAA,IAAA,4BAAA,OAAA,QAAA;;;;;;AAK3C,IAAA,yBAAA,GAAA,OAAA,EAAA;;AACE,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,UAAA,EAAA,EAAuC,GAAA,QAAA,EAAA;AAEzC,IAAA,uBAAA;;AACA,IAAA,yBAAA,GAAA,SAAA,EAAA;AAIE,IAAA,2BAAA,iBAAA,SAAA,mEAAA,QAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,MAAA,6BAAA,OAAA,YAAA,MAAA,MAAA,OAAA,aAAA;AAAA,aAAA,sBAAA,MAAA;IAAA,CAAA;AACA,IAAA,qBAAA,SAAA,SAAA,6DAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,SAAA,CAAU;IAAA,CAAA;AALrB,IAAA,uBAAA,EAMC;;;;AAFC,IAAA,oBAAA,CAAA;AAAA,IAAA,2BAAA,WAAA,OAAA,UAAA;;;;;AAjBR,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA6C,GAAA,OAAA,EAAA;AAEzC,IAAA,qBAAA,GAAA,oCAAA,GAAA,GAAA,MAAA,EAAA,EAAsC,GAAA,mCAAA,GAAA,GAAA,KAAA,EAAA;AAExC,IAAA,uBAAA;AAEA,IAAA,yBAAA,GAAA,OAAA,EAAA;AAEE,IAAA,qBAAA,GAAA,qCAAA,GAAA,GAAA,OAAA,EAAA;AAeA,IAAA,uBAAA,CAAA;AACF,IAAA,uBAAA,EAAM;;;;AAtBqB,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,KAAA;AACE,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,QAAA;AAKI,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,UAAA;;;;;AAkCvB,IAAA,yBAAA,GAAA,OAAA,EAAA;;AACE,IAAA,yBAAA,GAAA,OAAA,EAAA;AAQE,IAAA,oBAAA,GAAA,YAAA,EAAA;AACF,IAAA,uBAAA,EAAM;;;;;AAPJ,IAAA,oBAAA;AAAA,IAAA,sBAAA,gBAAA,OAAA,cAAA,OAAA,OAAA,OAAA,WAAA,YAAA,UAAA,GAAA,EAAuD,cAAA,OAAA,cAAA,OAAA,OAAA,OAAA,WAAA,YAAA,UAAA,QAAA,OAAA,cAAA,OAAA,OAAA,OAAA,WAAA,eAAA,MAAA;;;;;;AAZ/D,IAAA,yBAAA,GAAA,MAAA,EAAA;AAKE,IAAA,qBAAA,SAAA,SAAA,mDAAA;AAAA,YAAA,YAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,OAAA,SAAA,CAAc;IAAA,CAAA;AAEvB,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA4B,GAAA,MAAA;AACpB,IAAA,iBAAA,CAAA;AAAkB,IAAA,uBAAA;AACxB,IAAA,qBAAA,GAAA,oCAAA,GAAA,GAAA,OAAA,EAAA;AAYF,IAAA,uBAAA,EAAM;;;;AAjBN,IAAA,sBAAA,SAAA,UAAA,KAAA;AADA,IAAA,sBAAA,YAAA,UAAA,QAAA;AAKQ,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,UAAA,KAAA;AACuB,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,UAAA,QAAA;;;;;AAoBnC,IAAA,yBAAA,GAAA,MAAA,EAAA,EAAwC,GAAA,MAAA,EAAA,EACmB,GAAA,OAAA,EAAA;AAErD,IAAA,oBAAA,GAAA,OAAA,EAAA;AACA,IAAA,yBAAA,GAAA,MAAA;AAAM,IAAA,iBAAA,GAAA,YAAA;AAAU,IAAA,uBAAA,EAAO,EACnB,EACH;;;;AALD,IAAA,oBAAA;;;;;;AASN,IAAA,yBAAA,GAAA,MAAA,EAAA,EAAuE,GAAA,MAAA,EAAA,EACd,GAAA,OAAA,EAAA;;AAEnD,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,UAAA,EAAA,EAAuC,GAAA,QAAA,EAAA;AAEzC,IAAA,uBAAA;;AACA,IAAA,yBAAA,GAAA,IAAA;AAAI,IAAA,iBAAA,CAAA;AAAyC,IAAA,uBAAA;AAC7C,IAAA,yBAAA,GAAA,GAAA;AAAG,IAAA,iBAAA,CAAA;AAA0D,IAAA,uBAAA,EAAI,EAC7D,EACH;;;;AATD,IAAA,oBAAA;;AAMI,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,gBAAA,mBAAA;AACD,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,oBAAA,gCAAA;;;;;AAkBL,IAAA,yBAAA,GAAA,QAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,UAAA,SAAA,UAAA,OAAA,OAAA,eAAA,SAAA,UAAA,GAAA,CAAA,IAAA,OAAA,eAAA,SAAA,UAAA,GAAA,GAAA,GAAA;;;;;AAIF,IAAA,yBAAA,GAAA,QAAA,EAAA;AACE,IAAA,iBAAA,CAAA;;AACF,IAAA,uBAAA;;;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,UAAA,SAAA,UAAA,OAAA,OAAA,eAAA,SAAA,UAAA,GAAA,CAAA,IAAA,sBAAA,GAAA,GAAA,OAAA,eAAA,SAAA,UAAA,GAAA,CAAA,GAAA,GAAA;;;;;AAIF,IAAA,yBAAA,GAAA,QAAA,EAAA;AACE,IAAA,iBAAA,CAAA;;AACF,IAAA,uBAAA;;;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,UAAA,SAAA,UAAA,OAAA,OAAA,eAAA,SAAA,UAAA,GAAA,CAAA,IAAA,sBAAA,GAAA,GAAA,OAAA,eAAA,SAAA,UAAA,GAAA,GAAA,QAAA,GAAA,GAAA;;;;;AAIF,IAAA,yBAAA,GAAA,QAAA,EAAA;AAKE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;;;AAHE,IAAA,qBAAA,WAAA,OAAA,cAAA,OAAA,eAAA,SAAA,UAAA,GAAA,GAAA,UAAA,WAAA,CAAA;AAEA,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,cAAA,OAAA,eAAA,SAAA,UAAA,GAAA,GAAA,UAAA,WAAA,GAAA,GAAA;;;;;AAIF,IAAA,yBAAA,GAAA,QAAA,EAAA,EAA6D,GAAA,QAAA,EAAA;AAMzD,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA,EAAO;;;;;;AAJL,IAAA,oBAAA;AAAA,IAAA,sBAAA,gBAAA,OAAA,eAAA,SAAA,UAAA,GAAA,CAAA,EAAuD,iBAAA,CAAA,OAAA,eAAA,SAAA,UAAA,GAAA,CAAA;AAGvD,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,eAAA,SAAA,UAAA,GAAA,IAAA,WAAA,YAAA,GAAA;;;;;;AAMF,IAAA,kCAAA,CAAA;AACE,IAAA,yBAAA,GAAA,cAAA,EAAA;AAIE,IAAA,qBAAA,WAAA,SAAA,wFAAA;AAAA,YAAA,YAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,UAAA,wBAAA,CAAA,EAAA;AAAA,aAAA,sBAAW,UAAA,OAAA,OAAA,CAAmB;IAAA,CAAA;AAE9B,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;;AANE,IAAA,oBAAA;AAAA,IAAA,qBAAA,WAAA,UAAA,WAAA,OAAA,EAAqC,YAAA,UAAA,QAAA,EAAA;AAKrC,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,UAAA,OAAA,GAAA;;;;;AARN,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,yDAAA,GAAA,GAAA,gBAAA,EAAA;AAUF,IAAA,uBAAA;;;;;AAVmC,IAAA,oBAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,kBAAA,OAAA,CAAA;;;;;AA1CrC,IAAA,yBAAA,GAAA,MAAA,EAAA;AAME,IAAA,qBAAA,GAAA,2CAAA,GAAA,GAAA,QAAA,EAAA,EAAuE,GAAA,2CAAA,GAAA,GAAA,QAAA,EAAA,EAKZ,GAAA,2CAAA,GAAA,GAAA,QAAA,EAAA,EAKJ,GAAA,2CAAA,GAAA,GAAA,QAAA,EAAA,EAStD,GAAA,2CAAA,GAAA,GAAA,QAAA,EAAA,EAK4D,GAAA,0CAAA,GAAA,GAAA,OAAA,EAAA;AAuB/D,IAAA,uBAAA;;;;AAlDE,IAAA,qBAAA,UAAA,UAAA,IAAA;AAGO,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,UAAA,SAAA,UAAA,CAAA,UAAA,IAAA;AAKA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,UAAA,SAAA,QAAA;AAKA,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,UAAA,SAAA,MAAA;AAMJ,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,UAAA,SAAA,OAAA;AAQI,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,UAAA,SAAA,SAAA;AAWD,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,UAAA,SAAA,SAAA;;;;;;AA/CV,IAAA,yBAAA,GAAA,MAAA,EAAA;AAIE,IAAA,qBAAA,SAAA,SAAA,oDAAA;AAAA,YAAA,UAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,WAAA,OAAA,CAAgB;IAAA,CAAA;AAEzB,IAAA,qBAAA,GAAA,oCAAA,GAAA,GAAA,MAAA,EAAA;AAsDF,IAAA,uBAAA;;;;;AAzDE,IAAA,sBAAA,YAAA,OAAA,WAAA,OAAA,CAAA;AAIqB,IAAA,oBAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,OAAA;;;;;;AA+EvB,IAAA,yBAAA,GAAA,UAAA,EAAA;AAKE,IAAA,qBAAA,SAAA,SAAA,wEAAA;AAAA,YAAA,WAAA,wBAAA,IAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,SAAA,QAAA,CAAc;IAAA,CAAA;AAEvB,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;;AALE,IAAA,sBAAA,UAAA,aAAA,OAAA,WAAA;AACA,IAAA,qBAAA,YAAA,aAAA,KAAA;AAGA,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,UAAA,GAAA;;;;;;AAnBN,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA4D,GAAA,cAAA,EAAA;AAMxD,IAAA,qBAAA,WAAA,SAAA,qEAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAW,OAAA,SAAA,OAAA,cAAuB,CAAC,CAAC;IAAA,CAAA;AAEpC,IAAA,iBAAA,GAAA,YAAA;AACF,IAAA,uBAAA;AAEA,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,+CAAA,GAAA,GAAA,UAAA,EAAA;AASF,IAAA,uBAAA;AAEA,IAAA,yBAAA,GAAA,cAAA,EAAA;AAKE,IAAA,qBAAA,WAAA,SAAA,qEAAA;AAAA,MAAA,wBAAA,IAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAW,OAAA,SAAA,OAAA,cAAuB,CAAC,CAAC;IAAA,CAAA;AAEpC,IAAA,iBAAA,GAAA,QAAA;AACF,IAAA,uBAAA,EAAa;;;;AA1BX,IAAA,oBAAA;AAAA,IAAA,qBAAA,YAAA,OAAA,gBAAA,CAAA;AAQmB,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,eAAA,CAAA;AAcnB,IAAA,oBAAA;AAAA,IAAA,qBAAA,YAAA,OAAA,gBAAA,OAAA,UAAA;;;;;AAnCN,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAyD,GAAA,OAAA,EAAA,EAC9B,GAAA,QAAA,EAAA;AAErB,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA,EAAO;AAIT,IAAA,qBAAA,GAAA,sCAAA,GAAA,GAAA,OAAA,EAAA;AAiCF,IAAA,uBAAA;;;;AAtCM,IAAA,oBAAA,CAAA;AAAA,IAAA,6BAAA,aAAA,OAAA,aAAA,GAAA,KAAA,OAAA,UAAA,QAAA,OAAA,aAAA,QAAA,SAAA;AAKqB,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,aAAA,OAAA,aAAA,CAAA;;;AAsC3B,IAAO,iBAAP,MAAO,gBAAc;EAChB,OAAc,CAAA;EACd,UAAyB,CAAA;EACzB,UAAyB,CAAA;EACzB,QAAQ;EACR,WAAW;EACX,UAAU;EACV,aAAa;EACb,WAAW;EACX,YAAY;EACZ,WAAW;EACX,UAAU;EACV,YAAY;EACZ,aAAa;EACb,aAAa;EACb,aAAa;EACb,eAAe;EACf,mBAAmB;EAElB,WAAW,IAAI,aAAY;EAC3B,aAAa,IAAI,aAAY;EAC7B,eAAe,IAAI,aAAY;EAC/B,aAAa,IAAI,aAAY;EAEvC,aAAa;EACb,aAAgC;EAChC,cAAc;EACd,gBAAuB,CAAA;EAEvB,eAAsB,CAAA;EACtB,gBAAuB,CAAA;EAEvB,WAAQ;AACN,SAAK,WAAU;EACjB;EAEA,cAAW;AACT,SAAK,WAAU;EACjB;EAEQ,aAAU;AAChB,SAAK,WAAU;AACf,SAAK,SAAQ;AACb,SAAK,aAAY;EACnB;EAEQ,aAAU;AAChB,QAAI,CAAC,KAAK,WAAW,KAAI,GAAI;AAC3B,WAAK,eAAe,CAAC,GAAG,KAAK,IAAI;AACjC;IACF;AAEA,UAAM,OAAO,KAAK,WAAW,YAAW;AACxC,SAAK,eAAe,KAAK,KAAK,OAAO,UAAO;AAC1C,aAAO,KAAK,QAAQ,KAAK,YAAS;AAChC,cAAM,QAAQ,KAAK,eAAe,MAAM,OAAO,GAAG;AAClD,eAAO,SAAS,MAAM,SAAQ,EAAG,YAAW,EAAG,SAAS,IAAI;MAC9D,CAAC;IACH,CAAC;AAED,SAAK,cAAc;EACrB;EAEQ,WAAQ;AACd,QAAI,CAAC,KAAK;AAAY;AAEtB,SAAK,aAAa,KAAK,CAAC,GAAG,MAAK;AAC9B,YAAM,SAAS,KAAK,eAAe,GAAG,KAAK,WAAY,MAAM;AAC7D,YAAM,SAAS,KAAK,eAAe,GAAG,KAAK,WAAY,MAAM;AAE7D,UAAI,aAAa;AACjB,UAAI,SAAS;AAAQ,qBAAa;AAClC,UAAI,SAAS;AAAQ,qBAAa;AAElC,aAAO,KAAK,WAAY,cAAc,SAAS,CAAC,aAAa;IAC/D,CAAC;EACH;EAEQ,eAAY;AAClB,QAAI,CAAC,KAAK,WAAW;AACnB,WAAK,gBAAgB,KAAK;AAC1B;IACF;AAEA,UAAM,cAAc,KAAK,cAAc,KAAK,KAAK;AACjD,UAAM,WAAW,aAAa,KAAK;AACnC,SAAK,gBAAgB,KAAK,aAAa,MAAM,YAAY,QAAQ;EACnE;EAEA,WAAQ;AACN,SAAK,WAAU;AACf,SAAK,aAAa,KAAK,KAAK,UAAU;EACxC;EAEA,OAAO,QAAmB;AACxB,QAAI,CAAC,OAAO;AAAU;AAEtB,QAAI,KAAK,YAAY,WAAW,OAAO,KAAK;AAC1C,WAAK,WAAW,YAAY,KAAK,WAAW,cAAc,QAAQ,SAAS;IAC7E,OAAO;AACL,WAAK,aAAa,EAAE,QAAQ,OAAO,KAAK,WAAW,MAAK;IAC1D;AAEA,SAAK,WAAU;AACf,SAAK,WAAW,KAAK,KAAK,UAAU;EACtC;EAEA,WAAW,MAAS;AAClB,QAAI,KAAK,YAAY;AACnB,WAAK,gBAAgB,IAAI;IAC3B;AACA,SAAK,SAAS,KAAK,IAAI;EACzB;EAEA,SAAS,MAAqB;AAC5B,QAAI,OAAO,SAAS;AAAU;AAE9B,SAAK,cAAc,KAAK,IAAI,GAAG,KAAK,IAAI,MAAM,KAAK,UAAU,CAAC;AAC9D,SAAK,aAAY;AACjB,SAAK,WAAW,KAAK,KAAK,WAAW;EACvC;EAEA,eAAe,MAAW,KAAW;AACnC,WAAO,IAAI,MAAM,GAAG,EAAE,OAAO,CAAC,KAAK,SAAS,MAAM,IAAI,GAAG,IAAI;EAC/D;EAEA,cAAc,OAAY,QAAY;AACpC,WAAO,SAAS,KAAK,GAAG,SAAS;EACnC;EAEA,cAAc,OAAY,QAAY;AACpC,WAAO,SAAS,KAAK,GAAG,SAAS;EACnC;EAEA,kBAAkB,MAAS;AACzB,WAAO,KAAK,QAAQ,OAAO,YAAU,CAAC,OAAO,WAAW,OAAO,QAAQ,IAAI,CAAC;EAC9E;EAEA,WAAW,MAAS;AAClB,WAAO,KAAK,cAAc,SAAS,IAAI;EACzC;EAEA,gBAAgB,MAAS;AACvB,UAAM,QAAQ,KAAK,cAAc,QAAQ,IAAI;AAC7C,QAAI,QAAQ,IAAI;AACd,WAAK,cAAc,OAAO,OAAO,CAAC;IACpC,OAAO;AACL,WAAK,cAAc,KAAK,IAAI;IAC9B;EACF;EAEA,iBAAc;AACZ,UAAM,QAA6B,CAAA;AACnC,UAAM,QAAQ,KAAK;AACnB,UAAM,UAAU,KAAK;AAErB,QAAI,SAAS,GAAG;AACd,eAAS,IAAI,GAAG,KAAK,OAAO,KAAK;AAC/B,cAAM,KAAK,CAAC;MACd;IACF,OAAO;AACL,YAAM,KAAK,CAAC;AAEZ,UAAI,UAAU,GAAG;AACf,cAAM,KAAK,KAAK;MAClB;AAEA,YAAM,QAAQ,KAAK,IAAI,GAAG,UAAU,CAAC;AACrC,YAAM,MAAM,KAAK,IAAI,QAAQ,GAAG,UAAU,CAAC;AAE3C,eAAS,IAAI,OAAO,KAAK,KAAK,KAAK;AACjC,cAAM,KAAK,CAAC;MACd;AAEA,UAAI,UAAU,QAAQ,GAAG;AACvB,cAAM,KAAK,KAAK;MAClB;AAEA,YAAM,KAAK,KAAK;IAClB;AAEA,WAAO;EACT;EAEA,UAAU,OAAe,MAAS;AAChC,WAAO,KAAK,MAAM;EACpB;EAEA,IAAI,aAAU;AACZ,WAAO,KAAK,KAAK,KAAK,aAAa,SAAS,KAAK,QAAQ;EAC3D;EAEA,IAAI,aAAU;AACZ,YAAQ,KAAK,cAAc,KAAK,KAAK;EACvC;EAEA,IAAI,WAAQ;AACV,WAAO,KAAK,IAAI,KAAK,aAAa,KAAK,UAAU,KAAK,aAAa,MAAM;EAC3E;;qCAtMW,iBAAc;EAAA;yEAAd,iBAAc,WAAA,CAAA,CAAA,WAAA,CAAA,GAAA,QAAA,EAAA,MAAA,QAAA,SAAA,WAAA,SAAA,WAAA,OAAA,SAAA,UAAA,YAAA,SAAA,WAAA,YAAA,cAAA,UAAA,YAAA,WAAA,aAAA,UAAA,YAAA,SAAA,WAAA,WAAA,aAAA,YAAA,cAAA,YAAA,cAAA,YAAA,cAAA,cAAA,gBAAA,kBAAA,mBAAA,GAAA,SAAA,EAAA,UAAA,YAAA,YAAA,cAAA,cAAA,gBAAA,YAAA,aAAA,GAAA,UAAA,CAAA,8BAAA,GAAA,oBAAA,KAAA,OAAA,IAAA,MAAA,IAAA,QAAA,CAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,SAAA,gBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,OAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,SAAA,qBAAA,GAAA,YAAA,SAAA,SAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,SAAA,eAAA,GAAA,MAAA,GAAA,CAAA,SAAA,aAAA,GAAA,MAAA,GAAA,CAAA,SAAA,aAAA,GAAA,YAAA,SAAA,GAAA,SAAA,WAAA,cAAA,GAAA,CAAA,SAAA,gBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,qBAAA,GAAA,CAAA,SAAA,eAAA,GAAA,MAAA,GAAA,CAAA,SAAA,kBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,SAAA,oBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,WAAA,aAAA,QAAA,QAAA,UAAA,gBAAA,GAAA,aAAA,GAAA,CAAA,MAAA,MAAA,MAAA,MAAA,KAAA,GAAA,GAAA,CAAA,KAAA,kBAAA,GAAA,CAAA,QAAA,QAAA,eAAA,aAAA,GAAA,gBAAA,GAAA,iBAAA,SAAA,SAAA,GAAA,CAAA,GAAA,qBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,SAAA,kBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,WAAA,aAAA,QAAA,QAAA,UAAA,gBAAA,GAAA,WAAA,GAAA,CAAA,UAAA,gBAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,SAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,WAAA,aAAA,QAAA,QAAA,UAAA,gBAAA,GAAA,YAAA,GAAA,CAAA,GAAA,aAAA,GAAA,OAAA,GAAA,CAAA,SAAA,cAAA,GAAA,SAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,SAAA,aAAA,GAAA,MAAA,GAAA,CAAA,SAAA,eAAA,GAAA,MAAA,GAAA,CAAA,SAAA,aAAA,GAAA,MAAA,GAAA,CAAA,SAAA,cAAA,GAAA,SAAA,GAAA,MAAA,GAAA,CAAA,SAAA,gBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,gBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,SAAA,SAAA,GAAA,CAAA,QAAA,MAAA,GAAA,WAAA,WAAA,UAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,SAAA,cAAA,GAAA,MAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,WAAA,WAAA,QAAA,MAAA,YAAA,mBAAA,GAAA,WAAA,UAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,SAAA,eAAA,GAAA,UAAA,YAAA,SAAA,GAAA,SAAA,SAAA,GAAA,CAAA,WAAA,WAAA,QAAA,MAAA,aAAA,gBAAA,GAAA,WAAA,UAAA,GAAA,CAAA,GAAA,eAAA,GAAA,SAAA,UAAA,CAAA,GAAA,UAAA,SAAA,wBAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;;AAxMvB,MAAA,yBAAA,GAAA,OAAA,CAAA;AAEE,MAAA,qBAAA,GAAA,+BAAA,GAAA,GAAA,OAAA,CAAA;AA4BA,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA2B,GAAA,SAAA,CAAA,EAC4D,GAAA,SAAA,CAAA,EAEzD,GAAA,IAAA;AAEtB,MAAA,qBAAA,GAAA,8BAAA,GAAA,GAAA,MAAA,CAAA;AAuBF,MAAA,uBAAA,EAAK;AAIP,MAAA,yBAAA,GAAA,SAAA,CAAA;AAEE,MAAA,qBAAA,GAAA,8BAAA,GAAA,GAAA,MAAA,CAAA,EAAwC,GAAA,8BAAA,IAAA,GAAA,MAAA,CAAA,EAU+B,IAAA,+BAAA,GAAA,GAAA,MAAA,CAAA;AA2EzE,MAAA,uBAAA,EAAQ,EACF;AAIV,MAAA,qBAAA,IAAA,gCAAA,GAAA,GAAA,OAAA,EAAA;AA0CF,MAAA,uBAAA;;;AAlM6B,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,UAAA;AA6BJ,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,iBAAA,IAAA,OAAA,EAA+B,eAAA,IAAA,SAAA;AAKzB,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,WAAA,IAAA,OAAA;AA4BlB,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,OAAA;AAUA,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,YAAA,CAAA,IAAA,QAAA,IAAA,KAAA,WAAA,EAAA;AAec,MAAA,oBAAA;AAAA,MAAA,qBAAA,WAAA,IAAA,aAAA,EAAkB,gBAAA,IAAA,SAAA;AAiEhB,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,cAAA,CAAA,IAAA,OAAA;;oBA5JrB,cAAY,SAAA,MAAA,aAAA,UAAE,aAAW,sBAAA,iBAAA,SAAE,eAAe,GAAA,QAAA,CAAA,k9SAAA,EAAA,CAAA;;;sEA0MzC,gBAAc,CAAA;UA7M1B;uBACW,aAAW,YACT,MAAI,SACP,CAAC,cAAc,aAAa,eAAe,GAAC,UAC3C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAsMT,QAAA,CAAA,i7PAAA,EAAA,CAAA;cAIQ,MAAI,CAAA;UAAZ;MACQ,SAAO,CAAA;UAAf;MACQ,SAAO,CAAA;UAAf;MACQ,OAAK,CAAA;UAAb;MACQ,UAAQ,CAAA;UAAhB;MACQ,SAAO,CAAA;UAAf;MACQ,YAAU,CAAA;UAAlB;MACQ,UAAQ,CAAA;UAAhB;MACQ,WAAS,CAAA;UAAjB;MACQ,UAAQ,CAAA;UAAhB;MACQ,SAAO,CAAA;UAAf;MACQ,WAAS,CAAA;UAAjB;MACQ,YAAU,CAAA;UAAlB;MACQ,YAAU,CAAA;UAAlB;MACQ,YAAU,CAAA;UAAlB;MACQ,cAAY,CAAA;UAApB;MACQ,kBAAgB,CAAA;UAAxB;MAES,UAAQ,CAAA;UAAjB;MACS,YAAU,CAAA;UAAnB;MACS,cAAY,CAAA;UAArB;MACS,YAAU,CAAA;UAAnB;;;;6EAtBU,gBAAc,EAAA,WAAA,kBAAA,UAAA,sDAAA,YAAA,IAAA,CAAA;AAAA,GAAA;", "names": []}