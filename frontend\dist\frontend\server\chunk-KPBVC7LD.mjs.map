{"version": 3, "sources": ["src/app/modules/stakeholders/stakeholders.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\n\nexport const routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./stakeholders.component').then(m => m.StakeholdersComponent)\n  }\n];\n"], "mappings": ";;;;;;AAEO,IAAM,SAAiB;EAC5B;IACE,MAAM;IACN,eAAe,MAAM,OAAO,sBAA0B,EAAE,KAAK,OAAK,EAAE,qBAAqB;;;", "names": []}