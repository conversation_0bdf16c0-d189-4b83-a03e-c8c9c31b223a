import './polyfills.server.mjs';
import {
  __spreadValues
} from "./chunk-5WKMABBB.mjs";

// src/app/modules/security/security.routes.ts
var routes = [
  __spreadValues({
    path: "",
    loadComponent: () => import("./chunk-LEJKBIUU.mjs").then((m) => m.SecurityComponent)
  }, true ? { \u0275entryName: "src/app/modules/security/security.component.ts" } : {}),
  __spreadValues({
    path: "vulnerabilities",
    loadComponent: () => import("./chunk-BGHJJD7H.mjs").then((m) => m.VulnerabilitiesComponent)
  }, true ? { \u0275entryName: "src/app/modules/security/vulnerabilities/vulnerabilities.component.ts" } : {}),
  __spreadValues({
    path: "assessments",
    loadComponent: () => import("./chunk-JOJ3ACSZ.mjs").then((m) => m.AssessmentsComponent)
  }, true ? { \u0275entryName: "src/app/modules/security/assessments/assessments.component.ts" } : {})
];
export {
  routes
};
//# sourceMappingURL=chunk-GRKJXI23.mjs.map
