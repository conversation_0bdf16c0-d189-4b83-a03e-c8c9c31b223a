<div class="container-fluid">
  <div class="row mb-4">
    <div class="col">
      <button class="btn btn-outline-secondary mb-3" (click)="onCancel()">
        <i class="bi bi-arrow-left me-2"></i>
        <span *ngIf="cameFromApplicationForm">Back to Application Form</span>
        <span *ngIf="!cameFromApplicationForm">Back to Dependencies</span>
      </button>
      <h1>Add New Dependency</h1>
      <p class="text-muted" *ngIf="cameFromApplicationForm">Create a new dependency to associate with your application.</p>
      <p class="text-muted" *ngIf="!cameFromApplicationForm">Create a new dependency that can be associated with applications.</p>
    </div>
  </div>
  
  <div class="row">
    <div class="col">
      <app-dependency-form (formSubmit)="onFormSubmit($event)" (cancel)="onCancel()"></app-dependency-form>
    </div>
  </div>
</div>