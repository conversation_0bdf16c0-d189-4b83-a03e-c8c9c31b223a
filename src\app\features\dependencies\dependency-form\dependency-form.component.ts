import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Dependency } from '../models/dependency.model';

@Component({
  selector: 'app-dependency-form',
  templateUrl: './dependency-form.component.html',
  styleUrls: ['./dependency-form.component.scss'],
  imports: [CommonModule, ReactiveFormsModule],
  standalone: true
})
export class DependencyFormComponent implements OnInit, OnChanges {
  @Input() dependency?: Dependency;
  @Output() formSubmit = new EventEmitter<Omit<Dependency, 'id'>>();
  @Output() cancel = new EventEmitter<void>();
  
  dependencyForm: FormGroup;
  isEditMode = false;
  
  constructor(private fb: FormBuilder) {
    this.dependencyForm = this.createForm();
  }
  
  ngOnInit(): void {
  }
  
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['dependency'] && this.dependency) {
      this.isEditMode = true;
      this.patchFormValues();
    }
  }
  
  createForm(): FormGroup {
    return this.fb.group({
      name: ['', Validators.required],
      description: ['', Validators.required],
      type: ['Internal', Validators.required],
      category: ['API', Validators.required],
      version: [''],
      url: [''],
      status: ['Active', Validators.required],
      owner: [''],
      integrationDetails: ['']
    });
  }
  
  patchFormValues(): void {
    if (this.dependency) {
      this.dependencyForm.patchValue({
        name: this.dependency.name,
        description: this.dependency.description,
        type: this.dependency.type,
        category: this.dependency.category,
        version: this.dependency.version || '',
        url: this.dependency.url || '',
        status: this.dependency.status,
        owner: this.dependency.owner || '',
        integrationDetails: this.dependency.integrationDetails || ''
      });
    }
  }
  
  onSubmit(): void {
    if (this.dependencyForm.valid) {
      this.formSubmit.emit(this.dependencyForm.value);
    }
  }
  
  onCancel(): void {
    this.cancel.emit();
  }
}