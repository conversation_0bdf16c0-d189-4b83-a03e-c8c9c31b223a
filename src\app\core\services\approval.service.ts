import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';

export interface ApprovalRequest {
  id: string;
  applicationId: string;
  applicationName: string;
  requestType: 'CREATE' | 'UPDATE';
  requestedBy: string;
  requestedAt: Date;
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  reviewedBy?: string;
  reviewedAt?: Date;
  comments?: string;
  changes?: Record<string, any>;
}

@Injectable({
  providedIn: 'root'
})
export class ApprovalService {
  private approvalRequests: ApprovalRequest[] = [];

  constructor() { }

  getApprovalRequests(): Observable<ApprovalRequest[]> {
    return of(this.approvalRequests);
  }

  getApprovalRequestById(id: string): Observable<ApprovalRequest | undefined> {
    const request = this.approvalRequests.find(req => req.id === id);
    return of(request);
  }

  getApprovalRequestsByApplicationId(applicationId: string): Observable<ApprovalRequest[]> {
    const requests = this.approvalRequests.filter(req => req.applicationId === applicationId);
    return of(requests);
  }

  createApprovalRequest(applicationId: string, applicationName: string, requestType: 'CREATE' | 'UPDATE', changes: Record<string, any>): Observable<ApprovalRequest> {
    const newRequest: ApprovalRequest = {
      id: (this.approvalRequests.length + 1).toString(),
      applicationId,
      applicationName,
      requestType,
      requestedBy: 'current-user', // In real app, get from auth service
      requestedAt: new Date(),
      status: 'PENDING',
      changes
    };

    this.approvalRequests.push(newRequest);
    return of(newRequest);
  }

  approveRequest(requestId: string, reviewerName: string, comments?: string): Observable<ApprovalRequest> {
    const request = this.approvalRequests.find(req => req.id === requestId);
    if (request && request.status === 'PENDING') {
      request.status = 'APPROVED';
      request.reviewedBy = reviewerName;
      request.reviewedAt = new Date();
      request.comments = comments;
    }
    return of(request!);
  }

  rejectRequest(requestId: string, reviewerName: string, comments?: string): Observable<ApprovalRequest> {
    const request = this.approvalRequests.find(req => req.id === requestId);
    if (request && request.status === 'PENDING') {
      request.status = 'REJECTED';
      request.reviewedBy = reviewerName;
      request.reviewedAt = new Date();
      request.comments = comments;
    }
    return of(request!);
  }

  hasActiveRequest(applicationId: string): Observable<boolean> {
    const hasActive = this.approvalRequests.some(
      req => req.applicationId === applicationId && req.status === 'PENDING'
    );
    return of(hasActive);
  }
}