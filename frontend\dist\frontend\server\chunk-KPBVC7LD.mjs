import './polyfills.server.mjs';
import {
  __spreadValues
} from "./chunk-5WKMABBB.mjs";

// src/app/modules/stakeholders/stakeholders.routes.ts
var routes = [
  __spreadValues({
    path: "",
    loadComponent: () => import("./chunk-7R5MMEYY.mjs").then((m) => m.StakeholdersComponent)
  }, true ? { \u0275entryName: "src/app/modules/stakeholders/stakeholders.component.ts" } : {})
];
export {
  routes
};
//# sourceMappingURL=chunk-KPBVC7LD.mjs.map
