import './polyfills.server.mjs';
import {
  TableComponent
} from "./chunk-FCEXAKIO.mjs";
import {
  ChartComponent
} from "./chunk-CIUJRO3I.mjs";
import {
  CardComponent
} from "./chunk-UJ4MSIYR.mjs";
import {
  FormBuilder,
  NgSelectOption,
  ReactiveFormsModule,
  ɵNgSelectMultipleOption
} from "./chunk-22WSPHJT.mjs";
import {
  ButtonComponent
} from "./chunk-NVGHB2VF.mjs";
import {
  RouterModule
} from "./chunk-MUYKQ5QZ.mjs";
import {
  CommonModule,
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵproperty,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1
} from "./chunk-IPMSWJNG.mjs";
import "./chunk-5WKMABBB.mjs";

// src/app/modules/security/security.component.ts
var SecurityComponent = class _SecurityComponent {
  fb;
  securityItems = [];
  filteredSecurityItems = [];
  loading = false;
  // Statistics
  stats = {
    total: 0,
    critical: 0,
    open: 0,
    applications: 0,
    resolvedThisMonth: 0,
    criticalPercentage: 0
  };
  // Chart data
  severityChartData = null;
  trendChartData = null;
  chartOptions = {
    responsive: true,
    maintainAspectRatio: false
  };
  // Table configuration
  tableColumns = [
    { key: "title", label: "Title", sortable: true },
    { key: "type", label: "Type", type: "badge", sortable: true },
    { key: "severity", label: "Severity", type: "badge", sortable: true },
    { key: "status", label: "Status", type: "badge", sortable: true },
    { key: "application", label: "Application", sortable: true },
    { key: "cvssScore", label: "CVSS", type: "number", sortable: true },
    { key: "assignee", label: "Assignee", sortable: true },
    { key: "discoveredDate", label: "Discovered", type: "date", sortable: true }
  ];
  tableActions = [
    {
      label: "View",
      icon: "M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7Z",
      variant: "ghost",
      action: (item) => this.viewSecurityItem(item)
    },
    {
      label: "Edit",
      icon: "M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",
      variant: "ghost",
      action: (item) => this.editSecurityItem(item)
    },
    {
      label: "Delete",
      icon: "M3 6h18m-2 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",
      variant: "error",
      action: (item) => this.deleteSecurityItem(item)
    }
  ];
  constructor(fb) {
    this.fb = fb;
  }
  ngOnInit() {
    this.loadSecurityItems();
  }
  loadSecurityItems() {
    this.loading = true;
    setTimeout(() => {
      this.securityItems = this.generateMockSecurityItems();
      this.filteredSecurityItems = [...this.securityItems];
      this.updateStats();
      this.updateChartData();
      this.loading = false;
    }, 1e3);
  }
  generateMockSecurityItems() {
    const types = ["vulnerability", "compliance", "assessment", "audit"];
    const severities = ["critical", "high", "medium", "low"];
    const statuses = ["open", "in_progress", "resolved", "false_positive"];
    const applications = ["App 1", "App 2", "App 3", "App 4", "App 5"];
    return Array.from({ length: 40 }, (_, i) => ({
      id: i + 1,
      title: `Security Issue ${i + 1}`,
      type: types[Math.floor(Math.random() * types.length)],
      severity: severities[Math.floor(Math.random() * severities.length)],
      status: statuses[Math.floor(Math.random() * statuses.length)],
      application: applications[Math.floor(Math.random() * applications.length)],
      discoveredDate: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1e3),
      resolvedDate: Math.random() > 0.6 ? new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1e3) : void 0,
      assignee: Math.random() > 0.3 ? `User ${Math.floor(Math.random() * 10) + 1}` : void 0,
      description: `Description for security issue ${i + 1}`,
      cveId: Math.random() > 0.7 ? `CVE-2024-${Math.floor(Math.random() * 9999) + 1e3}` : void 0,
      cvssScore: Math.random() > 0.5 ? Math.round(Math.random() * 10 * 10) / 10 : void 0
    }));
  }
  updateStats() {
    this.stats.total = this.securityItems.length;
    this.stats.critical = this.securityItems.filter((s) => s.severity === "critical").length;
    this.stats.open = this.securityItems.filter((s) => s.status === "open" || s.status === "in_progress").length;
    this.stats.applications = new Set(this.securityItems.map((s) => s.application)).size;
    this.stats.resolvedThisMonth = this.securityItems.filter((s) => s.resolvedDate && new Date(s.resolvedDate).getMonth() === (/* @__PURE__ */ new Date()).getMonth()).length;
    this.stats.criticalPercentage = Math.round(this.stats.critical / this.stats.total * 100);
  }
  updateChartData() {
    const severityCount = this.securityItems.reduce((acc, item) => {
      acc[item.severity] = (acc[item.severity] || 0) + 1;
      return acc;
    }, {});
    this.severityChartData = {
      labels: Object.keys(severityCount),
      datasets: [{
        data: Object.values(severityCount),
        backgroundColor: ["#ef4444", "#f97316", "#f59e0b", "#22c55e"]
      }]
    };
    this.trendChartData = {
      labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
      datasets: [
        {
          label: "New Vulnerabilities",
          data: [12, 19, 8, 15, 10, 7],
          borderColor: "#ef4444",
          backgroundColor: "rgba(239, 68, 68, 0.1)",
          tension: 0.4
        },
        {
          label: "Resolved",
          data: [8, 15, 12, 18, 14, 16],
          borderColor: "#22c55e",
          backgroundColor: "rgba(34, 197, 94, 0.1)",
          tension: 0.4
        }
      ]
    };
  }
  openAddModal() {
    console.log("Add security item modal");
  }
  viewSecurityItem(item) {
    console.log("View security item:", item);
  }
  editSecurityItem(item) {
    console.log("Edit security item:", item);
  }
  deleteSecurityItem(item) {
    if (confirm(`Are you sure you want to delete ${item.title}?`)) {
      this.securityItems = this.securityItems.filter((s) => s.id !== item.id);
      this.filteredSecurityItems = this.filteredSecurityItems.filter((s) => s.id !== item.id);
      this.updateStats();
      this.updateChartData();
    }
  }
  onSearchInput(event) {
    const target = event.target;
    this.onSearchChanged(target.value);
  }
  onSearchChanged(searchTerm) {
    this.applyFilters();
  }
  onFilterChanged(filterType, event) {
    const target = event.target;
    this.applyFilters();
  }
  onSortChanged(sort) {
    console.log("Sort changed:", sort);
  }
  applyFilters() {
    this.filteredSecurityItems = [...this.securityItems];
  }
  static \u0275fac = function SecurityComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _SecurityComponent)(\u0275\u0275directiveInject(FormBuilder));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _SecurityComponent, selectors: [["app-security"]], decls: 79, vars: 15, consts: [[1, "security-page"], [1, "page-content"], [1, "page-header"], [1, "header-content"], [1, "page-subtitle"], [1, "header-actions"], ["variant", "primary", "leftIcon", "M12 4v16m8-8H4", 3, "clicked"], [1, "stats-grid"], ["title", "Total Vulnerabilities"], [1, "stat-content"], [1, "stat-number"], [1, "stat-change"], ["title", "Critical Issues"], [1, "stat-number", "critical"], ["title", "Open Issues"], [1, "stat-number", "warning"], ["title", "Resolved This Month"], [1, "stat-number", "success"], [1, "charts-section"], ["title", "Vulnerabilities by Severity", "subtitle", "Distribution by severity level"], ["type", "doughnut", "height", "300px", 3, "data", "options"], ["title", "Security Trends", "subtitle", "Monthly vulnerability trends"], ["type", "line", "height", "300px", 3, "data", "options"], ["title", "Security Issues", "subtitle", "All security vulnerabilities and assessments"], [1, "table-controls"], [1, "search-controls"], ["type", "text", "placeholder", "Search security issues...", 1, "search-input", 3, "input"], [1, "filter-select", 3, "change"], ["value", ""], ["value", "critical"], ["value", "high"], ["value", "medium"], ["value", "low"], ["value", "open"], ["value", "in_progress"], ["value", "resolved"], ["value", "false_positive"], ["value", "vulnerability"], ["value", "compliance"], ["value", "assessment"], ["value", "audit"], [3, "sortChanged", "columns", "data", "actions", "loading", "sortable"]], template: function SecurityComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "div", 3)(4, "h1");
      \u0275\u0275text(5, "Security Overview");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(6, "p", 4);
      \u0275\u0275text(7, "Monitor vulnerabilities and security assessments");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(8, "div", 5)(9, "app-button", 6);
      \u0275\u0275listener("clicked", function SecurityComponent_Template_app_button_clicked_9_listener() {
        return ctx.openAddModal();
      });
      \u0275\u0275text(10, " Add Security Item ");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(11, "div", 7)(12, "app-card", 8)(13, "div", 9)(14, "div", 10);
      \u0275\u0275text(15);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(16, "div", 11);
      \u0275\u0275text(17);
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(18, "app-card", 12)(19, "div", 9)(20, "div", 13);
      \u0275\u0275text(21);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(22, "div", 11);
      \u0275\u0275text(23);
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(24, "app-card", 14)(25, "div", 9)(26, "div", 15);
      \u0275\u0275text(27);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(28, "div", 11);
      \u0275\u0275text(29, "Need attention");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(30, "app-card", 16)(31, "div", 9)(32, "div", 17);
      \u0275\u0275text(33);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(34, "div", 11);
      \u0275\u0275text(35, "Security improvements");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(36, "div", 18)(37, "app-card", 19);
      \u0275\u0275element(38, "app-chart", 20);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(39, "app-card", 21);
      \u0275\u0275element(40, "app-chart", 22);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(41, "app-card", 23)(42, "div", 24)(43, "div", 25)(44, "input", 26);
      \u0275\u0275listener("input", function SecurityComponent_Template_input_input_44_listener($event) {
        return ctx.onSearchInput($event);
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(45, "select", 27);
      \u0275\u0275listener("change", function SecurityComponent_Template_select_change_45_listener($event) {
        return ctx.onFilterChanged("severity", $event);
      });
      \u0275\u0275elementStart(46, "option", 28);
      \u0275\u0275text(47, "All Severities");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(48, "option", 29);
      \u0275\u0275text(49, "Critical");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(50, "option", 30);
      \u0275\u0275text(51, "High");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(52, "option", 31);
      \u0275\u0275text(53, "Medium");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(54, "option", 32);
      \u0275\u0275text(55, "Low");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(56, "select", 27);
      \u0275\u0275listener("change", function SecurityComponent_Template_select_change_56_listener($event) {
        return ctx.onFilterChanged("status", $event);
      });
      \u0275\u0275elementStart(57, "option", 28);
      \u0275\u0275text(58, "All Statuses");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(59, "option", 33);
      \u0275\u0275text(60, "Open");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(61, "option", 34);
      \u0275\u0275text(62, "In Progress");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(63, "option", 35);
      \u0275\u0275text(64, "Resolved");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(65, "option", 36);
      \u0275\u0275text(66, "False Positive");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(67, "select", 27);
      \u0275\u0275listener("change", function SecurityComponent_Template_select_change_67_listener($event) {
        return ctx.onFilterChanged("type", $event);
      });
      \u0275\u0275elementStart(68, "option", 28);
      \u0275\u0275text(69, "All Types");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(70, "option", 37);
      \u0275\u0275text(71, "Vulnerability");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(72, "option", 38);
      \u0275\u0275text(73, "Compliance");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(74, "option", 39);
      \u0275\u0275text(75, "Assessment");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(76, "option", 40);
      \u0275\u0275text(77, "Audit");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(78, "app-table", 41);
      \u0275\u0275listener("sortChanged", function SecurityComponent_Template_app_table_sortChanged_78_listener($event) {
        return ctx.onSortChanged($event);
      });
      \u0275\u0275elementEnd()()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(15);
      \u0275\u0275textInterpolate(ctx.stats.total);
      \u0275\u0275advance(2);
      \u0275\u0275textInterpolate1("Across ", ctx.stats.applications, " applications");
      \u0275\u0275advance(4);
      \u0275\u0275textInterpolate(ctx.stats.critical);
      \u0275\u0275advance(2);
      \u0275\u0275textInterpolate1("", ctx.stats.criticalPercentage, "% of total");
      \u0275\u0275advance(4);
      \u0275\u0275textInterpolate(ctx.stats.open);
      \u0275\u0275advance(6);
      \u0275\u0275textInterpolate(ctx.stats.resolvedThisMonth);
      \u0275\u0275advance(5);
      \u0275\u0275property("data", ctx.severityChartData)("options", ctx.chartOptions);
      \u0275\u0275advance(2);
      \u0275\u0275property("data", ctx.trendChartData)("options", ctx.chartOptions);
      \u0275\u0275advance(38);
      \u0275\u0275property("columns", ctx.tableColumns)("data", ctx.filteredSecurityItems)("actions", ctx.tableActions)("loading", ctx.loading)("sortable", true);
    }
  }, dependencies: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    NgSelectOption,
    \u0275NgSelectMultipleOption,
    CardComponent,
    ButtonComponent,
    TableComponent,
    ChartComponent
  ], styles: [`

.security-page[_ngcontent-%COMP%] {
  min-height: 100%;
}
.page-content[_ngcontent-%COMP%] {
  padding: var(--spacing-xl);
}
.page-header[_ngcontent-%COMP%] {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-xl);
}
.header-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--secondary-900);
}
.page-subtitle[_ngcontent-%COMP%] {
  margin: 0;
  font-size: var(--text-lg);
  color: var(--secondary-600);
}
.stats-grid[_ngcontent-%COMP%] {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}
.stat-content[_ngcontent-%COMP%] {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
}
.stat-number[_ngcontent-%COMP%] {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--secondary-900);
}
.stat-number.critical[_ngcontent-%COMP%] {
  color: var(--error-600);
}
.stat-number.warning[_ngcontent-%COMP%] {
  color: var(--warning-600);
}
.stat-number.success[_ngcontent-%COMP%] {
  color: var(--success-600);
}
.stat-change[_ngcontent-%COMP%] {
  font-size: var(--text-sm);
  color: var(--secondary-600);
}
.charts-section[_ngcontent-%COMP%] {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}
.table-controls[_ngcontent-%COMP%] {
  margin-bottom: var(--spacing-lg);
}
.search-controls[_ngcontent-%COMP%] {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
  flex-wrap: wrap;
}
.search-input[_ngcontent-%COMP%], 
.filter-select[_ngcontent-%COMP%] {
  height: 40px;
  padding: 0 var(--spacing-md);
  border: 1px solid var(--secondary-300);
  border-radius: var(--radius-md);
  background: white;
  font-size: var(--text-sm);
  color: var(--secondary-800);
  transition: all 0.2s ease;
}
.search-input[_ngcontent-%COMP%]:focus, 
.filter-select[_ngcontent-%COMP%]:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}
.search-input[_ngcontent-%COMP%] {
  flex: 1;
  min-width: 200px;
}
.filter-select[_ngcontent-%COMP%] {
  min-width: 120px;
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-sm) center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
  appearance: none;
}
@media (max-width: 768px) {
  .page-header[_ngcontent-%COMP%] {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }
  .stats-grid[_ngcontent-%COMP%] {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  .charts-section[_ngcontent-%COMP%] {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  .search-controls[_ngcontent-%COMP%] {
    flex-direction: column;
    align-items: stretch;
  }
  .search-input[_ngcontent-%COMP%], 
   .filter-select[_ngcontent-%COMP%] {
    min-width: auto;
  }
}
/*# sourceMappingURL=security.component.css.map */`] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(SecurityComponent, [{
    type: Component,
    args: [{ selector: "app-security", standalone: true, imports: [
      CommonModule,
      RouterModule,
      ReactiveFormsModule,
      CardComponent,
      ButtonComponent,
      TableComponent,
      ChartComponent
    ], template: `
    <div class="security-page">
      <div class="page-content">
        <div class="page-header">
          <div class="header-content">
            <h1>Security Overview</h1>
            <p class="page-subtitle">Monitor vulnerabilities and security assessments</p>
          </div>
          <div class="header-actions">
            <app-button
              variant="primary"
              leftIcon="M12 4v16m8-8H4"
              (clicked)="openAddModal()"
            >
              Add Security Item
            </app-button>
          </div>
        </div>

      <!-- Statistics Cards -->
      <div class="stats-grid">
        <app-card title="Total Vulnerabilities">
          <div class="stat-content">
            <div class="stat-number">{{ stats.total }}</div>
            <div class="stat-change">Across {{ stats.applications }} applications</div>
          </div>
        </app-card>

        <app-card title="Critical Issues">
          <div class="stat-content">
            <div class="stat-number critical">{{ stats.critical }}</div>
            <div class="stat-change">{{ stats.criticalPercentage }}% of total</div>
          </div>
        </app-card>

        <app-card title="Open Issues">
          <div class="stat-content">
            <div class="stat-number warning">{{ stats.open }}</div>
            <div class="stat-change">Need attention</div>
          </div>
        </app-card>

        <app-card title="Resolved This Month">
          <div class="stat-content">
            <div class="stat-number success">{{ stats.resolvedThisMonth }}</div>
            <div class="stat-change">Security improvements</div>
          </div>
        </app-card>
      </div>

      <!-- Charts Section -->
      <div class="charts-section">
        <app-card title="Vulnerabilities by Severity" subtitle="Distribution by severity level">
          <app-chart
            type="doughnut"
            [data]="severityChartData"
            [options]="chartOptions"
            height="300px"
          ></app-chart>
        </app-card>

        <app-card title="Security Trends" subtitle="Monthly vulnerability trends">
          <app-chart
            type="line"
            [data]="trendChartData"
            [options]="chartOptions"
            height="300px"
          ></app-chart>
        </app-card>
      </div>

      <!-- Security Items Table -->
      <app-card title="Security Issues" subtitle="All security vulnerabilities and assessments">
        <div class="table-controls">
          <div class="search-controls">
            <input
              type="text"
              placeholder="Search security issues..."
              class="search-input"
              (input)="onSearchInput($event)"
            >
            <select class="filter-select" (change)="onFilterChanged('severity', $event)">
              <option value="">All Severities</option>
              <option value="critical">Critical</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="low">Low</option>
            </select>
            <select class="filter-select" (change)="onFilterChanged('status', $event)">
              <option value="">All Statuses</option>
              <option value="open">Open</option>
              <option value="in_progress">In Progress</option>
              <option value="resolved">Resolved</option>
              <option value="false_positive">False Positive</option>
            </select>
            <select class="filter-select" (change)="onFilterChanged('type', $event)">
              <option value="">All Types</option>
              <option value="vulnerability">Vulnerability</option>
              <option value="compliance">Compliance</option>
              <option value="assessment">Assessment</option>
              <option value="audit">Audit</option>
            </select>
          </div>
        </div>

        <app-table
          [columns]="tableColumns"
          [data]="filteredSecurityItems"
          [actions]="tableActions"
          [loading]="loading"
          [sortable]="true"
          (sortChanged)="onSortChanged($event)"
        ></app-table>
      </app-card>
      </div>
    </div>
  `, styles: [`/* angular:styles/component:scss;f1ce24180e20ae1e322bff1479bbbc37866b9728841245e65f94b054f5913c84;C:/Users/<USER>/Documents/VibeCoding/AppCatalog/app-catalog/frontend/src/app/modules/security/security.component.ts */
.security-page {
  min-height: 100%;
}
.page-content {
  padding: var(--spacing-xl);
}
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-xl);
}
.header-content h1 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--secondary-900);
}
.page-subtitle {
  margin: 0;
  font-size: var(--text-lg);
  color: var(--secondary-600);
}
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}
.stat-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
}
.stat-number {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--secondary-900);
}
.stat-number.critical {
  color: var(--error-600);
}
.stat-number.warning {
  color: var(--warning-600);
}
.stat-number.success {
  color: var(--success-600);
}
.stat-change {
  font-size: var(--text-sm);
  color: var(--secondary-600);
}
.charts-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}
.table-controls {
  margin-bottom: var(--spacing-lg);
}
.search-controls {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
  flex-wrap: wrap;
}
.search-input,
.filter-select {
  height: 40px;
  padding: 0 var(--spacing-md);
  border: 1px solid var(--secondary-300);
  border-radius: var(--radius-md);
  background: white;
  font-size: var(--text-sm);
  color: var(--secondary-800);
  transition: all 0.2s ease;
}
.search-input:focus,
.filter-select:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}
.search-input {
  flex: 1;
  min-width: 200px;
}
.filter-select {
  min-width: 120px;
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-sm) center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
  appearance: none;
}
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }
  .stats-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  .charts-section {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  .search-controls {
    flex-direction: column;
    align-items: stretch;
  }
  .search-input,
  .filter-select {
    min-width: auto;
  }
}
/*# sourceMappingURL=security.component.css.map */
`] }]
  }], () => [{ type: FormBuilder }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(SecurityComponent, { className: "SecurityComponent", filePath: "src/app/modules/security/security.component.ts", lineNumber: 301 });
})();
export {
  SecurityComponent
};
//# sourceMappingURL=chunk-LEJKBIUU.mjs.map
