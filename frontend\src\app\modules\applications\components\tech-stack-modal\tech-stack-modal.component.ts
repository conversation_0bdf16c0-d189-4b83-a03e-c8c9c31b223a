import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { SlideModalComponent } from '../../../../shared/components/slide-modal/slide-modal.component';
import { FormInputComponent } from '../../../../shared/components/form-input/form-input.component';

export interface TechStackFormData {
  category: string;
  technology: string;
  version: string;
  purpose: string;
  isCore: boolean;
}

@Component({
  selector: 'app-tech-stack-modal',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, SlideModalComponent, FormInputComponent],
  template: `
    <app-slide-modal
      [isOpen]="isOpen"
      [title]="editMode ? 'Edit Technology' : 'Add Technology'"
      subtitle="Configure technology stack details"
      [loading]="loading"
      [canConfirm]="techStackForm.valid"
      confirmText="Save Technology"
      (closed)="onClose()"
      (confirmed)="onSave()"
      (cancelled)="onClose()"
    >
      <form [formGroup]="techStackForm" class="tech-stack-form">
        <div class="form-grid">
          <div class="form-group">
            <label class="form-label">Category</label>
            <select formControlName="category" class="form-select">
              <option value="frontend">Frontend</option>
              <option value="backend">Backend</option>
              <option value="database">Database</option>
              <option value="infrastructure">Infrastructure</option>
              <option value="monitoring">Monitoring</option>
              <option value="security">Security</option>
              <option value="testing">Testing</option>
              <option value="deployment">Deployment</option>
              <option value="communication">Communication</option>
            </select>
          </div>

          <app-form-input
            label="Technology"
            placeholder="e.g., React, Node.js, PostgreSQL"
            formControlName="technology"
            [required]="true"
            [errorMessage]="getFieldError('technology')"
          ></app-form-input>

          <app-form-input
            label="Version"
            placeholder="e.g., 18.2.0, 16.x"
            formControlName="version"
            [required]="true"
            [errorMessage]="getFieldError('version')"
          ></app-form-input>

          <div class="form-group">
            <label class="checkbox-label">
              <input
                type="checkbox"
                formControlName="isCore"
                class="checkbox-input"
              >
              <span class="checkbox-text">Core Technology</span>
              <span class="checkbox-description">This is a core/critical technology for the application</span>
            </label>
          </div>

          <div class="form-group full-width">
            <label class="form-label">Purpose</label>
            <textarea
              formControlName="purpose"
              class="form-textarea"
              placeholder="Describe how this technology is used in the application..."
              rows="3"
            ></textarea>
          </div>
        </div>
      </form>
    </app-slide-modal>
  `,
  styles: [`
    .tech-stack-form {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-lg);
    }

    .form-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-lg);
    }

    .form-group {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-sm);
    }

    .form-group.full-width {
      grid-column: 1 / -1;
    }

    .form-label {
      font-size: var(--text-sm);
      font-weight: 500;
      color: var(--secondary-700);
    }

    .form-select,
    .form-textarea {
      border: 1px solid var(--secondary-300);
      border-radius: var(--radius-md);
      background: white;
      font-size: var(--text-sm);
      color: var(--secondary-800);
      transition: all 0.2s ease;

      &:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px var(--primary-100);
      }
    }

    .form-select {
      height: 40px;
      padding: 0 var(--spacing-md);
      cursor: pointer;
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
      background-position: right var(--spacing-sm) center;
      background-repeat: no-repeat;
      background-size: 16px;
      padding-right: 40px;
      appearance: none;
    }

    .form-textarea {
      padding: var(--spacing-md);
      resize: vertical;
      min-height: 80px;
      font-family: inherit;
    }

    .checkbox-label {
      display: flex;
      align-items: flex-start;
      gap: var(--spacing-sm);
      cursor: pointer;
      padding: var(--spacing-md);
      border: 1px solid var(--secondary-200);
      border-radius: var(--radius-md);
      transition: all 0.2s ease;

      &:hover {
        border-color: var(--primary-300);
        background: var(--primary-25);
      }
    }

    .checkbox-input {
      margin: 0;
      margin-top: 2px;
    }

    .checkbox-text {
      font-size: var(--text-sm);
      font-weight: 500;
      color: var(--secondary-900);
      margin-bottom: var(--spacing-xs);
    }

    .checkbox-description {
      font-size: var(--text-xs);
      color: var(--secondary-600);
      display: block;
    }

    @media (max-width: 768px) {
      .form-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
      }
    }
  `]
})
export class TechStackModalComponent implements OnInit {
  @Input() isOpen = false;
  @Input() editMode = false;
  @Input() initialData: TechStackFormData | null = null;
  @Input() loading = false;

  @Output() closed = new EventEmitter<void>();
  @Output() saved = new EventEmitter<TechStackFormData>();

  techStackForm!: FormGroup;

  constructor(private fb: FormBuilder) {}

  ngOnInit(): void {
    this.initializeForm();
  }

  ngOnChanges(): void {
    if (this.techStackForm && this.initialData) {
      this.techStackForm.patchValue(this.initialData);
    }
  }

  private initializeForm(): void {
    this.techStackForm = this.fb.group({
      category: ['frontend', [Validators.required]],
      technology: ['', [Validators.required, Validators.minLength(2)]],
      version: ['', [Validators.required]],
      purpose: ['', [Validators.required]],
      isCore: [false]
    });

    if (this.initialData) {
      this.techStackForm.patchValue(this.initialData);
    }
  }

  getFieldError(fieldName: string): string {
    const field = this.techStackForm.get(fieldName);
    if (field && field.invalid && field.touched) {
      if (field.errors?.['required']) {
        return `${fieldName} is required`;
      }
      if (field.errors?.['minlength']) {
        return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;
      }
    }
    return '';
  }

  onClose(): void {
    this.techStackForm.reset();
    this.closed.emit();
  }

  onSave(): void {
    if (this.techStackForm.valid) {
      this.saved.emit(this.techStackForm.value);
    }
  }
}
