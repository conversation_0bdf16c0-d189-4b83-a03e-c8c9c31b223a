import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { AuthService } from '../../services/auth.service';
import { LoginInputComponent } from '../login-input/login-input.component';
import { ButtonComponent } from '../../../../shared/components/button/button.component';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, LoginInputComponent, ButtonComponent],
  template: `
    <div class="login-container">
      <div class="login-card">
        <!-- Logo/Branding -->
        <div class="login-header">
          <div class="logo-container">
            <svg class="logo-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
            </svg>
            <h1 class="logo-text">App Catalog</h1>
          </div>
          <h2 class="login-title">
            Welcome back
          </h2>
          <p class="login-subtitle">
            Sign in to your account to continue
          </p>
        </div>

        <!-- Error Message -->
        <div class="error-message" *ngIf="error">
          <svg class="error-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="15" y1="9" x2="9" y2="15"></line>
            <line x1="9" y1="9" x2="15" y2="15"></line>
          </svg>
          <span>{{ error }}</span>
        </div>

        <!-- Login Form -->
        <form class="login-form" [formGroup]="loginForm" (ngSubmit)="onSubmit()">
          <!-- Email Input -->
          <app-login-input
            formControlName="email"
            type="email"
            label="Email address"
            inputId="email"
            [errorMessage]="getFieldError('email')"
            [disabled]="loading"
            placeholder="Enter your email"
            [required]="true"
          ></app-login-input>

          <!-- Password Input -->
          <app-login-input
            formControlName="password"
            type="password"
            label="Password"
            inputId="password"
            [errorMessage]="getFieldError('password')"
            [disabled]="loading"
            placeholder="Enter your password"
            [required]="true"
          ></app-login-input>

          <!-- Remember Me & Forgot Password -->
          <div class="form-options">
            <div class="remember-me">
              <input
                id="remember-me"
                formControlName="rememberMe"
                type="checkbox"
                [disabled]="loading"
                class="checkbox-input"
              >
              <label for="remember-me" class="checkbox-label">
                Remember me
              </label>
            </div>

            <div class="forgot-password">
              <a href="#" class="forgot-link">
                Forgot your password?
              </a>
            </div>
          </div>

          <!-- Submit Button -->
          <app-button
            type="submit"
            variant="primary"
            size="md"
            [disabled]="loginForm.invalid"
            [loading]="loading"
            [fullWidth]="true"
            loadingText="Signing in..."
            leftIcon="M5 13l4 4L19 7"
            (clicked)="onSubmit()"
          >
            Sign in
          </app-button>
        </form>

        <!-- Additional Links -->
        <div class="login-footer">
          <p class="footer-text">
            Don't have an account?
            <a href="#" class="footer-link">Contact your administrator</a>
          </p>
        </div>
      </div>
    </div>
  `,
  styles: [`
    :host {
      display: block;
      min-height: 100vh;
    }

    .login-container {
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: var(--spacing-lg);
      background: linear-gradient(
        135deg,
        var(--neutral-50) 0%,
        var(--neutral-100) 100%
      );
    }

    .login-card {
      width: 100%;
      max-width: 480px;
      background: white;
      border-radius: var(--radius-xl);
      box-shadow: var(--shadow-xl);
      border: 1px solid var(--secondary-200);
      overflow: hidden;
    }

    .login-header {
      padding: var(--spacing-2xl) var(--spacing-xl) var(--spacing-xl);
      text-align: center;
      background: linear-gradient(
        135deg,
        var(--primary-50) 0%,
        white 100%
      );
      border-bottom: 1px solid var(--secondary-100);
    }

    .logo-container {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: var(--spacing-md);
      margin-bottom: var(--spacing-lg);
    }

    .logo-icon {
      width: 32px;
      height: 32px;
      color: var(--primary-600);
      stroke-width: 2;
    }

    .logo-text {
      font-size: var(--text-xl);
      font-weight: 700;
      color: var(--secondary-900);
      margin: 0;
    }

    .login-title {
      font-size: var(--text-2xl);
      font-weight: 600;
      color: var(--secondary-900);
      margin: 0 0 var(--spacing-sm) 0;
      line-height: var(--leading-tight);
    }

    .login-subtitle {
      font-size: var(--text-sm);
      color: var(--secondary-600);
      margin: 0;
      line-height: var(--leading-normal);
    }

    .error-message {
      margin: var(--spacing-lg) var(--spacing-xl) 0;
      padding: var(--spacing-md);
      background: var(--error-50);
      border: 1px solid var(--error-200);
      border-radius: var(--radius-md);
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      font-size: var(--text-sm);
      color: var(--error-700);
    }

    .error-icon {
      width: 16px;
      height: 16px;
      color: var(--error-500);
      flex-shrink: 0;
      stroke-width: 2;
    }

    .login-form {
      padding: var(--spacing-xl);
      display: flex;
      flex-direction: column;
      gap: var(--spacing-lg);
    }

    .form-options {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: var(--spacing-md);
    }

    .remember-me {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
    }

    .checkbox-input {
      width: 16px;
      height: 16px;
      border: 1px solid var(--secondary-300);
      border-radius: var(--radius-sm);
      background: white;
      cursor: pointer;
      transition: all 0.2s ease;

      &:checked {
        background-color: var(--primary-600);
        border-color: var(--primary-600);
      }

      &:focus {
        outline: none;
        box-shadow: 0 0 0 3px var(--primary-100);
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }

    .checkbox-label {
      font-size: var(--text-sm);
      color: var(--secondary-700);
      cursor: pointer;
      user-select: none;
    }

    .forgot-password {
      flex-shrink: 0;
    }

    .forgot-link {
      font-size: var(--text-sm);
      color: var(--primary-600);
      text-decoration: none;
      font-weight: 500;
      transition: color 0.2s ease;

      &:hover {
        color: var(--primary-700);
        text-decoration: underline;
      }

      &:focus {
        outline: none;
        color: var(--primary-700);
        text-decoration: underline;
      }
    }

    .login-footer {
      padding: var(--spacing-lg) var(--spacing-xl) var(--spacing-xl);
      text-align: center;
      background: var(--secondary-50);
      border-top: 1px solid var(--secondary-100);
    }

    .footer-text {
      font-size: var(--text-sm);
      color: var(--secondary-600);
      margin: 0;
    }

    .footer-link {
      color: var(--primary-600);
      text-decoration: none;
      font-weight: 500;
      transition: color 0.2s ease;

      &:hover {
        color: var(--primary-700);
        text-decoration: underline;
      }

      &:focus {
        outline: none;
        color: var(--primary-700);
        text-decoration: underline;
      }
    }

    /* Responsive Design */
    @media (max-width: 480px) {
      .login-container {
        padding: var(--spacing-md);
      }

      .login-card {
        max-width: 100%;
      }

      .login-header {
        padding: var(--spacing-xl) var(--spacing-lg) var(--spacing-lg);
      }

      .login-form {
        padding: var(--spacing-lg);
      }

      .login-footer {
        padding: var(--spacing-md) var(--spacing-lg) var(--spacing-lg);
      }

      .form-options {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
      }

      .forgot-password {
        align-self: flex-end;
      }
    }

    /* High Contrast Mode Support */
    @media (prefers-contrast: high) {
      .login-card {
        border-width: 2px;
      }

      .checkbox-input {
        border-width: 2px;
      }
    }

    /* Reduced Motion Support */
    @media (prefers-reduced-motion: reduce) {
      .checkbox-input,
      .forgot-link,
      .footer-link {
        transition: none;
      }
    }
  `]
})
export class LoginComponent {
  loginForm: FormGroup;
  loading = false;
  error = '';
  returnUrl: string = '/dashboard';

  constructor(
    private formBuilder: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private authService: AuthService
  ) {
    this.loginForm = this.formBuilder.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required]],
      rememberMe: [false]
    });

    // Get return url from route parameters or default to '/'
    this.route.queryParams.subscribe(params => {
      this.returnUrl = params['returnUrl'] || '/dashboard';
    });

    // Redirect if already logged in
    if (this.authService.isAuthenticated()) {
      this.router.navigate([this.returnUrl]);
    }
  }

  getFieldError(fieldName: string): string {
    const field = this.loginForm.get(fieldName);
    if (field && field.errors && field.touched) {
      if (field.errors['required']) {
        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;
      }
      if (field.errors['email']) {
        return 'Please enter a valid email address';
      }
    }
    return '';
  }

  onSubmit() {
    if (this.loginForm.invalid) {
      // Mark all fields as touched to trigger validation messages
      Object.keys(this.loginForm.controls).forEach(key => {
        const control = this.loginForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    this.loading = true;
    this.error = '';

    this.authService.login(this.loginForm.value).subscribe({
      next: () => {
        this.router.navigate([this.returnUrl]);
      },
      error: (error: any) => {
        this.error = error.message || 'An error occurred during login. Please try again.';
        this.loading = false;
      }
    });
  }
}
