import './polyfills.server.mjs';
import {
  CardComponent
} from "./chunk-UJ4MSIYR.mjs";
import {
  CommonModule,
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵdefineComponent,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵtext
} from "./chunk-IPMSWJNG.mjs";
import "./chunk-5WKMABBB.mjs";

// src/app/modules/security/assessments/assessments.component.ts
var AssessmentsComponent = class _AssessmentsComponent {
  static \u0275fac = function AssessmentsComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _AssessmentsComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _AssessmentsComponent, selectors: [["app-assessments"]], decls: 7, vars: 0, consts: [[1, "assessments-page"], ["title", "Assessment Management", "subtitle", "Schedule and track security assessments"], [1, "placeholder"]], template: function AssessmentsComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "h1");
      \u0275\u0275text(2, "Security Assessments");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(3, "app-card", 1)(4, "div", 2)(5, "p");
      \u0275\u0275text(6, "Security assessments module coming soon...");
      \u0275\u0275elementEnd()()()();
    }
  }, dependencies: [CommonModule, CardComponent], styles: ["\n\n.assessments-page[_ngcontent-%COMP%] {\n  min-height: 100%;\n}\n.placeholder[_ngcontent-%COMP%] {\n  padding: var(--spacing-xl);\n  text-align: center;\n  color: var(--secondary-600);\n}\n/*# sourceMappingURL=assessments.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AssessmentsComponent, [{
    type: Component,
    args: [{ selector: "app-assessments", standalone: true, imports: [CommonModule, CardComponent], template: `
    <div class="assessments-page">
      <h1>Security Assessments</h1>
      <app-card title="Assessment Management" subtitle="Schedule and track security assessments">
        <div class="placeholder">
          <p>Security assessments module coming soon...</p>
        </div>
      </app-card>
    </div>
  `, styles: ["/* angular:styles/component:scss;dea13569113e7cae48bc162162ebada63a413f08c74cbc3142cea00b792a4a8b;C:/Users/<USER>/Documents/VibeCoding/AppCatalog/app-catalog/frontend/src/app/modules/security/assessments/assessments.component.ts */\n.assessments-page {\n  min-height: 100%;\n}\n.placeholder {\n  padding: var(--spacing-xl);\n  text-align: center;\n  color: var(--secondary-600);\n}\n/*# sourceMappingURL=assessments.component.css.map */\n"] }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(AssessmentsComponent, { className: "AssessmentsComponent", filePath: "src/app/modules/security/assessments/assessments.component.ts", lineNumber: 30 });
})();
export {
  AssessmentsComponent
};
//# sourceMappingURL=chunk-JOJ3ACSZ.mjs.map
