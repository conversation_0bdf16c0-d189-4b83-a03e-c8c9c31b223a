import {
  FormInputComponent
} from "./chunk-WQFVHIJL.js";
import {
  CheckboxControlValueAccessor,
  FormBuilder,
  FormControlName,
  FormGroupDirective,
  NgControlStatus,
  NgControlStatusGroup,
  NgSelectOption,
  ReactiveFormsModule,
  RequiredValidator,
  SelectControlValueAccessor,
  Validators,
  ɵNgNoValidate,
  ɵNgSelectMultipleOption
} from "./chunk-SM75SJZE.js";
import {
  CardComponent
} from "./chunk-SLWZQAXJ.js";
import {
  ButtonComponent
} from "./chunk-QMVBAXS7.js";
import {
  CommonModule,
  Component,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵproperty,
  ɵɵtext
} from "./chunk-3JQLQ36P.js";
import "./chunk-WDMUDEB6.js";

// src/app/modules/settings/settings.component.ts
var SettingsComponent = class _SettingsComponent {
  fb;
  profileForm;
  notificationForm;
  appearanceForm;
  systemForm;
  saving = {
    profile: false,
    notifications: false,
    appearance: false,
    system: false
  };
  exporting = false;
  clearing = false;
  resetting = false;
  constructor(fb) {
    this.fb = fb;
  }
  ngOnInit() {
    this.initializeForms();
    this.loadSettings();
  }
  initializeForms() {
    this.profileForm = this.fb.group({
      fullName: ["John Doe", [Validators.required]],
      email: ["<EMAIL>", [Validators.required, Validators.email]],
      jobTitle: ["System Administrator"],
      department: ["IT"]
    });
    this.notificationForm = this.fb.group({
      emailNotifications: [true],
      pushNotifications: [true],
      securityAlerts: [true],
      dependencyUpdates: [true],
      weeklyReports: [false]
    });
    this.appearanceForm = this.fb.group({
      theme: ["light"],
      language: ["en"],
      compactMode: [false],
      sidebarCollapsed: [false]
    });
    this.systemForm = this.fb.group({
      autoSave: [true],
      autoSaveInterval: [5, [Validators.min(1), Validators.max(60)]],
      sessionTimeout: [30, [Validators.min(5), Validators.max(480)]],
      enableAnalytics: [true],
      enableTelemetry: [false]
    });
  }
  loadSettings() {
    const savedSettings = localStorage.getItem("appSettings");
    if (savedSettings) {
      try {
        const settings = JSON.parse(savedSettings);
        if (settings.profile)
          this.profileForm.patchValue(settings.profile);
        if (settings.notifications)
          this.notificationForm.patchValue(settings.notifications);
        if (settings.appearance)
          this.appearanceForm.patchValue(settings.appearance);
        if (settings.system)
          this.systemForm.patchValue(settings.system);
      } catch (error) {
        console.error("Error loading settings:", error);
      }
    }
  }
  saveToStorage() {
    const settings = {
      profile: this.profileForm.value,
      notifications: this.notificationForm.value,
      appearance: this.appearanceForm.value,
      system: this.systemForm.value
    };
    localStorage.setItem("appSettings", JSON.stringify(settings));
  }
  saveProfile() {
    if (this.profileForm.valid) {
      this.saving.profile = true;
      setTimeout(() => {
        this.saveToStorage();
        this.saving.profile = false;
        console.log("Profile saved:", this.profileForm.value);
      }, 1e3);
    }
  }
  saveNotifications() {
    this.saving.notifications = true;
    setTimeout(() => {
      this.saveToStorage();
      this.saving.notifications = false;
      console.log("Notifications saved:", this.notificationForm.value);
    }, 1e3);
  }
  saveAppearance() {
    this.saving.appearance = true;
    setTimeout(() => {
      this.saveToStorage();
      this.saving.appearance = false;
      console.log("Appearance saved:", this.appearanceForm.value);
    }, 1e3);
  }
  saveSystem() {
    if (this.systemForm.valid) {
      this.saving.system = true;
      setTimeout(() => {
        this.saveToStorage();
        this.saving.system = false;
        console.log("System settings saved:", this.systemForm.value);
      }, 1e3);
    }
  }
  exportData() {
    this.exporting = true;
    setTimeout(() => {
      const data = {
        profile: this.profileForm.value,
        notifications: this.notificationForm.value,
        appearance: this.appearanceForm.value,
        system: this.systemForm.value,
        exportDate: (/* @__PURE__ */ new Date()).toISOString()
      };
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: "application/json" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = "app-catalog-settings.json";
      a.click();
      URL.revokeObjectURL(url);
      this.exporting = false;
      console.log("Data exported");
    }, 1500);
  }
  clearCache() {
    this.clearing = true;
    setTimeout(() => {
      if ("caches" in window) {
        caches.keys().then((names) => {
          names.forEach((name) => caches.delete(name));
        });
      }
      this.clearing = false;
      console.log("Cache cleared");
    }, 1e3);
  }
  resetSettings() {
    if (confirm("Are you sure you want to reset all settings to default values? This action cannot be undone.")) {
      this.resetting = true;
      setTimeout(() => {
        localStorage.removeItem("appSettings");
        this.initializeForms();
        this.resetting = false;
        console.log("Settings reset to defaults");
      }, 1e3);
    }
  }
  static \u0275fac = function SettingsComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _SettingsComponent)(\u0275\u0275directiveInject(FormBuilder));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _SettingsComponent, selectors: [["app-settings"]], decls: 158, vars: 17, consts: [[1, "settings-page"], [1, "settings-header"], [1, "settings-subtitle"], [1, "settings-content"], ["title", "User Profile", "subtitle", "Manage your personal information"], [1, "settings-form", 3, "formGroup"], [1, "form-grid"], ["label", "Full Name", "placeholder", "Enter your full name", "formControlName", "fullName", 3, "required"], ["label", "Email Address", "placeholder", "Enter your email", "formControlName", "email", "type", "email", 3, "required"], ["label", "Job Title", "placeholder", "Enter your job title", "formControlName", "jobTitle"], ["label", "Department", "placeholder", "Enter your department", "formControlName", "department"], [1, "form-actions"], ["variant", "primary", 3, "clicked", "loading"], ["title", "Notifications", "subtitle", "Configure how you receive notifications"], [1, "checkbox-grid"], [1, "checkbox-item"], ["type", "checkbox", "formControlName", "emailNotifications", 1, "checkbox-input"], [1, "checkbox-content"], [1, "checkbox-title"], [1, "checkbox-description"], ["type", "checkbox", "formControlName", "pushNotifications", 1, "checkbox-input"], ["type", "checkbox", "formControlName", "securityAlerts", 1, "checkbox-input"], ["type", "checkbox", "formControlName", "dependencyUpdates", 1, "checkbox-input"], ["type", "checkbox", "formControlName", "weeklyReports", 1, "checkbox-input"], ["title", "Appearance", "subtitle", "Customize the look and feel of the application"], [1, "form-group"], [1, "form-label"], ["formControlName", "theme", 1, "form-select"], ["value", "light"], ["value", "dark"], ["value", "auto"], ["formControlName", "language", 1, "form-select"], ["value", "en"], ["value", "es"], ["value", "fr"], ["value", "de"], ["type", "checkbox", "formControlName", "compactMode", 1, "checkbox-input"], ["type", "checkbox", "formControlName", "sidebarCollapsed", 1, "checkbox-input"], ["title", "System", "subtitle", "Configure system behavior and performance"], ["label", "Auto-save Interval (minutes)", "placeholder", "5", "formControlName", "autoSaveInterval", "type", "number", 3, "min", "max"], ["label", "Session Timeout (minutes)", "placeholder", "30", "formControlName", "sessionTimeout", "type", "number", 3, "min", "max"], ["type", "checkbox", "formControlName", "autoSave", 1, "checkbox-input"], ["type", "checkbox", "formControlName", "enableAnalytics", 1, "checkbox-input"], ["type", "checkbox", "formControlName", "enableTelemetry", 1, "checkbox-input"], ["title", "Data Management", "subtitle", "Manage your data and privacy settings"], [1, "settings-form"], [1, "data-actions"], [1, "action-item"], [1, "action-content"], [1, "action-title"], [1, "action-description"], ["variant", "secondary", 3, "clicked", "loading"], [1, "action-item", "danger"], ["variant", "error", 3, "clicked", "loading"]], template: function SettingsComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "h1");
      \u0275\u0275text(3, "Settings");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(4, "p", 2);
      \u0275\u0275text(5, "Configure your application preferences and system settings");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(6, "div", 3)(7, "app-card", 4)(8, "form", 5)(9, "div", 6);
      \u0275\u0275element(10, "app-form-input", 7)(11, "app-form-input", 8)(12, "app-form-input", 9)(13, "app-form-input", 10);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(14, "div", 11)(15, "app-button", 12);
      \u0275\u0275listener("clicked", function SettingsComponent_Template_app_button_clicked_15_listener() {
        return ctx.saveProfile();
      });
      \u0275\u0275text(16, " Save Profile ");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(17, "app-card", 13)(18, "form", 5)(19, "div", 14)(20, "label", 15);
      \u0275\u0275element(21, "input", 16);
      \u0275\u0275elementStart(22, "div", 17)(23, "span", 18);
      \u0275\u0275text(24, "Email Notifications");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(25, "span", 19);
      \u0275\u0275text(26, "Receive notifications via email");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(27, "label", 15);
      \u0275\u0275element(28, "input", 20);
      \u0275\u0275elementStart(29, "div", 17)(30, "span", 18);
      \u0275\u0275text(31, "Push Notifications");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(32, "span", 19);
      \u0275\u0275text(33, "Receive browser push notifications");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(34, "label", 15);
      \u0275\u0275element(35, "input", 21);
      \u0275\u0275elementStart(36, "div", 17)(37, "span", 18);
      \u0275\u0275text(38, "Security Alerts");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(39, "span", 19);
      \u0275\u0275text(40, "Get notified about security vulnerabilities");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(41, "label", 15);
      \u0275\u0275element(42, "input", 22);
      \u0275\u0275elementStart(43, "div", 17)(44, "span", 18);
      \u0275\u0275text(45, "Dependency Updates");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(46, "span", 19);
      \u0275\u0275text(47, "Notifications for dependency updates");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(48, "label", 15);
      \u0275\u0275element(49, "input", 23);
      \u0275\u0275elementStart(50, "div", 17)(51, "span", 18);
      \u0275\u0275text(52, "Weekly Reports");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(53, "span", 19);
      \u0275\u0275text(54, "Receive weekly summary reports");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(55, "div", 11)(56, "app-button", 12);
      \u0275\u0275listener("clicked", function SettingsComponent_Template_app_button_clicked_56_listener() {
        return ctx.saveNotifications();
      });
      \u0275\u0275text(57, " Save Notifications ");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(58, "app-card", 24)(59, "form", 5)(60, "div", 6)(61, "div", 25)(62, "label", 26);
      \u0275\u0275text(63, "Theme");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(64, "select", 27)(65, "option", 28);
      \u0275\u0275text(66, "Light");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(67, "option", 29);
      \u0275\u0275text(68, "Dark");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(69, "option", 30);
      \u0275\u0275text(70, "Auto (System)");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(71, "div", 25)(72, "label", 26);
      \u0275\u0275text(73, "Language");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(74, "select", 31)(75, "option", 32);
      \u0275\u0275text(76, "English");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(77, "option", 33);
      \u0275\u0275text(78, "Spanish");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(79, "option", 34);
      \u0275\u0275text(80, "French");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(81, "option", 35);
      \u0275\u0275text(82, "German");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(83, "div", 14)(84, "label", 15);
      \u0275\u0275element(85, "input", 36);
      \u0275\u0275elementStart(86, "div", 17)(87, "span", 18);
      \u0275\u0275text(88, "Compact Mode");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(89, "span", 19);
      \u0275\u0275text(90, "Use a more compact interface layout");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(91, "label", 15);
      \u0275\u0275element(92, "input", 37);
      \u0275\u0275elementStart(93, "div", 17)(94, "span", 18);
      \u0275\u0275text(95, "Collapsed Sidebar");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(96, "span", 19);
      \u0275\u0275text(97, "Start with sidebar collapsed by default");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(98, "div", 11)(99, "app-button", 12);
      \u0275\u0275listener("clicked", function SettingsComponent_Template_app_button_clicked_99_listener() {
        return ctx.saveAppearance();
      });
      \u0275\u0275text(100, " Save Appearance ");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(101, "app-card", 38)(102, "form", 5)(103, "div", 6);
      \u0275\u0275element(104, "app-form-input", 39)(105, "app-form-input", 40);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(106, "div", 14)(107, "label", 15);
      \u0275\u0275element(108, "input", 41);
      \u0275\u0275elementStart(109, "div", 17)(110, "span", 18);
      \u0275\u0275text(111, "Auto-save");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(112, "span", 19);
      \u0275\u0275text(113, "Automatically save changes periodically");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(114, "label", 15);
      \u0275\u0275element(115, "input", 42);
      \u0275\u0275elementStart(116, "div", 17)(117, "span", 18);
      \u0275\u0275text(118, "Analytics");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(119, "span", 19);
      \u0275\u0275text(120, "Help improve the application by sharing usage data");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(121, "label", 15);
      \u0275\u0275element(122, "input", 43);
      \u0275\u0275elementStart(123, "div", 17)(124, "span", 18);
      \u0275\u0275text(125, "Telemetry");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(126, "span", 19);
      \u0275\u0275text(127, "Share performance and error data");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(128, "div", 11)(129, "app-button", 12);
      \u0275\u0275listener("clicked", function SettingsComponent_Template_app_button_clicked_129_listener() {
        return ctx.saveSystem();
      });
      \u0275\u0275text(130, " Save System Settings ");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(131, "app-card", 44)(132, "div", 45)(133, "div", 46)(134, "div", 47)(135, "div", 48)(136, "h3", 49);
      \u0275\u0275text(137, "Export Data");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(138, "p", 50);
      \u0275\u0275text(139, "Download all your application data in JSON format");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(140, "app-button", 51);
      \u0275\u0275listener("clicked", function SettingsComponent_Template_app_button_clicked_140_listener() {
        return ctx.exportData();
      });
      \u0275\u0275text(141, " Export ");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(142, "div", 47)(143, "div", 48)(144, "h3", 49);
      \u0275\u0275text(145, "Clear Cache");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(146, "p", 50);
      \u0275\u0275text(147, "Clear application cache and temporary data");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(148, "app-button", 51);
      \u0275\u0275listener("clicked", function SettingsComponent_Template_app_button_clicked_148_listener() {
        return ctx.clearCache();
      });
      \u0275\u0275text(149, " Clear Cache ");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(150, "div", 52)(151, "div", 48)(152, "h3", 49);
      \u0275\u0275text(153, "Reset Settings");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(154, "p", 50);
      \u0275\u0275text(155, "Reset all settings to default values");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(156, "app-button", 53);
      \u0275\u0275listener("clicked", function SettingsComponent_Template_app_button_clicked_156_listener() {
        return ctx.resetSettings();
      });
      \u0275\u0275text(157, " Reset All ");
      \u0275\u0275elementEnd()()()()()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(8);
      \u0275\u0275property("formGroup", ctx.profileForm);
      \u0275\u0275advance(2);
      \u0275\u0275property("required", true);
      \u0275\u0275advance();
      \u0275\u0275property("required", true);
      \u0275\u0275advance(4);
      \u0275\u0275property("loading", ctx.saving.profile);
      \u0275\u0275advance(3);
      \u0275\u0275property("formGroup", ctx.notificationForm);
      \u0275\u0275advance(38);
      \u0275\u0275property("loading", ctx.saving.notifications);
      \u0275\u0275advance(3);
      \u0275\u0275property("formGroup", ctx.appearanceForm);
      \u0275\u0275advance(40);
      \u0275\u0275property("loading", ctx.saving.appearance);
      \u0275\u0275advance(3);
      \u0275\u0275property("formGroup", ctx.systemForm);
      \u0275\u0275advance(2);
      \u0275\u0275property("min", 1)("max", 60);
      \u0275\u0275advance();
      \u0275\u0275property("min", 5)("max", 480);
      \u0275\u0275advance(24);
      \u0275\u0275property("loading", ctx.saving.system);
      \u0275\u0275advance(11);
      \u0275\u0275property("loading", ctx.exporting);
      \u0275\u0275advance(8);
      \u0275\u0275property("loading", ctx.clearing);
      \u0275\u0275advance(8);
      \u0275\u0275property("loading", ctx.resetting);
    }
  }, dependencies: [CommonModule, ReactiveFormsModule, \u0275NgNoValidate, NgSelectOption, \u0275NgSelectMultipleOption, CheckboxControlValueAccessor, SelectControlValueAccessor, NgControlStatus, NgControlStatusGroup, RequiredValidator, FormGroupDirective, FormControlName, CardComponent, FormInputComponent, ButtonComponent], styles: [`

.settings-page[_ngcontent-%COMP%] {
  min-height: 100%;
}
.settings-header[_ngcontent-%COMP%] {
  margin-bottom: var(--spacing-xl);
}
.settings-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--secondary-900);
}
.settings-subtitle[_ngcontent-%COMP%] {
  margin: 0;
  font-size: var(--text-lg);
  color: var(--secondary-600);
}
.settings-content[_ngcontent-%COMP%] {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}
.settings-form[_ngcontent-%COMP%] {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}
.form-grid[_ngcontent-%COMP%] {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}
.form-group[_ngcontent-%COMP%] {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}
.form-label[_ngcontent-%COMP%] {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--secondary-700);
}
.form-select[_ngcontent-%COMP%] {
  height: 40px;
  padding: 0 var(--spacing-md);
  border: 1px solid var(--secondary-300);
  border-radius: var(--radius-md);
  background: white;
  font-size: var(--text-sm);
  color: var(--secondary-800);
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-sm) center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
  appearance: none;
  transition: all 0.2s ease;
}
.form-select[_ngcontent-%COMP%]:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}
.checkbox-grid[_ngcontent-%COMP%] {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-md);
}
.checkbox-item[_ngcontent-%COMP%] {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  border: 1px solid var(--secondary-200);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
}
.checkbox-item[_ngcontent-%COMP%]:hover {
  border-color: var(--primary-300);
  background: var(--primary-25);
}
.checkbox-item[_ngcontent-%COMP%]:has(.checkbox-input:checked) {
  border-color: var(--primary-500);
  background: var(--primary-50);
}
.checkbox-input[_ngcontent-%COMP%] {
  margin: 0;
  margin-top: 2px;
  accent-color: var(--primary-500);
}
.checkbox-content[_ngcontent-%COMP%] {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}
.checkbox-title[_ngcontent-%COMP%] {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--secondary-900);
}
.checkbox-description[_ngcontent-%COMP%] {
  font-size: var(--text-xs);
  color: var(--secondary-600);
  line-height: var(--leading-relaxed);
}
.form-actions[_ngcontent-%COMP%] {
  display: flex;
  justify-content: flex-end;
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--secondary-200);
}
.data-actions[_ngcontent-%COMP%] {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}
.action-item[_ngcontent-%COMP%] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  border: 1px solid var(--secondary-200);
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
}
.action-item[_ngcontent-%COMP%]:hover {
  border-color: var(--secondary-300);
  background: var(--secondary-25);
}
.action-item.danger[_ngcontent-%COMP%] {
  border-color: var(--error-200);
  background: var(--error-25);
}
.action-item.danger[_ngcontent-%COMP%]:hover {
  border-color: var(--error-300);
  background: var(--error-50);
}
.action-content[_ngcontent-%COMP%] {
  flex: 1;
  margin-right: var(--spacing-md);
}
.action-title[_ngcontent-%COMP%] {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--secondary-900);
}
.action-description[_ngcontent-%COMP%] {
  margin: 0;
  font-size: var(--text-sm);
  color: var(--secondary-600);
  line-height: var(--leading-relaxed);
}
.danger[_ngcontent-%COMP%]   .action-title[_ngcontent-%COMP%] {
  color: var(--error-700);
}
.danger[_ngcontent-%COMP%]   .action-description[_ngcontent-%COMP%] {
  color: var(--error-600);
}
@media (max-width: 768px) {
  .form-grid[_ngcontent-%COMP%] {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  .checkbox-grid[_ngcontent-%COMP%] {
    grid-template-columns: 1fr;
  }
  .action-item[_ngcontent-%COMP%] {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }
  .action-content[_ngcontent-%COMP%] {
    margin-right: 0;
  }
  .form-actions[_ngcontent-%COMP%] {
    justify-content: stretch;
  }
  .form-actions[_ngcontent-%COMP%]   app-button[_ngcontent-%COMP%] {
    width: 100%;
  }
}
/*# sourceMappingURL=settings.component.css.map */`] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(SettingsComponent, [{
    type: Component,
    args: [{ selector: "app-settings", standalone: true, imports: [CommonModule, ReactiveFormsModule, CardComponent, FormInputComponent, ButtonComponent], template: `
    <div class="settings-page">
      <div class="settings-header">
        <h1>Settings</h1>
        <p class="settings-subtitle">Configure your application preferences and system settings</p>
      </div>

      <div class="settings-content">
        <!-- User Profile Settings -->
        <app-card title="User Profile" subtitle="Manage your personal information">
          <form [formGroup]="profileForm" class="settings-form">
            <div class="form-grid">
              <app-form-input
                label="Full Name"
                placeholder="Enter your full name"
                formControlName="fullName"
                [required]="true"
              ></app-form-input>

              <app-form-input
                label="Email Address"
                placeholder="Enter your email"
                formControlName="email"
                type="email"
                [required]="true"
              ></app-form-input>

              <app-form-input
                label="Job Title"
                placeholder="Enter your job title"
                formControlName="jobTitle"
              ></app-form-input>

              <app-form-input
                label="Department"
                placeholder="Enter your department"
                formControlName="department"
              ></app-form-input>
            </div>

            <div class="form-actions">
              <app-button
                variant="primary"
                (clicked)="saveProfile()"
                [loading]="saving.profile"
              >
                Save Profile
              </app-button>
            </div>
          </form>
        </app-card>

        <!-- Notification Settings -->
        <app-card title="Notifications" subtitle="Configure how you receive notifications">
          <form [formGroup]="notificationForm" class="settings-form">
            <div class="checkbox-grid">
              <label class="checkbox-item">
                <input type="checkbox" formControlName="emailNotifications" class="checkbox-input">
                <div class="checkbox-content">
                  <span class="checkbox-title">Email Notifications</span>
                  <span class="checkbox-description">Receive notifications via email</span>
                </div>
              </label>

              <label class="checkbox-item">
                <input type="checkbox" formControlName="pushNotifications" class="checkbox-input">
                <div class="checkbox-content">
                  <span class="checkbox-title">Push Notifications</span>
                  <span class="checkbox-description">Receive browser push notifications</span>
                </div>
              </label>

              <label class="checkbox-item">
                <input type="checkbox" formControlName="securityAlerts" class="checkbox-input">
                <div class="checkbox-content">
                  <span class="checkbox-title">Security Alerts</span>
                  <span class="checkbox-description">Get notified about security vulnerabilities</span>
                </div>
              </label>

              <label class="checkbox-item">
                <input type="checkbox" formControlName="dependencyUpdates" class="checkbox-input">
                <div class="checkbox-content">
                  <span class="checkbox-title">Dependency Updates</span>
                  <span class="checkbox-description">Notifications for dependency updates</span>
                </div>
              </label>

              <label class="checkbox-item">
                <input type="checkbox" formControlName="weeklyReports" class="checkbox-input">
                <div class="checkbox-content">
                  <span class="checkbox-title">Weekly Reports</span>
                  <span class="checkbox-description">Receive weekly summary reports</span>
                </div>
              </label>
            </div>

            <div class="form-actions">
              <app-button
                variant="primary"
                (clicked)="saveNotifications()"
                [loading]="saving.notifications"
              >
                Save Notifications
              </app-button>
            </div>
          </form>
        </app-card>

        <!-- Appearance Settings -->
        <app-card title="Appearance" subtitle="Customize the look and feel of the application">
          <form [formGroup]="appearanceForm" class="settings-form">
            <div class="form-grid">
              <div class="form-group">
                <label class="form-label">Theme</label>
                <select formControlName="theme" class="form-select">
                  <option value="light">Light</option>
                  <option value="dark">Dark</option>
                  <option value="auto">Auto (System)</option>
                </select>
              </div>

              <div class="form-group">
                <label class="form-label">Language</label>
                <select formControlName="language" class="form-select">
                  <option value="en">English</option>
                  <option value="es">Spanish</option>
                  <option value="fr">French</option>
                  <option value="de">German</option>
                </select>
              </div>
            </div>

            <div class="checkbox-grid">
              <label class="checkbox-item">
                <input type="checkbox" formControlName="compactMode" class="checkbox-input">
                <div class="checkbox-content">
                  <span class="checkbox-title">Compact Mode</span>
                  <span class="checkbox-description">Use a more compact interface layout</span>
                </div>
              </label>

              <label class="checkbox-item">
                <input type="checkbox" formControlName="sidebarCollapsed" class="checkbox-input">
                <div class="checkbox-content">
                  <span class="checkbox-title">Collapsed Sidebar</span>
                  <span class="checkbox-description">Start with sidebar collapsed by default</span>
                </div>
              </label>
            </div>

            <div class="form-actions">
              <app-button
                variant="primary"
                (clicked)="saveAppearance()"
                [loading]="saving.appearance"
              >
                Save Appearance
              </app-button>
            </div>
          </form>
        </app-card>

        <!-- System Settings -->
        <app-card title="System" subtitle="Configure system behavior and performance">
          <form [formGroup]="systemForm" class="settings-form">
            <div class="form-grid">
              <app-form-input
                label="Auto-save Interval (minutes)"
                placeholder="5"
                formControlName="autoSaveInterval"
                type="number"
                [min]="1"
                [max]="60"
              ></app-form-input>

              <app-form-input
                label="Session Timeout (minutes)"
                placeholder="30"
                formControlName="sessionTimeout"
                type="number"
                [min]="5"
                [max]="480"
              ></app-form-input>
            </div>

            <div class="checkbox-grid">
              <label class="checkbox-item">
                <input type="checkbox" formControlName="autoSave" class="checkbox-input">
                <div class="checkbox-content">
                  <span class="checkbox-title">Auto-save</span>
                  <span class="checkbox-description">Automatically save changes periodically</span>
                </div>
              </label>

              <label class="checkbox-item">
                <input type="checkbox" formControlName="enableAnalytics" class="checkbox-input">
                <div class="checkbox-content">
                  <span class="checkbox-title">Analytics</span>
                  <span class="checkbox-description">Help improve the application by sharing usage data</span>
                </div>
              </label>

              <label class="checkbox-item">
                <input type="checkbox" formControlName="enableTelemetry" class="checkbox-input">
                <div class="checkbox-content">
                  <span class="checkbox-title">Telemetry</span>
                  <span class="checkbox-description">Share performance and error data</span>
                </div>
              </label>
            </div>

            <div class="form-actions">
              <app-button
                variant="primary"
                (clicked)="saveSystem()"
                [loading]="saving.system"
              >
                Save System Settings
              </app-button>
            </div>
          </form>
        </app-card>

        <!-- Data Management -->
        <app-card title="Data Management" subtitle="Manage your data and privacy settings">
          <div class="settings-form">
            <div class="data-actions">
              <div class="action-item">
                <div class="action-content">
                  <h3 class="action-title">Export Data</h3>
                  <p class="action-description">Download all your application data in JSON format</p>
                </div>
                <app-button
                  variant="secondary"
                  (clicked)="exportData()"
                  [loading]="exporting"
                >
                  Export
                </app-button>
              </div>

              <div class="action-item">
                <div class="action-content">
                  <h3 class="action-title">Clear Cache</h3>
                  <p class="action-description">Clear application cache and temporary data</p>
                </div>
                <app-button
                  variant="secondary"
                  (clicked)="clearCache()"
                  [loading]="clearing"
                >
                  Clear Cache
                </app-button>
              </div>

              <div class="action-item danger">
                <div class="action-content">
                  <h3 class="action-title">Reset Settings</h3>
                  <p class="action-description">Reset all settings to default values</p>
                </div>
                <app-button
                  variant="error"
                  (clicked)="resetSettings()"
                  [loading]="resetting"
                >
                  Reset All
                </app-button>
              </div>
            </div>
          </div>
        </app-card>
      </div>
    </div>
  `, styles: [`/* angular:styles/component:scss;4eb29c592b0f8014e47ce3ba4e945fa76ded14f9c0823d055320ebbffdeb3d95;C:/Users/<USER>/Documents/VibeCoding/AppCatalog/app-catalog/frontend/src/app/modules/settings/settings.component.ts */
.settings-page {
  min-height: 100%;
}
.settings-header {
  margin-bottom: var(--spacing-xl);
}
.settings-header h1 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--secondary-900);
}
.settings-subtitle {
  margin: 0;
  font-size: var(--text-lg);
  color: var(--secondary-600);
}
.settings-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}
.settings-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}
.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}
.form-label {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--secondary-700);
}
.form-select {
  height: 40px;
  padding: 0 var(--spacing-md);
  border: 1px solid var(--secondary-300);
  border-radius: var(--radius-md);
  background: white;
  font-size: var(--text-sm);
  color: var(--secondary-800);
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-sm) center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
  appearance: none;
  transition: all 0.2s ease;
}
.form-select:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}
.checkbox-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-md);
}
.checkbox-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  border: 1px solid var(--secondary-200);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
}
.checkbox-item:hover {
  border-color: var(--primary-300);
  background: var(--primary-25);
}
.checkbox-item:has(.checkbox-input:checked) {
  border-color: var(--primary-500);
  background: var(--primary-50);
}
.checkbox-input {
  margin: 0;
  margin-top: 2px;
  accent-color: var(--primary-500);
}
.checkbox-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}
.checkbox-title {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--secondary-900);
}
.checkbox-description {
  font-size: var(--text-xs);
  color: var(--secondary-600);
  line-height: var(--leading-relaxed);
}
.form-actions {
  display: flex;
  justify-content: flex-end;
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--secondary-200);
}
.data-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}
.action-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  border: 1px solid var(--secondary-200);
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
}
.action-item:hover {
  border-color: var(--secondary-300);
  background: var(--secondary-25);
}
.action-item.danger {
  border-color: var(--error-200);
  background: var(--error-25);
}
.action-item.danger:hover {
  border-color: var(--error-300);
  background: var(--error-50);
}
.action-content {
  flex: 1;
  margin-right: var(--spacing-md);
}
.action-title {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--secondary-900);
}
.action-description {
  margin: 0;
  font-size: var(--text-sm);
  color: var(--secondary-600);
  line-height: var(--leading-relaxed);
}
.danger .action-title {
  color: var(--error-700);
}
.danger .action-description {
  color: var(--error-600);
}
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  .checkbox-grid {
    grid-template-columns: 1fr;
  }
  .action-item {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }
  .action-content {
    margin-right: 0;
  }
  .form-actions {
    justify-content: stretch;
  }
  .form-actions app-button {
    width: 100%;
  }
}
/*# sourceMappingURL=settings.component.css.map */
`] }]
  }], () => [{ type: FormBuilder }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(SettingsComponent, { className: "SettingsComponent", filePath: "src/app/modules/settings/settings.component.ts", lineNumber: 534 });
})();
export {
  SettingsComponent
};
//# sourceMappingURL=chunk-PPMWMVO4.js.map
