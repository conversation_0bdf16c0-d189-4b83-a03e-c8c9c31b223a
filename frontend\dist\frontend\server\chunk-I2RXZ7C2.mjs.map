{"version": 3, "sources": ["src/app/modules/applications/applications.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\n\nexport const routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./applications-list/applications-list.component').then(m => m.ApplicationsListComponent)\n  },\n  {\n    path: 'new',\n    loadComponent: () => import('./application-form/application-form.component').then(m => m.ApplicationFormComponent)\n  },\n  {\n    path: ':id',\n    loadComponent: () => import('./application-detail/application-detail.component').then(m => m.ApplicationDetailComponent)\n  },\n  {\n    path: ':id/edit',\n    loadComponent: () => import('./application-form/application-form.component').then(m => m.ApplicationFormComponent)\n  }\n];\n"], "mappings": ";;;;;;AAEO,IAAM,SAAiB;EAC5B;IACE,MAAM;IACN,eAAe,MAAM,OAAO,sBAAiD,EAAE,KAAK,OAAK,EAAE,yBAAyB;;EAEtH;IACE,MAAM;IACN,eAAe,MAAM,OAAO,sBAA+C,EAAE,KAAK,OAAK,EAAE,wBAAwB;;EAEnH;IACE,MAAM;IACN,eAAe,MAAM,OAAO,sBAAmD,EAAE,KAAK,OAAK,EAAE,0BAA0B;;EAEzH;IACE,MAAM;IACN,eAAe,MAAM,OAAO,sBAA+C,EAAE,KAAK,OAAK,EAAE,wBAAwB;;;", "names": []}