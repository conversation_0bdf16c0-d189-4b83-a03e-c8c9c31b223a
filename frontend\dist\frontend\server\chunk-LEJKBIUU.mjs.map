{"version": 3, "sources": ["src/app/modules/security/security.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { ReactiveFormsModule, FormBuilder } from '@angular/forms';\nimport { CardComponent } from '../../shared/components/card/card.component';\nimport { ButtonComponent } from '../../shared/components/button/button.component';\nimport { TableComponent, TableColumn, TableAction } from '../../shared/components/table/table.component';\nimport { ChartComponent } from '../../shared/components/chart/chart.component';\n\ninterface SecurityItem {\n  id: number;\n  title: string;\n  type: string;\n  severity: string;\n  status: string;\n  application: string;\n  discoveredDate: Date;\n  resolvedDate?: Date;\n  assignee?: string;\n  description: string;\n  cveId?: string;\n  cvssScore?: number;\n}\n\n@Component({\n  selector: 'app-security',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    ReactiveFormsModule,\n    CardComponent,\n    ButtonComponent,\n    TableComponent,\n    ChartComponent\n  ],\n  template: `\n    <div class=\"security-page\">\n      <div class=\"page-content\">\n        <div class=\"page-header\">\n          <div class=\"header-content\">\n            <h1>Security Overview</h1>\n            <p class=\"page-subtitle\">Monitor vulnerabilities and security assessments</p>\n          </div>\n          <div class=\"header-actions\">\n            <app-button\n              variant=\"primary\"\n              leftIcon=\"M12 4v16m8-8H4\"\n              (clicked)=\"openAddModal()\"\n            >\n              Add Security Item\n            </app-button>\n          </div>\n        </div>\n\n      <!-- Statistics Cards -->\n      <div class=\"stats-grid\">\n        <app-card title=\"Total Vulnerabilities\">\n          <div class=\"stat-content\">\n            <div class=\"stat-number\">{{ stats.total }}</div>\n            <div class=\"stat-change\">Across {{ stats.applications }} applications</div>\n          </div>\n        </app-card>\n\n        <app-card title=\"Critical Issues\">\n          <div class=\"stat-content\">\n            <div class=\"stat-number critical\">{{ stats.critical }}</div>\n            <div class=\"stat-change\">{{ stats.criticalPercentage }}% of total</div>\n          </div>\n        </app-card>\n\n        <app-card title=\"Open Issues\">\n          <div class=\"stat-content\">\n            <div class=\"stat-number warning\">{{ stats.open }}</div>\n            <div class=\"stat-change\">Need attention</div>\n          </div>\n        </app-card>\n\n        <app-card title=\"Resolved This Month\">\n          <div class=\"stat-content\">\n            <div class=\"stat-number success\">{{ stats.resolvedThisMonth }}</div>\n            <div class=\"stat-change\">Security improvements</div>\n          </div>\n        </app-card>\n      </div>\n\n      <!-- Charts Section -->\n      <div class=\"charts-section\">\n        <app-card title=\"Vulnerabilities by Severity\" subtitle=\"Distribution by severity level\">\n          <app-chart\n            type=\"doughnut\"\n            [data]=\"severityChartData\"\n            [options]=\"chartOptions\"\n            height=\"300px\"\n          ></app-chart>\n        </app-card>\n\n        <app-card title=\"Security Trends\" subtitle=\"Monthly vulnerability trends\">\n          <app-chart\n            type=\"line\"\n            [data]=\"trendChartData\"\n            [options]=\"chartOptions\"\n            height=\"300px\"\n          ></app-chart>\n        </app-card>\n      </div>\n\n      <!-- Security Items Table -->\n      <app-card title=\"Security Issues\" subtitle=\"All security vulnerabilities and assessments\">\n        <div class=\"table-controls\">\n          <div class=\"search-controls\">\n            <input\n              type=\"text\"\n              placeholder=\"Search security issues...\"\n              class=\"search-input\"\n              (input)=\"onSearchInput($event)\"\n            >\n            <select class=\"filter-select\" (change)=\"onFilterChanged('severity', $event)\">\n              <option value=\"\">All Severities</option>\n              <option value=\"critical\">Critical</option>\n              <option value=\"high\">High</option>\n              <option value=\"medium\">Medium</option>\n              <option value=\"low\">Low</option>\n            </select>\n            <select class=\"filter-select\" (change)=\"onFilterChanged('status', $event)\">\n              <option value=\"\">All Statuses</option>\n              <option value=\"open\">Open</option>\n              <option value=\"in_progress\">In Progress</option>\n              <option value=\"resolved\">Resolved</option>\n              <option value=\"false_positive\">False Positive</option>\n            </select>\n            <select class=\"filter-select\" (change)=\"onFilterChanged('type', $event)\">\n              <option value=\"\">All Types</option>\n              <option value=\"vulnerability\">Vulnerability</option>\n              <option value=\"compliance\">Compliance</option>\n              <option value=\"assessment\">Assessment</option>\n              <option value=\"audit\">Audit</option>\n            </select>\n          </div>\n        </div>\n\n        <app-table\n          [columns]=\"tableColumns\"\n          [data]=\"filteredSecurityItems\"\n          [actions]=\"tableActions\"\n          [loading]=\"loading\"\n          [sortable]=\"true\"\n          (sortChanged)=\"onSortChanged($event)\"\n        ></app-table>\n      </app-card>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .security-page {\n      min-height: 100%;\n    }\n\n    .page-content {\n      padding: var(--spacing-xl);\n    }\n\n    .page-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: flex-start;\n      margin-bottom: var(--spacing-xl);\n    }\n\n    .header-content h1 {\n      margin: 0 0 var(--spacing-sm) 0;\n      font-size: var(--text-3xl);\n      font-weight: 700;\n      color: var(--secondary-900);\n    }\n\n    .page-subtitle {\n      margin: 0;\n      font-size: var(--text-lg);\n      color: var(--secondary-600);\n    }\n\n    .stats-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n      gap: var(--spacing-lg);\n      margin-bottom: var(--spacing-xl);\n    }\n\n    .stat-content {\n      display: flex;\n      flex-direction: column;\n      gap: var(--spacing-sm);\n      padding: var(--spacing-lg);\n    }\n\n    .stat-number {\n      font-size: var(--text-3xl);\n      font-weight: 700;\n      color: var(--secondary-900);\n\n      &.critical {\n        color: var(--error-600);\n      }\n\n      &.warning {\n        color: var(--warning-600);\n      }\n\n      &.success {\n        color: var(--success-600);\n      }\n    }\n\n    .stat-change {\n      font-size: var(--text-sm);\n      color: var(--secondary-600);\n    }\n\n    .charts-section {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n      gap: var(--spacing-lg);\n      margin-bottom: var(--spacing-xl);\n    }\n\n    .table-controls {\n      margin-bottom: var(--spacing-lg);\n    }\n\n    .search-controls {\n      display: flex;\n      gap: var(--spacing-md);\n      align-items: center;\n      flex-wrap: wrap;\n    }\n\n    .search-input,\n    .filter-select {\n      height: 40px;\n      padding: 0 var(--spacing-md);\n      border: 1px solid var(--secondary-300);\n      border-radius: var(--radius-md);\n      background: white;\n      font-size: var(--text-sm);\n      color: var(--secondary-800);\n      transition: all 0.2s ease;\n\n      &:focus {\n        outline: none;\n        border-color: var(--primary-500);\n        box-shadow: 0 0 0 3px var(--primary-100);\n      }\n    }\n\n    .search-input {\n      flex: 1;\n      min-width: 200px;\n    }\n\n    .filter-select {\n      min-width: 120px;\n      cursor: pointer;\n      background-image: url(\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e\");\n      background-position: right var(--spacing-sm) center;\n      background-repeat: no-repeat;\n      background-size: 16px;\n      padding-right: 40px;\n      appearance: none;\n    }\n\n    @media (max-width: 768px) {\n      .page-header {\n        flex-direction: column;\n        gap: var(--spacing-md);\n        align-items: stretch;\n      }\n\n      .stats-grid {\n        grid-template-columns: 1fr;\n        gap: var(--spacing-md);\n      }\n\n      .charts-section {\n        grid-template-columns: 1fr;\n        gap: var(--spacing-md);\n      }\n\n      .search-controls {\n        flex-direction: column;\n        align-items: stretch;\n      }\n\n      .search-input,\n      .filter-select {\n        min-width: auto;\n      }\n    }\n  `]\n})\nexport class SecurityComponent implements OnInit {\n  securityItems: SecurityItem[] = [];\n  filteredSecurityItems: SecurityItem[] = [];\n  loading = false;\n\n  // Statistics\n  stats = {\n    total: 0,\n    critical: 0,\n    open: 0,\n    applications: 0,\n    resolvedThisMonth: 0,\n    criticalPercentage: 0\n  };\n\n  // Chart data\n  severityChartData: any = null;\n  trendChartData: any = null;\n  chartOptions = {\n    responsive: true,\n    maintainAspectRatio: false\n  };\n\n  // Table configuration\n  tableColumns: TableColumn[] = [\n    { key: 'title', label: 'Title', sortable: true },\n    { key: 'type', label: 'Type', type: 'badge', sortable: true },\n    { key: 'severity', label: 'Severity', type: 'badge', sortable: true },\n    { key: 'status', label: 'Status', type: 'badge', sortable: true },\n    { key: 'application', label: 'Application', sortable: true },\n    { key: 'cvssScore', label: 'CVSS', type: 'number', sortable: true },\n    { key: 'assignee', label: 'Assignee', sortable: true },\n    { key: 'discoveredDate', label: 'Discovered', type: 'date', sortable: true }\n  ];\n\n  tableActions: TableAction[] = [\n    {\n      label: 'View',\n      icon: 'M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7Z',\n      variant: 'ghost',\n      action: (item) => this.viewSecurityItem(item)\n    },\n    {\n      label: 'Edit',\n      icon: 'M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7',\n      variant: 'ghost',\n      action: (item) => this.editSecurityItem(item)\n    },\n    {\n      label: 'Delete',\n      icon: 'M3 6h18m-2 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2',\n      variant: 'error',\n      action: (item) => this.deleteSecurityItem(item)\n    }\n  ];\n\n  constructor(private fb: FormBuilder) {}\n\n  ngOnInit(): void {\n    this.loadSecurityItems();\n  }\n\n  private loadSecurityItems(): void {\n    this.loading = true;\n\n    // Mock data - replace with actual API call\n    setTimeout(() => {\n      this.securityItems = this.generateMockSecurityItems();\n      this.filteredSecurityItems = [...this.securityItems];\n      this.updateStats();\n      this.updateChartData();\n      this.loading = false;\n    }, 1000);\n  }\n\n  private generateMockSecurityItems(): SecurityItem[] {\n    const types = ['vulnerability', 'compliance', 'assessment', 'audit'];\n    const severities = ['critical', 'high', 'medium', 'low'];\n    const statuses = ['open', 'in_progress', 'resolved', 'false_positive'];\n    const applications = ['App 1', 'App 2', 'App 3', 'App 4', 'App 5'];\n\n    return Array.from({ length: 40 }, (_, i) => ({\n      id: i + 1,\n      title: `Security Issue ${i + 1}`,\n      type: types[Math.floor(Math.random() * types.length)],\n      severity: severities[Math.floor(Math.random() * severities.length)],\n      status: statuses[Math.floor(Math.random() * statuses.length)],\n      application: applications[Math.floor(Math.random() * applications.length)],\n      discoveredDate: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000),\n      resolvedDate: Math.random() > 0.6 ? new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000) : undefined,\n      assignee: Math.random() > 0.3 ? `User ${Math.floor(Math.random() * 10) + 1}` : undefined,\n      description: `Description for security issue ${i + 1}`,\n      cveId: Math.random() > 0.7 ? `CVE-2024-${Math.floor(Math.random() * 9999) + 1000}` : undefined,\n      cvssScore: Math.random() > 0.5 ? Math.round((Math.random() * 10) * 10) / 10 : undefined\n    }));\n  }\n\n  private updateStats(): void {\n    this.stats.total = this.securityItems.length;\n    this.stats.critical = this.securityItems.filter(s => s.severity === 'critical').length;\n    this.stats.open = this.securityItems.filter(s => s.status === 'open' || s.status === 'in_progress').length;\n    this.stats.applications = new Set(this.securityItems.map(s => s.application)).size;\n    this.stats.resolvedThisMonth = this.securityItems.filter(s =>\n      s.resolvedDate && new Date(s.resolvedDate).getMonth() === new Date().getMonth()\n    ).length;\n    this.stats.criticalPercentage = Math.round((this.stats.critical / this.stats.total) * 100);\n  }\n\n  private updateChartData(): void {\n    // Severity distribution chart\n    const severityCount = this.securityItems.reduce((acc, item) => {\n      acc[item.severity] = (acc[item.severity] || 0) + 1;\n      return acc;\n    }, {} as any);\n\n    this.severityChartData = {\n      labels: Object.keys(severityCount),\n      datasets: [{\n        data: Object.values(severityCount),\n        backgroundColor: ['#ef4444', '#f97316', '#f59e0b', '#22c55e']\n      }]\n    };\n\n    // Trend chart (mock data)\n    this.trendChartData = {\n      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],\n      datasets: [\n        {\n          label: 'New Vulnerabilities',\n          data: [12, 19, 8, 15, 10, 7],\n          borderColor: '#ef4444',\n          backgroundColor: 'rgba(239, 68, 68, 0.1)',\n          tension: 0.4\n        },\n        {\n          label: 'Resolved',\n          data: [8, 15, 12, 18, 14, 16],\n          borderColor: '#22c55e',\n          backgroundColor: 'rgba(34, 197, 94, 0.1)',\n          tension: 0.4\n        }\n      ]\n    };\n  }\n\n  openAddModal(): void {\n    // Implement add modal\n    console.log('Add security item modal');\n  }\n\n  viewSecurityItem(item: SecurityItem): void {\n    // Implement view functionality\n    console.log('View security item:', item);\n  }\n\n  editSecurityItem(item: SecurityItem): void {\n    // Implement edit functionality\n    console.log('Edit security item:', item);\n  }\n\n  deleteSecurityItem(item: SecurityItem): void {\n    if (confirm(`Are you sure you want to delete ${item.title}?`)) {\n      this.securityItems = this.securityItems.filter(s => s.id !== item.id);\n      this.filteredSecurityItems = this.filteredSecurityItems.filter(s => s.id !== item.id);\n      this.updateStats();\n      this.updateChartData();\n    }\n  }\n\n  onSearchInput(event: Event): void {\n    const target = event.target as HTMLInputElement;\n    this.onSearchChanged(target.value);\n  }\n\n  onSearchChanged(searchTerm: string): void {\n    this.applyFilters();\n  }\n\n  onFilterChanged(filterType: string, event: Event): void {\n    const target = event.target as HTMLSelectElement;\n    // Apply filter logic here\n    this.applyFilters();\n  }\n\n  onSortChanged(sort: any): void {\n    // Implement sorting logic\n    console.log('Sort changed:', sort);\n  }\n\n  private applyFilters(): void {\n    // Implement filtering logic\n    this.filteredSecurityItems = [...this.securityItems];\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4SM,IAAO,oBAAP,MAAO,mBAAiB;EAwDR;EAvDpB,gBAAgC,CAAA;EAChC,wBAAwC,CAAA;EACxC,UAAU;;EAGV,QAAQ;IACN,OAAO;IACP,UAAU;IACV,MAAM;IACN,cAAc;IACd,mBAAmB;IACnB,oBAAoB;;;EAItB,oBAAyB;EACzB,iBAAsB;EACtB,eAAe;IACb,YAAY;IACZ,qBAAqB;;;EAIvB,eAA8B;IAC5B,EAAE,KAAK,SAAS,OAAO,SAAS,UAAU,KAAI;IAC9C,EAAE,KAAK,QAAQ,OAAO,QAAQ,MAAM,SAAS,UAAU,KAAI;IAC3D,EAAE,KAAK,YAAY,OAAO,YAAY,MAAM,SAAS,UAAU,KAAI;IACnE,EAAE,KAAK,UAAU,OAAO,UAAU,MAAM,SAAS,UAAU,KAAI;IAC/D,EAAE,KAAK,eAAe,OAAO,eAAe,UAAU,KAAI;IAC1D,EAAE,KAAK,aAAa,OAAO,QAAQ,MAAM,UAAU,UAAU,KAAI;IACjE,EAAE,KAAK,YAAY,OAAO,YAAY,UAAU,KAAI;IACpD,EAAE,KAAK,kBAAkB,OAAO,cAAc,MAAM,QAAQ,UAAU,KAAI;;EAG5E,eAA8B;IAC5B;MACE,OAAO;MACP,MAAM;MACN,SAAS;MACT,QAAQ,CAAC,SAAS,KAAK,iBAAiB,IAAI;;IAE9C;MACE,OAAO;MACP,MAAM;MACN,SAAS;MACT,QAAQ,CAAC,SAAS,KAAK,iBAAiB,IAAI;;IAE9C;MACE,OAAO;MACP,MAAM;MACN,SAAS;MACT,QAAQ,CAAC,SAAS,KAAK,mBAAmB,IAAI;;;EAIlD,YAAoB,IAAe;AAAf,SAAA,KAAA;EAAkB;EAEtC,WAAQ;AACN,SAAK,kBAAiB;EACxB;EAEQ,oBAAiB;AACvB,SAAK,UAAU;AAGf,eAAW,MAAK;AACd,WAAK,gBAAgB,KAAK,0BAAyB;AACnD,WAAK,wBAAwB,CAAC,GAAG,KAAK,aAAa;AACnD,WAAK,YAAW;AAChB,WAAK,gBAAe;AACpB,WAAK,UAAU;IACjB,GAAG,GAAI;EACT;EAEQ,4BAAyB;AAC/B,UAAM,QAAQ,CAAC,iBAAiB,cAAc,cAAc,OAAO;AACnE,UAAM,aAAa,CAAC,YAAY,QAAQ,UAAU,KAAK;AACvD,UAAM,WAAW,CAAC,QAAQ,eAAe,YAAY,gBAAgB;AACrE,UAAM,eAAe,CAAC,SAAS,SAAS,SAAS,SAAS,OAAO;AAEjE,WAAO,MAAM,KAAK,EAAE,QAAQ,GAAE,GAAI,CAAC,GAAG,OAAO;MAC3C,IAAI,IAAI;MACR,OAAO,kBAAkB,IAAI,CAAC;MAC9B,MAAM,MAAM,KAAK,MAAM,KAAK,OAAM,IAAK,MAAM,MAAM,CAAC;MACpD,UAAU,WAAW,KAAK,MAAM,KAAK,OAAM,IAAK,WAAW,MAAM,CAAC;MAClE,QAAQ,SAAS,KAAK,MAAM,KAAK,OAAM,IAAK,SAAS,MAAM,CAAC;MAC5D,aAAa,aAAa,KAAK,MAAM,KAAK,OAAM,IAAK,aAAa,MAAM,CAAC;MACzE,gBAAgB,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,OAAM,IAAK,KAAK,KAAK,KAAK,KAAK,GAAI;MAC9E,cAAc,KAAK,OAAM,IAAK,MAAM,IAAI,KAAK,KAAK,IAAG,IAAK,KAAK,OAAM,IAAK,KAAK,KAAK,KAAK,KAAK,GAAI,IAAI;MACtG,UAAU,KAAK,OAAM,IAAK,MAAM,QAAQ,KAAK,MAAM,KAAK,OAAM,IAAK,EAAE,IAAI,CAAC,KAAK;MAC/E,aAAa,kCAAkC,IAAI,CAAC;MACpD,OAAO,KAAK,OAAM,IAAK,MAAM,YAAY,KAAK,MAAM,KAAK,OAAM,IAAK,IAAI,IAAI,GAAI,KAAK;MACrF,WAAW,KAAK,OAAM,IAAK,MAAM,KAAK,MAAO,KAAK,OAAM,IAAK,KAAM,EAAE,IAAI,KAAK;MAC9E;EACJ;EAEQ,cAAW;AACjB,SAAK,MAAM,QAAQ,KAAK,cAAc;AACtC,SAAK,MAAM,WAAW,KAAK,cAAc,OAAO,OAAK,EAAE,aAAa,UAAU,EAAE;AAChF,SAAK,MAAM,OAAO,KAAK,cAAc,OAAO,OAAK,EAAE,WAAW,UAAU,EAAE,WAAW,aAAa,EAAE;AACpG,SAAK,MAAM,eAAe,IAAI,IAAI,KAAK,cAAc,IAAI,OAAK,EAAE,WAAW,CAAC,EAAE;AAC9E,SAAK,MAAM,oBAAoB,KAAK,cAAc,OAAO,OACvD,EAAE,gBAAgB,IAAI,KAAK,EAAE,YAAY,EAAE,SAAQ,OAAO,oBAAI,KAAI,GAAG,SAAQ,CAAE,EAC/E;AACF,SAAK,MAAM,qBAAqB,KAAK,MAAO,KAAK,MAAM,WAAW,KAAK,MAAM,QAAS,GAAG;EAC3F;EAEQ,kBAAe;AAErB,UAAM,gBAAgB,KAAK,cAAc,OAAO,CAAC,KAAK,SAAQ;AAC5D,UAAI,KAAK,QAAQ,KAAK,IAAI,KAAK,QAAQ,KAAK,KAAK;AACjD,aAAO;IACT,GAAG,CAAA,CAAS;AAEZ,SAAK,oBAAoB;MACvB,QAAQ,OAAO,KAAK,aAAa;MACjC,UAAU,CAAC;QACT,MAAM,OAAO,OAAO,aAAa;QACjC,iBAAiB,CAAC,WAAW,WAAW,WAAW,SAAS;OAC7D;;AAIH,SAAK,iBAAiB;MACpB,QAAQ,CAAC,OAAO,OAAO,OAAO,OAAO,OAAO,KAAK;MACjD,UAAU;QACR;UACE,OAAO;UACP,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC;UAC3B,aAAa;UACb,iBAAiB;UACjB,SAAS;;QAEX;UACE,OAAO;UACP,MAAM,CAAC,GAAG,IAAI,IAAI,IAAI,IAAI,EAAE;UAC5B,aAAa;UACb,iBAAiB;UACjB,SAAS;;;;EAIjB;EAEA,eAAY;AAEV,YAAQ,IAAI,yBAAyB;EACvC;EAEA,iBAAiB,MAAkB;AAEjC,YAAQ,IAAI,uBAAuB,IAAI;EACzC;EAEA,iBAAiB,MAAkB;AAEjC,YAAQ,IAAI,uBAAuB,IAAI;EACzC;EAEA,mBAAmB,MAAkB;AACnC,QAAI,QAAQ,mCAAmC,KAAK,KAAK,GAAG,GAAG;AAC7D,WAAK,gBAAgB,KAAK,cAAc,OAAO,OAAK,EAAE,OAAO,KAAK,EAAE;AACpE,WAAK,wBAAwB,KAAK,sBAAsB,OAAO,OAAK,EAAE,OAAO,KAAK,EAAE;AACpF,WAAK,YAAW;AAChB,WAAK,gBAAe;IACtB;EACF;EAEA,cAAc,OAAY;AACxB,UAAM,SAAS,MAAM;AACrB,SAAK,gBAAgB,OAAO,KAAK;EACnC;EAEA,gBAAgB,YAAkB;AAChC,SAAK,aAAY;EACnB;EAEA,gBAAgB,YAAoB,OAAY;AAC9C,UAAM,SAAS,MAAM;AAErB,SAAK,aAAY;EACnB;EAEA,cAAc,MAAS;AAErB,YAAQ,IAAI,iBAAiB,IAAI;EACnC;EAEQ,eAAY;AAElB,SAAK,wBAAwB,CAAC,GAAG,KAAK,aAAa;EACrD;;qCAhMW,oBAAiB,4BAAA,WAAA,CAAA;EAAA;yEAAjB,oBAAiB,WAAA,CAAA,CAAA,cAAA,CAAA,GAAA,OAAA,IAAA,MAAA,IAAA,QAAA,CAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,WAAA,WAAA,YAAA,kBAAA,GAAA,SAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,SAAA,uBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,CAAA,GAAA,eAAA,UAAA,GAAA,CAAA,SAAA,aAAA,GAAA,CAAA,GAAA,eAAA,SAAA,GAAA,CAAA,SAAA,qBAAA,GAAA,CAAA,GAAA,eAAA,SAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,SAAA,+BAAA,YAAA,gCAAA,GAAA,CAAA,QAAA,YAAA,UAAA,SAAA,GAAA,QAAA,SAAA,GAAA,CAAA,SAAA,mBAAA,YAAA,8BAAA,GAAA,CAAA,QAAA,QAAA,UAAA,SAAA,GAAA,QAAA,SAAA,GAAA,CAAA,SAAA,mBAAA,YAAA,8CAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,QAAA,QAAA,eAAA,6BAAA,GAAA,gBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,QAAA,GAAA,CAAA,SAAA,EAAA,GAAA,CAAA,SAAA,UAAA,GAAA,CAAA,SAAA,MAAA,GAAA,CAAA,SAAA,QAAA,GAAA,CAAA,SAAA,KAAA,GAAA,CAAA,SAAA,MAAA,GAAA,CAAA,SAAA,aAAA,GAAA,CAAA,SAAA,UAAA,GAAA,CAAA,SAAA,gBAAA,GAAA,CAAA,SAAA,eAAA,GAAA,CAAA,SAAA,YAAA,GAAA,CAAA,SAAA,YAAA,GAAA,CAAA,SAAA,OAAA,GAAA,CAAA,GAAA,eAAA,WAAA,QAAA,WAAA,WAAA,UAAA,CAAA,GAAA,UAAA,SAAA,2BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAvQ1B,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA2B,GAAA,OAAA,CAAA,EACC,GAAA,OAAA,CAAA,EACC,GAAA,OAAA,CAAA,EACK,GAAA,IAAA;AACtB,MAAA,iBAAA,GAAA,mBAAA;AAAiB,MAAA,uBAAA;AACrB,MAAA,yBAAA,GAAA,KAAA,CAAA;AAAyB,MAAA,iBAAA,GAAA,kDAAA;AAAgD,MAAA,uBAAA,EAAI;AAE/E,MAAA,yBAAA,GAAA,OAAA,CAAA,EAA4B,GAAA,cAAA,CAAA;AAIxB,MAAA,qBAAA,WAAA,SAAA,2DAAA;AAAA,eAAW,IAAA,aAAA;MAAc,CAAA;AAEzB,MAAA,iBAAA,IAAA,qBAAA;AACF,MAAA,uBAAA,EAAa,EACT;AAIV,MAAA,yBAAA,IAAA,OAAA,CAAA,EAAwB,IAAA,YAAA,CAAA,EACkB,IAAA,OAAA,CAAA,EACZ,IAAA,OAAA,EAAA;AACC,MAAA,iBAAA,EAAA;AAAiB,MAAA,uBAAA;AAC1C,MAAA,yBAAA,IAAA,OAAA,EAAA;AAAyB,MAAA,iBAAA,EAAA;AAA4C,MAAA,uBAAA,EAAM,EACvE;AAGR,MAAA,yBAAA,IAAA,YAAA,EAAA,EAAkC,IAAA,OAAA,CAAA,EACN,IAAA,OAAA,EAAA;AACU,MAAA,iBAAA,EAAA;AAAoB,MAAA,uBAAA;AACtD,MAAA,yBAAA,IAAA,OAAA,EAAA;AAAyB,MAAA,iBAAA,EAAA;AAAwC,MAAA,uBAAA,EAAM,EACnE;AAGR,MAAA,yBAAA,IAAA,YAAA,EAAA,EAA8B,IAAA,OAAA,CAAA,EACF,IAAA,OAAA,EAAA;AACS,MAAA,iBAAA,EAAA;AAAgB,MAAA,uBAAA;AACjD,MAAA,yBAAA,IAAA,OAAA,EAAA;AAAyB,MAAA,iBAAA,IAAA,gBAAA;AAAc,MAAA,uBAAA,EAAM,EACzC;AAGR,MAAA,yBAAA,IAAA,YAAA,EAAA,EAAsC,IAAA,OAAA,CAAA,EACV,IAAA,OAAA,EAAA;AACS,MAAA,iBAAA,EAAA;AAA6B,MAAA,uBAAA;AAC9D,MAAA,yBAAA,IAAA,OAAA,EAAA;AAAyB,MAAA,iBAAA,IAAA,uBAAA;AAAqB,MAAA,uBAAA,EAAM,EAChD,EACG;AAIb,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA4B,IAAA,YAAA,EAAA;AAExB,MAAA,oBAAA,IAAA,aAAA,EAAA;AAMF,MAAA,uBAAA;AAEA,MAAA,yBAAA,IAAA,YAAA,EAAA;AACE,MAAA,oBAAA,IAAA,aAAA,EAAA;AAMF,MAAA,uBAAA,EAAW;AAIb,MAAA,yBAAA,IAAA,YAAA,EAAA,EAA0F,IAAA,OAAA,EAAA,EAC5D,IAAA,OAAA,EAAA,EACG,IAAA,SAAA,EAAA;AAKzB,MAAA,qBAAA,SAAA,SAAA,mDAAA,QAAA;AAAA,eAAS,IAAA,cAAA,MAAA;MAAqB,CAAA;AAJhC,MAAA,uBAAA;AAMA,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA8B,MAAA,qBAAA,UAAA,SAAA,qDAAA,QAAA;AAAA,eAAU,IAAA,gBAAgB,YAAU,MAAA;MAAS,CAAA;AACzE,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAiB,MAAA,iBAAA,IAAA,gBAAA;AAAc,MAAA,uBAAA;AAC/B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAyB,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA;AACjC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAqB,MAAA,iBAAA,IAAA,MAAA;AAAI,MAAA,uBAAA;AACzB,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAuB,MAAA,iBAAA,IAAA,QAAA;AAAM,MAAA,uBAAA;AAC7B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAoB,MAAA,iBAAA,IAAA,KAAA;AAAG,MAAA,uBAAA,EAAS;AAElC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA8B,MAAA,qBAAA,UAAA,SAAA,qDAAA,QAAA;AAAA,eAAU,IAAA,gBAAgB,UAAQ,MAAA;MAAS,CAAA;AACvE,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAiB,MAAA,iBAAA,IAAA,cAAA;AAAY,MAAA,uBAAA;AAC7B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAqB,MAAA,iBAAA,IAAA,MAAA;AAAI,MAAA,uBAAA;AACzB,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA4B,MAAA,iBAAA,IAAA,aAAA;AAAW,MAAA,uBAAA;AACvC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAyB,MAAA,iBAAA,IAAA,UAAA;AAAQ,MAAA,uBAAA;AACjC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA+B,MAAA,iBAAA,IAAA,gBAAA;AAAc,MAAA,uBAAA,EAAS;AAExD,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA8B,MAAA,qBAAA,UAAA,SAAA,qDAAA,QAAA;AAAA,eAAU,IAAA,gBAAgB,QAAM,MAAA;MAAS,CAAA;AACrE,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAiB,MAAA,iBAAA,IAAA,WAAA;AAAS,MAAA,uBAAA;AAC1B,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA8B,MAAA,iBAAA,IAAA,eAAA;AAAa,MAAA,uBAAA;AAC3C,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA2B,MAAA,iBAAA,IAAA,YAAA;AAAU,MAAA,uBAAA;AACrC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAA2B,MAAA,iBAAA,IAAA,YAAA;AAAU,MAAA,uBAAA;AACrC,MAAA,yBAAA,IAAA,UAAA,EAAA;AAAsB,MAAA,iBAAA,IAAA,OAAA;AAAK,MAAA,uBAAA,EAAS,EAC7B,EACL;AAGR,MAAA,yBAAA,IAAA,aAAA,EAAA;AAME,MAAA,qBAAA,eAAA,SAAA,6DAAA,QAAA;AAAA,eAAe,IAAA,cAAA,MAAA;MAAqB,CAAA;AACrC,MAAA,uBAAA,EAAY,EACJ,EACL;;;AA3FyB,MAAA,oBAAA,EAAA;AAAA,MAAA,4BAAA,IAAA,MAAA,KAAA;AACA,MAAA,oBAAA,CAAA;AAAA,MAAA,6BAAA,WAAA,IAAA,MAAA,cAAA,eAAA;AAMS,MAAA,oBAAA,CAAA;AAAA,MAAA,4BAAA,IAAA,MAAA,QAAA;AACT,MAAA,oBAAA,CAAA;AAAA,MAAA,6BAAA,IAAA,IAAA,MAAA,oBAAA,YAAA;AAMQ,MAAA,oBAAA,CAAA;AAAA,MAAA,4BAAA,IAAA,MAAA,IAAA;AAOA,MAAA,oBAAA,CAAA;AAAA,MAAA,4BAAA,IAAA,MAAA,iBAAA;AAWjC,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,iBAAA,EAA0B,WAAA,IAAA,YAAA;AAS1B,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,cAAA,EAAuB,WAAA,IAAA,YAAA;AA0CzB,MAAA,oBAAA,EAAA;AAAA,MAAA,qBAAA,WAAA,IAAA,YAAA,EAAwB,QAAA,IAAA,qBAAA,EACM,WAAA,IAAA,YAAA,EACN,WAAA,IAAA,OAAA,EACL,YAAA,IAAA;;;IArHzB;IACA;IACA;IAAmB;IAAA;IACnB;IACA;IACA;IACA;EAAc,GAAA,QAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mDAAA,EAAA,CAAA;;;sEA0QL,mBAAiB,CAAA;UApR7B;uBACW,gBAAc,YACZ,MAAI,SACP;MACP;MACA;MACA;MACA;MACA;MACA;MACA;OACD,UACS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAoHT,QAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAAA,EAAA,CAAA;;;;6EAoJU,mBAAiB,EAAA,WAAA,qBAAA,UAAA,kDAAA,YAAA,IAAA,CAAA;AAAA,GAAA;", "names": []}