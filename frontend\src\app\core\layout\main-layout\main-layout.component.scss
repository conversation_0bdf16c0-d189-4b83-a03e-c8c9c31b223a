.layout-container {
  min-height: 100vh;
  background-color: var(--neutral-50);
}

.main-content {
  margin-top: 64px; /* Header height */
  margin-left: 0;
  min-height: calc(100vh - 64px);
  transition: margin-left 0.3s ease;
  background-color: var(--neutral-50);

  &.sidebar-open {
    margin-left: 280px; /* Sidebar width */
  }
}

.auth-content {
  min-height: 100vh;
  width: 100%;
  background-color: var(--neutral-50);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  /* Add a subtle gradient background */
  background-image: linear-gradient(
    135deg,
    var(--neutral-50) 0%,
    var(--neutral-100) 100%
  );
}

.content-wrapper {
  padding: var(--spacing-xl);
  max-width: 100%;
  min-height: calc(100vh - 64px - 2 * var(--spacing-xl));
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(2px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--secondary-200);
  border-top: 3px solid var(--primary-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin: 0;
  font-size: var(--text-sm);
  color: var(--secondary-600);
  font-weight: 500;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 767px) {
  .main-content {
    margin-left: 0 !important;
  }

  .content-wrapper {
    padding: var(--spacing-lg);
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  .content-wrapper {
    padding: var(--spacing-lg) var(--spacing-xl);
  }
}

/* Print Styles */
@media print {
  .layout-container {
    background: white;
  }

  .main-content {
    margin: 0;
    min-height: auto;
  }

  .content-wrapper {
    padding: 0;
  }

  .loading-overlay {
    display: none;
  }
}
