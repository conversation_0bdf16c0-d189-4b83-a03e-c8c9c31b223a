<!DOCTYPE html><html lang="en"><head>
  <meta charset="utf-8">
  <title>Frontend</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" type="image/x-icon" href="favicon.ico">
<link rel="stylesheet" href="styles.css"><style ng-app-id="ng">

.layout-container[_ngcontent-ng-c3058278431] {
  min-height: 100vh;
  background-color: var(--neutral-50);
}
.main-content[_ngcontent-ng-c3058278431] {
  margin-top: 64px;
  margin-left: 0;
  min-height: calc(100vh - 64px);
  transition: margin-left 0.3s ease;
  background-color: var(--neutral-50);
}
.main-content.sidebar-open[_ngcontent-ng-c3058278431] {
  margin-left: 280px;
}
.auth-content[_ngcontent-ng-c3058278431] {
  min-height: 100vh;
  width: 100%;
  background-color: var(--neutral-50);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-image:
    linear-gradient(
      135deg,
      var(--neutral-50) 0%,
      var(--neutral-100) 100%);
}
.content-wrapper[_ngcontent-ng-c3058278431] {
  padding: var(--spacing-xl);
  max-width: 100%;
  min-height: calc(100vh - 64px - 2 * var(--spacing-xl));
}
.loading-overlay[_ngcontent-ng-c3058278431] {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  -webkit-backdrop-filter: blur(2px);
  backdrop-filter: blur(2px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
}
.loading-spinner[_ngcontent-ng-c3058278431] {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
}
.spinner[_ngcontent-ng-c3058278431] {
  width: 40px;
  height: 40px;
  border: 3px solid var(--secondary-200);
  border-top: 3px solid var(--primary-500);
  border-radius: 50%;
  animation: _ngcontent-ng-c3058278431_spin 1s linear infinite;
}
.loading-text[_ngcontent-ng-c3058278431] {
  margin: 0;
  font-size: var(--text-sm);
  color: var(--secondary-600);
  font-weight: 500;
}
@keyframes _ngcontent-ng-c3058278431_spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
@media (max-width: 767px) {
  .main-content[_ngcontent-ng-c3058278431] {
    margin-left: 0 !important;
  }
  .content-wrapper[_ngcontent-ng-c3058278431] {
    padding: var(--spacing-lg);
  }
}
@media (min-width: 768px) and (max-width: 1024px) {
  .content-wrapper[_ngcontent-ng-c3058278431] {
    padding: var(--spacing-lg) var(--spacing-xl);
  }
}
@media print {
  .layout-container[_ngcontent-ng-c3058278431] {
    background: white;
  }
  .main-content[_ngcontent-ng-c3058278431] {
    margin: 0;
    min-height: auto;
  }
  .content-wrapper[_ngcontent-ng-c3058278431] {
    padding: 0;
  }
  .loading-overlay[_ngcontent-ng-c3058278431] {
    display: none;
  }
}
/*# sourceMappingURL=/main-layout.component.css.map */</style><style ng-app-id="ng">

[_nghost-ng-c1513685420] {
  display: block;
  min-height: 100vh;
}
.login-container[_ngcontent-ng-c1513685420] {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
  background:
    linear-gradient(
      135deg,
      var(--neutral-50) 0%,
      var(--neutral-100) 100%);
}
.login-card[_ngcontent-ng-c1513685420] {
  width: 100%;
  max-width: 420px;
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--secondary-200);
  overflow: hidden;
}
.login-header[_ngcontent-ng-c1513685420] {
  padding: var(--spacing-2xl) var(--spacing-xl) var(--spacing-xl);
  text-align: center;
  background:
    linear-gradient(
      135deg,
      var(--primary-50) 0%,
      white 100%);
  border-bottom: 1px solid var(--secondary-100);
}
.logo-container[_ngcontent-ng-c1513685420] {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}
.logo-icon[_ngcontent-ng-c1513685420] {
  width: 32px;
  height: 32px;
  color: var(--primary-600);
  stroke-width: 2;
}
.logo-text[_ngcontent-ng-c1513685420] {
  font-size: var(--text-xl);
  font-weight: 700;
  color: var(--secondary-900);
  margin: 0;
}
.login-title[_ngcontent-ng-c1513685420] {
  font-size: var(--text-2xl);
  font-weight: 600;
  color: var(--secondary-900);
  margin: 0 0 var(--spacing-sm) 0;
  line-height: var(--leading-tight);
}
.login-subtitle[_ngcontent-ng-c1513685420] {
  font-size: var(--text-sm);
  color: var(--secondary-600);
  margin: 0;
  line-height: var(--leading-normal);
}
.error-message[_ngcontent-ng-c1513685420] {
  margin: var(--spacing-lg) var(--spacing-xl) 0;
  padding: var(--spacing-md);
  background: var(--error-50);
  border: 1px solid var(--error-200);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--text-sm);
  color: var(--error-700);
}
.error-icon[_ngcontent-ng-c1513685420] {
  width: 16px;
  height: 16px;
  color: var(--error-500);
  flex-shrink: 0;
  stroke-width: 2;
}
.login-form[_ngcontent-ng-c1513685420] {
  padding: var(--spacing-xl);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}
.form-options[_ngcontent-ng-c1513685420] {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-md);
}
.remember-me[_ngcontent-ng-c1513685420] {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}
.checkbox-input[_ngcontent-ng-c1513685420] {
  width: 16px;
  height: 16px;
  border: 1px solid var(--secondary-300);
  border-radius: var(--radius-sm);
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
}
.checkbox-input[_ngcontent-ng-c1513685420]:checked {
  background-color: var(--primary-600);
  border-color: var(--primary-600);
}
.checkbox-input[_ngcontent-ng-c1513685420]:focus {
  outline: none;
  box-shadow: 0 0 0 3px var(--primary-100);
}
.checkbox-input[_ngcontent-ng-c1513685420]:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
.checkbox-label[_ngcontent-ng-c1513685420] {
  font-size: var(--text-sm);
  color: var(--secondary-700);
  cursor: pointer;
  -webkit-user-select: none;
  user-select: none;
}
.forgot-password[_ngcontent-ng-c1513685420] {
  flex-shrink: 0;
}
.forgot-link[_ngcontent-ng-c1513685420] {
  font-size: var(--text-sm);
  color: var(--primary-600);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}
.forgot-link[_ngcontent-ng-c1513685420]:hover {
  color: var(--primary-700);
  text-decoration: underline;
}
.forgot-link[_ngcontent-ng-c1513685420]:focus {
  outline: none;
  color: var(--primary-700);
  text-decoration: underline;
}
.login-footer[_ngcontent-ng-c1513685420] {
  padding: var(--spacing-lg) var(--spacing-xl) var(--spacing-xl);
  text-align: center;
  background: var(--secondary-50);
  border-top: 1px solid var(--secondary-100);
}
.footer-text[_ngcontent-ng-c1513685420] {
  font-size: var(--text-sm);
  color: var(--secondary-600);
  margin: 0;
}
.footer-link[_ngcontent-ng-c1513685420] {
  color: var(--primary-600);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}
.footer-link[_ngcontent-ng-c1513685420]:hover {
  color: var(--primary-700);
  text-decoration: underline;
}
.footer-link[_ngcontent-ng-c1513685420]:focus {
  outline: none;
  color: var(--primary-700);
  text-decoration: underline;
}
@media (max-width: 480px) {
  .login-container[_ngcontent-ng-c1513685420] {
    padding: var(--spacing-md);
  }
  .login-card[_ngcontent-ng-c1513685420] {
    max-width: 100%;
  }
  .login-header[_ngcontent-ng-c1513685420] {
    padding: var(--spacing-xl) var(--spacing-lg) var(--spacing-lg);
  }
  .login-form[_ngcontent-ng-c1513685420] {
    padding: var(--spacing-lg);
  }
  .login-footer[_ngcontent-ng-c1513685420] {
    padding: var(--spacing-md) var(--spacing-lg) var(--spacing-lg);
  }
  .form-options[_ngcontent-ng-c1513685420] {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }
  .forgot-password[_ngcontent-ng-c1513685420] {
    align-self: flex-end;
  }
}
@media (prefers-contrast: high) {
  .login-card[_ngcontent-ng-c1513685420] {
    border-width: 2px;
  }
  .checkbox-input[_ngcontent-ng-c1513685420] {
    border-width: 2px;
  }
}
@media (prefers-reduced-motion: reduce) {
  .checkbox-input[_ngcontent-ng-c1513685420], 
   .forgot-link[_ngcontent-ng-c1513685420], 
   .footer-link[_ngcontent-ng-c1513685420] {
    transition: none;
  }
}
/*# sourceMappingURL=/login.component.css.map */</style><style ng-app-id="ng">

[_nghost-ng-c3847913775] {
  display: block;
  width: 100%;
}
.form-group[_ngcontent-ng-c3847913775] {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}
.form-label[_ngcontent-ng-c3847913775] {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--secondary-700);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}
.required-indicator[_ngcontent-ng-c3847913775] {
  color: var(--error-500);
  font-weight: 600;
}
.input-wrapper[_ngcontent-ng-c3847913775] {
  position: relative;
}
.form-input[_ngcontent-ng-c3847913775] {
  width: 100%;
  height: 44px;
  padding: 0 var(--spacing-md);
  border: 1px solid var(--secondary-300);
  border-radius: var(--radius-md);
  background: white;
  font-size: var(--text-sm);
  color: var(--secondary-800);
  transition: all 0.2s ease;
  outline: none;
}
.form-input[_ngcontent-ng-c3847913775]::placeholder {
  color: var(--secondary-400);
}
.form-input[_ngcontent-ng-c3847913775]:focus {
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}
.form-input[_ngcontent-ng-c3847913775]:disabled {
  background: var(--secondary-50);
  color: var(--secondary-500);
  cursor: not-allowed;
  border-color: var(--secondary-200);
}
.form-input.error[_ngcontent-ng-c3847913775] {
  border-color: var(--error-500);
  box-shadow: 0 0 0 3px var(--error-100);
}
.form-input.error[_ngcontent-ng-c3847913775]:focus {
  border-color: var(--error-500);
  box-shadow: 0 0 0 3px var(--error-100);
}
.error-message[_ngcontent-ng-c3847913775] {
  font-size: var(--text-sm);
  color: var(--error-600);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}
@media (prefers-contrast: high) {
  .form-input[_ngcontent-ng-c3847913775] {
    border-width: 2px;
  }
}
@media (prefers-reduced-motion: reduce) {
  .form-input[_ngcontent-ng-c3847913775] {
    transition: none;
  }
}
/*# sourceMappingURL=/login-input.component.css.map */</style><style ng-app-id="ng">

.btn[_ngcontent-ng-c1979755174] {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  font-family: inherit;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  white-space: nowrap;
  -webkit-user-select: none;
  user-select: none;
  outline: none;
}
.btn[_ngcontent-ng-c1979755174]:focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}
.btn[_ngcontent-ng-c1979755174]:disabled, 
.btn.btn-disabled[_ngcontent-ng-c1979755174] {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}
.btn.btn-loading[_ngcontent-ng-c1979755174] {
  cursor: wait;
}
.btn-xs[_ngcontent-ng-c1979755174] {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--text-xs);
  line-height: 1.25;
  min-height: 24px;
}
.btn-sm[_ngcontent-ng-c1979755174] {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--text-sm);
  line-height: 1.25;
  min-height: 32px;
}
.btn-md[_ngcontent-ng-c1979755174] {
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: var(--text-sm);
  line-height: 1.5;
  min-height: 40px;
}
.btn-lg[_ngcontent-ng-c1979755174] {
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--text-base);
  line-height: 1.5;
  min-height: 48px;
}
.btn-xl[_ngcontent-ng-c1979755174] {
  padding: var(--spacing-lg) var(--spacing-2xl);
  font-size: var(--text-lg);
  line-height: 1.5;
  min-height: 56px;
}
.btn-primary[_ngcontent-ng-c1979755174] {
  background-color: var(--primary-500);
  border-color: var(--primary-500);
  color: white;
}
.btn-primary[_ngcontent-ng-c1979755174]:hover:not(:disabled) {
  background-color: var(--primary-600);
  border-color: var(--primary-600);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}
.btn-primary[_ngcontent-ng-c1979755174]:active:not(:disabled) {
  background-color: var(--primary-700);
  border-color: var(--primary-700);
  transform: translateY(0);
}
.btn-secondary[_ngcontent-ng-c1979755174] {
  background-color: var(--secondary-100);
  border-color: var(--secondary-200);
  color: var(--secondary-800);
}
.btn-secondary[_ngcontent-ng-c1979755174]:hover:not(:disabled) {
  background-color: var(--secondary-200);
  border-color: var(--secondary-300);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}
.btn-secondary[_ngcontent-ng-c1979755174]:active:not(:disabled) {
  background-color: var(--secondary-300);
  border-color: var(--secondary-400);
  transform: translateY(0);
}
.btn-success[_ngcontent-ng-c1979755174] {
  background-color: var(--success-500);
  border-color: var(--success-500);
  color: white;
}
.btn-success[_ngcontent-ng-c1979755174]:hover:not(:disabled) {
  background-color: var(--success-600);
  border-color: var(--success-600);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}
.btn-success[_ngcontent-ng-c1979755174]:active:not(:disabled) {
  background-color: var(--success-700);
  border-color: var(--success-700);
  transform: translateY(0);
}
.btn-warning[_ngcontent-ng-c1979755174] {
  background-color: var(--warning-500);
  border-color: var(--warning-500);
  color: white;
}
.btn-warning[_ngcontent-ng-c1979755174]:hover:not(:disabled) {
  background-color: var(--warning-600);
  border-color: var(--warning-600);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}
.btn-warning[_ngcontent-ng-c1979755174]:active:not(:disabled) {
  background-color: var(--warning-700);
  border-color: var(--warning-700);
  transform: translateY(0);
}
.btn-error[_ngcontent-ng-c1979755174] {
  background-color: var(--error-500);
  border-color: var(--error-500);
  color: white;
}
.btn-error[_ngcontent-ng-c1979755174]:hover:not(:disabled) {
  background-color: var(--error-600);
  border-color: var(--error-600);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}
.btn-error[_ngcontent-ng-c1979755174]:active:not(:disabled) {
  background-color: var(--error-700);
  border-color: var(--error-700);
  transform: translateY(0);
}
.btn-ghost[_ngcontent-ng-c1979755174] {
  background-color: transparent;
  border-color: transparent;
  color: var(--secondary-700);
}
.btn-ghost[_ngcontent-ng-c1979755174]:hover:not(:disabled) {
  background-color: var(--secondary-100);
  color: var(--secondary-900);
}
.btn-ghost[_ngcontent-ng-c1979755174]:active:not(:disabled) {
  background-color: var(--secondary-200);
}
.btn-outline[_ngcontent-ng-c1979755174] {
  background-color: transparent;
  border-color: var(--secondary-300);
  color: var(--secondary-700);
}
.btn-outline[_ngcontent-ng-c1979755174]:hover:not(:disabled) {
  background-color: var(--secondary-50);
  border-color: var(--secondary-400);
  color: var(--secondary-900);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}
.btn-outline[_ngcontent-ng-c1979755174]:active:not(:disabled) {
  background-color: var(--secondary-100);
  border-color: var(--secondary-500);
  transform: translateY(0);
}
.btn-full-width[_ngcontent-ng-c1979755174] {
  width: 100%;
}
.btn-rounded[_ngcontent-ng-c1979755174] {
  border-radius: var(--radius-2xl);
}
.button-icon[_ngcontent-ng-c1979755174] {
  flex-shrink: 0;
}
.btn-xs[_ngcontent-ng-c1979755174]   .button-icon[_ngcontent-ng-c1979755174] {
  width: 12px;
  height: 12px;
}
.btn-sm[_ngcontent-ng-c1979755174]   .button-icon[_ngcontent-ng-c1979755174] {
  width: 14px;
  height: 14px;
}
.btn-md[_ngcontent-ng-c1979755174]   .button-icon[_ngcontent-ng-c1979755174] {
  width: 16px;
  height: 16px;
}
.btn-lg[_ngcontent-ng-c1979755174]   .button-icon[_ngcontent-ng-c1979755174] {
  width: 18px;
  height: 18px;
}
.btn-xl[_ngcontent-ng-c1979755174]   .button-icon[_ngcontent-ng-c1979755174] {
  width: 20px;
  height: 20px;
}
.button-spinner[_ngcontent-ng-c1979755174] {
  flex-shrink: 0;
  animation: _ngcontent-ng-c1979755174_spin 1s linear infinite;
}
.btn-xs[_ngcontent-ng-c1979755174]   .button-spinner[_ngcontent-ng-c1979755174] {
  width: 12px;
  height: 12px;
}
.btn-sm[_ngcontent-ng-c1979755174]   .button-spinner[_ngcontent-ng-c1979755174] {
  width: 14px;
  height: 14px;
}
.btn-md[_ngcontent-ng-c1979755174]   .button-spinner[_ngcontent-ng-c1979755174] {
  width: 16px;
  height: 16px;
}
.btn-lg[_ngcontent-ng-c1979755174]   .button-spinner[_ngcontent-ng-c1979755174] {
  width: 18px;
  height: 18px;
}
.btn-xl[_ngcontent-ng-c1979755174]   .button-spinner[_ngcontent-ng-c1979755174] {
  width: 20px;
  height: 20px;
}
@keyframes _ngcontent-ng-c1979755174_spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.button-content[_ngcontent-ng-c1979755174] {
  display: flex;
  align-items: center;
  gap: inherit;
}
.button-loading-text[_ngcontent-ng-c1979755174] {
  display: flex;
  align-items: center;
}
.button-badge[_ngcontent-ng-c1979755174] {
  position: absolute;
  top: -6px;
  right: -6px;
  min-width: 18px;
  height: 18px;
  padding: 0 4px;
  background: var(--error-500);
  color: white;
  font-size: 10px;
  font-weight: 600;
  border-radius: 9px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  border: 2px solid white;
}
.sr-only[_ngcontent-ng-c1979755174] {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
.btn[_ngcontent-ng-c1979755174]:focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}
.btn-primary[_ngcontent-ng-c1979755174]:focus-visible {
  outline-color: var(--primary-300);
}
.btn-success[_ngcontent-ng-c1979755174]:focus-visible {
  outline-color: var(--success-300);
}
.btn-warning[_ngcontent-ng-c1979755174]:focus-visible {
  outline-color: var(--warning-300);
}
.btn-error[_ngcontent-ng-c1979755174]:focus-visible {
  outline-color: var(--error-300);
}
@media (prefers-contrast: high) {
  .btn[_ngcontent-ng-c1979755174] {
    border-width: 2px;
  }
  .btn-ghost[_ngcontent-ng-c1979755174] {
    border-color: currentColor;
  }
}
@media (prefers-reduced-motion: reduce) {
  .btn[_ngcontent-ng-c1979755174] {
    transition: none;
  }
  .button-spinner[_ngcontent-ng-c1979755174] {
    animation: none;
  }
  .btn[_ngcontent-ng-c1979755174]:hover:not(:disabled) {
    transform: none;
  }
}
/*# sourceMappingURL=/button.component.css.map */</style></head>
<body><!--nghm--><script type="text/javascript" id="ng-event-dispatch-contract">(()=>{function p(t,n,r,o,e,i,f,m){return{eventType:t,event:n,targetElement:r,eic:o,timeStamp:e,eia:i,eirp:f,eiack:m}}function u(t){let n=[],r=e=>{n.push(e)};return{c:t,q:n,et:[],etc:[],d:r,h:e=>{r(p(e.type,e,e.target,t,Date.now()))}}}function s(t,n,r){for(let o=0;o<n.length;o++){let e=n[o];(r?t.etc:t.et).push(e),t.c.addEventListener(e,t.h,r)}}function c(t,n,r,o,e=window){let i=u(t);e._ejsas||(e._ejsas={}),e._ejsas[n]=i,s(i,r),s(i,o,!0)}window.__jsaction_bootstrap=c;})();
</script><script>window.__jsaction_bootstrap(document.body,"ng",["submit","change","input","compositionstart","compositionend","click"],["blur"]);</script>
  <app-root ng-version="19.2.13" ngh="4" ng-server-context="ssg"><app-main-layout _nghost-ng-c3058278431="" ngh="3"><div _ngcontent-ng-c3058278431="" class="layout-container"><!--container--><!--container--><main _ngcontent-ng-c3058278431="" class="auth-content"><router-outlet _ngcontent-ng-c3058278431=""></router-outlet><app-login _nghost-ng-c1513685420="" ngh="2"><div _ngcontent-ng-c1513685420="" class="login-container"><div _ngcontent-ng-c1513685420="" class="login-card"><div _ngcontent-ng-c1513685420="" class="login-header"><div _ngcontent-ng-c1513685420="" class="logo-container"><svg _ngcontent-ng-c1513685420="" viewBox="0 0 24 24" fill="none" stroke="currentColor" class="logo-icon"><path _ngcontent-ng-c1513685420="" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path></svg><h1 _ngcontent-ng-c1513685420="" class="logo-text">App Catalog</h1></div><h2 _ngcontent-ng-c1513685420="" class="login-title"> Welcome back </h2><p _ngcontent-ng-c1513685420="" class="login-subtitle"> Sign in to your account to continue </p></div><!--bindings={
  "ng-reflect-ng-if": ""
}--><form _ngcontent-ng-c1513685420="" novalidate="" class="login-form ng-untouched ng-pristine ng-invalid" ng-reflect-form="[object Object]" jsaction="submit:;"><app-login-input _ngcontent-ng-c1513685420="" formcontrolname="email" type="email" label="Email address" inputid="email" placeholder="Enter your email" _nghost-ng-c3847913775="" ng-reflect-type="email" ng-reflect-label="Email address" ng-reflect-input-id="email" ng-reflect-placeholder="Enter your email" ng-reflect-name="email" ng-reflect-error-message="" ng-reflect-disabled="false" ng-reflect-is-disabled="false" ng-reflect-required="true" class="ng-untouched ng-pristine ng-invalid" required="" ngh="0"><div _ngcontent-ng-c3847913775="" class="form-group"><label _ngcontent-ng-c3847913775="" class="form-label" for="email"> Email address <span _ngcontent-ng-c3847913775="" class="required-indicator">*</span><!--bindings={
  "ng-reflect-ng-if": "true"
}--></label><!--bindings={
  "ng-reflect-ng-if": "Email address"
}--><div _ngcontent-ng-c3847913775="" class="input-wrapper"><input _ngcontent-ng-c3847913775="" class="form-input ng-untouched ng-pristine ng-valid" id="email" type="email" placeholder="Enter your email" ng-reflect-is-disabled="false" ng-reflect-model="" value="" jsaction="input:;blur:;compositionstart:;compositionend:;"></div><!--container--></div></app-login-input><app-login-input _ngcontent-ng-c1513685420="" formcontrolname="password" type="password" label="Password" inputid="password" placeholder="Enter your password" _nghost-ng-c3847913775="" ng-reflect-type="password" ng-reflect-label="Password" ng-reflect-input-id="password" ng-reflect-placeholder="Enter your password" ng-reflect-name="password" ng-reflect-error-message="" ng-reflect-disabled="false" ng-reflect-is-disabled="false" ng-reflect-required="true" class="ng-untouched ng-pristine ng-invalid" required="" ngh="0"><div _ngcontent-ng-c3847913775="" class="form-group"><label _ngcontent-ng-c3847913775="" class="form-label" for="password"> Password <span _ngcontent-ng-c3847913775="" class="required-indicator">*</span><!--bindings={
  "ng-reflect-ng-if": "true"
}--></label><!--bindings={
  "ng-reflect-ng-if": "Password"
}--><div _ngcontent-ng-c3847913775="" class="input-wrapper"><input _ngcontent-ng-c3847913775="" class="form-input ng-untouched ng-pristine ng-valid" id="password" type="password" placeholder="Enter your password" ng-reflect-is-disabled="false" ng-reflect-model="" value="" jsaction="input:;blur:;compositionstart:;compositionend:;"></div><!--container--></div></app-login-input><div _ngcontent-ng-c1513685420="" class="form-options"><div _ngcontent-ng-c1513685420="" class="remember-me"><input _ngcontent-ng-c1513685420="" id="remember-me" formcontrolname="rememberMe" type="checkbox" class="checkbox-input ng-untouched ng-pristine ng-valid" ng-reflect-name="rememberMe" ng-reflect-is-disabled="false" jsaction="change:;blur:;"><label _ngcontent-ng-c1513685420="" for="remember-me" class="checkbox-label"> Remember me </label></div><div _ngcontent-ng-c1513685420="" class="forgot-password"><a _ngcontent-ng-c1513685420="" href="#" class="forgot-link"> Forgot your password? </a></div></div><app-button _ngcontent-ng-c1513685420="" type="submit" variant="primary" size="lg" loadingtext="Signing in..." lefticon="M5 13l4 4L19 7" _nghost-ng-c1979755174="" ng-reflect-type="submit" ng-reflect-variant="primary" ng-reflect-size="lg" ng-reflect-loading-text="Signing in..." ng-reflect-left-icon="M5 13l4 4L19 7" ng-reflect-disabled="true" ng-reflect-loading="false" ng-reflect-full-width="true" ngh="1"><button _ngcontent-ng-c1979755174="" class="btn btn-disabled btn-full-width btn-lg btn-primary" type="submit" disabled="" jsaction="click:;"><!--bindings={
  "ng-reflect-ng-if": "false"
}--><svg _ngcontent-ng-c1979755174="" viewBox="0 0 24 24" fill="none" stroke="currentColor" class="button-icon button-icon-left"><path _ngcontent-ng-c1979755174="" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg><!--bindings={
  "ng-reflect-ng-if": "true"
}--><span _ngcontent-ng-c1979755174="" class="button-content"> Sign in </span><!--bindings={
  "ng-reflect-ng-if": "false"
}--><!--bindings={
  "ng-reflect-ng-if": ""
}--><!--bindings={
  "ng-reflect-ng-if": ""
}--></button></app-button></form><div _ngcontent-ng-c1513685420="" class="login-footer"><p _ngcontent-ng-c1513685420="" class="footer-text"> Don't have an account? <a _ngcontent-ng-c1513685420="" href="#" class="footer-link">Contact your administrator</a></p></div></div></div></app-login><!--container--></main><!--container--><!--container--></div></app-main-layout></app-root>
<link rel="modulepreload" href="chunk-B3HRM3IA.js"><link rel="modulepreload" href="chunk-BUYEQKW2.js"><link rel="modulepreload" href="chunk-SM75SJZE.js"><link rel="modulepreload" href="chunk-IKU57TF7.js"><link rel="modulepreload" href="chunk-3JQLQ36P.js"><link rel="modulepreload" href="chunk-WDMUDEB6.js"><script src="polyfills.js" type="module"></script><script src="main.js" type="module"></script>
<link rel="modulepreload" href="chunk-D3FEWDN2.js">
<link rel="modulepreload" href="chunk-424ELUZE.js">
<link rel="modulepreload" href="chunk-2GSYAZRY.js">
<link rel="modulepreload" href="chunk-WQFVHIJL.js">
<link rel="modulepreload" href="chunk-6DLBFET6.js">
<link rel="modulepreload" href="chunk-QMVBAXS7.js">


<script id="ng-state" type="application/json">{"__nghData__":[{"t":{"1":"t4","4":"t6"},"c":{"1":[{"i":"t4","r":1,"t":{"2":"t5"},"c":{"2":[{"i":"t5","r":1}]}}],"4":[]}},{"t":{"1":"t7","2":"t8","5":"t9","6":"t10","7":"t11"},"c":{"1":[],"2":[{"i":"t8","r":1}],"5":[],"6":[],"7":[]}},{"t":{"12":"t3"},"c":{"12":[]},"n":{"25":"24f2n3f"}},{"t":{"1":"t0","2":"t1","3":"t2","4":"t12"},"c":{"1":[],"2":[],"3":[{"i":"t2","r":1,"c":{"1":[{"i":"c1513685420","r":1}]}}],"4":[]}},{}]}</script></body></html>