import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, ActivatedRoute } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { CardComponent } from '../../../shared/components/card/card.component';
import { ButtonComponent } from '../../../shared/components/button/button.component';
import { ApplicationsService } from '../applications.service';
import { Application } from '../../../shared/models/application.model';

@Component({
  selector: 'app-application-detail',
  standalone: true,
  imports: [CommonModule, RouterModule, CardComponent, ButtonComponent],
  template: `
    <div class="application-detail-page">
      <!-- Loading State -->
      <div *ngIf="isLoading" class="loading-state">
        <div class="loading-content">
          <div class="loading-spinner"></div>
          <p>Loading application details...</p>
        </div>
      </div>

      <!-- Error State -->
      <div *ngIf="hasError && !isLoading" class="error-state">
        <div class="error-content">
          <svg class="error-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="8" x2="12" y2="12"></line>
            <line x1="12" y1="16" x2="12.01" y2="16"></line>
          </svg>
          <h3>Failed to load application</h3>
          <p>There was an error loading the application details. Please try again.</p>
          <app-button variant="primary" (click)="loadApplication()">
            Retry
          </app-button>
        </div>
      </div>

      <!-- Application Content -->
      <div *ngIf="!isLoading && !hasError && application" class="application-content">
        <!-- Hero Section -->
        <div class="hero-section">
          <div class="hero-background"></div>
          <div class="hero-content">
            <div class="hero-info">
              <div class="app-icon">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                  <circle cx="9" cy="9" r="2"></circle>
                  <path d="m21 15-3.086-3.086a2 2 0 0 0-1.414-.586H13"></path>
                  <path d="M9 21v-6a2 2 0 0 1 2-2h4"></path>
                </svg>
              </div>
              <div class="app-details">
                <h1 class="app-title">{{ application.name }}</h1>
                <p class="app-description">{{ application.description }}</p>
                <div class="app-badges">
                  <span class="status-badge" [class]="'status-' + application.status.toLowerCase()">
                    {{ application.status }}
                  </span>
                  <span class="criticality-badge" [class]="'criticality-' + application.criticality.toLowerCase()">
                    {{ application.criticality }}
                  </span>
                  <span class="department-badge">{{ application.department }}</span>
                </div>
              </div>
            </div>
            <div class="hero-actions">
              <app-button variant="ghost" routerLink="/applications" leftIcon="M15 19l-7-7 7-7">
                Back to List
              </app-button>
              <app-button variant="primary" [routerLink]="['/applications', application.id, 'edit']" leftIcon="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7">
                Edit Application
              </app-button>
            </div>
          </div>
        </div>

        <!-- Performance Scores -->
        <div class="scores-section">
          <div class="scores-grid">
            <div class="score-card">
              <div class="score-icon health">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
                </svg>
              </div>
              <div class="score-content">
                <div class="score-value">{{ application.healthScore }}</div>
                <div class="score-label">Health Score</div>
              </div>
              <div class="score-indicator" [class]="getScoreClass(application.healthScore)"></div>
            </div>

            <div class="score-card">
              <div class="score-icon security">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                </svg>
              </div>
              <div class="score-content">
                <div class="score-value">{{ application.securityScore }}</div>
                <div class="score-label">Security Score</div>
              </div>
              <div class="score-indicator" [class]="getScoreClass(application.securityScore)"></div>
            </div>

            <div class="score-card">
              <div class="score-icon performance">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <circle cx="12" cy="12" r="10"></circle>
                  <polyline points="12,6 12,12 16,14"></polyline>
                </svg>
              </div>
              <div class="score-content">
                <div class="score-value">{{ application.performanceScore }}</div>
                <div class="score-label">Performance Score</div>
              </div>
              <div class="score-indicator" [class]="getScoreClass(application.performanceScore)"></div>
            </div>
          </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
          <div class="content-grid">
            <!-- Basic Information Card -->
            <div class="info-card">
              <div class="card-header">
                <div class="card-icon">
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                    <polyline points="14,2 14,8 20,8"></polyline>
                    <line x1="16" y1="13" x2="8" y2="13"></line>
                    <line x1="16" y1="17" x2="8" y2="17"></line>
                    <polyline points="10,9 9,9 8,9"></polyline>
                  </svg>
                </div>
                <h3 class="card-title">Application Details</h3>
              </div>
              <div class="card-content">
                <div class="detail-grid">
                  <div class="detail-item">
                    <span class="detail-label">Owner</span>
                    <span class="detail-value">{{ application.owner }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Department</span>
                    <span class="detail-value">{{ application.department }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Version</span>
                    <span class="detail-value version-badge">{{ application.version }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Repository</span>
                    <a [href]="application.repository" target="_blank" class="detail-link" *ngIf="application.repository">
                      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" class="link-icon">
                        <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22"></path>
                      </svg>
                      View Repository
                    </a>
                    <span class="detail-value muted" *ngIf="!application.repository">Not specified</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Deployment</span>
                    <a [href]="application.url" target="_blank" class="detail-link" *ngIf="application.url">
                      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" class="link-icon">
                        <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                        <polyline points="15,3 21,3 21,9"></polyline>
                        <line x1="10" y1="14" x2="21" y2="3"></line>
                      </svg>
                      Open Application
                    </a>
                    <span class="detail-value muted" *ngIf="!application.url">Not deployed</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Documentation</span>
                    <a [href]="application.documentation" target="_blank" class="detail-link" *ngIf="application.documentation">
                      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" class="link-icon">
                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                        <polyline points="14,2 14,8 20,8"></polyline>
                      </svg>
                      View Documentation
                    </a>
                    <span class="detail-value muted" *ngIf="!application.documentation">Not available</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Created</span>
                    <span class="detail-value">{{ application.createdDate | date:'medium' }}</span>
                  </div>
                  <div class="detail-item">
                    <span class="detail-label">Last Updated</span>
                    <span class="detail-value">{{ application.lastUpdated | date:'medium' }}</span>
                  </div>
                </div>
              </div>
            </div>

          <!-- Quick Stats -->
          <app-card title="Quick Stats" class="stats-card">
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-value">{{ application.dependencies?.length || 0 }}</div>
                <div class="stat-label">Dependencies</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ application.techStack?.length || 0 }}</div>
                <div class="stat-label">Tech Stack Items</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ application.stakeholders?.length || 0 }}</div>
                <div class="stat-label">Stakeholders</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ application.documents?.length || 0 }}</div>
                <div class="stat-label">Documents</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ application.vulnerabilities?.length || 0 }}</div>
                <div class="stat-label">Vulnerabilities</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ application.securityAssessments?.length || 0 }}</div>
                <div class="stat-label">Security Assessments</div>
              </div>
            </div>
          </app-card>

          <!-- Performance Scores -->
          <app-card title="Performance Scores" class="scores-card">
            <div class="scores-grid">
              <div class="score-item">
                <div class="score-circle">
                  <div class="score-value" [class]="getScoreClass(application.healthScore)">{{ application.healthScore }}%</div>
                  <div class="score-label">Health</div>
                </div>
              </div>
              <div class="score-item">
                <div class="score-circle">
                  <div class="score-value" [class]="getScoreClass(application.securityScore)">{{ application.securityScore }}%</div>
                  <div class="score-label">Security</div>
                </div>
              </div>
              <div class="score-item">
                <div class="score-circle">
                  <div class="score-value" [class]="getScoreClass(application.performanceScore)">{{ application.performanceScore }}%</div>
                  <div class="score-label">Performance</div>
                </div>
              </div>
            </div>
          </app-card>

          <!-- Security Overview -->
          <app-card title="Security Overview" class="security-card">
            <div class="security-content">
              <div class="vulnerability-summary">
                <div class="vuln-item">
                  <span class="vuln-count critical">{{ getVulnerabilityCount('critical') }}</span>
                  <span class="vuln-label">Critical</span>
                </div>
                <div class="vuln-item">
                  <span class="vuln-count high">{{ getVulnerabilityCount('high') }}</span>
                  <span class="vuln-label">High</span>
                </div>
                <div class="vuln-item">
                  <span class="vuln-count medium">{{ getVulnerabilityCount('medium') }}</span>
                  <span class="vuln-label">Medium</span>
                </div>
                <div class="vuln-item">
                  <span class="vuln-count low">{{ getVulnerabilityCount('low') }}</span>
                  <span class="vuln-label">Low</span>
                </div>
              </div>
            </div>
          </app-card>

          <!-- Dependencies -->
          <app-card title="Dependencies" class="dependencies-card" *ngIf="application.dependencies && application.dependencies.length > 0">
            <div class="dependencies-list">
              <div class="dependency-item" *ngFor="let dep of application.dependencies">
                <div class="dependency-header">
                  <h4 class="dependency-name">{{ dep.name }}</h4>
                  <span class="dependency-type">{{ dep.type }}</span>
                  <span class="dependency-criticality" [class]="'criticality-' + dep.criticality">{{ dep.criticality }}</span>
                </div>
                <div class="dependency-details">
                  <span class="dependency-version">v{{ dep.version }}</span>
                  <span class="dependency-status" [class]="'status-' + dep.status">{{ dep.status }}</span>
                  <span class="dependency-internal" *ngIf="dep.isInternal">Internal</span>
                </div>
                <p class="dependency-description" *ngIf="dep.description">{{ dep.description }}</p>
              </div>
            </div>
          </app-card>

          <!-- Tech Stack -->
          <app-card title="Technology Stack" class="techstack-card" *ngIf="application.techStack && application.techStack.length > 0">
            <div class="techstack-list">
              <div class="techstack-item" *ngFor="let tech of application.techStack">
                <div class="techstack-header">
                  <h4 class="techstack-name">{{ tech.name }}</h4>
                  <span class="techstack-category">{{ tech.category }}</span>
                </div>
                <div class="techstack-details">
                  <span class="techstack-version">v{{ tech.version }}</span>
                </div>
              </div>
            </div>
          </app-card>

          <!-- Stakeholders -->
          <app-card title="Stakeholders" class="stakeholders-card" *ngIf="application.stakeholders && application.stakeholders.length > 0">
            <div class="stakeholders-list">
              <div class="stakeholder-item" *ngFor="let stakeholder of application.stakeholders">
                <div class="stakeholder-header">
                  <h4 class="stakeholder-name">{{ stakeholder.name }}</h4>
                  <span class="stakeholder-role">{{ stakeholder.role }}</span>
                  <span class="stakeholder-primary" *ngIf="stakeholder.isPrimary">Primary</span>
                </div>
                <div class="stakeholder-details">
                  <span class="stakeholder-email">{{ stakeholder.email }}</span>
                  <span class="stakeholder-department">{{ stakeholder.department }}</span>
                </div>
                <p class="stakeholder-responsibility" *ngIf="stakeholder.responsibility">{{ stakeholder.responsibility }}</p>
              </div>
            </div>
          </app-card>

          <!-- Documents -->
          <app-card title="Documentation" class="documents-card" *ngIf="application.documents && application.documents.length > 0">
            <div class="documents-list">
              <div class="document-item" *ngFor="let doc of application.documents">
                <div class="document-header">
                  <h4 class="document-title">{{ doc.title }}</h4>
                  <span class="document-type">{{ doc.type }}</span>
                  <span class="document-version">v{{ doc.version }}</span>
                </div>
                <p class="document-description" *ngIf="doc.description">{{ doc.description }}</p>
                <div class="document-meta">
                  <span class="document-uploaded" *ngIf="doc.uploadedAt">Uploaded {{ doc.uploadedAt | date:'short' }}</span>
                  <span class="document-size" *ngIf="doc.fileSize">{{ doc.fileSize | number }} bytes</span>
                </div>
              </div>
            </div>
          </app-card>

          <!-- Vulnerabilities -->
          <app-card title="Vulnerabilities" class="vulnerabilities-card" *ngIf="application.vulnerabilities && application.vulnerabilities.length > 0">
            <div class="vulnerabilities-list">
              <div class="vulnerability-item" *ngFor="let vuln of application.vulnerabilities">
                <div class="vulnerability-header">
                  <h4 class="vulnerability-title">{{ vuln.title }}</h4>
                  <span class="vulnerability-severity" [class]="'severity-' + vuln.severity">{{ vuln.severity }}</span>
                  <span class="vulnerability-status" [class]="'status-' + vuln.status">{{ vuln.status }}</span>
                </div>
                <p class="vulnerability-description">{{ vuln.description }}</p>
                <div class="vulnerability-meta">
                  <span class="vulnerability-cve" *ngIf="vuln.cveId">{{ vuln.cveId }}</span>
                  <span class="vulnerability-score" *ngIf="vuln.cvssScore">CVSS: {{ vuln.cvssScore }}</span>
                  <span class="vulnerability-discovered">Discovered {{ vuln.discoveredAt | date:'short' }}</span>
                </div>
              </div>
            </div>
          </app-card>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .application-detail-page {
      min-height: 100%;
      background: var(--neutral-50);
    }

    /* Hero Section */
    .hero-section {
      position: relative;
      background: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-800) 100%);
      color: white;
      overflow: hidden;
      margin-bottom: var(--spacing-xl);
    }

    .hero-background {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
      opacity: 0.3;
    }

    .hero-content {
      position: relative;
      padding: var(--spacing-3xl) var(--spacing-xl);
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: var(--spacing-xl);
    }

    .hero-info {
      display: flex;
      align-items: center;
      gap: var(--spacing-lg);
      flex: 1;
    }

    .app-icon {
      width: 80px;
      height: 80px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: var(--radius-xl);
      display: flex;
      align-items: center;
      justify-content: center;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .app-icon svg {
      width: 40px;
      height: 40px;
      color: white;
    }

    .app-details {
      flex: 1;
    }

    .app-title {
      margin: 0 0 var(--spacing-sm) 0;
      font-size: var(--text-4xl);
      font-weight: 700;
      color: white;
      line-height: var(--leading-tight);
    }

    .app-description {
      margin: 0 0 var(--spacing-md) 0;
      font-size: var(--text-lg);
      color: rgba(255, 255, 255, 0.9);
      line-height: var(--leading-relaxed);
      max-width: 600px;
    }

    .app-badges {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      flex-wrap: wrap;
    }

    .status-badge,
    .criticality-badge,
    .department-badge {
      padding: var(--spacing-xs) var(--spacing-md);
      border-radius: var(--radius-full);
      font-size: var(--text-sm);
      font-weight: 500;
      background: rgba(255, 255, 255, 0.2);
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.3);
      backdrop-filter: blur(10px);
    }

    .hero-actions {
      display: flex;
      gap: var(--spacing-sm);
      flex-shrink: 0;
    }

    /* Scores Section */
    .scores-section {
      margin-bottom: var(--spacing-xl);
    }

    .scores-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: var(--spacing-lg);
      padding: 0 var(--spacing-xl);
    }

    .score-card {
      background: white;
      border-radius: var(--radius-xl);
      padding: var(--spacing-xl);
      box-shadow: var(--shadow-lg);
      border: 1px solid var(--secondary-200);
      display: flex;
      align-items: center;
      gap: var(--spacing-lg);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;

      &:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-xl);
      }
    }

    .score-icon {
      width: 60px;
      height: 60px;
      border-radius: var(--radius-lg);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      &.health {
        background: linear-gradient(135deg, var(--success-500), var(--success-600));
        color: white;
      }

      &.security {
        background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
        color: white;
      }

      &.performance {
        background: linear-gradient(135deg, var(--warning-500), var(--warning-600));
        color: white;
      }

      svg {
        width: 28px;
        height: 28px;
      }
    }

    .score-content {
      flex: 1;
    }

    .score-value {
      font-size: var(--text-3xl);
      font-weight: 700;
      color: var(--secondary-900);
      margin-bottom: var(--spacing-xs);
    }

    .score-label {
      font-size: var(--text-sm);
      color: var(--secondary-600);
      font-weight: 500;
    }

    .score-indicator {
      position: absolute;
      top: 0;
      right: 0;
      width: 4px;
      height: 100%;

      &.score-excellent {
        background: var(--success-500);
      }

      &.score-good {
        background: var(--primary-500);
      }

      &.score-fair {
        background: var(--warning-500);
      }

      &.score-poor {
        background: var(--error-500);
      }
    }

    /* Main Content */
    .main-content {
      padding: 0 var(--spacing-xl) var(--spacing-xl);
    }

    .content-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: var(--spacing-xl);
    }

    /* Modern Card Styles */
    .info-card {
      background: white;
      border-radius: var(--radius-xl);
      box-shadow: var(--shadow-lg);
      border: 1px solid var(--secondary-200);
      overflow: hidden;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-xl);
      }
    }

    .card-header {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg);
      border-bottom: 1px solid var(--secondary-100);
      background: linear-gradient(135deg, var(--neutral-50), var(--neutral-100));
    }

    .card-icon {
      width: 48px;
      height: 48px;
      background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
      border-radius: var(--radius-lg);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      flex-shrink: 0;

      svg {
        width: 24px;
        height: 24px;
      }
    }

    .card-title {
      margin: 0;
      font-size: var(--text-lg);
      font-weight: 600;
      color: var(--secondary-900);
    }

    .card-content {
      padding: var(--spacing-xl);
    }

    .detail-grid {
      display: grid;
      gap: var(--spacing-lg);
    }

    .detail-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: var(--spacing-md) 0;
      border-bottom: 1px solid var(--secondary-100);

      &:last-child {
        border-bottom: none;
      }
    }

    .detail-label {
      font-size: var(--text-sm);
      font-weight: 500;
      color: var(--secondary-600);
      flex-shrink: 0;
      min-width: 120px;
    }

    .detail-value {
      font-size: var(--text-sm);
      color: var(--secondary-900);
      font-weight: 500;
      text-align: right;

      &.muted {
        color: var(--secondary-500);
        font-style: italic;
      }

      &.version-badge {
        background: var(--primary-100);
        color: var(--primary-700);
        padding: var(--spacing-xs) var(--spacing-sm);
        border-radius: var(--radius-sm);
        font-size: var(--text-xs);
        font-weight: 600;
      }
    }

    .detail-link {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      color: var(--primary-600);
      text-decoration: none;
      font-size: var(--text-sm);
      font-weight: 500;
      transition: all 0.2s ease;

      &:hover {
        color: var(--primary-700);
        text-decoration: underline;
      }

      .link-icon {
        width: 16px;
        height: 16px;
        flex-shrink: 0;
      }
    }

    /* Loading State */
    .loading-state {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 400px;
      padding: var(--spacing-xl);
    }

    .loading-content {
      text-align: center;
    }

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 3px solid var(--secondary-200);
      border-top: 3px solid var(--primary-500);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto var(--spacing-md);
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .loading-content p {
      margin: 0;
      color: var(--secondary-600);
      font-size: var(--text-sm);
    }

    /* Error State */
    .error-state {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 400px;
      padding: var(--spacing-xl);
    }

    .error-content {
      text-align: center;
      max-width: 400px;
    }

    .error-icon {
      width: 64px;
      height: 64px;
      color: var(--error-500);
      margin: 0 auto var(--spacing-lg);
    }

    .error-content h3 {
      margin-bottom: var(--spacing-sm);
      color: var(--secondary-900);
    }

    .error-content p {
      margin-bottom: var(--spacing-lg);
      color: var(--secondary-600);
    }

    .page-header {
      margin-bottom: var(--spacing-xl);
    }

    .header-content {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      gap: var(--spacing-lg);
    }

    .header-info {
      flex: 1;
    }

    .page-title {
      margin: 0 0 var(--spacing-xs) 0;
      font-size: var(--text-3xl);
      font-weight: 700;
      color: var(--secondary-900);
    }

    .page-subtitle {
      margin: 0 0 var(--spacing-md) 0;
      font-size: var(--text-base);
      color: var(--secondary-600);
      line-height: var(--leading-relaxed);
    }

    .header-meta {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      flex-wrap: wrap;
    }

    .app-status {
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--radius-sm);
      font-size: var(--text-xs);
      font-weight: 500;
      text-transform: uppercase;

      &.status-production {
        background: var(--success-100);
        color: var(--success-700);
      }

      &.status-development {
        background: var(--primary-100);
        color: var(--primary-700);
      }

      &.status-testing {
        background: var(--warning-100);
        color: var(--warning-700);
      }

      &.status-deprecated {
        background: var(--error-100);
        color: var(--error-700);
      }
    }

    .app-criticality {
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--radius-sm);
      font-size: var(--text-xs);
      font-weight: 500;

      &.criticality-critical {
        background: var(--error-100);
        color: var(--error-700);
      }

      &.criticality-high {
        background: var(--warning-100);
        color: var(--warning-700);
      }

      &.criticality-medium {
        background: var(--primary-100);
        color: var(--primary-700);
      }

      &.criticality-low {
        background: var(--success-100);
        color: var(--success-700);
      }
    }

    .app-department {
      color: var(--secondary-500);
      font-size: var(--text-sm);
      font-weight: 500;
    }

    .header-actions {
      display: flex;
      gap: var(--spacing-sm);
      flex-shrink: 0;
    }

    .detail-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: var(--spacing-lg);
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--spacing-md);
      padding: var(--spacing-lg);
    }

    .info-item {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-xs);
    }

    .info-item label {
      font-size: var(--text-sm);
      font-weight: 500;
      color: var(--secondary-600);
    }

    .info-item span {
      font-size: var(--text-sm);
      color: var(--secondary-900);
    }

    .status-badge, .criticality-badge {
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--radius-sm);
      font-size: var(--text-xs);
      font-weight: 500;
      text-transform: uppercase;
      width: fit-content;
    }

    .status-production {
      background: var(--success-100);
      color: var(--success-700);
    }

    .criticality-critical {
      background: var(--error-100);
      color: var(--error-700);
    }

    .repo-link, .deployment-link {
      color: var(--primary-600);
      text-decoration: none;
      font-size: var(--text-sm);
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: var(--spacing-lg);
      padding: var(--spacing-lg);
    }

    .stat-item {
      text-align: center;
    }

    .stat-value {
      font-size: var(--text-2xl);
      font-weight: 700;
      color: var(--primary-600);
      margin-bottom: var(--spacing-xs);
    }

    .stat-label {
      font-size: var(--text-sm);
      color: var(--secondary-600);
    }

    /* Performance Scores */
    .scores-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: var(--spacing-lg);
      padding: var(--spacing-lg);
    }

    .score-item {
      text-align: center;
    }

    .score-circle {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--spacing-sm);
    }

    .score-circle .score-value {
      font-size: var(--text-2xl);
      font-weight: 700;
      padding: var(--spacing-md);
      border-radius: 50%;
      width: 80px;
      height: 80px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 3px solid;

      &.score-excellent {
        color: var(--success-700);
        border-color: var(--success-500);
        background: var(--success-50);
      }

      &.score-good {
        color: var(--primary-700);
        border-color: var(--primary-500);
        background: var(--primary-50);
      }

      &.score-fair {
        color: var(--warning-700);
        border-color: var(--warning-500);
        background: var(--warning-50);
      }

      &.score-poor {
        color: var(--error-700);
        border-color: var(--error-500);
        background: var(--error-50);
      }
    }

    .score-circle .score-label {
      font-size: var(--text-sm);
      font-weight: 500;
      color: var(--secondary-700);
    }

    .security-content {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-lg);
      padding: var(--spacing-lg);
    }

    .security-score {
      text-align: center;
    }

    .score-value {
      font-size: var(--text-3xl);
      font-weight: 700;
      color: var(--success-600);
      margin-bottom: var(--spacing-xs);
    }

    .score-label {
      font-size: var(--text-sm);
      color: var(--secondary-600);
    }

    .vulnerability-summary {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: var(--spacing-md);
    }

    .vuln-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
    }

    .vuln-count {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: var(--text-xs);
      font-weight: 600;
      color: white;

      &.critical { background: var(--error-500); }
      &.high { background: var(--warning-500); }
      &.medium { background: var(--primary-500); }
      &.low { background: var(--success-500); }
    }

    .vuln-label {
      font-size: var(--text-sm);
      color: var(--secondary-700);
    }

    /* Dependencies */
    .dependencies-list {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-md);
    }

    .dependency-item {
      padding: var(--spacing-md);
      border: 1px solid var(--secondary-200);
      border-radius: var(--radius-md);
      background: white;
    }

    .dependency-header {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      margin-bottom: var(--spacing-sm);
      flex-wrap: wrap;
    }

    .dependency-name {
      margin: 0;
      font-size: var(--text-sm);
      font-weight: 600;
      color: var(--secondary-900);
    }

    .dependency-type {
      padding: var(--spacing-xs) var(--spacing-sm);
      background: var(--neutral-100);
      color: var(--secondary-700);
      border-radius: var(--radius-sm);
      font-size: var(--text-xs);
      font-weight: 500;
    }

    .dependency-criticality {
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--radius-sm);
      font-size: var(--text-xs);
      font-weight: 500;

      &.criticality-critical {
        background: var(--error-100);
        color: var(--error-700);
      }

      &.criticality-high {
        background: var(--warning-100);
        color: var(--warning-700);
      }

      &.criticality-medium {
        background: var(--primary-100);
        color: var(--primary-700);
      }

      &.criticality-low {
        background: var(--success-100);
        color: var(--success-700);
      }
    }

    .dependency-details {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      margin-bottom: var(--spacing-sm);
      flex-wrap: wrap;
    }

    .dependency-version {
      font-size: var(--text-sm);
      color: var(--secondary-600);
      font-weight: 500;
    }

    .dependency-status {
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--radius-sm);
      font-size: var(--text-xs);
      font-weight: 500;

      &.status-active {
        background: var(--success-100);
        color: var(--success-700);
      }

      &.status-deprecated {
        background: var(--warning-100);
        color: var(--warning-700);
      }

      &.status-end_of_life {
        background: var(--error-100);
        color: var(--error-700);
      }
    }

    .dependency-internal {
      padding: var(--spacing-xs) var(--spacing-sm);
      background: var(--primary-100);
      color: var(--primary-700);
      border-radius: var(--radius-sm);
      font-size: var(--text-xs);
      font-weight: 500;
    }

    .dependency-description {
      margin: 0;
      font-size: var(--text-sm);
      color: var(--secondary-600);
      line-height: var(--leading-relaxed);
    }

    /* Tech Stack */
    .techstack-list {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: var(--spacing-md);
    }

    .techstack-item {
      padding: var(--spacing-md);
      border: 1px solid var(--secondary-200);
      border-radius: var(--radius-md);
      background: white;
    }

    .techstack-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: var(--spacing-sm);
    }

    .techstack-name {
      margin: 0;
      font-size: var(--text-sm);
      font-weight: 600;
      color: var(--secondary-900);
    }

    .techstack-category {
      padding: var(--spacing-xs) var(--spacing-sm);
      background: var(--neutral-100);
      color: var(--secondary-700);
      border-radius: var(--radius-sm);
      font-size: var(--text-xs);
      font-weight: 500;
    }

    .techstack-version {
      font-size: var(--text-sm);
      color: var(--secondary-600);
      font-weight: 500;
    }

    /* Stakeholders */
    .stakeholders-list {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-md);
    }

    .stakeholder-item {
      padding: var(--spacing-md);
      border: 1px solid var(--secondary-200);
      border-radius: var(--radius-md);
      background: white;
    }

    .stakeholder-header {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      margin-bottom: var(--spacing-sm);
      flex-wrap: wrap;
    }

    .stakeholder-name {
      margin: 0;
      font-size: var(--text-sm);
      font-weight: 600;
      color: var(--secondary-900);
    }

    .stakeholder-role {
      padding: var(--spacing-xs) var(--spacing-sm);
      background: var(--primary-100);
      color: var(--primary-700);
      border-radius: var(--radius-sm);
      font-size: var(--text-xs);
      font-weight: 500;
    }

    .stakeholder-primary {
      padding: var(--spacing-xs) var(--spacing-sm);
      background: var(--success-100);
      color: var(--success-700);
      border-radius: var(--radius-sm);
      font-size: var(--text-xs);
      font-weight: 500;
    }

    .stakeholder-details {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      margin-bottom: var(--spacing-sm);
      flex-wrap: wrap;
    }

    .stakeholder-email {
      font-size: var(--text-sm);
      color: var(--secondary-600);
    }

    .stakeholder-department {
      font-size: var(--text-sm);
      color: var(--secondary-500);
    }

    .stakeholder-responsibility {
      margin: 0;
      font-size: var(--text-sm);
      color: var(--secondary-600);
      line-height: var(--leading-relaxed);
    }

    /* Documents */
    .documents-list {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-md);
    }

    .document-item {
      padding: var(--spacing-md);
      border: 1px solid var(--secondary-200);
      border-radius: var(--radius-md);
      background: white;
    }

    .document-header {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      margin-bottom: var(--spacing-sm);
      flex-wrap: wrap;
    }

    .document-title {
      margin: 0;
      font-size: var(--text-sm);
      font-weight: 600;
      color: var(--secondary-900);
    }

    .document-type {
      padding: var(--spacing-xs) var(--spacing-sm);
      background: var(--neutral-100);
      color: var(--secondary-700);
      border-radius: var(--radius-sm);
      font-size: var(--text-xs);
      font-weight: 500;
    }

    .document-version {
      padding: var(--spacing-xs) var(--spacing-sm);
      background: var(--primary-100);
      color: var(--primary-700);
      border-radius: var(--radius-sm);
      font-size: var(--text-xs);
      font-weight: 500;
    }

    .document-description {
      margin: 0 0 var(--spacing-sm) 0;
      font-size: var(--text-sm);
      color: var(--secondary-600);
      line-height: var(--leading-relaxed);
    }

    .document-meta {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      flex-wrap: wrap;
    }

    .document-uploaded,
    .document-size {
      font-size: var(--text-xs);
      color: var(--secondary-500);
    }

    /* Vulnerabilities */
    .vulnerabilities-list {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-md);
    }

    .vulnerability-item {
      padding: var(--spacing-md);
      border: 1px solid var(--secondary-200);
      border-radius: var(--radius-md);
      background: white;
    }

    .vulnerability-header {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      margin-bottom: var(--spacing-sm);
      flex-wrap: wrap;
    }

    .vulnerability-title {
      margin: 0;
      font-size: var(--text-sm);
      font-weight: 600;
      color: var(--secondary-900);
    }

    .vulnerability-severity {
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--radius-sm);
      font-size: var(--text-xs);
      font-weight: 500;

      &.severity-critical {
        background: var(--error-100);
        color: var(--error-700);
      }

      &.severity-high {
        background: var(--warning-100);
        color: var(--warning-700);
      }

      &.severity-medium {
        background: var(--primary-100);
        color: var(--primary-700);
      }

      &.severity-low {
        background: var(--success-100);
        color: var(--success-700);
      }
    }

    .vulnerability-status {
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--radius-sm);
      font-size: var(--text-xs);
      font-weight: 500;

      &.status-open {
        background: var(--error-100);
        color: var(--error-700);
      }

      &.status-in_progress {
        background: var(--warning-100);
        color: var(--warning-700);
      }

      &.status-resolved {
        background: var(--success-100);
        color: var(--success-700);
      }
    }

    .vulnerability-description {
      margin: 0 0 var(--spacing-sm) 0;
      font-size: var(--text-sm);
      color: var(--secondary-600);
      line-height: var(--leading-relaxed);
    }

    .vulnerability-meta {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      flex-wrap: wrap;
    }

    .vulnerability-cve,
    .vulnerability-score,
    .vulnerability-discovered {
      font-size: var(--text-xs);
      color: var(--secondary-500);
    }

    @media (max-width: 768px) {
      .header-content {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-md);
      }

      .header-actions {
        justify-content: flex-end;
      }

      .detail-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
      }

      .info-grid {
        grid-template-columns: 1fr;
      }

      .stats-grid {
        grid-template-columns: repeat(2, 1fr);
      }

      .vulnerability-summary {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class ApplicationDetailComponent implements OnInit, OnDestroy {
  application: Application | null = null;
  isLoading = true;
  hasError = false;
  applicationId: number | null = null;

  private destroy$ = new Subject<void>();

  constructor(
    private route: ActivatedRoute,
    private applicationsService: ApplicationsService
  ) {}

  ngOnInit(): void {
    this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {
      this.applicationId = +params['id'];
      if (this.applicationId) {
        this.loadApplication();
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadApplication(): void {
    if (!this.applicationId) return;

    this.isLoading = true;
    this.hasError = false;

    this.applicationsService.getApplication(this.applicationId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (app) => {
          this.application = app;
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading application:', error);
          this.hasError = true;
          this.isLoading = false;
        }
      });
  }

  getScoreClass(score: number): string {
    if (score >= 90) return 'score-excellent';
    if (score >= 80) return 'score-good';
    if (score >= 70) return 'score-fair';
    return 'score-poor';
  }

  getVulnerabilityCount(severity: string): number {
    if (!this.application?.vulnerabilities) return 0;
    return this.application.vulnerabilities.filter(v => v.severity.toLowerCase() === severity.toLowerCase()).length;
  }
}
