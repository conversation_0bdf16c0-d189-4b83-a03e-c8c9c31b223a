import {
  AuthService
} from "./chunk-B3HRM3IA.js";
import "./chunk-BUYEQKW2.js";
import {
  CheckboxControlValueAccessor,
  DefaultValueAccessor,
  FormBuilder,
  FormControlName,
  FormGroupDirective,
  FormsModule,
  NG_VALUE_ACCESSOR,
  NgControlStatus,
  NgControlStatusGroup,
  NgModel,
  ReactiveFormsModule,
  RequiredValidator,
  Validators,
  ɵNgNoValidate
} from "./chunk-SM75SJZE.js";
import {
  ButtonComponent
} from "./chunk-QMVBAXS7.js";
import {
  ActivatedRoute,
  Router
} from "./chunk-IKU57TF7.js";
import {
  CommonModule,
  Component,
  Input,
  NgIf,
  forwardRef,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵProvidersFeature,
  ɵɵadvance,
  ɵɵclassProp,
  ɵɵconditional,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵnamespaceHTML,
  ɵɵnamespaceSVG,
  ɵɵnextContext,
  ɵɵproperty,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtwoWayBindingSet,
  ɵɵtwoWayListener,
  ɵɵtwoWayProperty
} from "./chunk-3JQLQ36P.js";
import "./chunk-WDMUDEB6.js";

// src/app/modules/auth/components/login-input/login-input.component.ts
function LoginInputComponent_label_1_span_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 7);
    \u0275\u0275text(1, "*");
    \u0275\u0275elementEnd();
  }
}
function LoginInputComponent_label_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "label", 5);
    \u0275\u0275text(1);
    \u0275\u0275template(2, LoginInputComponent_label_1_span_2_Template, 2, 0, "span", 6);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275property("for", ctx_r0.inputId);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r0.label, " ");
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.required);
  }
}
function LoginInputComponent_Conditional_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p", 4);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.errorMessage);
  }
}
var LoginInputComponent = class _LoginInputComponent {
  label = "";
  type = "text";
  inputId = "";
  placeholder = "";
  errorMessage = "";
  disabled = false;
  required = false;
  value = "";
  touched = false;
  onChange = (value) => {
  };
  onTouched = () => {
  };
  writeValue(value) {
    this.value = value;
  }
  registerOnChange(fn) {
    this.onChange = fn;
  }
  registerOnTouched(fn) {
    this.onTouched = fn;
  }
  setDisabledState(isDisabled) {
    this.disabled = isDisabled;
  }
  onInput(event) {
    const target = event.target;
    this.value = target.value;
    this.onChange(this.value);
  }
  onBlur() {
    if (!this.touched) {
      this.touched = true;
      this.onTouched();
    }
  }
  static \u0275fac = function LoginInputComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _LoginInputComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _LoginInputComponent, selectors: [["app-login-input"]], inputs: { label: "label", type: "type", inputId: "inputId", placeholder: "placeholder", errorMessage: "errorMessage", disabled: "disabled", required: "required" }, features: [\u0275\u0275ProvidersFeature([
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => _LoginInputComponent),
      multi: true
    }
  ])], decls: 5, vars: 9, consts: [[1, "form-group"], ["class", "form-label", 3, "for", 4, "ngIf"], [1, "input-wrapper"], [1, "form-input", 3, "ngModelChange", "input", "blur", "id", "type", "placeholder", "disabled", "ngModel"], [1, "error-message"], [1, "form-label", 3, "for"], ["class", "required-indicator", 4, "ngIf"], [1, "required-indicator"]], template: function LoginInputComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0);
      \u0275\u0275template(1, LoginInputComponent_label_1_Template, 3, 3, "label", 1);
      \u0275\u0275elementStart(2, "div", 2)(3, "input", 3);
      \u0275\u0275twoWayListener("ngModelChange", function LoginInputComponent_Template_input_ngModelChange_3_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.value, $event) || (ctx.value = $event);
        return $event;
      });
      \u0275\u0275listener("input", function LoginInputComponent_Template_input_input_3_listener($event) {
        return ctx.onInput($event);
      })("blur", function LoginInputComponent_Template_input_blur_3_listener() {
        return ctx.onBlur();
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275template(4, LoginInputComponent_Conditional_4_Template, 2, 1, "p", 4);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.label);
      \u0275\u0275advance(2);
      \u0275\u0275classProp("error", !!ctx.errorMessage);
      \u0275\u0275property("id", ctx.inputId)("type", ctx.type)("placeholder", ctx.placeholder)("disabled", ctx.disabled);
      \u0275\u0275twoWayProperty("ngModel", ctx.value);
      \u0275\u0275advance();
      \u0275\u0275conditional(ctx.errorMessage ? 4 : -1);
    }
  }, dependencies: [CommonModule, NgIf, FormsModule, DefaultValueAccessor, NgControlStatus, NgModel], styles: ["\n\n[_nghost-%COMP%] {\n  display: block;\n  width: 100%;\n}\n.form-group[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-sm);\n}\n.form-label[_ngcontent-%COMP%] {\n  font-size: var(--text-sm);\n  font-weight: 500;\n  color: var(--secondary-700);\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-xs);\n}\n.required-indicator[_ngcontent-%COMP%] {\n  color: var(--error-500);\n  font-weight: 600;\n}\n.input-wrapper[_ngcontent-%COMP%] {\n  position: relative;\n}\n.form-input[_ngcontent-%COMP%] {\n  width: 100%;\n  height: 44px;\n  padding: 0 var(--spacing-md);\n  border: 1px solid var(--secondary-300);\n  border-radius: var(--radius-md);\n  background: white;\n  font-size: var(--text-sm);\n  color: var(--secondary-800);\n  transition: all 0.2s ease;\n  outline: none;\n}\n.form-input[_ngcontent-%COMP%]::placeholder {\n  color: var(--secondary-400);\n}\n.form-input[_ngcontent-%COMP%]:focus {\n  border-color: var(--primary-500);\n  box-shadow: 0 0 0 3px var(--primary-100);\n}\n.form-input[_ngcontent-%COMP%]:disabled {\n  background: var(--secondary-50);\n  color: var(--secondary-500);\n  cursor: not-allowed;\n  border-color: var(--secondary-200);\n}\n.form-input.error[_ngcontent-%COMP%] {\n  border-color: var(--error-500);\n  box-shadow: 0 0 0 3px var(--error-100);\n}\n.form-input.error[_ngcontent-%COMP%]:focus {\n  border-color: var(--error-500);\n  box-shadow: 0 0 0 3px var(--error-100);\n}\n.error-message[_ngcontent-%COMP%] {\n  font-size: var(--text-sm);\n  color: var(--error-600);\n  margin: 0;\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-xs);\n}\n@media (prefers-contrast: high) {\n  .form-input[_ngcontent-%COMP%] {\n    border-width: 2px;\n  }\n}\n@media (prefers-reduced-motion: reduce) {\n  .form-input[_ngcontent-%COMP%] {\n    transition: none;\n  }\n}\n/*# sourceMappingURL=login-input.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(LoginInputComponent, [{
    type: Component,
    args: [{ selector: "app-login-input", standalone: true, imports: [CommonModule, FormsModule], providers: [
      {
        provide: NG_VALUE_ACCESSOR,
        useExisting: forwardRef(() => LoginInputComponent),
        multi: true
      }
    ], template: `
    <div class="form-group">
      <label *ngIf="label" [for]="inputId" class="form-label">
        {{ label }}
        <span class="required-indicator" *ngIf="required">*</span>
      </label>

      <div class="input-wrapper">
        <input
          [id]="inputId"
          [type]="type"
          [placeholder]="placeholder"
          [disabled]="disabled"
          [(ngModel)]="value"
          (input)="onInput($event)"
          (blur)="onBlur()"
          class="form-input"
          [class.error]="!!errorMessage"
        >
      </div>

      @if (errorMessage) {
        <p class="error-message">{{ errorMessage }}</p>
      }
    </div>
  `, styles: ["/* angular:styles/component:scss;202e9b4532cb6db0714ce700cfdc6677c699eef61f75f762e85d2c0fee11279c;C:/Users/<USER>/Documents/VibeCoding/AppCatalog/app-catalog/frontend/src/app/modules/auth/components/login-input/login-input.component.ts */\n:host {\n  display: block;\n  width: 100%;\n}\n.form-group {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-sm);\n}\n.form-label {\n  font-size: var(--text-sm);\n  font-weight: 500;\n  color: var(--secondary-700);\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-xs);\n}\n.required-indicator {\n  color: var(--error-500);\n  font-weight: 600;\n}\n.input-wrapper {\n  position: relative;\n}\n.form-input {\n  width: 100%;\n  height: 44px;\n  padding: 0 var(--spacing-md);\n  border: 1px solid var(--secondary-300);\n  border-radius: var(--radius-md);\n  background: white;\n  font-size: var(--text-sm);\n  color: var(--secondary-800);\n  transition: all 0.2s ease;\n  outline: none;\n}\n.form-input::placeholder {\n  color: var(--secondary-400);\n}\n.form-input:focus {\n  border-color: var(--primary-500);\n  box-shadow: 0 0 0 3px var(--primary-100);\n}\n.form-input:disabled {\n  background: var(--secondary-50);\n  color: var(--secondary-500);\n  cursor: not-allowed;\n  border-color: var(--secondary-200);\n}\n.form-input.error {\n  border-color: var(--error-500);\n  box-shadow: 0 0 0 3px var(--error-100);\n}\n.form-input.error:focus {\n  border-color: var(--error-500);\n  box-shadow: 0 0 0 3px var(--error-100);\n}\n.error-message {\n  font-size: var(--text-sm);\n  color: var(--error-600);\n  margin: 0;\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-xs);\n}\n@media (prefers-contrast: high) {\n  .form-input {\n    border-width: 2px;\n  }\n}\n@media (prefers-reduced-motion: reduce) {\n  .form-input {\n    transition: none;\n  }\n}\n/*# sourceMappingURL=login-input.component.css.map */\n"] }]
  }], null, { label: [{
    type: Input
  }], type: [{
    type: Input
  }], inputId: [{
    type: Input
  }], placeholder: [{
    type: Input
  }], errorMessage: [{
    type: Input
  }], disabled: [{
    type: Input
  }], required: [{
    type: Input
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(LoginInputComponent, { className: "LoginInputComponent", filePath: "src/app/modules/auth/components/login-input/login-input.component.ts", lineNumber: 135 });
})();

// src/app/modules/auth/components/login/login.component.ts
function LoginComponent_div_12_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 23);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(1, "svg", 24);
    \u0275\u0275element(2, "circle", 25)(3, "line", 26)(4, "line", 27);
    \u0275\u0275elementEnd();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(5, "span");
    \u0275\u0275text(6);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(6);
    \u0275\u0275textInterpolate(ctx_r0.error);
  }
}
var LoginComponent = class _LoginComponent {
  formBuilder;
  route;
  router;
  authService;
  loginForm;
  loading = false;
  error = "";
  returnUrl = "/dashboard";
  constructor(formBuilder, route, router, authService) {
    this.formBuilder = formBuilder;
    this.route = route;
    this.router = router;
    this.authService = authService;
    this.loginForm = this.formBuilder.group({
      email: ["", [Validators.required, Validators.email]],
      password: ["", [Validators.required]],
      rememberMe: [false]
    });
    this.route.queryParams.subscribe((params) => {
      this.returnUrl = params["returnUrl"] || "/dashboard";
    });
    if (this.authService.isAuthenticated()) {
      this.router.navigate([this.returnUrl]);
    }
  }
  getFieldError(fieldName) {
    const field = this.loginForm.get(fieldName);
    if (field && field.errors && field.touched) {
      if (field.errors["required"]) {
        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;
      }
      if (field.errors["email"]) {
        return "Please enter a valid email address";
      }
    }
    return "";
  }
  onSubmit() {
    if (this.loginForm.invalid) {
      Object.keys(this.loginForm.controls).forEach((key) => {
        const control = this.loginForm.get(key);
        control?.markAsTouched();
      });
      return;
    }
    this.loading = true;
    this.error = "";
    this.authService.login(this.loginForm.value).subscribe({
      next: () => {
        this.router.navigate([this.returnUrl]);
      },
      error: (error) => {
        this.error = error.message || "An error occurred during login. Please try again.";
        this.loading = false;
      }
    });
  }
  static \u0275fac = function LoginComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _LoginComponent)(\u0275\u0275directiveInject(FormBuilder), \u0275\u0275directiveInject(ActivatedRoute), \u0275\u0275directiveInject(Router), \u0275\u0275directiveInject(AuthService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _LoginComponent, selectors: [["app-login"]], decls: 31, vars: 12, consts: [[1, "login-container"], [1, "login-card"], [1, "login-header"], [1, "logo-container"], ["viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", 1, "logo-icon"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"], [1, "logo-text"], [1, "login-title"], [1, "login-subtitle"], ["class", "error-message", 4, "ngIf"], [1, "login-form", 3, "ngSubmit", "formGroup"], ["formControlName", "email", "type", "email", "label", "Email address", "inputId", "email", "placeholder", "Enter your email", 3, "errorMessage", "disabled", "required"], ["formControlName", "password", "type", "password", "label", "Password", "inputId", "password", "placeholder", "Enter your password", 3, "errorMessage", "disabled", "required"], [1, "form-options"], [1, "remember-me"], ["id", "remember-me", "formControlName", "rememberMe", "type", "checkbox", 1, "checkbox-input", 3, "disabled"], ["for", "remember-me", 1, "checkbox-label"], [1, "forgot-password"], ["href", "#", 1, "forgot-link"], ["type", "submit", "variant", "primary", "size", "lg", "loadingText", "Signing in...", "leftIcon", "M5 13l4 4L19 7", 3, "clicked", "disabled", "loading", "fullWidth"], [1, "login-footer"], [1, "footer-text"], ["href", "#", 1, "footer-link"], [1, "error-message"], ["viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", 1, "error-icon"], ["cx", "12", "cy", "12", "r", "10"], ["x1", "15", "y1", "9", "x2", "9", "y2", "15"], ["x1", "9", "y1", "9", "x2", "15", "y2", "15"]], template: function LoginComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "div", 3);
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(4, "svg", 4);
      \u0275\u0275element(5, "path", 5);
      \u0275\u0275elementEnd();
      \u0275\u0275namespaceHTML();
      \u0275\u0275elementStart(6, "h1", 6);
      \u0275\u0275text(7, "App Catalog");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(8, "h2", 7);
      \u0275\u0275text(9, " Welcome back ");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(10, "p", 8);
      \u0275\u0275text(11, " Sign in to your account to continue ");
      \u0275\u0275elementEnd()();
      \u0275\u0275template(12, LoginComponent_div_12_Template, 7, 1, "div", 9);
      \u0275\u0275elementStart(13, "form", 10);
      \u0275\u0275listener("ngSubmit", function LoginComponent_Template_form_ngSubmit_13_listener() {
        return ctx.onSubmit();
      });
      \u0275\u0275element(14, "app-login-input", 11)(15, "app-login-input", 12);
      \u0275\u0275elementStart(16, "div", 13)(17, "div", 14);
      \u0275\u0275element(18, "input", 15);
      \u0275\u0275elementStart(19, "label", 16);
      \u0275\u0275text(20, " Remember me ");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(21, "div", 17)(22, "a", 18);
      \u0275\u0275text(23, " Forgot your password? ");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(24, "app-button", 19);
      \u0275\u0275listener("clicked", function LoginComponent_Template_app_button_clicked_24_listener() {
        return ctx.onSubmit();
      });
      \u0275\u0275text(25, " Sign in ");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(26, "div", 20)(27, "p", 21);
      \u0275\u0275text(28, " Don't have an account? ");
      \u0275\u0275elementStart(29, "a", 22);
      \u0275\u0275text(30, "Contact your administrator");
      \u0275\u0275elementEnd()()()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(12);
      \u0275\u0275property("ngIf", ctx.error);
      \u0275\u0275advance();
      \u0275\u0275property("formGroup", ctx.loginForm);
      \u0275\u0275advance();
      \u0275\u0275property("errorMessage", ctx.getFieldError("email"))("disabled", ctx.loading)("required", true);
      \u0275\u0275advance();
      \u0275\u0275property("errorMessage", ctx.getFieldError("password"))("disabled", ctx.loading)("required", true);
      \u0275\u0275advance(3);
      \u0275\u0275property("disabled", ctx.loading);
      \u0275\u0275advance(6);
      \u0275\u0275property("disabled", ctx.loginForm.invalid)("loading", ctx.loading)("fullWidth", true);
    }
  }, dependencies: [CommonModule, NgIf, ReactiveFormsModule, \u0275NgNoValidate, CheckboxControlValueAccessor, NgControlStatus, NgControlStatusGroup, RequiredValidator, FormGroupDirective, FormControlName, LoginInputComponent, ButtonComponent], styles: ["\n\n[_nghost-%COMP%] {\n  display: block;\n  min-height: 100vh;\n}\n.login-container[_ngcontent-%COMP%] {\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: var(--spacing-lg);\n  background:\n    linear-gradient(\n      135deg,\n      var(--neutral-50) 0%,\n      var(--neutral-100) 100%);\n}\n.login-card[_ngcontent-%COMP%] {\n  width: 100%;\n  max-width: 420px;\n  background: white;\n  border-radius: var(--radius-xl);\n  box-shadow: var(--shadow-xl);\n  border: 1px solid var(--secondary-200);\n  overflow: hidden;\n}\n.login-header[_ngcontent-%COMP%] {\n  padding: var(--spacing-2xl) var(--spacing-xl) var(--spacing-xl);\n  text-align: center;\n  background:\n    linear-gradient(\n      135deg,\n      var(--primary-50) 0%,\n      white 100%);\n  border-bottom: 1px solid var(--secondary-100);\n}\n.logo-container[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: var(--spacing-md);\n  margin-bottom: var(--spacing-lg);\n}\n.logo-icon[_ngcontent-%COMP%] {\n  width: 32px;\n  height: 32px;\n  color: var(--primary-600);\n  stroke-width: 2;\n}\n.logo-text[_ngcontent-%COMP%] {\n  font-size: var(--text-xl);\n  font-weight: 700;\n  color: var(--secondary-900);\n  margin: 0;\n}\n.login-title[_ngcontent-%COMP%] {\n  font-size: var(--text-2xl);\n  font-weight: 600;\n  color: var(--secondary-900);\n  margin: 0 0 var(--spacing-sm) 0;\n  line-height: var(--leading-tight);\n}\n.login-subtitle[_ngcontent-%COMP%] {\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n  margin: 0;\n  line-height: var(--leading-normal);\n}\n.error-message[_ngcontent-%COMP%] {\n  margin: var(--spacing-lg) var(--spacing-xl) 0;\n  padding: var(--spacing-md);\n  background: var(--error-50);\n  border: 1px solid var(--error-200);\n  border-radius: var(--radius-md);\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n  font-size: var(--text-sm);\n  color: var(--error-700);\n}\n.error-icon[_ngcontent-%COMP%] {\n  width: 16px;\n  height: 16px;\n  color: var(--error-500);\n  flex-shrink: 0;\n  stroke-width: 2;\n}\n.login-form[_ngcontent-%COMP%] {\n  padding: var(--spacing-xl);\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-lg);\n}\n.form-options[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  gap: var(--spacing-md);\n}\n.remember-me[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n}\n.checkbox-input[_ngcontent-%COMP%] {\n  width: 16px;\n  height: 16px;\n  border: 1px solid var(--secondary-300);\n  border-radius: var(--radius-sm);\n  background: white;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n.checkbox-input[_ngcontent-%COMP%]:checked {\n  background-color: var(--primary-600);\n  border-color: var(--primary-600);\n}\n.checkbox-input[_ngcontent-%COMP%]:focus {\n  outline: none;\n  box-shadow: 0 0 0 3px var(--primary-100);\n}\n.checkbox-input[_ngcontent-%COMP%]:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n.checkbox-label[_ngcontent-%COMP%] {\n  font-size: var(--text-sm);\n  color: var(--secondary-700);\n  cursor: pointer;\n  -webkit-user-select: none;\n  user-select: none;\n}\n.forgot-password[_ngcontent-%COMP%] {\n  flex-shrink: 0;\n}\n.forgot-link[_ngcontent-%COMP%] {\n  font-size: var(--text-sm);\n  color: var(--primary-600);\n  text-decoration: none;\n  font-weight: 500;\n  transition: color 0.2s ease;\n}\n.forgot-link[_ngcontent-%COMP%]:hover {\n  color: var(--primary-700);\n  text-decoration: underline;\n}\n.forgot-link[_ngcontent-%COMP%]:focus {\n  outline: none;\n  color: var(--primary-700);\n  text-decoration: underline;\n}\n.login-footer[_ngcontent-%COMP%] {\n  padding: var(--spacing-lg) var(--spacing-xl) var(--spacing-xl);\n  text-align: center;\n  background: var(--secondary-50);\n  border-top: 1px solid var(--secondary-100);\n}\n.footer-text[_ngcontent-%COMP%] {\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n  margin: 0;\n}\n.footer-link[_ngcontent-%COMP%] {\n  color: var(--primary-600);\n  text-decoration: none;\n  font-weight: 500;\n  transition: color 0.2s ease;\n}\n.footer-link[_ngcontent-%COMP%]:hover {\n  color: var(--primary-700);\n  text-decoration: underline;\n}\n.footer-link[_ngcontent-%COMP%]:focus {\n  outline: none;\n  color: var(--primary-700);\n  text-decoration: underline;\n}\n@media (max-width: 480px) {\n  .login-container[_ngcontent-%COMP%] {\n    padding: var(--spacing-md);\n  }\n  .login-card[_ngcontent-%COMP%] {\n    max-width: 100%;\n  }\n  .login-header[_ngcontent-%COMP%] {\n    padding: var(--spacing-xl) var(--spacing-lg) var(--spacing-lg);\n  }\n  .login-form[_ngcontent-%COMP%] {\n    padding: var(--spacing-lg);\n  }\n  .login-footer[_ngcontent-%COMP%] {\n    padding: var(--spacing-md) var(--spacing-lg) var(--spacing-lg);\n  }\n  .form-options[_ngcontent-%COMP%] {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: var(--spacing-sm);\n  }\n  .forgot-password[_ngcontent-%COMP%] {\n    align-self: flex-end;\n  }\n}\n@media (prefers-contrast: high) {\n  .login-card[_ngcontent-%COMP%] {\n    border-width: 2px;\n  }\n  .checkbox-input[_ngcontent-%COMP%] {\n    border-width: 2px;\n  }\n}\n@media (prefers-reduced-motion: reduce) {\n  .checkbox-input[_ngcontent-%COMP%], \n   .forgot-link[_ngcontent-%COMP%], \n   .footer-link[_ngcontent-%COMP%] {\n    transition: none;\n  }\n}\n/*# sourceMappingURL=login.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(LoginComponent, [{
    type: Component,
    args: [{ selector: "app-login", standalone: true, imports: [CommonModule, ReactiveFormsModule, LoginInputComponent, ButtonComponent], template: `
    <div class="login-container">
      <div class="login-card">
        <!-- Logo/Branding -->
        <div class="login-header">
          <div class="logo-container">
            <svg class="logo-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
            </svg>
            <h1 class="logo-text">App Catalog</h1>
          </div>
          <h2 class="login-title">
            Welcome back
          </h2>
          <p class="login-subtitle">
            Sign in to your account to continue
          </p>
        </div>

        <!-- Error Message -->
        <div class="error-message" *ngIf="error">
          <svg class="error-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="15" y1="9" x2="9" y2="15"></line>
            <line x1="9" y1="9" x2="15" y2="15"></line>
          </svg>
          <span>{{ error }}</span>
        </div>

        <!-- Login Form -->
        <form class="login-form" [formGroup]="loginForm" (ngSubmit)="onSubmit()">
          <!-- Email Input -->
          <app-login-input
            formControlName="email"
            type="email"
            label="Email address"
            inputId="email"
            [errorMessage]="getFieldError('email')"
            [disabled]="loading"
            placeholder="Enter your email"
            [required]="true"
          ></app-login-input>

          <!-- Password Input -->
          <app-login-input
            formControlName="password"
            type="password"
            label="Password"
            inputId="password"
            [errorMessage]="getFieldError('password')"
            [disabled]="loading"
            placeholder="Enter your password"
            [required]="true"
          ></app-login-input>

          <!-- Remember Me & Forgot Password -->
          <div class="form-options">
            <div class="remember-me">
              <input
                id="remember-me"
                formControlName="rememberMe"
                type="checkbox"
                [disabled]="loading"
                class="checkbox-input"
              >
              <label for="remember-me" class="checkbox-label">
                Remember me
              </label>
            </div>

            <div class="forgot-password">
              <a href="#" class="forgot-link">
                Forgot your password?
              </a>
            </div>
          </div>

          <!-- Submit Button -->
          <app-button
            type="submit"
            variant="primary"
            size="lg"
            [disabled]="loginForm.invalid"
            [loading]="loading"
            [fullWidth]="true"
            loadingText="Signing in..."
            leftIcon="M5 13l4 4L19 7"
            (clicked)="onSubmit()"
          >
            Sign in
          </app-button>
        </form>

        <!-- Additional Links -->
        <div class="login-footer">
          <p class="footer-text">
            Don't have an account?
            <a href="#" class="footer-link">Contact your administrator</a>
          </p>
        </div>
      </div>
    </div>
  `, styles: ["/* angular:styles/component:scss;241e4bffc804ca9b4715fc9cddf569a5ecac298fd4b4e98178425eb2b6ddae7b;C:/Users/<USER>/Documents/VibeCoding/AppCatalog/app-catalog/frontend/src/app/modules/auth/components/login/login.component.ts */\n:host {\n  display: block;\n  min-height: 100vh;\n}\n.login-container {\n  min-height: 100vh;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: var(--spacing-lg);\n  background:\n    linear-gradient(\n      135deg,\n      var(--neutral-50) 0%,\n      var(--neutral-100) 100%);\n}\n.login-card {\n  width: 100%;\n  max-width: 420px;\n  background: white;\n  border-radius: var(--radius-xl);\n  box-shadow: var(--shadow-xl);\n  border: 1px solid var(--secondary-200);\n  overflow: hidden;\n}\n.login-header {\n  padding: var(--spacing-2xl) var(--spacing-xl) var(--spacing-xl);\n  text-align: center;\n  background:\n    linear-gradient(\n      135deg,\n      var(--primary-50) 0%,\n      white 100%);\n  border-bottom: 1px solid var(--secondary-100);\n}\n.logo-container {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: var(--spacing-md);\n  margin-bottom: var(--spacing-lg);\n}\n.logo-icon {\n  width: 32px;\n  height: 32px;\n  color: var(--primary-600);\n  stroke-width: 2;\n}\n.logo-text {\n  font-size: var(--text-xl);\n  font-weight: 700;\n  color: var(--secondary-900);\n  margin: 0;\n}\n.login-title {\n  font-size: var(--text-2xl);\n  font-weight: 600;\n  color: var(--secondary-900);\n  margin: 0 0 var(--spacing-sm) 0;\n  line-height: var(--leading-tight);\n}\n.login-subtitle {\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n  margin: 0;\n  line-height: var(--leading-normal);\n}\n.error-message {\n  margin: var(--spacing-lg) var(--spacing-xl) 0;\n  padding: var(--spacing-md);\n  background: var(--error-50);\n  border: 1px solid var(--error-200);\n  border-radius: var(--radius-md);\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n  font-size: var(--text-sm);\n  color: var(--error-700);\n}\n.error-icon {\n  width: 16px;\n  height: 16px;\n  color: var(--error-500);\n  flex-shrink: 0;\n  stroke-width: 2;\n}\n.login-form {\n  padding: var(--spacing-xl);\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-lg);\n}\n.form-options {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  gap: var(--spacing-md);\n}\n.remember-me {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n}\n.checkbox-input {\n  width: 16px;\n  height: 16px;\n  border: 1px solid var(--secondary-300);\n  border-radius: var(--radius-sm);\n  background: white;\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n.checkbox-input:checked {\n  background-color: var(--primary-600);\n  border-color: var(--primary-600);\n}\n.checkbox-input:focus {\n  outline: none;\n  box-shadow: 0 0 0 3px var(--primary-100);\n}\n.checkbox-input:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n.checkbox-label {\n  font-size: var(--text-sm);\n  color: var(--secondary-700);\n  cursor: pointer;\n  -webkit-user-select: none;\n  user-select: none;\n}\n.forgot-password {\n  flex-shrink: 0;\n}\n.forgot-link {\n  font-size: var(--text-sm);\n  color: var(--primary-600);\n  text-decoration: none;\n  font-weight: 500;\n  transition: color 0.2s ease;\n}\n.forgot-link:hover {\n  color: var(--primary-700);\n  text-decoration: underline;\n}\n.forgot-link:focus {\n  outline: none;\n  color: var(--primary-700);\n  text-decoration: underline;\n}\n.login-footer {\n  padding: var(--spacing-lg) var(--spacing-xl) var(--spacing-xl);\n  text-align: center;\n  background: var(--secondary-50);\n  border-top: 1px solid var(--secondary-100);\n}\n.footer-text {\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n  margin: 0;\n}\n.footer-link {\n  color: var(--primary-600);\n  text-decoration: none;\n  font-weight: 500;\n  transition: color 0.2s ease;\n}\n.footer-link:hover {\n  color: var(--primary-700);\n  text-decoration: underline;\n}\n.footer-link:focus {\n  outline: none;\n  color: var(--primary-700);\n  text-decoration: underline;\n}\n@media (max-width: 480px) {\n  .login-container {\n    padding: var(--spacing-md);\n  }\n  .login-card {\n    max-width: 100%;\n  }\n  .login-header {\n    padding: var(--spacing-xl) var(--spacing-lg) var(--spacing-lg);\n  }\n  .login-form {\n    padding: var(--spacing-lg);\n  }\n  .login-footer {\n    padding: var(--spacing-md) var(--spacing-lg) var(--spacing-lg);\n  }\n  .form-options {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: var(--spacing-sm);\n  }\n  .forgot-password {\n    align-self: flex-end;\n  }\n}\n@media (prefers-contrast: high) {\n  .login-card {\n    border-width: 2px;\n  }\n  .checkbox-input {\n    border-width: 2px;\n  }\n}\n@media (prefers-reduced-motion: reduce) {\n  .checkbox-input,\n  .forgot-link,\n  .footer-link {\n    transition: none;\n  }\n}\n/*# sourceMappingURL=login.component.css.map */\n"] }]
  }], () => [{ type: FormBuilder }, { type: ActivatedRoute }, { type: Router }, { type: AuthService }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(LoginComponent, { className: "LoginComponent", filePath: "src/app/modules/auth/components/login/login.component.ts", lineNumber: 374 });
})();
export {
  LoginComponent
};
//# sourceMappingURL=chunk-YN7OMHQZ.js.map
