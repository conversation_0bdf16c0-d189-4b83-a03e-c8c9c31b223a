<div class="form-container">
  <form [formGroup]="applicationForm" (ngSubmit)="onSubmit()">
    <!-- Tab Navigation -->
    <div class="tabs-container mb-4">
      <ul class="nav nav-tabs">
        <li class="nav-item">
          <a class="nav-link" [class.active]="activeTab === 'basic'" (click)="setActiveTab('basic')" href="javascript:void(0)">Basic Information</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" [class.active]="activeTab === 'technical'" (click)="setActiveTab('technical')" href="javascript:void(0)">Technical Details</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" [class.active]="activeTab === 'team'" (click)="setActiveTab('team')" href="javascript:void(0)">Team Information</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" [class.active]="activeTab === 'documents'" (click)="setActiveTab('documents')" href="javascript:void(0)">Documents</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" [class.active]="activeTab === 'dependencies'" (click)="setActiveTab('dependencies')" href="javascript:void(0)">Dependencies</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" [class.active]="activeTab === 'security'" (click)="setActiveTab('security')" href="javascript:void(0)">Security</a>
        </li>
      </ul>
    </div>

    <!-- Basic Information Tab -->
    <div class="form-section" *ngIf="activeTab === 'basic'">
      <h2>Basic Information</h2>

      <div class="form-group">
        <label for="name">Application Name</label>
        <input id="name" type="text" formControlName="name" placeholder="Enter application name">
        <div *ngIf="applicationForm.get('name')?.invalid && applicationForm.get('name')?.touched" class="error-message">
          Application name is required
        </div>
      </div>

      <div class="form-group">
        <label for="description">Description</label>
        <textarea id="description" formControlName="description" placeholder="Enter application description"></textarea>
        <div *ngIf="applicationForm.get('description')?.invalid && applicationForm.get('description')?.touched" class="error-message">
          Description is required
        </div>
      </div>

      <div class="form-group">
        <label for="type">Application Type</label>
        <select id="type" formControlName="type">
          <option value="Web Application">Web Application</option>
          <option value="Mobile Application">Mobile Application</option>
          <option value="Desktop Application">Desktop Application</option>
          <option value="API Service">API Service</option>
        </select>
      </div>

      <div class="form-group">
        <label for="businessTeam">Business Team</label>
        <input id="businessTeam" type="text" formControlName="businessTeam" placeholder="Enter business team">
        <div *ngIf="applicationForm.get('businessTeam')?.invalid && applicationForm.get('businessTeam')?.touched" class="error-message">
          Business team is required
        </div>
      </div>

      <div class="form-group">
        <label for="status">Status</label>
        <select id="status" formControlName="status">
          <option value="Active">Active</option>
          <option value="In Development">In Development</option>
          <option value="Maintenance">Maintenance</option>
          <option value="Retired">Retired</option>
        </select>
      </div>

      <div class="form-group">
        <label for="contactEmail">Contact Email</label>
        <input id="contactEmail" type="email" formControlName="contactEmail" placeholder="Enter contact email">
        <div *ngIf="applicationForm.get('contactEmail')?.invalid && applicationForm.get('contactEmail')?.touched" class="error-message">
          Please enter a valid email address
        </div>
      </div>

      <div class="form-group">
        <label for="supportUrl">Support URL</label>
        <input id="supportUrl" type="url" formControlName="supportUrl" placeholder="Enter support URL">
      </div>

      <div class="form-group">
        <h3>Tags</h3>
        <div formArrayName="tags">
          <div *ngFor="let tag of getTagsArray().controls; let i = index" class="array-item">
            <input type="text" [formControlName]="i" placeholder="Tag name">
            <button type="button" class="btn-remove" (click)="removeTag(i)">
              <i class="bi bi-trash"></i>
            </button>
          </div>
          <button type="button" class="btn-add" (click)="addTag()">
            <i class="bi bi-plus-circle"></i> Add Tag
          </button>
        </div>
      </div>
    </div>

    <!-- Technical Details Tab -->
    <div class="form-section" *ngIf="activeTab === 'technical'">
      <h2>Technical Details</h2>

      <div formGroupName="frontend">
        <h3>Frontend</h3>
        <div class="form-group">
          <label for="frontendFramework">Framework</label>
          <input id="frontendFramework" type="text" formControlName="framework" placeholder="e.g. Angular, React">
          <div *ngIf="applicationForm.get('frontend.framework')?.invalid && applicationForm.get('frontend.framework')?.touched" class="error-message">
            Frontend framework is required
          </div>
        </div>

        <div class="form-group">
          <label for="frontendVersion">Version</label>
          <input id="frontendVersion" type="text" formControlName="version" placeholder="e.g. 18.2.0">
          <div *ngIf="applicationForm.get('frontend.version')?.invalid && applicationForm.get('frontend.version')?.touched" class="error-message">
            Version is required
          </div>
        </div>

        <div class="form-group">
          <label for="frontendUrl">Deployment URL</label>
          <input id="frontendUrl" type="url" formControlName="deploymentUrl" placeholder="https://example.com">
          <div *ngIf="applicationForm.get('frontend.deploymentUrl')?.invalid && applicationForm.get('frontend.deploymentUrl')?.touched" class="error-message">
            Valid URL is required
          </div>
        </div>
      </div>

      <div formGroupName="backend">
        <h3>Backend</h3>
        <div class="form-group">
          <label for="backendFramework">Framework</label>
          <input id="backendFramework" type="text" formControlName="framework" placeholder="e.g. .NET Core, Node.js">
          <div *ngIf="applicationForm.get('backend.framework')?.invalid && applicationForm.get('backend.framework')?.touched" class="error-message">
            Backend framework is required
          </div>
        </div>

        <div class="form-group">
          <label for="backendVersion">Version</label>
          <input id="backendVersion" type="text" formControlName="version" placeholder="e.g. 8.0">
          <div *ngIf="applicationForm.get('backend.version')?.invalid && applicationForm.get('backend.version')?.touched" class="error-message">
            Version is required
          </div>
        </div>

        <div class="form-group">
          <label for="backendUrl">Deployment URL</label>
          <input id="backendUrl" type="url" formControlName="deploymentUrl" placeholder="https://api.example.com">
          <div *ngIf="applicationForm.get('backend.deploymentUrl')?.invalid && applicationForm.get('backend.deploymentUrl')?.touched" class="error-message">
            Valid URL is required
          </div>
        </div>
      </div>

      <div formGroupName="database">
        <h3>Database</h3>
        <div class="form-group">
          <label for="dbType">Type</label>
          <input id="dbType" type="text" formControlName="type" placeholder="e.g. SQL Server, MySQL">
          <div *ngIf="applicationForm.get('database.type')?.invalid && applicationForm.get('database.type')?.touched" class="error-message">
            Database type is required
          </div>
        </div>

        <div class="form-group">
          <label for="dbVersion">Version</label>
          <input id="dbVersion" type="text" formControlName="version" placeholder="e.g. 2022, 8.0">
          <div *ngIf="applicationForm.get('database.version')?.invalid && applicationForm.get('database.version')?.touched" class="error-message">
            Version is required
          </div>
        </div>

        <div class="form-group">
          <label for="dbServer">Server</label>
          <input id="dbServer" type="text" formControlName="server" placeholder="e.g. SQLDB01">
          <div *ngIf="applicationForm.get('database.server')?.invalid && applicationForm.get('database.server')?.touched" class="error-message">
            Server is required
          </div>
        </div>

        <div class="form-group">
          <label for="dbName">Database Name</label>
          <input id="dbName" type="text" formControlName="dbName" placeholder="e.g. HRPortalDB">
          <div *ngIf="applicationForm.get('database.dbName')?.invalid && applicationForm.get('database.dbName')?.touched" class="error-message">
            Database name is required
          </div>
        </div>

        <div class="form-group">
          <label for="dbAuth">Authentication Type</label>
          <select id="dbAuth" formControlName="authType">
            <option value="Windows Authentication">Windows Authentication</option>
            <option value="SQL Authentication">SQL Authentication</option>
          </select>
        </div>

        <div class="form-group">
          <label for="dbUsername">Username</label>
          <input id="dbUsername" type="text" formControlName="username" placeholder="Service account username">
          <div *ngIf="applicationForm.get('database.username')?.invalid && applicationForm.get('database.username')?.touched" class="error-message">
            Username is required
          </div>
        </div>
      </div>

      <div class="form-group">
        <h3>Server Information</h3>
        <div formArrayName="serverInfo">
          <div *ngFor="let server of getServerInfoArray().controls; let i = index" class="array-item">
            <input type="text" [formControlName]="i" placeholder="Server name">
            <button type="button" class="btn-remove" (click)="removeServerInfo(i)">
              <i class="bi bi-trash"></i>
            </button>
          </div>
          <button type="button" class="btn-add" (click)="addServerInfo()">
            <i class="bi bi-plus-circle"></i> Add Server
          </button>
        </div>
      </div>

      <div class="form-group">
        <label for="repoUrl">Repository URL</label>
        <input id="repoUrl" type="url" formControlName="repositoryUrl" placeholder="https://dev.azure.com/company/repo">
      </div>

      <div class="form-group">
        <h3>Deployment Information</h3>
        <div formArrayName="deploymentInfo">
          <div *ngFor="let deployment of getDeploymentInfoArray().controls; let i = index" class="deployment-item" [formGroupName]="i">
            <div class="deployment-header">
              <h4>Environment #{{i+1}}</h4>
              <button type="button" class="btn-remove" (click)="removeDeploymentInfo(i)">
                <i class="bi bi-trash"></i>
              </button>
            </div>
            <div class="deployment-body">
              <div class="form-group">
                <label>Environment</label>
                <input type="text" formControlName="environment" placeholder="e.g. Production, Staging, Development">
              </div>
              <div class="form-group">
                <label>URL</label>
                <input type="url" formControlName="url" placeholder="https://example.com">
              </div>
              <div class="form-group">
                <label>Version</label>
                <input type="text" formControlName="version" placeholder="e.g. 1.2.3">
              </div>
              <div class="form-group">
                <label>Deployment Date</label>
                <input type="date" formControlName="deploymentDate">
              </div>
            </div>
          </div>
          <button type="button" class="btn-add" (click)="addDeploymentInfo()">
            <i class="bi bi-plus-circle"></i> Add Deployment Environment
          </button>
        </div>
      </div>
    </div>

    <!-- Team Information Tab -->
    <div class="form-section" *ngIf="activeTab === 'team'">
      <h2>Team Information</h2>

      <div formGroupName="teamInfo">
        <div class="form-group">
          <label for="businessUnit">Business Unit</label>
          <input id="businessUnit" type="text" formControlName="businessUnit" placeholder="e.g. HR Technology">
        </div>

        <div class="form-group">
          <label for="productOwner">Product Owner</label>
          <input id="productOwner" type="text" formControlName="productOwner" placeholder="Product owner name">
        </div>

        <div class="form-group">
          <label for="technicalLead">Technical Lead</label>
          <input id="technicalLead" type="text" formControlName="technicalLead" placeholder="Technical lead name">
        </div>

        <div class="form-group">
          <label>Developers</label>
          <div formArrayName="developers">
            <div *ngFor="let dev of getDevelopersArray().controls; let i = index" class="array-item">
              <input type="text" [formControlName]="i" placeholder="Developer name">
              <button type="button" class="btn-remove" (click)="removeDeveloper(i)">
                <i class="bi bi-trash"></i>
              </button>
            </div>
            <button type="button" class="btn-add" (click)="addDeveloper()">
              <i class="bi bi-plus-circle"></i> Add Developer
            </button>
          </div>
        </div>

        <div class="form-group">
          <label>Business Analysts</label>
          <div formArrayName="businessAnalysts">
            <div *ngFor="let ba of getBusinessAnalystsArray().controls; let i = index" class="array-item">
              <input type="text" [formControlName]="i" placeholder="Business Analyst name">
              <button type="button" class="btn-remove" (click)="removeBusinessAnalyst(i)">
                <i class="bi bi-trash"></i>
              </button>
            </div>
            <button type="button" class="btn-add" (click)="addBusinessAnalyst()">
              <i class="bi bi-plus-circle"></i> Add Business Analyst
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Documents Tab -->
    <div class="form-section" *ngIf="activeTab === 'documents'">
      <h2>Documents</h2>

      <app-document-upload [(documents)]="uploadedDocuments"></app-document-upload>
    </div>

    <!-- Dependencies Tab -->
    <div class="form-section" *ngIf="activeTab === 'dependencies'">
      <h2>Dependencies</h2>

      <div class="alert alert-info mb-4">
        <i class="bi bi-info-circle me-2"></i>
        Select the dependencies that this application relies on.
      </div>

      <div class="dependencies-list">
        <div class="row mb-3">
          <div class="col d-flex justify-content-between align-items-center">
            <h3>Available Dependencies</h3>
            <button type="button" class="btn btn-primary" (click)="createNewDependency()">
              <i class="bi bi-plus-circle me-2"></i>Add New Dependency
            </button>
          </div>
        </div>

        <div class="dependency-items">
          <ng-container *ngIf="(allDependencies$ | async) as dependencies; else loadingDependencies">
            <div class="alert alert-warning" *ngIf="dependencies.length === 0">
              No dependencies available. Please create dependencies first.
            </div>
            <div class="dependency-item" *ngFor="let dependency of dependencies"
                 [class.selected]="isDependencySelected(dependency.id!)"
                 (click)="toggleDependency(dependency.id!)">
              <div class="dependency-info">
                <div class="dependency-title">
                  <i class="bi" [ngClass]="{'bi-box': dependency.type === 'Internal', 'bi-globe': dependency.type === 'External'}"></i>
                  <span>{{dependency.name}}</span>
                </div>
                <div class="dependency-meta">
                  <span class="badge" [ngClass]="{
                    'bg-primary': dependency.type === 'Internal',
                    'bg-info': dependency.type === 'External'
                  }">{{dependency.type}}</span>
                  <span class="badge bg-secondary">{{dependency.category}}</span>
                </div>
                <div class="dependency-description">
                  {{dependency.description}}
                </div>
              </div>
              <div class="dependency-actions">
                <i class="bi" [ngClass]="{'bi-check-circle-fill': isDependencySelected(dependency.id!), 'bi-circle': !isDependencySelected(dependency.id!)}"></i>
              </div>
            </div>
          </ng-container>
          <ng-template #loadingDependencies>
            <div class="text-center py-4">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
              <p class="mt-2">Loading dependencies...</p>
            </div>
          </ng-template>
        </div>
      </div>
    </div>

    <!-- Security Tab -->
    <div class="form-section" *ngIf="activeTab === 'security'">
      <h2>Security</h2>

      <div class="alert alert-info mb-4">
        <i class="bi bi-info-circle me-2"></i>
        Associate security vulnerabilities with this application.
      </div>

      <div class="vulnerabilities-list">
        <div class="row mb-3">
          <div class="col">
            <h3>Vulnerabilities</h3>
          </div>
        </div>

        <div class="vulnerability-items">
          <ng-container *ngIf="(allVulnerabilities$ | async) as vulnerabilities; else loadingVulnerabilities">
            <div class="alert alert-warning" *ngIf="vulnerabilities.length === 0">
              No vulnerabilities available.
            </div>
            <div class="vulnerability-item" *ngFor="let vulnerability of vulnerabilities"
                 [class.selected]="isVulnerabilitySelected(vulnerability.id!)"
                 (click)="toggleVulnerability(vulnerability.id!)">
              <div class="vulnerability-info">
                <div class="vulnerability-title">
                  <i class="bi bi-shield-exclamation me-2"></i>
                  <span>{{vulnerability.title}}</span>
                </div>
                <div class="vulnerability-meta">
                  <span class="badge" [ngClass]="{
                    'bg-danger': vulnerability.severity === 'Critical',
                    'bg-warning text-dark': vulnerability.severity === 'High',
                    'bg-info text-dark': vulnerability.severity === 'Medium',
                    'bg-success': vulnerability.severity === 'Low'
                  }">{{vulnerability.severity}}</span>
                  <span class="badge" [ngClass]="{
                    'bg-danger': vulnerability.status === 'Open',
                    'bg-warning text-dark': vulnerability.status === 'In Progress',
                    'bg-success': vulnerability.status === 'Fixed',
                    'bg-secondary': vulnerability.status === 'Closed',
                    'bg-dark': vulnerability.status === 'Won\'t Fix'
                  }">{{vulnerability.status}}</span>
                </div>
                <div class="vulnerability-description">
                  {{vulnerability.description}}
                </div>
              </div>
              <div class="vulnerability-actions">
                <i class="bi" [ngClass]="{'bi-check-circle-fill': isVulnerabilitySelected(vulnerability.id!), 'bi-circle': !isVulnerabilitySelected(vulnerability.id!)}"></i>
              </div>
            </div>
          </ng-container>
          <ng-template #loadingVulnerabilities>
            <div class="text-center py-4">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
              <p class="mt-2">Loading vulnerabilities...</p>
            </div>
          </ng-template>
        </div>
      </div>
    </div>

    <div class="form-actions">
      <button type="button" class="btn-cancel" (click)="onCancel()">Cancel</button>
      <button type="submit" class="btn-submit" [disabled]="applicationForm.invalid">
        {{ isEditMode ? 'Update' : 'Create' }} Application
      </button>
    </div>
  </form>
</div>