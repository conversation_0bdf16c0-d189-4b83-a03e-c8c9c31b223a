import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';

interface CustomField {
  name: string;
  type: 'text' | 'number' | 'select';
  options?: string[];
  required: boolean;
}

interface ValidationRule {
  field: string;
  rule: string;
  message: string;
}

@Component({
  selector: 'app-settings',
  templateUrl: './settings.component.html',
  styleUrls: ['./settings.component.scss'],
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule]
})
export class SettingsComponent implements OnInit {
  customFields: CustomField[] = [
    { name: 'Team Size', type: 'number', required: true },
    { name: 'Project Status', type: 'select', options: ['Active', 'Maintenance', 'Deprecated'], required: true },
    { name: 'Cloud Provider', type: 'text', required: false },
    { name: 'Compliance Level', type: 'select', options: ['Low', 'Medium', 'High'], required: true }
  ];

  validationRules: ValidationRule[] = [
    { field: 'applicationName', rule: '^[a-zA-Z0-9-_]+$', message: 'Only alphanumeric characters, hyphens, and underscores allowed' },
    { field: 'version', rule: '^\\d+\\.\\d+\\.\\d+$', message: 'Must follow semantic versioning (e.g., 1.0.0)' },
    { field: 'email', rule: '^[^@]+@[^@]+\\.[^@]+$', message: 'Must be a valid email address' }
  ];

  newFieldForm: FormGroup = this.fb.group({
    name: ['', Validators.required],
    type: ['text', Validators.required],
    options: [''],
    required: [false]
  });

  newRuleForm: FormGroup = this.fb.group({
    field: ['', Validators.required],
    rule: ['', Validators.required],
    message: ['', Validators.required]
  });

  constructor(private fb: FormBuilder) {}

  ngOnInit() {
    this.initializeForms();
  }

  private initializeForms() {
    this.newFieldForm = this.fb.group({
      name: ['', [Validators.required]],
      type: ['text', [Validators.required]],
      options: [''],
      required: [false]
    });

    this.newRuleForm = this.fb.group({
      field: ['', [Validators.required]],
      rule: ['', [Validators.required]],
      message: ['', [Validators.required]]
    });
  }

  addCustomField() {
    if (this.newFieldForm.valid) {
      const newField = this.newFieldForm.value;
      if (newField.type === 'select' && newField.options) {
        newField.options = newField.options.split(',').map((opt: string) => opt.trim());
      }
      this.customFields.push(newField);
      this.newFieldForm.reset({ type: 'text', required: false });
    }
  }

  addValidationRule() {
    if (this.newRuleForm.valid) {
      this.validationRules.push(this.newRuleForm.value);
      this.newRuleForm.reset();
    }
  }

  saveSettings() {
    // In a real application, this would save to a backend service
    console.log('Saving settings...');
    console.log('Custom Fields:', this.customFields);
    console.log('Validation Rules:', this.validationRules);
  }

  removeCustomField(index: number) {
    this.customFields.splice(index, 1);
  }

  removeValidationRule(index: number) {
    this.validationRules.splice(index, 1);
  }
}