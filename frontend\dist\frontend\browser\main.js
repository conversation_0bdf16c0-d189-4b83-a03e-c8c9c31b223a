import {
  AuthService
} from "./chunk-B3HRM3IA.js";
import "./chunk-BUYEQKW2.js";
import {
  DefaultValueAccessor,
  FormsModule,
  NgControlStatus,
  NgModel
} from "./chunk-SM75SJZE.js";
import {
  Router,
  RouterLink,
  RouterLinkActive,
  RouterModule,
  RouterOutlet,
  bootstrapApplication,
  provideClientHydration,
  provideHttpClient,
  provideRouter,
  withEventReplay,
  withInterceptors
} from "./chunk-IKU57TF7.js";
import {
  CommonModule,
  Component,
  DatePipe,
  EventEmitter,
  HostListener,
  Inject,
  Input,
  NgForOf,
  NgIf,
  Output,
  PLATFORM_ID,
  Subject,
  catchError,
  inject,
  isPlatformBrowser,
  provideZoneChangeDetection,
  setClassMetadata,
  takeUntil,
  tap,
  throwError,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵattribute,
  ɵɵclassMap,
  ɵɵclassProp,
  ɵɵconditional,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵnamespaceHTML,
  ɵɵnamespaceSVG,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind2,
  ɵɵproperty,
  ɵɵpureFunction1,
  ɵɵresetView,
  ɵɵresolveWindow,
  ɵɵrestoreView,
  ɵɵsanitizeUrl,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtwoWayBindingSet,
  ɵɵtwoWayListener,
  ɵɵtwoWayProperty
} from "./chunk-3JQLQ36P.js";
import {
  __spreadValues
} from "./chunk-WDMUDEB6.js";

// src/app/modules/auth/guards/auth.guard.ts
var authGuard = (route, state) => {
  const authService = inject(AuthService);
  const router = inject(Router);
  if (authService.isAuthenticated()) {
    return true;
  }
  const currentUrl = state.url;
  router.navigate(["/auth/login"], { queryParams: { returnUrl: currentUrl } });
  return false;
};

// src/app/app.routes.ts
var routes = [
  {
    path: "",
    redirectTo: "/dashboard",
    pathMatch: "full"
  },
  __spreadValues({
    path: "auth",
    loadChildren: () => import("./chunk-DAQJAKPB.js").then((m) => m.routes)
  }, false ? { \u0275entryName: "src/app/modules/auth/auth.routes.ts" } : {}),
  __spreadValues({
    path: "dashboard",
    canActivate: [authGuard],
    loadComponent: () => import("./chunk-CFQZSEMB.js").then((m) => m.DashboardComponent)
  }, false ? { \u0275entryName: "src/app/modules/dashboard/dashboard.component.ts" } : {}),
  __spreadValues({
    path: "applications",
    canActivate: [authGuard],
    loadChildren: () => import("./chunk-D3FEWDN2.js").then((m) => m.routes)
  }, false ? { \u0275entryName: "src/app/modules/applications/applications.routes.ts" } : {}),
  __spreadValues({
    path: "dependencies",
    canActivate: [authGuard],
    loadChildren: () => import("./chunk-BWGURGGP.js").then((m) => m.routes)
  }, false ? { \u0275entryName: "src/app/modules/dependencies/dependencies.routes.ts" } : {}),
  __spreadValues({
    path: "tech-stack",
    canActivate: [authGuard],
    loadChildren: () => import("./chunk-MNDQRKXJ.js").then((m) => m.routes)
  }, false ? { \u0275entryName: "src/app/modules/tech-stack/tech-stack.routes.ts" } : {}),
  __spreadValues({
    path: "stakeholders",
    canActivate: [authGuard],
    loadChildren: () => import("./chunk-DHQBOIC5.js").then((m) => m.routes)
  }, false ? { \u0275entryName: "src/app/modules/stakeholders/stakeholders.routes.ts" } : {}),
  __spreadValues({
    path: "documentation",
    canActivate: [authGuard],
    loadChildren: () => import("./chunk-DZG26DDU.js").then((m) => m.routes)
  }, false ? { \u0275entryName: "src/app/modules/documentation/documentation.routes.ts" } : {}),
  __spreadValues({
    path: "security",
    canActivate: [authGuard],
    loadChildren: () => import("./chunk-O3MRB7GS.js").then((m) => m.routes)
  }, false ? { \u0275entryName: "src/app/modules/security/security.routes.ts" } : {}),
  __spreadValues({
    path: "reports",
    canActivate: [authGuard],
    loadComponent: () => import("./chunk-GO226WFT.js").then((m) => m.ReportsComponent)
  }, false ? { \u0275entryName: "src/app/modules/reports/reports.component.ts" } : {}),
  __spreadValues({
    path: "settings",
    canActivate: [authGuard],
    loadComponent: () => import("./chunk-PPMWMVO4.js").then((m) => m.SettingsComponent)
  }, false ? { \u0275entryName: "src/app/modules/settings/settings.component.ts" } : {}),
  {
    path: "**",
    redirectTo: "/dashboard"
  }
];

// src/app/modules/auth/interceptors/auth.interceptor.ts
var AuthInterceptor = (request, next) => {
  const authService = inject(AuthService);
  const token = localStorage.getItem("auth_token") || sessionStorage.getItem("auth_token");
  if (token) {
    request = request.clone({
      setHeaders: {
        Authorization: `Bearer ${token}`
      }
    });
  }
  return next(request).pipe(catchError((error) => {
    if (error.status === 401) {
      authService.logout();
    }
    return throwError(() => error);
  }));
};

// src/app/app.config.ts
var appConfig = {
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes),
    provideClientHydration(withEventReplay()),
    provideHttpClient(withInterceptors([AuthInterceptor]))
  ]
};

// src/app/core/layout/header/header.component.ts
function HeaderComponent_button_20_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "button", 38);
    \u0275\u0275listener("click", function HeaderComponent_button_20_Template_button_click_0_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.clearSearch());
    });
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(1, "svg", 4);
    \u0275\u0275element(2, "line", 39)(3, "line", 40);
    \u0275\u0275elementEnd()();
  }
}
function HeaderComponent_span_27_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 41);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r1.notificationCount > 99 ? "99+" : ctx_r1.notificationCount, " ");
  }
}
function HeaderComponent_div_44_div_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 50)(1, "div", 51);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(2, "svg", 4);
    \u0275\u0275element(3, "path");
    \u0275\u0275elementEnd()();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(4, "div", 52)(5, "p", 53);
    \u0275\u0275text(6);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(7, "p", 54);
    \u0275\u0275text(8);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(9, "span", 55);
    \u0275\u0275text(10);
    \u0275\u0275pipe(11, "date");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const notification_r4 = ctx.$implicit;
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275classMap("notification-" + notification_r4.type);
    \u0275\u0275advance(2);
    \u0275\u0275attribute("d", ctx_r1.getNotificationIcon(notification_r4.type));
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(notification_r4.title);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(notification_r4.message);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(\u0275\u0275pipeBind2(11, 6, notification_r4.timestamp, "short"));
  }
}
function HeaderComponent_div_44_div_8_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 56)(1, "p");
    \u0275\u0275text(2, "No new notifications");
    \u0275\u0275elementEnd()();
  }
}
function HeaderComponent_div_44_Template(rf, ctx) {
  if (rf & 1) {
    const _r3 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 42)(1, "div", 43)(2, "h3");
    \u0275\u0275text(3, "Notifications");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(4, "button", 44);
    \u0275\u0275listener("click", function HeaderComponent_div_44_Template_button_click_4_listener() {
      \u0275\u0275restoreView(_r3);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.markAllAsRead());
    });
    \u0275\u0275text(5, " Mark all as read ");
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(6, "div", 45);
    \u0275\u0275template(7, HeaderComponent_div_44_div_7_Template, 12, 9, "div", 46)(8, HeaderComponent_div_44_div_8_Template, 3, 0, "div", 47);
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(9, "div", 48)(10, "a", 49);
    \u0275\u0275text(11, "View all notifications");
    \u0275\u0275elementEnd()()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(7);
    \u0275\u0275property("ngForOf", ctx_r1.notifications);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r1.notifications.length === 0);
  }
}
function HeaderComponent_div_45_Template(rf, ctx) {
  if (rf & 1) {
    const _r5 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 57)(1, "div", 45)(2, "a", 58);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(3, "svg", 4);
    \u0275\u0275element(4, "path", 59)(5, "circle", 60);
    \u0275\u0275elementEnd();
    \u0275\u0275text(6, " Profile Settings ");
    \u0275\u0275elementEnd();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(7, "a", 61);
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(8, "svg", 4);
    \u0275\u0275element(9, "circle", 26)(10, "path", 27);
    \u0275\u0275elementEnd();
    \u0275\u0275text(11, " Preferences ");
    \u0275\u0275elementEnd();
    \u0275\u0275namespaceHTML();
    \u0275\u0275element(12, "div", 62);
    \u0275\u0275elementStart(13, "button", 63);
    \u0275\u0275listener("click", function HeaderComponent_div_45_Template_button_click_13_listener() {
      \u0275\u0275restoreView(_r5);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.logout());
    });
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(14, "svg", 4);
    \u0275\u0275element(15, "path", 64)(16, "polyline", 65)(17, "line", 66);
    \u0275\u0275elementEnd();
    \u0275\u0275text(18, " Sign Out ");
    \u0275\u0275elementEnd()()();
  }
}
var HeaderComponent = class _HeaderComponent {
  authService;
  sidebarOpen = false;
  sidebarToggle = new EventEmitter();
  searchPerformed = new EventEmitter();
  searchTerm = "";
  showNotifications = false;
  showUserMenu = false;
  showQuickActions = false;
  // Mock data - replace with actual service calls
  userName = "John Doe";
  userRole = "System Administrator";
  userAvatar = "";
  notificationCount = 3;
  notifications = [
    {
      id: 1,
      type: "warning",
      title: "Security Alert",
      message: "Critical vulnerability detected in Payment Service",
      timestamp: /* @__PURE__ */ new Date()
    },
    {
      id: 2,
      type: "info",
      title: "Dependency Update",
      message: "Angular 17 is now available for upgrade",
      timestamp: /* @__PURE__ */ new Date()
    },
    {
      id: 3,
      type: "success",
      title: "Assessment Complete",
      message: "Security assessment for User Management completed",
      timestamp: /* @__PURE__ */ new Date()
    }
  ];
  toggleSidebar() {
    this.sidebarToggle.emit();
  }
  onSearch(event) {
    this.searchTerm = event.target.value;
  }
  performSearch() {
    if (this.searchTerm.trim()) {
      this.searchPerformed.emit(this.searchTerm.trim());
    }
  }
  clearSearch() {
    this.searchTerm = "";
    this.searchPerformed.emit("");
  }
  toggleNotifications() {
    this.showNotifications = !this.showNotifications;
    this.showUserMenu = false;
    this.showQuickActions = false;
  }
  toggleUserMenu() {
    this.showUserMenu = !this.showUserMenu;
    this.showNotifications = false;
    this.showQuickActions = false;
  }
  toggleQuickActions() {
    this.showQuickActions = !this.showQuickActions;
    this.showNotifications = false;
    this.showUserMenu = false;
  }
  markAllAsRead() {
    this.notificationCount = 0;
    this.notifications = [];
  }
  onAvatarError(event) {
    event.target.src = "/assets/images/default-avatar.png";
  }
  getNotificationIcon(type) {
    switch (type) {
      case "warning":
        return "M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z";
      case "success":
        return "M22 11.08V12a10 10 0 1 1-5.93-9.14";
      case "error":
        return "M18 6L6 18M6 6l12 12";
      default:
        return "M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z";
    }
  }
  constructor(authService) {
    this.authService = authService;
  }
  logout() {
    this.authService.logout();
  }
  static \u0275fac = function HeaderComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _HeaderComponent)(\u0275\u0275directiveInject(AuthService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _HeaderComponent, selectors: [["app-header"]], inputs: { sidebarOpen: "sidebarOpen" }, outputs: { sidebarToggle: "sidebarToggle", searchPerformed: "searchPerformed" }, decls: 46, vars: 13, consts: [[1, "header"], [1, "header-content"], [1, "header-brand"], [1, "sidebar-toggle", 3, "click"], ["viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", 1, "icon"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M4 6h16M4 12h16M4 18h16"], [1, "brand-info"], [1, "brand-title"], ["routerLink", "/", 1, "brand-link"], ["viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", 1, "brand-icon"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2", "d", "M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"], [1, "brand-subtitle"], [1, "header-search"], [1, "search-container"], ["viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", 1, "search-icon"], ["cx", "11", "cy", "11", "r", "8"], ["d", "m21 21-4.35-4.35"], ["type", "text", "placeholder", "Search applications, dependencies, or documentation...", 1, "search-input", 3, "ngModelChange", "input", "keyup.enter", "ngModel"], ["class", "search-clear", "aria-label", "Clear search", 3, "click", 4, "ngIf"], [1, "header-actions"], [1, "action-item"], [1, "action-button", 3, "click"], ["d", "M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"], ["d", "M13.73 21a2 2 0 0 1-3.46 0"], ["class", "notification-badge", 4, "ngIf"], ["aria-label", "Quick actions", 1, "action-button", 3, "click"], ["cx", "12", "cy", "12", "r", "3"], ["d", "M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"], ["aria-label", "User menu", 1, "user-profile", 3, "click"], [1, "user-avatar"], [1, "avatar-image", 3, "error", "src", "alt"], [1, "user-info"], [1, "user-name"], [1, "user-role"], ["viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", 1, "chevron-icon"], ["points", "6,9 12,15 18,9"], ["class", "dropdown notifications-dropdown", 4, "ngIf"], ["class", "dropdown user-dropdown", 4, "ngIf"], ["aria-label", "Clear search", 1, "search-clear", 3, "click"], ["x1", "18", "y1", "6", "x2", "6", "y2", "18"], ["x1", "6", "y1", "6", "x2", "18", "y2", "18"], [1, "notification-badge"], [1, "dropdown", "notifications-dropdown"], [1, "dropdown-header"], [1, "mark-all-read", 3, "click"], [1, "dropdown-content"], ["class", "notification-item", 4, "ngFor", "ngForOf"], ["class", "no-notifications", 4, "ngIf"], [1, "dropdown-footer"], ["routerLink", "/notifications", 1, "view-all-link"], [1, "notification-item"], [1, "notification-icon"], [1, "notification-content"], [1, "notification-title"], [1, "notification-message"], [1, "notification-time"], [1, "no-notifications"], [1, "dropdown", "user-dropdown"], ["routerLink", "/profile", 1, "dropdown-item"], ["d", "M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"], ["cx", "12", "cy", "7", "r", "4"], ["routerLink", "/preferences", 1, "dropdown-item"], [1, "dropdown-divider"], [1, "dropdown-item", "logout-item", 3, "click"], ["d", "M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"], ["points", "16,17 21,12 16,7"], ["x1", "21", "y1", "12", "x2", "9", "y2", "12"]], template: function HeaderComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "header", 0)(1, "div", 1)(2, "div", 2)(3, "button", 3);
      \u0275\u0275listener("click", function HeaderComponent_Template_button_click_3_listener() {
        return ctx.toggleSidebar();
      });
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(4, "svg", 4);
      \u0275\u0275element(5, "path", 5);
      \u0275\u0275elementEnd()();
      \u0275\u0275namespaceHTML();
      \u0275\u0275elementStart(6, "div", 6)(7, "h1", 7)(8, "a", 8);
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(9, "svg", 9);
      \u0275\u0275element(10, "path", 10);
      \u0275\u0275elementEnd();
      \u0275\u0275text(11, " App Catalog ");
      \u0275\u0275elementEnd()();
      \u0275\u0275namespaceHTML();
      \u0275\u0275elementStart(12, "span", 11);
      \u0275\u0275text(13, "Internal Application Management");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(14, "div", 12)(15, "div", 13);
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(16, "svg", 14);
      \u0275\u0275element(17, "circle", 15)(18, "path", 16);
      \u0275\u0275elementEnd();
      \u0275\u0275namespaceHTML();
      \u0275\u0275elementStart(19, "input", 17);
      \u0275\u0275twoWayListener("ngModelChange", function HeaderComponent_Template_input_ngModelChange_19_listener($event) {
        \u0275\u0275twoWayBindingSet(ctx.searchTerm, $event) || (ctx.searchTerm = $event);
        return $event;
      });
      \u0275\u0275listener("input", function HeaderComponent_Template_input_input_19_listener($event) {
        return ctx.onSearch($event);
      })("keyup.enter", function HeaderComponent_Template_input_keyup_enter_19_listener() {
        return ctx.performSearch();
      });
      \u0275\u0275elementEnd();
      \u0275\u0275template(20, HeaderComponent_button_20_Template, 4, 0, "button", 18);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(21, "div", 19)(22, "div", 20)(23, "button", 21);
      \u0275\u0275listener("click", function HeaderComponent_Template_button_click_23_listener() {
        return ctx.toggleNotifications();
      });
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(24, "svg", 4);
      \u0275\u0275element(25, "path", 22)(26, "path", 23);
      \u0275\u0275elementEnd();
      \u0275\u0275template(27, HeaderComponent_span_27_Template, 2, 1, "span", 24);
      \u0275\u0275elementEnd()();
      \u0275\u0275namespaceHTML();
      \u0275\u0275elementStart(28, "div", 20)(29, "button", 25);
      \u0275\u0275listener("click", function HeaderComponent_Template_button_click_29_listener() {
        return ctx.toggleQuickActions();
      });
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(30, "svg", 4);
      \u0275\u0275element(31, "circle", 26)(32, "path", 27);
      \u0275\u0275elementEnd()()();
      \u0275\u0275namespaceHTML();
      \u0275\u0275elementStart(33, "div", 20)(34, "button", 28);
      \u0275\u0275listener("click", function HeaderComponent_Template_button_click_34_listener() {
        return ctx.toggleUserMenu();
      });
      \u0275\u0275elementStart(35, "div", 29)(36, "img", 30);
      \u0275\u0275listener("error", function HeaderComponent_Template_img_error_36_listener($event) {
        return ctx.onAvatarError($event);
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(37, "div", 31)(38, "span", 32);
      \u0275\u0275text(39);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(40, "span", 33);
      \u0275\u0275text(41);
      \u0275\u0275elementEnd()();
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(42, "svg", 34);
      \u0275\u0275element(43, "polyline", 35);
      \u0275\u0275elementEnd()()()()();
      \u0275\u0275template(44, HeaderComponent_div_44_Template, 12, 2, "div", 36)(45, HeaderComponent_div_45_Template, 19, 0, "div", 37);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275advance(3);
      \u0275\u0275attribute("aria-label", ctx.sidebarOpen ? "Close sidebar" : "Open sidebar");
      \u0275\u0275advance(16);
      \u0275\u0275twoWayProperty("ngModel", ctx.searchTerm);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.searchTerm);
      \u0275\u0275advance(3);
      \u0275\u0275classProp("has-notifications", ctx.notificationCount > 0);
      \u0275\u0275attribute("aria-label", "Notifications" + (ctx.notificationCount > 0 ? " (" + ctx.notificationCount + " unread)" : ""));
      \u0275\u0275advance(4);
      \u0275\u0275property("ngIf", ctx.notificationCount > 0);
      \u0275\u0275advance(9);
      \u0275\u0275property("src", ctx.userAvatar || "/assets/images/default-avatar.png", \u0275\u0275sanitizeUrl)("alt", ctx.userName + " avatar");
      \u0275\u0275advance(3);
      \u0275\u0275textInterpolate(ctx.userName);
      \u0275\u0275advance(2);
      \u0275\u0275textInterpolate(ctx.userRole);
      \u0275\u0275advance(3);
      \u0275\u0275property("ngIf", ctx.showNotifications);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.showUserMenu);
    }
  }, dependencies: [CommonModule, NgForOf, NgIf, DatePipe, RouterModule, RouterLink, FormsModule, DefaultValueAccessor, NgControlStatus, NgModel], styles: ["\n\n.header[_ngcontent-%COMP%] {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 64px;\n  background: white;\n  border-bottom: 1px solid var(--secondary-200);\n  box-shadow: var(--shadow-sm);\n  z-index: var(--z-fixed);\n}\n.header-content[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  height: 100%;\n  padding: 0 var(--spacing-lg);\n  max-width: 100%;\n}\n.header-brand[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-md);\n  min-width: 0;\n}\n.sidebar-toggle[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 40px;\n  height: 40px;\n  border: none;\n  background: none;\n  border-radius: var(--radius-md);\n  color: var(--secondary-600);\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n.sidebar-toggle[_ngcontent-%COMP%]:hover {\n  background-color: var(--secondary-100);\n  color: var(--secondary-800);\n}\n.sidebar-toggle[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\n  width: 20px;\n  height: 20px;\n}\n.brand-info[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  min-width: 0;\n}\n.brand-title[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: var(--text-xl);\n  font-weight: 600;\n  line-height: 1.2;\n}\n.brand-link[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n  color: var(--secondary-900);\n  text-decoration: none;\n  transition: color 0.2s ease;\n}\n.brand-link[_ngcontent-%COMP%]:hover {\n  color: var(--primary-600);\n  text-decoration: none;\n}\n.brand-icon[_ngcontent-%COMP%] {\n  width: 24px;\n  height: 24px;\n  color: var(--primary-600);\n}\n.brand-subtitle[_ngcontent-%COMP%] {\n  font-size: var(--text-xs);\n  color: var(--secondary-500);\n  font-weight: 400;\n}\n.header-search[_ngcontent-%COMP%] {\n  flex: 1;\n  max-width: 600px;\n  margin: 0 var(--spacing-xl);\n}\n.search-container[_ngcontent-%COMP%] {\n  position: relative;\n  display: flex;\n  align-items: center;\n}\n.search-icon[_ngcontent-%COMP%] {\n  position: absolute;\n  left: var(--spacing-md);\n  width: 18px;\n  height: 18px;\n  color: var(--secondary-400);\n  z-index: 1;\n}\n.search-input[_ngcontent-%COMP%] {\n  width: 100%;\n  height: 40px;\n  padding: 0 var(--spacing-lg) 0 44px;\n  border: 1px solid var(--secondary-200);\n  border-radius: var(--radius-lg);\n  background: var(--neutral-50);\n  font-size: var(--text-sm);\n  color: var(--secondary-800);\n  transition: all 0.2s ease;\n}\n.search-input[_ngcontent-%COMP%]::placeholder {\n  color: var(--secondary-400);\n}\n.search-input[_ngcontent-%COMP%]:focus {\n  outline: none;\n  border-color: var(--primary-500);\n  background: white;\n  box-shadow: 0 0 0 3px var(--primary-100);\n}\n.search-clear[_ngcontent-%COMP%] {\n  position: absolute;\n  right: var(--spacing-md);\n  width: 20px;\n  height: 20px;\n  border: none;\n  background: none;\n  color: var(--secondary-400);\n  cursor: pointer;\n  border-radius: var(--radius-sm);\n  transition: color 0.2s ease;\n}\n.search-clear[_ngcontent-%COMP%]:hover {\n  color: var(--secondary-600);\n}\n.search-clear[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\n  width: 16px;\n  height: 16px;\n}\n.header-actions[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n}\n.action-item[_ngcontent-%COMP%] {\n  position: relative;\n}\n.action-button[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 40px;\n  height: 40px;\n  border: none;\n  background: none;\n  border-radius: var(--radius-md);\n  color: var(--secondary-600);\n  cursor: pointer;\n  transition: all 0.2s ease;\n  position: relative;\n}\n.action-button[_ngcontent-%COMP%]:hover {\n  background-color: var(--secondary-100);\n  color: var(--secondary-800);\n}\n.action-button.has-notifications[_ngcontent-%COMP%] {\n  color: var(--primary-600);\n}\n.action-button[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\n  width: 20px;\n  height: 20px;\n}\n.notification-badge[_ngcontent-%COMP%] {\n  position: absolute;\n  top: 6px;\n  right: 6px;\n  min-width: 18px;\n  height: 18px;\n  padding: 0 4px;\n  background: var(--error-500);\n  color: white;\n  font-size: 10px;\n  font-weight: 600;\n  border-radius: 9px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  line-height: 1;\n}\n.user-profile[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border: none;\n  background: none;\n  border-radius: var(--radius-md);\n  cursor: pointer;\n  transition: background-color 0.2s ease;\n}\n.user-profile[_ngcontent-%COMP%]:hover {\n  background-color: var(--secondary-100);\n}\n.user-avatar[_ngcontent-%COMP%] {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  overflow: hidden;\n  background: var(--secondary-200);\n}\n.avatar-image[_ngcontent-%COMP%] {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n.user-info[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n  min-width: 0;\n}\n.user-name[_ngcontent-%COMP%] {\n  font-size: var(--text-sm);\n  font-weight: 500;\n  color: var(--secondary-900);\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  max-width: 120px;\n}\n.user-role[_ngcontent-%COMP%] {\n  font-size: var(--text-xs);\n  color: var(--secondary-500);\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  max-width: 120px;\n}\n.chevron-icon[_ngcontent-%COMP%] {\n  width: 16px;\n  height: 16px;\n  color: var(--secondary-400);\n}\n.dropdown[_ngcontent-%COMP%] {\n  position: absolute;\n  top: calc(100% + 8px);\n  right: 0;\n  min-width: 320px;\n  background: white;\n  border: 1px solid var(--secondary-200);\n  border-radius: var(--radius-lg);\n  box-shadow: var(--shadow-lg);\n  z-index: var(--z-dropdown);\n  overflow: hidden;\n}\n.notifications-dropdown[_ngcontent-%COMP%] {\n  width: 380px;\n}\n.user-dropdown[_ngcontent-%COMP%] {\n  width: 240px;\n}\n.dropdown-header[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: var(--spacing-md) var(--spacing-lg);\n  border-bottom: 1px solid var(--secondary-200);\n  background: var(--neutral-50);\n}\n.dropdown-header[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: var(--text-base);\n  font-weight: 600;\n  color: var(--secondary-900);\n}\n.mark-all-read[_ngcontent-%COMP%] {\n  font-size: var(--text-xs);\n  color: var(--primary-600);\n  background: none;\n  border: none;\n  cursor: pointer;\n  padding: var(--spacing-xs);\n  border-radius: var(--radius-sm);\n  transition: background-color 0.2s ease;\n}\n.mark-all-read[_ngcontent-%COMP%]:hover {\n  background-color: var(--primary-50);\n}\n.dropdown-content[_ngcontent-%COMP%] {\n  max-height: 400px;\n  overflow-y: auto;\n}\n.notification-item[_ngcontent-%COMP%] {\n  display: flex;\n  gap: var(--spacing-md);\n  padding: var(--spacing-md) var(--spacing-lg);\n  border-bottom: 1px solid var(--secondary-100);\n  transition: background-color 0.2s ease;\n}\n.notification-item[_ngcontent-%COMP%]:hover {\n  background-color: var(--neutral-50);\n}\n.notification-item[_ngcontent-%COMP%]:last-child {\n  border-bottom: none;\n}\n.notification-icon[_ngcontent-%COMP%] {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n}\n.notification-icon.notification-warning[_ngcontent-%COMP%] {\n  background: var(--warning-100);\n  color: var(--warning-600);\n}\n.notification-icon.notification-success[_ngcontent-%COMP%] {\n  background: var(--success-100);\n  color: var(--success-600);\n}\n.notification-icon.notification-error[_ngcontent-%COMP%] {\n  background: var(--error-100);\n  color: var(--error-600);\n}\n.notification-icon.notification-info[_ngcontent-%COMP%] {\n  background: var(--primary-100);\n  color: var(--primary-600);\n}\n.notification-icon[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\n  width: 16px;\n  height: 16px;\n}\n.notification-content[_ngcontent-%COMP%] {\n  flex: 1;\n  min-width: 0;\n}\n.notification-title[_ngcontent-%COMP%] {\n  margin: 0 0 var(--spacing-xs) 0;\n  font-size: var(--text-sm);\n  font-weight: 500;\n  color: var(--secondary-900);\n}\n.notification-message[_ngcontent-%COMP%] {\n  margin: 0 0 var(--spacing-xs) 0;\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n  line-height: var(--leading-relaxed);\n}\n.notification-time[_ngcontent-%COMP%] {\n  font-size: var(--text-xs);\n  color: var(--secondary-400);\n}\n.no-notifications[_ngcontent-%COMP%] {\n  padding: var(--spacing-xl);\n  text-align: center;\n  color: var(--secondary-500);\n}\n.dropdown-footer[_ngcontent-%COMP%] {\n  padding: var(--spacing-md) var(--spacing-lg);\n  border-top: 1px solid var(--secondary-200);\n  background: var(--neutral-50);\n}\n.view-all-link[_ngcontent-%COMP%] {\n  display: block;\n  text-align: center;\n  font-size: var(--text-sm);\n  color: var(--primary-600);\n  text-decoration: none;\n  font-weight: 500;\n}\n.view-all-link[_ngcontent-%COMP%]:hover {\n  color: var(--primary-700);\n  text-decoration: underline;\n}\n.dropdown-item[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-md);\n  width: 100%;\n  padding: var(--spacing-md) var(--spacing-lg);\n  border: none;\n  background: none;\n  text-align: left;\n  font-size: var(--text-sm);\n  color: var(--secondary-700);\n  text-decoration: none;\n  cursor: pointer;\n  transition: background-color 0.2s ease;\n}\n.dropdown-item[_ngcontent-%COMP%]:hover {\n  background-color: var(--neutral-50);\n  color: var(--secondary-900);\n  text-decoration: none;\n}\n.dropdown-item[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\n  width: 16px;\n  height: 16px;\n  color: var(--secondary-500);\n}\n.dropdown-divider[_ngcontent-%COMP%] {\n  height: 1px;\n  background: var(--secondary-200);\n  margin: var(--spacing-xs) 0;\n}\n.logout-item[_ngcontent-%COMP%] {\n  color: var(--error-600);\n}\n.logout-item[_ngcontent-%COMP%]:hover {\n  background-color: var(--error-50);\n  color: var(--error-700);\n}\n.logout-item[_ngcontent-%COMP%]   .icon[_ngcontent-%COMP%] {\n  color: var(--error-500);\n}\n@media (max-width: 768px) {\n  .header-search[_ngcontent-%COMP%] {\n    display: none;\n  }\n  .user-info[_ngcontent-%COMP%] {\n    display: none;\n  }\n  .brand-subtitle[_ngcontent-%COMP%] {\n    display: none;\n  }\n  .dropdown[_ngcontent-%COMP%] {\n    right: var(--spacing-md);\n    left: var(--spacing-md);\n    width: auto;\n    min-width: auto;\n  }\n}\n/*# sourceMappingURL=header.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(HeaderComponent, [{
    type: Component,
    args: [{ selector: "app-header", standalone: true, imports: [CommonModule, RouterModule, FormsModule], template: `
    <header class="header">
      <div class="header-content">
        <!-- Logo and Brand -->
        <div class="header-brand">
          <button
            class="sidebar-toggle"
            (click)="toggleSidebar()"
            [attr.aria-label]="sidebarOpen ? 'Close sidebar' : 'Open sidebar'"
          >
            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>

          <div class="brand-info">
            <h1 class="brand-title">
              <a routerLink="/" class="brand-link">
                <svg class="brand-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                App Catalog
              </a>
            </h1>
            <span class="brand-subtitle">Internal Application Management</span>
          </div>
        </div>

        <!-- Search Bar -->
        <div class="header-search">
          <div class="search-container">
            <svg class="search-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="11" cy="11" r="8"></circle>
              <path d="m21 21-4.35-4.35"></path>
            </svg>
            <input
              type="text"
              class="search-input"
              placeholder="Search applications, dependencies, or documentation..."
              [(ngModel)]="searchTerm"
              (input)="onSearch($event)"
              (keyup.enter)="performSearch()"
            >
            <button
              class="search-clear"
              *ngIf="searchTerm"
              (click)="clearSearch()"
              aria-label="Clear search"
            >
              <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </div>
        </div>

        <!-- Header Actions -->
        <div class="header-actions">
          <!-- Notifications -->
          <div class="action-item">
            <button
              class="action-button"
              [class.has-notifications]="notificationCount > 0"
              (click)="toggleNotifications()"
              [attr.aria-label]="'Notifications' + (notificationCount > 0 ? ' (' + notificationCount + ' unread)' : '')"
            >
              <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"></path>
                <path d="M13.73 21a2 2 0 0 1-3.46 0"></path>
              </svg>
              <span class="notification-badge" *ngIf="notificationCount > 0">
                {{ notificationCount > 99 ? '99+' : notificationCount }}
              </span>
            </button>
          </div>

          <!-- Quick Actions -->
          <div class="action-item">
            <button
              class="action-button"
              (click)="toggleQuickActions()"
              aria-label="Quick actions"
            >
              <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <circle cx="12" cy="12" r="3"></circle>
                <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
              </svg>
            </button>
          </div>

          <!-- User Profile -->
          <div class="action-item">
            <button
              class="user-profile"
              (click)="toggleUserMenu()"
              aria-label="User menu"
            >
              <div class="user-avatar">
                <img
                  [src]="userAvatar || '/assets/images/default-avatar.png'"
                  [alt]="userName + ' avatar'"
                  class="avatar-image"
                  (error)="onAvatarError($event)"
                >
              </div>
              <div class="user-info">
                <span class="user-name">{{ userName }}</span>
                <span class="user-role">{{ userRole }}</span>
              </div>
              <svg class="chevron-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <polyline points="6,9 12,15 18,9"></polyline>
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- Notifications Dropdown -->
      <div class="dropdown notifications-dropdown" *ngIf="showNotifications">
        <div class="dropdown-header">
          <h3>Notifications</h3>
          <button class="mark-all-read" (click)="markAllAsRead()">
            Mark all as read
          </button>
        </div>
        <div class="dropdown-content">
          <div class="notification-item" *ngFor="let notification of notifications">
            <div class="notification-icon" [class]="'notification-' + notification.type">
              <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path [attr.d]="getNotificationIcon(notification.type)"></path>
              </svg>
            </div>
            <div class="notification-content">
              <p class="notification-title">{{ notification.title }}</p>
              <p class="notification-message">{{ notification.message }}</p>
              <span class="notification-time">{{ notification.timestamp | date:'short' }}</span>
            </div>
          </div>
          <div class="no-notifications" *ngIf="notifications.length === 0">
            <p>No new notifications</p>
          </div>
        </div>
        <div class="dropdown-footer">
          <a routerLink="/notifications" class="view-all-link">View all notifications</a>
        </div>
      </div>

      <!-- User Menu Dropdown -->
      <div class="dropdown user-dropdown" *ngIf="showUserMenu">
        <div class="dropdown-content">
          <a routerLink="/profile" class="dropdown-item">
            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
              <circle cx="12" cy="7" r="4"></circle>
            </svg>
            Profile Settings
          </a>
          <a routerLink="/preferences" class="dropdown-item">
            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="12" cy="12" r="3"></circle>
              <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
            </svg>
            Preferences
          </a>
          <div class="dropdown-divider"></div>
          <button class="dropdown-item logout-item" (click)="logout()">
            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
              <polyline points="16,17 21,12 16,7"></polyline>
              <line x1="21" y1="12" x2="9" y2="12"></line>
            </svg>
            Sign Out
          </button>
        </div>
      </div>
    </header>
  `, styles: ["/* src/app/core/layout/header/header.component.scss */\n.header {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 64px;\n  background: white;\n  border-bottom: 1px solid var(--secondary-200);\n  box-shadow: var(--shadow-sm);\n  z-index: var(--z-fixed);\n}\n.header-content {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  height: 100%;\n  padding: 0 var(--spacing-lg);\n  max-width: 100%;\n}\n.header-brand {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-md);\n  min-width: 0;\n}\n.sidebar-toggle {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 40px;\n  height: 40px;\n  border: none;\n  background: none;\n  border-radius: var(--radius-md);\n  color: var(--secondary-600);\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n.sidebar-toggle:hover {\n  background-color: var(--secondary-100);\n  color: var(--secondary-800);\n}\n.sidebar-toggle .icon {\n  width: 20px;\n  height: 20px;\n}\n.brand-info {\n  display: flex;\n  flex-direction: column;\n  min-width: 0;\n}\n.brand-title {\n  margin: 0;\n  font-size: var(--text-xl);\n  font-weight: 600;\n  line-height: 1.2;\n}\n.brand-link {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n  color: var(--secondary-900);\n  text-decoration: none;\n  transition: color 0.2s ease;\n}\n.brand-link:hover {\n  color: var(--primary-600);\n  text-decoration: none;\n}\n.brand-icon {\n  width: 24px;\n  height: 24px;\n  color: var(--primary-600);\n}\n.brand-subtitle {\n  font-size: var(--text-xs);\n  color: var(--secondary-500);\n  font-weight: 400;\n}\n.header-search {\n  flex: 1;\n  max-width: 600px;\n  margin: 0 var(--spacing-xl);\n}\n.search-container {\n  position: relative;\n  display: flex;\n  align-items: center;\n}\n.search-icon {\n  position: absolute;\n  left: var(--spacing-md);\n  width: 18px;\n  height: 18px;\n  color: var(--secondary-400);\n  z-index: 1;\n}\n.search-input {\n  width: 100%;\n  height: 40px;\n  padding: 0 var(--spacing-lg) 0 44px;\n  border: 1px solid var(--secondary-200);\n  border-radius: var(--radius-lg);\n  background: var(--neutral-50);\n  font-size: var(--text-sm);\n  color: var(--secondary-800);\n  transition: all 0.2s ease;\n}\n.search-input::placeholder {\n  color: var(--secondary-400);\n}\n.search-input:focus {\n  outline: none;\n  border-color: var(--primary-500);\n  background: white;\n  box-shadow: 0 0 0 3px var(--primary-100);\n}\n.search-clear {\n  position: absolute;\n  right: var(--spacing-md);\n  width: 20px;\n  height: 20px;\n  border: none;\n  background: none;\n  color: var(--secondary-400);\n  cursor: pointer;\n  border-radius: var(--radius-sm);\n  transition: color 0.2s ease;\n}\n.search-clear:hover {\n  color: var(--secondary-600);\n}\n.search-clear .icon {\n  width: 16px;\n  height: 16px;\n}\n.header-actions {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n}\n.action-item {\n  position: relative;\n}\n.action-button {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 40px;\n  height: 40px;\n  border: none;\n  background: none;\n  border-radius: var(--radius-md);\n  color: var(--secondary-600);\n  cursor: pointer;\n  transition: all 0.2s ease;\n  position: relative;\n}\n.action-button:hover {\n  background-color: var(--secondary-100);\n  color: var(--secondary-800);\n}\n.action-button.has-notifications {\n  color: var(--primary-600);\n}\n.action-button .icon {\n  width: 20px;\n  height: 20px;\n}\n.notification-badge {\n  position: absolute;\n  top: 6px;\n  right: 6px;\n  min-width: 18px;\n  height: 18px;\n  padding: 0 4px;\n  background: var(--error-500);\n  color: white;\n  font-size: 10px;\n  font-weight: 600;\n  border-radius: 9px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  line-height: 1;\n}\n.user-profile {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border: none;\n  background: none;\n  border-radius: var(--radius-md);\n  cursor: pointer;\n  transition: background-color 0.2s ease;\n}\n.user-profile:hover {\n  background-color: var(--secondary-100);\n}\n.user-avatar {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  overflow: hidden;\n  background: var(--secondary-200);\n}\n.avatar-image {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n}\n.user-info {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n  min-width: 0;\n}\n.user-name {\n  font-size: var(--text-sm);\n  font-weight: 500;\n  color: var(--secondary-900);\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  max-width: 120px;\n}\n.user-role {\n  font-size: var(--text-xs);\n  color: var(--secondary-500);\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  max-width: 120px;\n}\n.chevron-icon {\n  width: 16px;\n  height: 16px;\n  color: var(--secondary-400);\n}\n.dropdown {\n  position: absolute;\n  top: calc(100% + 8px);\n  right: 0;\n  min-width: 320px;\n  background: white;\n  border: 1px solid var(--secondary-200);\n  border-radius: var(--radius-lg);\n  box-shadow: var(--shadow-lg);\n  z-index: var(--z-dropdown);\n  overflow: hidden;\n}\n.notifications-dropdown {\n  width: 380px;\n}\n.user-dropdown {\n  width: 240px;\n}\n.dropdown-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: var(--spacing-md) var(--spacing-lg);\n  border-bottom: 1px solid var(--secondary-200);\n  background: var(--neutral-50);\n}\n.dropdown-header h3 {\n  margin: 0;\n  font-size: var(--text-base);\n  font-weight: 600;\n  color: var(--secondary-900);\n}\n.mark-all-read {\n  font-size: var(--text-xs);\n  color: var(--primary-600);\n  background: none;\n  border: none;\n  cursor: pointer;\n  padding: var(--spacing-xs);\n  border-radius: var(--radius-sm);\n  transition: background-color 0.2s ease;\n}\n.mark-all-read:hover {\n  background-color: var(--primary-50);\n}\n.dropdown-content {\n  max-height: 400px;\n  overflow-y: auto;\n}\n.notification-item {\n  display: flex;\n  gap: var(--spacing-md);\n  padding: var(--spacing-md) var(--spacing-lg);\n  border-bottom: 1px solid var(--secondary-100);\n  transition: background-color 0.2s ease;\n}\n.notification-item:hover {\n  background-color: var(--neutral-50);\n}\n.notification-item:last-child {\n  border-bottom: none;\n}\n.notification-icon {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  flex-shrink: 0;\n}\n.notification-icon.notification-warning {\n  background: var(--warning-100);\n  color: var(--warning-600);\n}\n.notification-icon.notification-success {\n  background: var(--success-100);\n  color: var(--success-600);\n}\n.notification-icon.notification-error {\n  background: var(--error-100);\n  color: var(--error-600);\n}\n.notification-icon.notification-info {\n  background: var(--primary-100);\n  color: var(--primary-600);\n}\n.notification-icon .icon {\n  width: 16px;\n  height: 16px;\n}\n.notification-content {\n  flex: 1;\n  min-width: 0;\n}\n.notification-title {\n  margin: 0 0 var(--spacing-xs) 0;\n  font-size: var(--text-sm);\n  font-weight: 500;\n  color: var(--secondary-900);\n}\n.notification-message {\n  margin: 0 0 var(--spacing-xs) 0;\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n  line-height: var(--leading-relaxed);\n}\n.notification-time {\n  font-size: var(--text-xs);\n  color: var(--secondary-400);\n}\n.no-notifications {\n  padding: var(--spacing-xl);\n  text-align: center;\n  color: var(--secondary-500);\n}\n.dropdown-footer {\n  padding: var(--spacing-md) var(--spacing-lg);\n  border-top: 1px solid var(--secondary-200);\n  background: var(--neutral-50);\n}\n.view-all-link {\n  display: block;\n  text-align: center;\n  font-size: var(--text-sm);\n  color: var(--primary-600);\n  text-decoration: none;\n  font-weight: 500;\n}\n.view-all-link:hover {\n  color: var(--primary-700);\n  text-decoration: underline;\n}\n.dropdown-item {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-md);\n  width: 100%;\n  padding: var(--spacing-md) var(--spacing-lg);\n  border: none;\n  background: none;\n  text-align: left;\n  font-size: var(--text-sm);\n  color: var(--secondary-700);\n  text-decoration: none;\n  cursor: pointer;\n  transition: background-color 0.2s ease;\n}\n.dropdown-item:hover {\n  background-color: var(--neutral-50);\n  color: var(--secondary-900);\n  text-decoration: none;\n}\n.dropdown-item .icon {\n  width: 16px;\n  height: 16px;\n  color: var(--secondary-500);\n}\n.dropdown-divider {\n  height: 1px;\n  background: var(--secondary-200);\n  margin: var(--spacing-xs) 0;\n}\n.logout-item {\n  color: var(--error-600);\n}\n.logout-item:hover {\n  background-color: var(--error-50);\n  color: var(--error-700);\n}\n.logout-item .icon {\n  color: var(--error-500);\n}\n@media (max-width: 768px) {\n  .header-search {\n    display: none;\n  }\n  .user-info {\n    display: none;\n  }\n  .brand-subtitle {\n    display: none;\n  }\n  .dropdown {\n    right: var(--spacing-md);\n    left: var(--spacing-md);\n    width: auto;\n    min-width: auto;\n  }\n}\n/*# sourceMappingURL=header.component.css.map */\n"] }]
  }], () => [{ type: AuthService }], { sidebarOpen: [{
    type: Input
  }], sidebarToggle: [{
    type: Output
  }], searchPerformed: [{
    type: Output
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(HeaderComponent, { className: "HeaderComponent", filePath: "src/app/core/layout/header/header.component.ts", lineNumber: 193 });
})();

// src/app/core/layout/sidebar/sidebar.component.ts
var _c0 = (a0) => ({ exact: a0 });
function SidebarComponent_li_4_a_1_span_5_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 17);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const item_r2 = \u0275\u0275nextContext(2).$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(item_r2.badge);
  }
}
function SidebarComponent_li_4_a_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "a", 12);
    \u0275\u0275listener("click", function SidebarComponent_li_4_a_1_Template_a_click_0_listener() {
      \u0275\u0275restoreView(_r1);
      const item_r2 = \u0275\u0275nextContext().$implicit;
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.onNavItemClick(item_r2));
    });
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(1, "svg", 13);
    \u0275\u0275element(2, "path", 14);
    \u0275\u0275elementEnd();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(3, "span", 15);
    \u0275\u0275text(4);
    \u0275\u0275elementEnd();
    \u0275\u0275template(5, SidebarComponent_li_4_a_1_span_5_Template, 2, 1, "span", 16);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const item_r2 = \u0275\u0275nextContext().$implicit;
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275property("routerLink", item_r2.route)("routerLinkActiveOptions", \u0275\u0275pureFunction1(7, _c0, item_r2.route === "/"));
    \u0275\u0275advance(2);
    \u0275\u0275attribute("d", item_r2.icon);
    \u0275\u0275advance();
    \u0275\u0275classProp("sr-only", ctx_r2.isCollapsed);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(item_r2.label);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", item_r2.badge && !ctx_r2.isCollapsed);
  }
}
function SidebarComponent_li_4_div_2_span_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 17);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const item_r2 = \u0275\u0275nextContext(2).$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(item_r2.badge);
  }
}
function SidebarComponent_li_4_div_2__svg_svg_7_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(0, "svg", 22);
    \u0275\u0275element(1, "polyline", 23);
    \u0275\u0275elementEnd();
  }
}
function SidebarComponent_li_4_div_2_ul_8_li_1_span_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "span", 31);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const subItem_r6 = \u0275\u0275nextContext().$implicit;
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(subItem_r6.badge);
  }
}
function SidebarComponent_li_4_div_2_ul_8_li_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r5 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "li", 26)(1, "a", 27);
    \u0275\u0275listener("click", function SidebarComponent_li_4_div_2_ul_8_li_1_Template_a_click_1_listener() {
      const subItem_r6 = \u0275\u0275restoreView(_r5).$implicit;
      const ctx_r2 = \u0275\u0275nextContext(4);
      return \u0275\u0275resetView(ctx_r2.onNavItemClick(subItem_r6));
    });
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(2, "svg", 28);
    \u0275\u0275element(3, "path", 14);
    \u0275\u0275elementEnd();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(4, "span", 29);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd();
    \u0275\u0275template(6, SidebarComponent_li_4_div_2_ul_8_li_1_span_6_Template, 2, 1, "span", 30);
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const subItem_r6 = ctx.$implicit;
    \u0275\u0275advance();
    \u0275\u0275property("routerLink", subItem_r6.route);
    \u0275\u0275advance(2);
    \u0275\u0275attribute("d", subItem_r6.icon);
    \u0275\u0275advance(2);
    \u0275\u0275textInterpolate(subItem_r6.label);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", subItem_r6.badge);
  }
}
function SidebarComponent_li_4_div_2_ul_8_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "ul", 24);
    \u0275\u0275template(1, SidebarComponent_li_4_div_2_ul_8_li_1_Template, 7, 4, "li", 25);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const item_r2 = \u0275\u0275nextContext(2).$implicit;
    \u0275\u0275advance();
    \u0275\u0275property("ngForOf", item_r2.children);
  }
}
function SidebarComponent_li_4_div_2_Template(rf, ctx) {
  if (rf & 1) {
    const _r4 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 18)(1, "button", 19);
    \u0275\u0275listener("click", function SidebarComponent_li_4_div_2_Template_button_click_1_listener() {
      \u0275\u0275restoreView(_r4);
      const item_r2 = \u0275\u0275nextContext().$implicit;
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.toggleNavGroup(item_r2));
    });
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(2, "svg", 13);
    \u0275\u0275element(3, "path", 14);
    \u0275\u0275elementEnd();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(4, "span", 15);
    \u0275\u0275text(5);
    \u0275\u0275elementEnd();
    \u0275\u0275template(6, SidebarComponent_li_4_div_2_span_6_Template, 2, 1, "span", 16)(7, SidebarComponent_li_4_div_2__svg_svg_7_Template, 2, 0, "svg", 20);
    \u0275\u0275elementEnd();
    \u0275\u0275template(8, SidebarComponent_li_4_div_2_ul_8_Template, 2, 1, "ul", 21);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const item_r2 = \u0275\u0275nextContext().$implicit;
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275advance();
    \u0275\u0275classProp("nav-group-expanded", item_r2.isExpanded);
    \u0275\u0275advance(2);
    \u0275\u0275attribute("d", item_r2.icon);
    \u0275\u0275advance();
    \u0275\u0275classProp("sr-only", ctx_r2.isCollapsed);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(item_r2.label);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", item_r2.badge && !ctx_r2.isCollapsed);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !ctx_r2.isCollapsed);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", item_r2.isExpanded && !ctx_r2.isCollapsed);
  }
}
function SidebarComponent_li_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "li", 9);
    \u0275\u0275template(1, SidebarComponent_li_4_a_1_Template, 6, 9, "a", 10)(2, SidebarComponent_li_4_div_2_Template, 9, 9, "div", 11);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const item_r2 = ctx.$implicit;
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", !item_r2.children);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", item_r2.children);
  }
}
function SidebarComponent_div_5_Template(rf, ctx) {
  if (rf & 1) {
    const _r7 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 32)(1, "div", 33)(2, "div", 34)(3, "div", 35);
    \u0275\u0275element(4, "div", 36);
    \u0275\u0275elementStart(5, "span", 37);
    \u0275\u0275text(6);
    \u0275\u0275elementEnd()();
    \u0275\u0275elementStart(7, "div", 38)(8, "span", 39);
    \u0275\u0275text(9);
    \u0275\u0275elementEnd()()();
    \u0275\u0275elementStart(10, "div", 40)(11, "button", 41);
    \u0275\u0275listener("click", function SidebarComponent_div_5_Template_button_click_11_listener() {
      \u0275\u0275restoreView(_r7);
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.openHelp());
    });
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(12, "svg", 42);
    \u0275\u0275element(13, "circle", 43)(14, "path", 44)(15, "line", 45);
    \u0275\u0275elementEnd()();
    \u0275\u0275namespaceHTML();
    \u0275\u0275elementStart(16, "button", 46);
    \u0275\u0275listener("click", function SidebarComponent_div_5_Template_button_click_16_listener() {
      \u0275\u0275restoreView(_r7);
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.openFeedback());
    });
    \u0275\u0275namespaceSVG();
    \u0275\u0275elementStart(17, "svg", 42);
    \u0275\u0275element(18, "path", 47);
    \u0275\u0275elementEnd()()()()();
  }
  if (rf & 2) {
    const ctx_r2 = \u0275\u0275nextContext();
    \u0275\u0275advance(3);
    \u0275\u0275classMap("status-" + ctx_r2.systemStatus.status);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(ctx_r2.systemStatus.text);
    \u0275\u0275advance(3);
    \u0275\u0275textInterpolate(ctx_r2.systemStatus.details);
  }
}
function SidebarComponent_div_9_Template(rf, ctx) {
  if (rf & 1) {
    const _r8 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "div", 48);
    \u0275\u0275listener("click", function SidebarComponent_div_9_Template_div_click_0_listener() {
      \u0275\u0275restoreView(_r8);
      const ctx_r2 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r2.closeSidebar());
    });
    \u0275\u0275elementEnd();
  }
}
var SidebarComponent = class _SidebarComponent {
  router;
  isOpen = true;
  isMobile = false;
  sidebarClose = new EventEmitter();
  isCollapsed = false;
  systemStatus = {
    status: "healthy",
    text: "All Systems Operational",
    details: "Last updated: 2 minutes ago"
  };
  navigationItems = [
    {
      id: "dashboard",
      label: "Dashboard",
      icon: "M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6",
      route: "/dashboard"
    },
    {
      id: "applications",
      label: "Applications",
      icon: "M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10",
      route: "/applications",
      badge: "127"
    },
    {
      id: "dependencies",
      label: "Dependencies",
      icon: "M8 16l2.879-2.879m0 0a3 3 0 104.243-4.242 3 3 0 00-4.243 4.242zM21 12a9 9 0 11-18 0 9 9 0 0118 0z",
      children: [
        {
          id: "dependencies-overview",
          label: "Overview",
          icon: "M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z",
          route: "/dependencies"
        },
        {
          id: "dependencies-health",
          label: "Health Check",
          icon: "M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z",
          route: "/dependencies/health",
          badge: "12"
        }
      ]
    },
    {
      id: "tech-stack",
      label: "Tech Stack",
      icon: "M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z",
      route: "/tech-stack"
    },
    {
      id: "stakeholders",
      label: "Stakeholders",
      icon: "M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a4 4 0 11-8 0 4 4 0 018 0z",
      route: "/stakeholders"
    },
    {
      id: "documentation",
      label: "Documentation",
      icon: "M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z",
      route: "/documentation"
    },
    {
      id: "security",
      label: "Security",
      icon: "M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z",
      children: [
        {
          id: "security-overview",
          label: "Overview",
          icon: "M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z",
          route: "/security"
        },
        {
          id: "vulnerabilities",
          label: "Vulnerabilities",
          icon: "M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z",
          route: "/security/vulnerabilities",
          badge: "8"
        },
        {
          id: "assessments",
          label: "Assessments",
          icon: "M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4",
          route: "/security/assessments"
        }
      ]
    },
    {
      id: "reports",
      label: "Reports",
      icon: "M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z",
      route: "/reports"
    },
    {
      id: "settings",
      label: "Settings",
      icon: "M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z M15 12a3 3 0 11-6 0 3 3 0 016 0z",
      route: "/settings"
    }
  ];
  constructor(router) {
    this.router = router;
  }
  toggleNavGroup(item) {
    if (this.isCollapsed) {
      this.isCollapsed = false;
    }
    item.isExpanded = !item.isExpanded;
  }
  onNavItemClick(item) {
    if (this.isMobile) {
      this.closeSidebar();
    }
  }
  toggleCollapse() {
    this.isCollapsed = !this.isCollapsed;
    if (this.isCollapsed) {
      this.navigationItems.forEach((item) => {
        if (item.children) {
          item.isExpanded = false;
        }
      });
    }
  }
  closeSidebar() {
    this.sidebarClose.emit();
  }
  openHelp() {
    console.log("Opening help...");
  }
  openFeedback() {
    console.log("Opening feedback...");
  }
  static \u0275fac = function SidebarComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _SidebarComponent)(\u0275\u0275directiveInject(Router));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _SidebarComponent, selectors: [["app-sidebar"]], inputs: { isOpen: "isOpen", isMobile: "isMobile" }, outputs: { sidebarClose: "sidebarClose" }, decls: 10, vars: 9, consts: [[1, "sidebar"], [1, "sidebar-content"], [1, "sidebar-nav"], [1, "nav-list"], ["class", "nav-item", 4, "ngFor", "ngForOf"], ["class", "sidebar-footer", 4, "ngIf"], [1, "sidebar-toggle", 3, "click", "title"], ["viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", 1, "toggle-icon"], ["class", "sidebar-overlay", 3, "click", 4, "ngIf"], [1, "nav-item"], ["class", "nav-link", "routerLinkActive", "nav-link-active", 3, "routerLink", "routerLinkActiveOptions", "click", 4, "ngIf"], ["class", "nav-group", 4, "ngIf"], ["routerLinkActive", "nav-link-active", 1, "nav-link", 3, "click", "routerLink", "routerLinkActiveOptions"], ["viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", 1, "nav-icon"], ["stroke-linecap", "round", "stroke-linejoin", "round", "stroke-width", "2"], [1, "nav-label"], ["class", "nav-badge", 4, "ngIf"], [1, "nav-badge"], [1, "nav-group"], [1, "nav-link", "nav-group-toggle", 3, "click"], ["class", "nav-chevron", "viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", 4, "ngIf"], ["class", "nav-submenu", 4, "ngIf"], ["viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", 1, "nav-chevron"], ["points", "9,18 15,12 9,6"], [1, "nav-submenu"], ["class", "nav-subitem", 4, "ngFor", "ngForOf"], [1, "nav-subitem"], ["routerLinkActive", "nav-sublink-active", 1, "nav-sublink", 3, "click", "routerLink"], ["viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", 1, "nav-subicon"], [1, "nav-sublabel"], ["class", "nav-subbadge", 4, "ngIf"], [1, "nav-subbadge"], [1, "sidebar-footer"], [1, "footer-content"], [1, "system-status"], [1, "status-indicator"], [1, "status-dot"], [1, "status-text"], [1, "status-details"], [1, "status-detail"], [1, "footer-actions"], ["title", "Help & Documentation", 1, "footer-action", 3, "click"], ["viewBox", "0 0 24 24", "fill", "none", "stroke", "currentColor", 1, "action-icon"], ["cx", "12", "cy", "12", "r", "10"], ["d", "M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"], ["x1", "12", "y1", "17", "x2", "12.01", "y2", "17"], ["title", "Send Feedback", 1, "footer-action", 3, "click"], ["d", "M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"], [1, "sidebar-overlay", 3, "click"]], template: function SidebarComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "aside", 0)(1, "div", 1)(2, "nav", 2)(3, "ul", 3);
      \u0275\u0275template(4, SidebarComponent_li_4_Template, 3, 2, "li", 4);
      \u0275\u0275elementEnd()();
      \u0275\u0275template(5, SidebarComponent_div_5_Template, 19, 4, "div", 5);
      \u0275\u0275elementStart(6, "button", 6);
      \u0275\u0275listener("click", function SidebarComponent_Template_button_click_6_listener() {
        return ctx.toggleCollapse();
      });
      \u0275\u0275namespaceSVG();
      \u0275\u0275elementStart(7, "svg", 7);
      \u0275\u0275element(8, "polyline");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275template(9, SidebarComponent_div_9_Template, 1, 0, "div", 8);
    }
    if (rf & 2) {
      \u0275\u0275classProp("sidebar-open", ctx.isOpen)("sidebar-collapsed", ctx.isCollapsed);
      \u0275\u0275advance(4);
      \u0275\u0275property("ngForOf", ctx.navigationItems);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", !ctx.isCollapsed);
      \u0275\u0275advance();
      \u0275\u0275property("title", ctx.isCollapsed ? "Expand sidebar" : "Collapse sidebar");
      \u0275\u0275advance(2);
      \u0275\u0275attribute("points", ctx.isCollapsed ? "9,18 15,12 9,6" : "15,18 9,12 15,6");
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.isOpen && ctx.isMobile);
    }
  }, dependencies: [CommonModule, NgForOf, NgIf, RouterModule, RouterLink, RouterLinkActive], styles: ["\n\n.sidebar[_ngcontent-%COMP%] {\n  position: fixed;\n  top: 64px;\n  left: 0;\n  bottom: 0;\n  width: 280px;\n  background: white;\n  border-right: 1px solid var(--secondary-200);\n  box-shadow: var(--shadow-sm);\n  z-index: var(--z-sticky);\n  transform: translateX(-100%);\n  transition: all 0.3s ease;\n}\n.sidebar.sidebar-open[_ngcontent-%COMP%] {\n  transform: translateX(0);\n}\n.sidebar.sidebar-collapsed[_ngcontent-%COMP%] {\n  width: 64px;\n}\n.sidebar-content[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  position: relative;\n}\n.sidebar-nav[_ngcontent-%COMP%] {\n  flex: 1;\n  padding: var(--spacing-lg) 0;\n  overflow-y: auto;\n  overflow-x: hidden;\n}\n.nav-list[_ngcontent-%COMP%] {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n.nav-item[_ngcontent-%COMP%] {\n  margin-bottom: var(--spacing-xs);\n}\n.nav-link[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-md);\n  padding: var(--spacing-md) var(--spacing-lg);\n  color: var(--secondary-700);\n  text-decoration: none;\n  font-size: var(--text-sm);\n  font-weight: 500;\n  border: none;\n  background: none;\n  width: 100%;\n  text-align: left;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  position: relative;\n}\n.nav-link[_ngcontent-%COMP%]:hover {\n  background-color: var(--secondary-50);\n  color: var(--secondary-900);\n}\n.nav-link.nav-link-active[_ngcontent-%COMP%] {\n  background-color: var(--primary-50);\n  color: var(--primary-700);\n  border-right: 3px solid var(--primary-500);\n}\n.nav-link.nav-link-active[_ngcontent-%COMP%]   .nav-icon[_ngcontent-%COMP%] {\n  color: var(--primary-600);\n}\n.nav-icon[_ngcontent-%COMP%] {\n  width: 20px;\n  height: 20px;\n  flex-shrink: 0;\n  color: var(--secondary-500);\n  transition: color 0.2s ease;\n}\n.nav-label[_ngcontent-%COMP%] {\n  flex: 1;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  transition: opacity 0.2s ease;\n}\n.nav-badge[_ngcontent-%COMP%] {\n  background: var(--primary-100);\n  color: var(--primary-700);\n  font-size: var(--text-xs);\n  font-weight: 600;\n  padding: 2px 6px;\n  border-radius: 10px;\n  min-width: 18px;\n  text-align: center;\n  line-height: 1.2;\n}\n.nav-chevron[_ngcontent-%COMP%] {\n  width: 16px;\n  height: 16px;\n  color: var(--secondary-400);\n  transition: transform 0.2s ease;\n}\n.nav-group-toggle.nav-group-expanded[_ngcontent-%COMP%]   .nav-chevron[_ngcontent-%COMP%] {\n  transform: rotate(90deg);\n}\n.nav-submenu[_ngcontent-%COMP%] {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n  background: var(--neutral-50);\n  border-top: 1px solid var(--secondary-100);\n  border-bottom: 1px solid var(--secondary-100);\n}\n.nav-subitem[_ngcontent-%COMP%] {\n  margin: 0;\n}\n.nav-sublink[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-md);\n  padding: var(--spacing-sm) var(--spacing-lg) var(--spacing-sm) 52px;\n  color: var(--secondary-600);\n  text-decoration: none;\n  font-size: var(--text-sm);\n  font-weight: 400;\n  transition: all 0.2s ease;\n}\n.nav-sublink[_ngcontent-%COMP%]:hover {\n  background-color: var(--secondary-100);\n  color: var(--secondary-800);\n}\n.nav-sublink.nav-sublink-active[_ngcontent-%COMP%] {\n  background-color: var(--primary-50);\n  color: var(--primary-700);\n  border-right: 3px solid var(--primary-500);\n}\n.nav-sublink.nav-sublink-active[_ngcontent-%COMP%]   .nav-subicon[_ngcontent-%COMP%] {\n  color: var(--primary-600);\n}\n.nav-subicon[_ngcontent-%COMP%] {\n  width: 16px;\n  height: 16px;\n  flex-shrink: 0;\n  color: var(--secondary-400);\n  transition: color 0.2s ease;\n}\n.nav-sublabel[_ngcontent-%COMP%] {\n  flex: 1;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n.nav-subbadge[_ngcontent-%COMP%] {\n  background: var(--warning-100);\n  color: var(--warning-700);\n  font-size: var(--text-xs);\n  font-weight: 600;\n  padding: 1px 5px;\n  border-radius: 8px;\n  min-width: 16px;\n  text-align: center;\n  line-height: 1.2;\n}\n.sidebar-footer[_ngcontent-%COMP%] {\n  padding: var(--spacing-lg);\n  border-top: 1px solid var(--secondary-200);\n  background: var(--neutral-50);\n}\n.footer-content[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-md);\n}\n.system-status[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-xs);\n}\n.status-indicator[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n}\n.status-dot[_ngcontent-%COMP%] {\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  flex-shrink: 0;\n}\n.status-text[_ngcontent-%COMP%] {\n  font-size: var(--text-sm);\n  font-weight: 500;\n  color: var(--secondary-800);\n}\n.status-details[_ngcontent-%COMP%] {\n  margin-left: 20px;\n}\n.status-detail[_ngcontent-%COMP%] {\n  font-size: var(--text-xs);\n  color: var(--secondary-500);\n}\n.status-healthy[_ngcontent-%COMP%]   .status-dot[_ngcontent-%COMP%] {\n  background: var(--success-500);\n}\n.status-warning[_ngcontent-%COMP%]   .status-dot[_ngcontent-%COMP%] {\n  background: var(--warning-500);\n}\n.status-critical[_ngcontent-%COMP%]   .status-dot[_ngcontent-%COMP%] {\n  background: var(--error-500);\n}\n.footer-actions[_ngcontent-%COMP%] {\n  display: flex;\n  gap: var(--spacing-sm);\n}\n.footer-action[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 32px;\n  height: 32px;\n  border: none;\n  background: none;\n  border-radius: var(--radius-md);\n  color: var(--secondary-500);\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n.footer-action[_ngcontent-%COMP%]:hover {\n  background-color: var(--secondary-100);\n  color: var(--secondary-700);\n}\n.action-icon[_ngcontent-%COMP%] {\n  width: 16px;\n  height: 16px;\n}\n.sidebar-toggle[_ngcontent-%COMP%] {\n  position: absolute;\n  top: 50%;\n  right: -12px;\n  transform: translateY(-50%);\n  width: 24px;\n  height: 24px;\n  border: 1px solid var(--secondary-200);\n  background: white;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  box-shadow: var(--shadow-sm);\n  transition: all 0.2s ease;\n  z-index: 1;\n}\n.sidebar-toggle[_ngcontent-%COMP%]:hover {\n  background-color: var(--secondary-50);\n  border-color: var(--secondary-300);\n}\n.toggle-icon[_ngcontent-%COMP%] {\n  width: 12px;\n  height: 12px;\n  color: var(--secondary-600);\n  transition: transform 0.2s ease;\n}\n.sidebar-collapsed[_ngcontent-%COMP%]   .nav-label[_ngcontent-%COMP%], \n.sidebar-collapsed[_ngcontent-%COMP%]   .nav-badge[_ngcontent-%COMP%], \n.sidebar-collapsed[_ngcontent-%COMP%]   .nav-chevron[_ngcontent-%COMP%], \n.sidebar-collapsed[_ngcontent-%COMP%]   .nav-submenu[_ngcontent-%COMP%], \n.sidebar-collapsed[_ngcontent-%COMP%]   .sidebar-footer[_ngcontent-%COMP%] {\n  opacity: 0;\n  pointer-events: none;\n}\n.sidebar-collapsed[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\n  justify-content: center;\n  padding: var(--spacing-md);\n}\n.sidebar-collapsed[_ngcontent-%COMP%]   .nav-icon[_ngcontent-%COMP%] {\n  margin: 0;\n}\n.sidebar-collapsed[_ngcontent-%COMP%]   .sidebar-toggle[_ngcontent-%COMP%]   .toggle-icon[_ngcontent-%COMP%] {\n  transform: rotate(180deg);\n}\n.sidebar-overlay[_ngcontent-%COMP%] {\n  position: fixed;\n  top: 64px;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  z-index: var(--z-modal-backdrop);\n  -webkit-backdrop-filter: blur(2px);\n  backdrop-filter: blur(2px);\n}\n.sr-only[_ngcontent-%COMP%] {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border: 0;\n}\n@media (min-width: 768px) {\n  .sidebar[_ngcontent-%COMP%] {\n    transform: translateX(0);\n  }\n  .sidebar-overlay[_ngcontent-%COMP%] {\n    display: none;\n  }\n}\n@media (max-width: 767px) {\n  .sidebar[_ngcontent-%COMP%] {\n    width: 280px;\n    z-index: var(--z-modal);\n  }\n  .sidebar-collapsed[_ngcontent-%COMP%] {\n    width: 280px;\n  }\n  .sidebar-collapsed[_ngcontent-%COMP%]   .nav-label[_ngcontent-%COMP%], \n   .sidebar-collapsed[_ngcontent-%COMP%]   .nav-badge[_ngcontent-%COMP%], \n   .sidebar-collapsed[_ngcontent-%COMP%]   .nav-chevron[_ngcontent-%COMP%], \n   .sidebar-collapsed[_ngcontent-%COMP%]   .nav-submenu[_ngcontent-%COMP%], \n   .sidebar-collapsed[_ngcontent-%COMP%]   .sidebar-footer[_ngcontent-%COMP%] {\n    opacity: 1;\n    pointer-events: auto;\n  }\n  .sidebar-collapsed[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\n    justify-content: flex-start;\n    padding: var(--spacing-md) var(--spacing-lg);\n  }\n  .sidebar-collapsed[_ngcontent-%COMP%]   .sidebar-toggle[_ngcontent-%COMP%] {\n    display: none;\n  }\n}\n.sidebar-nav[_ngcontent-%COMP%]::-webkit-scrollbar {\n  width: 4px;\n}\n.sidebar-nav[_ngcontent-%COMP%]::-webkit-scrollbar-track {\n  background: transparent;\n}\n.sidebar-nav[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\n  background: var(--secondary-300);\n  border-radius: 2px;\n}\n.sidebar-nav[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\n  background: var(--secondary-400);\n}\n.nav-submenu[_ngcontent-%COMP%] {\n  animation: _ngcontent-%COMP%_slideDown 0.2s ease-out;\n}\n@keyframes _ngcontent-%COMP%_slideDown {\n  from {\n    opacity: 0;\n    max-height: 0;\n  }\n  to {\n    opacity: 1;\n    max-height: 200px;\n  }\n}\n/*# sourceMappingURL=sidebar.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(SidebarComponent, [{
    type: Component,
    args: [{ selector: "app-sidebar", standalone: true, imports: [CommonModule, RouterModule], template: `
    <aside class="sidebar" [class.sidebar-open]="isOpen" [class.sidebar-collapsed]="isCollapsed">
      <div class="sidebar-content">
        <!-- Navigation Menu -->
        <nav class="sidebar-nav">
          <ul class="nav-list">
            <li class="nav-item" *ngFor="let item of navigationItems">
              <!-- Single Level Item -->
              <a 
                *ngIf="!item.children" 
                [routerLink]="item.route"
                class="nav-link"
                routerLinkActive="nav-link-active"
                [routerLinkActiveOptions]="{ exact: item.route === '/' }"
                (click)="onNavItemClick(item)"
              >
                <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" [attr.d]="item.icon" />
                </svg>
                <span class="nav-label" [class.sr-only]="isCollapsed">{{ item.label }}</span>
                <span class="nav-badge" *ngIf="item.badge && !isCollapsed">{{ item.badge }}</span>
              </a>

              <!-- Multi Level Item -->
              <div *ngIf="item.children" class="nav-group">
                <button 
                  class="nav-link nav-group-toggle"
                  [class.nav-group-expanded]="item.isExpanded"
                  (click)="toggleNavGroup(item)"
                >
                  <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" [attr.d]="item.icon" />
                  </svg>
                  <span class="nav-label" [class.sr-only]="isCollapsed">{{ item.label }}</span>
                  <span class="nav-badge" *ngIf="item.badge && !isCollapsed">{{ item.badge }}</span>
                  <svg class="nav-chevron" *ngIf="!isCollapsed" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <polyline points="9,18 15,12 9,6"></polyline>
                  </svg>
                </button>

                <!-- Submenu -->
                <ul class="nav-submenu" *ngIf="item.isExpanded && !isCollapsed">
                  <li class="nav-subitem" *ngFor="let subItem of item.children">
                    <a 
                      [routerLink]="subItem.route"
                      class="nav-sublink"
                      routerLinkActive="nav-sublink-active"
                      (click)="onNavItemClick(subItem)"
                    >
                      <svg class="nav-subicon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" [attr.d]="subItem.icon" />
                      </svg>
                      <span class="nav-sublabel">{{ subItem.label }}</span>
                      <span class="nav-subbadge" *ngIf="subItem.badge">{{ subItem.badge }}</span>
                    </a>
                  </li>
                </ul>
              </div>
            </li>
          </ul>
        </nav>

        <!-- Sidebar Footer -->
        <div class="sidebar-footer" *ngIf="!isCollapsed">
          <div class="footer-content">
            <div class="system-status">
              <div class="status-indicator" [class]="'status-' + systemStatus.status">
                <div class="status-dot"></div>
                <span class="status-text">{{ systemStatus.text }}</span>
              </div>
              <div class="status-details">
                <span class="status-detail">{{ systemStatus.details }}</span>
              </div>
            </div>
            
            <div class="footer-actions">
              <button class="footer-action" (click)="openHelp()" title="Help & Documentation">
                <svg class="action-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <circle cx="12" cy="12" r="10"></circle>
                  <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path>
                  <line x1="12" y1="17" x2="12.01" y2="17"></line>
                </svg>
              </button>
              <button class="footer-action" (click)="openFeedback()" title="Send Feedback">
                <svg class="action-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- Collapse Toggle -->
        <button 
          class="sidebar-toggle"
          (click)="toggleCollapse()"
          [title]="isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'"
        >
          <svg class="toggle-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <polyline [attr.points]="isCollapsed ? '9,18 15,12 9,6' : '15,18 9,12 15,6'"></polyline>
          </svg>
        </button>
      </div>
    </aside>

    <!-- Sidebar Overlay (Mobile) -->
    <div 
      class="sidebar-overlay" 
      *ngIf="isOpen && isMobile"
      (click)="closeSidebar()"
    ></div>
  `, styles: ["/* src/app/core/layout/sidebar/sidebar.component.scss */\n.sidebar {\n  position: fixed;\n  top: 64px;\n  left: 0;\n  bottom: 0;\n  width: 280px;\n  background: white;\n  border-right: 1px solid var(--secondary-200);\n  box-shadow: var(--shadow-sm);\n  z-index: var(--z-sticky);\n  transform: translateX(-100%);\n  transition: all 0.3s ease;\n}\n.sidebar.sidebar-open {\n  transform: translateX(0);\n}\n.sidebar.sidebar-collapsed {\n  width: 64px;\n}\n.sidebar-content {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  position: relative;\n}\n.sidebar-nav {\n  flex: 1;\n  padding: var(--spacing-lg) 0;\n  overflow-y: auto;\n  overflow-x: hidden;\n}\n.nav-list {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n.nav-item {\n  margin-bottom: var(--spacing-xs);\n}\n.nav-link {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-md);\n  padding: var(--spacing-md) var(--spacing-lg);\n  color: var(--secondary-700);\n  text-decoration: none;\n  font-size: var(--text-sm);\n  font-weight: 500;\n  border: none;\n  background: none;\n  width: 100%;\n  text-align: left;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  position: relative;\n}\n.nav-link:hover {\n  background-color: var(--secondary-50);\n  color: var(--secondary-900);\n}\n.nav-link.nav-link-active {\n  background-color: var(--primary-50);\n  color: var(--primary-700);\n  border-right: 3px solid var(--primary-500);\n}\n.nav-link.nav-link-active .nav-icon {\n  color: var(--primary-600);\n}\n.nav-icon {\n  width: 20px;\n  height: 20px;\n  flex-shrink: 0;\n  color: var(--secondary-500);\n  transition: color 0.2s ease;\n}\n.nav-label {\n  flex: 1;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  transition: opacity 0.2s ease;\n}\n.nav-badge {\n  background: var(--primary-100);\n  color: var(--primary-700);\n  font-size: var(--text-xs);\n  font-weight: 600;\n  padding: 2px 6px;\n  border-radius: 10px;\n  min-width: 18px;\n  text-align: center;\n  line-height: 1.2;\n}\n.nav-chevron {\n  width: 16px;\n  height: 16px;\n  color: var(--secondary-400);\n  transition: transform 0.2s ease;\n}\n.nav-group-toggle.nav-group-expanded .nav-chevron {\n  transform: rotate(90deg);\n}\n.nav-submenu {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n  background: var(--neutral-50);\n  border-top: 1px solid var(--secondary-100);\n  border-bottom: 1px solid var(--secondary-100);\n}\n.nav-subitem {\n  margin: 0;\n}\n.nav-sublink {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-md);\n  padding: var(--spacing-sm) var(--spacing-lg) var(--spacing-sm) 52px;\n  color: var(--secondary-600);\n  text-decoration: none;\n  font-size: var(--text-sm);\n  font-weight: 400;\n  transition: all 0.2s ease;\n}\n.nav-sublink:hover {\n  background-color: var(--secondary-100);\n  color: var(--secondary-800);\n}\n.nav-sublink.nav-sublink-active {\n  background-color: var(--primary-50);\n  color: var(--primary-700);\n  border-right: 3px solid var(--primary-500);\n}\n.nav-sublink.nav-sublink-active .nav-subicon {\n  color: var(--primary-600);\n}\n.nav-subicon {\n  width: 16px;\n  height: 16px;\n  flex-shrink: 0;\n  color: var(--secondary-400);\n  transition: color 0.2s ease;\n}\n.nav-sublabel {\n  flex: 1;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n.nav-subbadge {\n  background: var(--warning-100);\n  color: var(--warning-700);\n  font-size: var(--text-xs);\n  font-weight: 600;\n  padding: 1px 5px;\n  border-radius: 8px;\n  min-width: 16px;\n  text-align: center;\n  line-height: 1.2;\n}\n.sidebar-footer {\n  padding: var(--spacing-lg);\n  border-top: 1px solid var(--secondary-200);\n  background: var(--neutral-50);\n}\n.footer-content {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-md);\n}\n.system-status {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-xs);\n}\n.status-indicator {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n}\n.status-dot {\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  flex-shrink: 0;\n}\n.status-text {\n  font-size: var(--text-sm);\n  font-weight: 500;\n  color: var(--secondary-800);\n}\n.status-details {\n  margin-left: 20px;\n}\n.status-detail {\n  font-size: var(--text-xs);\n  color: var(--secondary-500);\n}\n.status-healthy .status-dot {\n  background: var(--success-500);\n}\n.status-warning .status-dot {\n  background: var(--warning-500);\n}\n.status-critical .status-dot {\n  background: var(--error-500);\n}\n.footer-actions {\n  display: flex;\n  gap: var(--spacing-sm);\n}\n.footer-action {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  width: 32px;\n  height: 32px;\n  border: none;\n  background: none;\n  border-radius: var(--radius-md);\n  color: var(--secondary-500);\n  cursor: pointer;\n  transition: all 0.2s ease;\n}\n.footer-action:hover {\n  background-color: var(--secondary-100);\n  color: var(--secondary-700);\n}\n.action-icon {\n  width: 16px;\n  height: 16px;\n}\n.sidebar-toggle {\n  position: absolute;\n  top: 50%;\n  right: -12px;\n  transform: translateY(-50%);\n  width: 24px;\n  height: 24px;\n  border: 1px solid var(--secondary-200);\n  background: white;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  box-shadow: var(--shadow-sm);\n  transition: all 0.2s ease;\n  z-index: 1;\n}\n.sidebar-toggle:hover {\n  background-color: var(--secondary-50);\n  border-color: var(--secondary-300);\n}\n.toggle-icon {\n  width: 12px;\n  height: 12px;\n  color: var(--secondary-600);\n  transition: transform 0.2s ease;\n}\n.sidebar-collapsed .nav-label,\n.sidebar-collapsed .nav-badge,\n.sidebar-collapsed .nav-chevron,\n.sidebar-collapsed .nav-submenu,\n.sidebar-collapsed .sidebar-footer {\n  opacity: 0;\n  pointer-events: none;\n}\n.sidebar-collapsed .nav-link {\n  justify-content: center;\n  padding: var(--spacing-md);\n}\n.sidebar-collapsed .nav-icon {\n  margin: 0;\n}\n.sidebar-collapsed .sidebar-toggle .toggle-icon {\n  transform: rotate(180deg);\n}\n.sidebar-overlay {\n  position: fixed;\n  top: 64px;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  z-index: var(--z-modal-backdrop);\n  -webkit-backdrop-filter: blur(2px);\n  backdrop-filter: blur(2px);\n}\n.sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border: 0;\n}\n@media (min-width: 768px) {\n  .sidebar {\n    transform: translateX(0);\n  }\n  .sidebar-overlay {\n    display: none;\n  }\n}\n@media (max-width: 767px) {\n  .sidebar {\n    width: 280px;\n    z-index: var(--z-modal);\n  }\n  .sidebar-collapsed {\n    width: 280px;\n  }\n  .sidebar-collapsed .nav-label,\n  .sidebar-collapsed .nav-badge,\n  .sidebar-collapsed .nav-chevron,\n  .sidebar-collapsed .nav-submenu,\n  .sidebar-collapsed .sidebar-footer {\n    opacity: 1;\n    pointer-events: auto;\n  }\n  .sidebar-collapsed .nav-link {\n    justify-content: flex-start;\n    padding: var(--spacing-md) var(--spacing-lg);\n  }\n  .sidebar-collapsed .sidebar-toggle {\n    display: none;\n  }\n}\n.sidebar-nav::-webkit-scrollbar {\n  width: 4px;\n}\n.sidebar-nav::-webkit-scrollbar-track {\n  background: transparent;\n}\n.sidebar-nav::-webkit-scrollbar-thumb {\n  background: var(--secondary-300);\n  border-radius: 2px;\n}\n.sidebar-nav::-webkit-scrollbar-thumb:hover {\n  background: var(--secondary-400);\n}\n.nav-submenu {\n  animation: slideDown 0.2s ease-out;\n}\n@keyframes slideDown {\n  from {\n    opacity: 0;\n    max-height: 0;\n  }\n  to {\n    opacity: 1;\n    max-height: 200px;\n  }\n}\n/*# sourceMappingURL=sidebar.component.css.map */\n"] }]
  }], () => [{ type: Router }], { isOpen: [{
    type: Input
  }], isMobile: [{
    type: Input
  }], sidebarClose: [{
    type: Output
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(SidebarComponent, { className: "SidebarComponent", filePath: "src/app/core/layout/sidebar/sidebar.component.ts", lineNumber: 133 });
})();

// src/app/core/layout/main-layout/main-layout.component.ts
function MainLayoutComponent_Conditional_1_Template(rf, ctx) {
  if (rf & 1) {
    const _r1 = \u0275\u0275getCurrentView();
    \u0275\u0275elementStart(0, "app-header", 3);
    \u0275\u0275listener("sidebarToggle", function MainLayoutComponent_Conditional_1_Template_app_header_sidebarToggle_0_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.toggleSidebar());
    })("searchPerformed", function MainLayoutComponent_Conditional_1_Template_app_header_searchPerformed_0_listener($event) {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.onSearch($event));
    });
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(1, "app-sidebar", 4);
    \u0275\u0275listener("sidebarClose", function MainLayoutComponent_Conditional_1_Template_app_sidebar_sidebarClose_1_listener() {
      \u0275\u0275restoreView(_r1);
      const ctx_r1 = \u0275\u0275nextContext();
      return \u0275\u0275resetView(ctx_r1.closeSidebar());
    });
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(2, "main", 5)(3, "div", 6);
    \u0275\u0275element(4, "router-outlet");
    \u0275\u0275elementEnd()();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275property("sidebarOpen", ctx_r1.sidebarOpen);
    \u0275\u0275advance();
    \u0275\u0275property("isOpen", ctx_r1.sidebarOpen)("isMobile", ctx_r1.isMobile);
    \u0275\u0275advance();
    \u0275\u0275classProp("sidebar-open", ctx_r1.sidebarOpen && !ctx_r1.isMobile);
  }
}
function MainLayoutComponent_Conditional_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 1)(1, "div", 7);
    \u0275\u0275element(2, "div", 8);
    \u0275\u0275elementStart(3, "p", 9);
    \u0275\u0275text(4, "Loading...");
    \u0275\u0275elementEnd()()();
  }
}
function MainLayoutComponent_Conditional_3_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "main", 2);
    \u0275\u0275element(1, "router-outlet");
    \u0275\u0275elementEnd();
  }
}
function MainLayoutComponent_Conditional_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 1)(1, "div", 7);
    \u0275\u0275element(2, "div", 8);
    \u0275\u0275elementStart(3, "p", 9);
    \u0275\u0275text(4, "Loading...");
    \u0275\u0275elementEnd()()();
  }
}
var MainLayoutComponent = class _MainLayoutComponent {
  platformId;
  authService;
  router;
  sidebarOpen = true;
  isMobile = false;
  isLoading = false;
  isAuthenticated = false;
  isInitialAuthCheck = true;
  destroy$ = new Subject();
  constructor(platformId, authService, router) {
    this.platformId = platformId;
    this.authService = authService;
    this.router = router;
    this.isAuthenticated = this.authService.isAuthenticated();
    this.authService.currentUser$.pipe(takeUntil(this.destroy$), tap(() => this.isInitialAuthCheck = false)).subscribe((user) => {
      this.isAuthenticated = !!user;
      if (!user && !this.isInitialAuthCheck) {
        this.router.navigate(["/auth/login"]);
      }
    });
    this.authService.isLoading$.pipe(takeUntil(this.destroy$)).subscribe((loading) => {
      this.isLoading = loading;
    });
  }
  ngOnInit() {
    this.checkScreenSize();
    this.initializeSidebar();
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
  onResize(event) {
    this.checkScreenSize();
  }
  checkScreenSize() {
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }
    const previousIsMobile = this.isMobile;
    this.isMobile = window.innerWidth < 768;
    if (previousIsMobile && !this.isMobile && this.isAuthenticated) {
      this.sidebarOpen = true;
    } else if (!previousIsMobile && this.isMobile) {
      this.sidebarOpen = false;
    }
  }
  initializeSidebar() {
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }
    if (this.isMobile) {
      this.sidebarOpen = false;
      return;
    }
    const savedSidebarState = localStorage.getItem("sidebarOpen");
    if (savedSidebarState !== null) {
      this.sidebarOpen = JSON.parse(savedSidebarState);
    }
  }
  toggleSidebar() {
    this.sidebarOpen = !this.sidebarOpen;
    this.saveSidebarState();
  }
  closeSidebar() {
    this.sidebarOpen = false;
    this.saveSidebarState();
  }
  saveSidebarState() {
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }
    if (!this.isMobile) {
      localStorage.setItem("sidebarOpen", JSON.stringify(this.sidebarOpen));
    }
  }
  onSearch(searchTerm) {
    console.log("Global search:", searchTerm);
  }
  setLoading(loading) {
    this.isLoading = loading;
  }
  static \u0275fac = function MainLayoutComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _MainLayoutComponent)(\u0275\u0275directiveInject(PLATFORM_ID), \u0275\u0275directiveInject(AuthService), \u0275\u0275directiveInject(Router));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _MainLayoutComponent, selectors: [["app-main-layout"]], hostBindings: function MainLayoutComponent_HostBindings(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275listener("resize", function MainLayoutComponent_resize_HostBindingHandler($event) {
        return ctx.onResize($event);
      }, false, \u0275\u0275resolveWindow);
    }
  }, decls: 5, vars: 2, consts: [[1, "layout-container"], [1, "loading-overlay"], [1, "auth-content"], [3, "sidebarToggle", "searchPerformed", "sidebarOpen"], [3, "sidebarClose", "isOpen", "isMobile"], [1, "main-content"], [1, "content-wrapper"], [1, "loading-spinner"], [1, "spinner"], [1, "loading-text"]], template: function MainLayoutComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0);
      \u0275\u0275template(1, MainLayoutComponent_Conditional_1_Template, 5, 5)(2, MainLayoutComponent_Conditional_2_Template, 5, 0, "div", 1)(3, MainLayoutComponent_Conditional_3_Template, 2, 0, "main", 2)(4, MainLayoutComponent_Conditional_4_Template, 5, 0, "div", 1);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275advance();
      \u0275\u0275conditional(ctx.isAuthenticated ? 1 : ctx.isInitialAuthCheck ? 2 : 3);
      \u0275\u0275advance(3);
      \u0275\u0275conditional(ctx.isLoading ? 4 : -1);
    }
  }, dependencies: [CommonModule, RouterOutlet, HeaderComponent, SidebarComponent], styles: ["\n\n.layout-container[_ngcontent-%COMP%] {\n  min-height: 100vh;\n  background-color: var(--neutral-50);\n}\n.main-content[_ngcontent-%COMP%] {\n  margin-top: 64px;\n  margin-left: 0;\n  min-height: calc(100vh - 64px);\n  transition: margin-left 0.3s ease;\n  background-color: var(--neutral-50);\n}\n.main-content.sidebar-open[_ngcontent-%COMP%] {\n  margin-left: 280px;\n}\n.auth-content[_ngcontent-%COMP%] {\n  min-height: 100vh;\n  width: 100%;\n  background-color: var(--neutral-50);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background-image:\n    linear-gradient(\n      135deg,\n      var(--neutral-50) 0%,\n      var(--neutral-100) 100%);\n}\n.content-wrapper[_ngcontent-%COMP%] {\n  padding: var(--spacing-xl);\n  max-width: 100%;\n  min-height: calc(100vh - 64px - 2 * var(--spacing-xl));\n}\n.loading-overlay[_ngcontent-%COMP%] {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(255, 255, 255, 0.8);\n  -webkit-backdrop-filter: blur(2px);\n  backdrop-filter: blur(2px);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: var(--z-modal);\n}\n.loading-spinner[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: var(--spacing-md);\n}\n.spinner[_ngcontent-%COMP%] {\n  width: 40px;\n  height: 40px;\n  border: 3px solid var(--secondary-200);\n  border-top: 3px solid var(--primary-500);\n  border-radius: 50%;\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\n}\n.loading-text[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n  font-weight: 500;\n}\n@keyframes _ngcontent-%COMP%_spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n@media (max-width: 767px) {\n  .main-content[_ngcontent-%COMP%] {\n    margin-left: 0 !important;\n  }\n  .content-wrapper[_ngcontent-%COMP%] {\n    padding: var(--spacing-lg);\n  }\n}\n@media (min-width: 768px) and (max-width: 1024px) {\n  .content-wrapper[_ngcontent-%COMP%] {\n    padding: var(--spacing-lg) var(--spacing-xl);\n  }\n}\n@media print {\n  .layout-container[_ngcontent-%COMP%] {\n    background: white;\n  }\n  .main-content[_ngcontent-%COMP%] {\n    margin: 0;\n    min-height: auto;\n  }\n  .content-wrapper[_ngcontent-%COMP%] {\n    padding: 0;\n  }\n  .loading-overlay[_ngcontent-%COMP%] {\n    display: none;\n  }\n}\n/*# sourceMappingURL=main-layout.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MainLayoutComponent, [{
    type: Component,
    args: [{ selector: "app-main-layout", standalone: true, imports: [CommonModule, RouterOutlet, HeaderComponent, SidebarComponent], template: `
    <div class="layout-container">
      @if (isAuthenticated) {
        <!-- Header -->
        <app-header
          [sidebarOpen]="sidebarOpen"
          (sidebarToggle)="toggleSidebar()"
          (searchPerformed)="onSearch($event)"
        ></app-header>

        <!-- Sidebar -->
        <app-sidebar
          [isOpen]="sidebarOpen"
          [isMobile]="isMobile"
          (sidebarClose)="closeSidebar()"
        ></app-sidebar>

        <!-- Main Content -->
        <main class="main-content" [class.sidebar-open]="sidebarOpen && !isMobile">
          <div class="content-wrapper">
            <router-outlet></router-outlet>
          </div>
        </main>
      } @else if (isInitialAuthCheck) {
        <!-- Show loading overlay during initial auth check -->
        <div class="loading-overlay">
          <div class="loading-spinner">
            <div class="spinner"></div>
            <p class="loading-text">Loading...</p>
          </div>
        </div>
      } @else {
        <!-- Auth Content -->  
        <main class="auth-content">
          <router-outlet></router-outlet>
        </main>
      }

      <!-- General loading overlay -->
      @if (isLoading) {
        <div class="loading-overlay">
          <div class="loading-spinner">
            <div class="spinner"></div>
            <p class="loading-text">Loading...</p>
          </div>
        </div>
      }
    </div>
  `, styles: ["/* src/app/core/layout/main-layout/main-layout.component.scss */\n.layout-container {\n  min-height: 100vh;\n  background-color: var(--neutral-50);\n}\n.main-content {\n  margin-top: 64px;\n  margin-left: 0;\n  min-height: calc(100vh - 64px);\n  transition: margin-left 0.3s ease;\n  background-color: var(--neutral-50);\n}\n.main-content.sidebar-open {\n  margin-left: 280px;\n}\n.auth-content {\n  min-height: 100vh;\n  width: 100%;\n  background-color: var(--neutral-50);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  background-image:\n    linear-gradient(\n      135deg,\n      var(--neutral-50) 0%,\n      var(--neutral-100) 100%);\n}\n.content-wrapper {\n  padding: var(--spacing-xl);\n  max-width: 100%;\n  min-height: calc(100vh - 64px - 2 * var(--spacing-xl));\n}\n.loading-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(255, 255, 255, 0.8);\n  -webkit-backdrop-filter: blur(2px);\n  backdrop-filter: blur(2px);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: var(--z-modal);\n}\n.loading-spinner {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: var(--spacing-md);\n}\n.spinner {\n  width: 40px;\n  height: 40px;\n  border: 3px solid var(--secondary-200);\n  border-top: 3px solid var(--primary-500);\n  border-radius: 50%;\n  animation: spin 1s linear infinite;\n}\n.loading-text {\n  margin: 0;\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n  font-weight: 500;\n}\n@keyframes spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n@media (max-width: 767px) {\n  .main-content {\n    margin-left: 0 !important;\n  }\n  .content-wrapper {\n    padding: var(--spacing-lg);\n  }\n}\n@media (min-width: 768px) and (max-width: 1024px) {\n  .content-wrapper {\n    padding: var(--spacing-lg) var(--spacing-xl);\n  }\n}\n@media print {\n  .layout-container {\n    background: white;\n  }\n  .main-content {\n    margin: 0;\n    min-height: auto;\n  }\n  .content-wrapper {\n    padding: 0;\n  }\n  .loading-overlay {\n    display: none;\n  }\n}\n/*# sourceMappingURL=main-layout.component.css.map */\n"] }]
  }], () => [{ type: Object, decorators: [{
    type: Inject,
    args: [PLATFORM_ID]
  }] }, { type: AuthService }, { type: Router }], { onResize: [{
    type: HostListener,
    args: ["window:resize", ["$event"]]
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(MainLayoutComponent, { className: "MainLayoutComponent", filePath: "src/app/core/layout/main-layout/main-layout.component.ts", lineNumber: 66 });
})();

// src/app/app.component.ts
var AppComponent = class _AppComponent {
  title = "Application Catalog System";
  static \u0275fac = function AppComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _AppComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _AppComponent, selectors: [["app-root"]], decls: 1, vars: 0, template: function AppComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275element(0, "app-main-layout");
    }
  }, dependencies: [MainLayoutComponent], encapsulation: 2 });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(AppComponent, [{
    type: Component,
    args: [{ selector: "app-root", standalone: true, imports: [MainLayoutComponent], template: `<app-main-layout></app-main-layout>` }]
  }], null, null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(AppComponent, { className: "AppComponent", filePath: "src/app/app.component.ts", lineNumber: 11 });
})();

// src/main.ts
bootstrapApplication(AppComponent, appConfig).catch((err) => console.error(err));
//# sourceMappingURL=main.js.map
