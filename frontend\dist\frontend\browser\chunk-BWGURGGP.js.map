{"version": 3, "sources": ["src/app/modules/dependencies/dependencies.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\n\nexport const routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./dependencies.component').then(m => m.DependenciesComponent)\n  },\n  {\n    path: 'health',\n    loadComponent: () => import('./dependency-health/dependency-health.component').then(m => m.DependencyHealthComponent)\n  }\n];\n"], "mappings": ";;;;;AAEO,IAAM,SAAiB;EAC5B;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAA0B,EAAE,KAAK,OAAK,EAAE,qBAAqB;;EAE3F;IACE,MAAM;IACN,eAAe,MAAM,OAAO,qBAAiD,EAAE,KAAK,OAAK,EAAE,yBAAyB;;;", "names": []}