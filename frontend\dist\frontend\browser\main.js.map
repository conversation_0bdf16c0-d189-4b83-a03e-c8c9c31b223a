{"version": 3, "sources": ["src/app/modules/auth/guards/auth.guard.ts", "src/app/app.routes.ts", "src/app/modules/auth/interceptors/auth.interceptor.ts", "src/app/app.config.ts", "src/app/core/layout/header/header.component.ts", "src/app/core/layout/sidebar/sidebar.component.ts", "src/app/core/layout/main-layout/main-layout.component.ts", "src/app/app.component.ts", "src/main.ts"], "sourcesContent": ["import { inject } from '@angular/core';\r\nimport { Router, type CanActivateFn } from '@angular/router';\r\nimport { AuthService } from '../services/auth.service';\r\n\r\nexport const authGuard: CanActivateFn = (route, state) => {\r\n  const authService = inject(AuthService);\r\n  const router = inject(Router);\r\n\r\n  if (authService.isAuthenticated()) {\r\n    return true;\r\n  }\r\n\r\n  // Store the attempted URL for redirecting after login\r\n  const currentUrl = state.url;\r\n  router.navigate(['/auth/login'], { queryParams: { returnUrl: currentUrl } });\r\n  return false;\r\n};\r\n", "import { Routes } from '@angular/router';\r\nimport { authGuard } from './modules/auth/guards/auth.guard';\r\n\r\nexport const routes: Routes = [\r\n  {\r\n    path: '',\r\n    redirectTo: '/dashboard',\r\n    pathMatch: 'full'\r\n  },\r\n  {\r\n    path: 'auth',\r\n    loadChildren: () => import('./modules/auth/auth.routes').then(m => m.routes)\r\n  },\r\n  {\r\n    path: 'dashboard',\r\n    canActivate: [authGuard],\r\n    loadComponent: () => import('./modules/dashboard/dashboard.component').then(m => m.DashboardComponent)\r\n  },\r\n  {\r\n    path: 'applications',\r\n    canActivate: [authGuard],\r\n    loadChildren: () => import('./modules/applications/applications.routes').then(m => m.routes)\r\n  },\r\n  {\r\n    path: 'dependencies',\r\n    canActivate: [authGuard],\r\n    loadChildren: () => import('./modules/dependencies/dependencies.routes').then(m => m.routes)\r\n  },\r\n  {\r\n    path: 'tech-stack',\r\n    canActivate: [authGuard],\r\n    loadChildren: () => import('./modules/tech-stack/tech-stack.routes').then(m => m.routes)\r\n  },\r\n  {    path: 'stakeholders',\r\n    canActivate: [authGuard],\r\n    loadChildren: () => import('./modules/stakeholders/stakeholders.routes').then(m => m.routes)\r\n  },\r\n  {\r\n    path: 'documentation',\r\n    canActivate: [authGuard],\r\n    loadChildren: () => import('./modules/documentation/documentation.routes').then(m => m.routes)\r\n  },\r\n  {\r\n    path: 'security',\r\n    canActivate: [authGuard],\r\n    loadChildren: () => import('./modules/security/security.routes').then(m => m.routes)\r\n  },\r\n  {\r\n    path: 'reports',\r\n    canActivate: [authGuard],\r\n    loadComponent: () => import('./modules/reports/reports.component').then(m => m.ReportsComponent)\r\n  },\r\n  {\r\n    path: 'settings',\r\n    canActivate: [authGuard],\r\n    loadComponent: () => import('./modules/settings/settings.component').then(m => m.SettingsComponent)  },\r\n  {\r\n    path: '**',\r\n    redirectTo: '/dashboard'\r\n  }\r\n];\r\n", "import { inject } from '@angular/core';\r\nimport {\r\n  HttpRequest,\r\n  HttpHandlerFn,\r\n  HttpInterceptorFn,\r\n  HttpErrorResponse\r\n} from '@angular/common/http';\r\nimport { catchError } from 'rxjs/operators';\r\nimport { throwError } from 'rxjs';\r\nimport { AuthService } from '../services/auth.service';\r\n\r\nexport const AuthInterceptor: HttpInterceptorFn = (\r\n  request: HttpRequest<unknown>,\r\n  next: HttpHandlerFn\r\n) => {\r\n  const authService = inject(AuthService);\r\n  const token = localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');\r\n  \r\n  if (token) {\r\n    request = request.clone({\r\n      setHeaders: {\r\n        Authorization: `Bearer ${token}`\r\n      }\r\n    });\r\n  }\r\n\r\n  return next(request).pipe(\r\n    catchError((error: HttpErrorResponse) => {\r\n      if (error.status === 401) {\r\n        // Auto logout if 401 response returned from api\r\n        authService.logout();\r\n      }\r\n      return throwError(() => error);\r\n    })\r\n  );\r\n};\r\n", "import { ApplicationConfig, provideZoneChangeDetection } from '@angular/core';\r\nimport { provideRouter } from '@angular/router';\r\nimport { provideHttpClient, withInterceptors } from '@angular/common/http';\r\n\r\nimport { routes } from './app.routes';\r\nimport { provideClientHydration, withEventReplay } from '@angular/platform-browser';\r\nimport { AuthInterceptor } from './modules/auth/interceptors/auth.interceptor';\r\n\r\nexport const appConfig: ApplicationConfig = {\r\n  providers: [\r\n    provideZoneChangeDetection({ eventCoalescing: true }),\r\n    provideRouter(routes),\r\n    provideClientHydration(withEventReplay()),\r\n    provideHttpClient(withInterceptors([AuthInterceptor]))\r\n  ]\r\n};\r\n", "import { Component, EventEmitter, Output, Input } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { FormsModule } from '@angular/forms';\nimport { AuthService } from '../../../modules/auth/services/auth.service';\n\n@Component({\n  selector: 'app-header',\n  standalone: true,\n  imports: [CommonModule, RouterModule, FormsModule],\n  template: `\n    <header class=\"header\">\n      <div class=\"header-content\">\n        <!-- Logo and Brand -->\n        <div class=\"header-brand\">\n          <button\n            class=\"sidebar-toggle\"\n            (click)=\"toggleSidebar()\"\n            [attr.aria-label]=\"sidebarOpen ? 'Close sidebar' : 'Open sidebar'\"\n          >\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n              <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\n                    d=\"M4 6h16M4 12h16M4 18h16\" />\n            </svg>\n          </button>\n\n          <div class=\"brand-info\">\n            <h1 class=\"brand-title\">\n              <a routerLink=\"/\" class=\"brand-link\">\n                <svg class=\"brand-icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"\n                        d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n                </svg>\n                App Catalog\n              </a>\n            </h1>\n            <span class=\"brand-subtitle\">Internal Application Management</span>\n          </div>\n        </div>\n\n        <!-- Search Bar -->\n        <div class=\"header-search\">\n          <div class=\"search-container\">\n            <svg class=\"search-icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n              <circle cx=\"11\" cy=\"11\" r=\"8\"></circle>\n              <path d=\"m21 21-4.35-4.35\"></path>\n            </svg>\n            <input\n              type=\"text\"\n              class=\"search-input\"\n              placeholder=\"Search applications, dependencies, or documentation...\"\n              [(ngModel)]=\"searchTerm\"\n              (input)=\"onSearch($event)\"\n              (keyup.enter)=\"performSearch()\"\n            >\n            <button\n              class=\"search-clear\"\n              *ngIf=\"searchTerm\"\n              (click)=\"clearSearch()\"\n              aria-label=\"Clear search\"\n            >\n              <svg class=\"icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n                <line x1=\"18\" y1=\"6\" x2=\"6\" y2=\"18\"></line>\n                <line x1=\"6\" y1=\"6\" x2=\"18\" y2=\"18\"></line>\n              </svg>\n            </button>\n          </div>\n        </div>\n\n        <!-- Header Actions -->\n        <div class=\"header-actions\">\n          <!-- Notifications -->\n          <div class=\"action-item\">\n            <button\n              class=\"action-button\"\n              [class.has-notifications]=\"notificationCount > 0\"\n              (click)=\"toggleNotifications()\"\n              [attr.aria-label]=\"'Notifications' + (notificationCount > 0 ? ' (' + notificationCount + ' unread)' : '')\"\n            >\n              <svg class=\"icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n                <path d=\"M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9\"></path>\n                <path d=\"M13.73 21a2 2 0 0 1-3.46 0\"></path>\n              </svg>\n              <span class=\"notification-badge\" *ngIf=\"notificationCount > 0\">\n                {{ notificationCount > 99 ? '99+' : notificationCount }}\n              </span>\n            </button>\n          </div>\n\n          <!-- Quick Actions -->\n          <div class=\"action-item\">\n            <button\n              class=\"action-button\"\n              (click)=\"toggleQuickActions()\"\n              aria-label=\"Quick actions\"\n            >\n              <svg class=\"icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n                <circle cx=\"12\" cy=\"12\" r=\"3\"></circle>\n                <path d=\"M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z\"></path>\n              </svg>\n            </button>\n          </div>\n\n          <!-- User Profile -->\n          <div class=\"action-item\">\n            <button\n              class=\"user-profile\"\n              (click)=\"toggleUserMenu()\"\n              aria-label=\"User menu\"\n            >\n              <div class=\"user-avatar\">\n                <img\n                  [src]=\"userAvatar || '/assets/images/default-avatar.png'\"\n                  [alt]=\"userName + ' avatar'\"\n                  class=\"avatar-image\"\n                  (error)=\"onAvatarError($event)\"\n                >\n              </div>\n              <div class=\"user-info\">\n                <span class=\"user-name\">{{ userName }}</span>\n                <span class=\"user-role\">{{ userRole }}</span>\n              </div>\n              <svg class=\"chevron-icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n                <polyline points=\"6,9 12,15 18,9\"></polyline>\n              </svg>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <!-- Notifications Dropdown -->\n      <div class=\"dropdown notifications-dropdown\" *ngIf=\"showNotifications\">\n        <div class=\"dropdown-header\">\n          <h3>Notifications</h3>\n          <button class=\"mark-all-read\" (click)=\"markAllAsRead()\">\n            Mark all as read\n          </button>\n        </div>\n        <div class=\"dropdown-content\">\n          <div class=\"notification-item\" *ngFor=\"let notification of notifications\">\n            <div class=\"notification-icon\" [class]=\"'notification-' + notification.type\">\n              <svg class=\"icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n                <path [attr.d]=\"getNotificationIcon(notification.type)\"></path>\n              </svg>\n            </div>\n            <div class=\"notification-content\">\n              <p class=\"notification-title\">{{ notification.title }}</p>\n              <p class=\"notification-message\">{{ notification.message }}</p>\n              <span class=\"notification-time\">{{ notification.timestamp | date:'short' }}</span>\n            </div>\n          </div>\n          <div class=\"no-notifications\" *ngIf=\"notifications.length === 0\">\n            <p>No new notifications</p>\n          </div>\n        </div>\n        <div class=\"dropdown-footer\">\n          <a routerLink=\"/notifications\" class=\"view-all-link\">View all notifications</a>\n        </div>\n      </div>\n\n      <!-- User Menu Dropdown -->\n      <div class=\"dropdown user-dropdown\" *ngIf=\"showUserMenu\">\n        <div class=\"dropdown-content\">\n          <a routerLink=\"/profile\" class=\"dropdown-item\">\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n              <path d=\"M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2\"></path>\n              <circle cx=\"12\" cy=\"7\" r=\"4\"></circle>\n            </svg>\n            Profile Settings\n          </a>\n          <a routerLink=\"/preferences\" class=\"dropdown-item\">\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n              <circle cx=\"12\" cy=\"12\" r=\"3\"></circle>\n              <path d=\"M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z\"></path>\n            </svg>\n            Preferences\n          </a>\n          <div class=\"dropdown-divider\"></div>\n          <button class=\"dropdown-item logout-item\" (click)=\"logout()\">\n            <svg class=\"icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n              <path d=\"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4\"></path>\n              <polyline points=\"16,17 21,12 16,7\"></polyline>\n              <line x1=\"21\" y1=\"12\" x2=\"9\" y2=\"12\"></line>\n            </svg>\n            Sign Out\n          </button>\n        </div>\n      </div>\n    </header>\n  `,\n  styleUrls: ['./header.component.scss']\n})\nexport class HeaderComponent {\n  @Input() sidebarOpen = false;\n  @Output() sidebarToggle = new EventEmitter<void>();\n  @Output() searchPerformed = new EventEmitter<string>();\n\n  searchTerm = '';\n  showNotifications = false;\n  showUserMenu = false;\n  showQuickActions = false;\n\n  // Mock data - replace with actual service calls\n  userName = 'John Doe';\n  userRole = 'System Administrator';\n  userAvatar = '';\n  notificationCount = 3;\n  notifications = [\n    {\n      id: 1,\n      type: 'warning',\n      title: 'Security Alert',\n      message: 'Critical vulnerability detected in Payment Service',\n      timestamp: new Date()\n    },\n    {\n      id: 2,\n      type: 'info',\n      title: 'Dependency Update',\n      message: 'Angular 17 is now available for upgrade',\n      timestamp: new Date()\n    },\n    {\n      id: 3,\n      type: 'success',\n      title: 'Assessment Complete',\n      message: 'Security assessment for User Management completed',\n      timestamp: new Date()\n    }\n  ];\n\n  toggleSidebar(): void {\n    this.sidebarToggle.emit();\n  }\n\n  onSearch(event: any): void {\n    this.searchTerm = event.target.value;\n  }\n\n  performSearch(): void {\n    if (this.searchTerm.trim()) {\n      this.searchPerformed.emit(this.searchTerm.trim());\n    }\n  }\n\n  clearSearch(): void {\n    this.searchTerm = '';\n    this.searchPerformed.emit('');\n  }\n\n  toggleNotifications(): void {\n    this.showNotifications = !this.showNotifications;\n    this.showUserMenu = false;\n    this.showQuickActions = false;\n  }\n\n  toggleUserMenu(): void {\n    this.showUserMenu = !this.showUserMenu;\n    this.showNotifications = false;\n    this.showQuickActions = false;\n  }\n\n  toggleQuickActions(): void {\n    this.showQuickActions = !this.showQuickActions;\n    this.showNotifications = false;\n    this.showUserMenu = false;\n  }\n\n  markAllAsRead(): void {\n    this.notificationCount = 0;\n    this.notifications = [];\n  }\n\n  onAvatarError(event: any): void {\n    event.target.src = '/assets/images/default-avatar.png';\n  }\n\n  getNotificationIcon(type: string): string {\n    switch (type) {\n      case 'warning':\n        return 'M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z';\n      case 'success':\n        return 'M22 11.08V12a10 10 0 1 1-5.93-9.14';\n      case 'error':\n        return 'M18 6L6 18M6 6l12 12';\n      default:\n        return 'M8.5 14.5A2.5 2.5 0 0 0 11 12c0-1.38-.5-2-1-3-1.072-2.143-.224-4.054 2-6 .5 2.5 2 4.9 4 6.5 2 1.6 3 3.5 3 5.5a7 7 0 1 1-14 0c0-1.153.433-2.294 1-3a2.5 2.5 0 0 0 2.5 2.5z';\n    }\n  }\n\n  constructor(private authService: AuthService) {}\n\n  logout(): void {\n    this.authService.logout();\n  }\n}\n", "import { Component, Input, Output, EventEmitter } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule, Router } from '@angular/router';\n\ninterface NavigationItem {\n  id: string;\n  label: string;\n  icon: string;\n  route?: string;\n  children?: NavigationItem[];\n  badge?: string | number;\n  isExpanded?: boolean;\n}\n\n@Component({\n  selector: 'app-sidebar',\n  standalone: true,\n  imports: [CommonModule, RouterModule],\n  template: `\n    <aside class=\"sidebar\" [class.sidebar-open]=\"isOpen\" [class.sidebar-collapsed]=\"isCollapsed\">\n      <div class=\"sidebar-content\">\n        <!-- Navigation Menu -->\n        <nav class=\"sidebar-nav\">\n          <ul class=\"nav-list\">\n            <li class=\"nav-item\" *ngFor=\"let item of navigationItems\">\n              <!-- Single Level Item -->\n              <a \n                *ngIf=\"!item.children\" \n                [routerLink]=\"item.route\"\n                class=\"nav-link\"\n                routerLinkActive=\"nav-link-active\"\n                [routerLinkActiveOptions]=\"{ exact: item.route === '/' }\"\n                (click)=\"onNavItemClick(item)\"\n              >\n                <svg class=\"nav-icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n                  <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" [attr.d]=\"item.icon\" />\n                </svg>\n                <span class=\"nav-label\" [class.sr-only]=\"isCollapsed\">{{ item.label }}</span>\n                <span class=\"nav-badge\" *ngIf=\"item.badge && !isCollapsed\">{{ item.badge }}</span>\n              </a>\n\n              <!-- Multi Level Item -->\n              <div *ngIf=\"item.children\" class=\"nav-group\">\n                <button \n                  class=\"nav-link nav-group-toggle\"\n                  [class.nav-group-expanded]=\"item.isExpanded\"\n                  (click)=\"toggleNavGroup(item)\"\n                >\n                  <svg class=\"nav-icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" [attr.d]=\"item.icon\" />\n                  </svg>\n                  <span class=\"nav-label\" [class.sr-only]=\"isCollapsed\">{{ item.label }}</span>\n                  <span class=\"nav-badge\" *ngIf=\"item.badge && !isCollapsed\">{{ item.badge }}</span>\n                  <svg class=\"nav-chevron\" *ngIf=\"!isCollapsed\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n                    <polyline points=\"9,18 15,12 9,6\"></polyline>\n                  </svg>\n                </button>\n\n                <!-- Submenu -->\n                <ul class=\"nav-submenu\" *ngIf=\"item.isExpanded && !isCollapsed\">\n                  <li class=\"nav-subitem\" *ngFor=\"let subItem of item.children\">\n                    <a \n                      [routerLink]=\"subItem.route\"\n                      class=\"nav-sublink\"\n                      routerLinkActive=\"nav-sublink-active\"\n                      (click)=\"onNavItemClick(subItem)\"\n                    >\n                      <svg class=\"nav-subicon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" [attr.d]=\"subItem.icon\" />\n                      </svg>\n                      <span class=\"nav-sublabel\">{{ subItem.label }}</span>\n                      <span class=\"nav-subbadge\" *ngIf=\"subItem.badge\">{{ subItem.badge }}</span>\n                    </a>\n                  </li>\n                </ul>\n              </div>\n            </li>\n          </ul>\n        </nav>\n\n        <!-- Sidebar Footer -->\n        <div class=\"sidebar-footer\" *ngIf=\"!isCollapsed\">\n          <div class=\"footer-content\">\n            <div class=\"system-status\">\n              <div class=\"status-indicator\" [class]=\"'status-' + systemStatus.status\">\n                <div class=\"status-dot\"></div>\n                <span class=\"status-text\">{{ systemStatus.text }}</span>\n              </div>\n              <div class=\"status-details\">\n                <span class=\"status-detail\">{{ systemStatus.details }}</span>\n              </div>\n            </div>\n            \n            <div class=\"footer-actions\">\n              <button class=\"footer-action\" (click)=\"openHelp()\" title=\"Help & Documentation\">\n                <svg class=\"action-icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n                  <circle cx=\"12\" cy=\"12\" r=\"10\"></circle>\n                  <path d=\"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3\"></path>\n                  <line x1=\"12\" y1=\"17\" x2=\"12.01\" y2=\"17\"></line>\n                </svg>\n              </button>\n              <button class=\"footer-action\" (click)=\"openFeedback()\" title=\"Send Feedback\">\n                <svg class=\"action-icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n                  <path d=\"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z\"></path>\n                </svg>\n              </button>\n            </div>\n          </div>\n        </div>\n\n        <!-- Collapse Toggle -->\n        <button \n          class=\"sidebar-toggle\"\n          (click)=\"toggleCollapse()\"\n          [title]=\"isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'\"\n        >\n          <svg class=\"toggle-icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\">\n            <polyline [attr.points]=\"isCollapsed ? '9,18 15,12 9,6' : '15,18 9,12 15,6'\"></polyline>\n          </svg>\n        </button>\n      </div>\n    </aside>\n\n    <!-- Sidebar Overlay (Mobile) -->\n    <div \n      class=\"sidebar-overlay\" \n      *ngIf=\"isOpen && isMobile\"\n      (click)=\"closeSidebar()\"\n    ></div>\n  `,\n  styleUrls: ['./sidebar.component.scss']\n})\nexport class SidebarComponent {\n  @Input() isOpen = true;\n  @Input() isMobile = false;\n  @Output() sidebarClose = new EventEmitter<void>();\n\n  isCollapsed = false;\n\n  systemStatus = {\n    status: 'healthy',\n    text: 'All Systems Operational',\n    details: 'Last updated: 2 minutes ago'\n  };\n\n  navigationItems: NavigationItem[] = [\n    {\n      id: 'dashboard',\n      label: 'Dashboard',\n      icon: 'M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6',\n      route: '/dashboard'\n    },\n    {\n      id: 'applications',\n      label: 'Applications',\n      icon: 'M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10',\n      route: '/applications',\n      badge: '127'\n    },\n    {\n      id: 'dependencies',\n      label: 'Dependencies',\n      icon: 'M8 16l2.879-2.879m0 0a3 3 0 104.243-4.242 3 3 0 00-4.243 4.242zM21 12a9 9 0 11-18 0 9 9 0 0118 0z',\n      children: [\n        {\n          id: 'dependencies-overview',\n          label: 'Overview',\n          icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z',\n          route: '/dependencies'\n        },\n        {\n          id: 'dependencies-health',\n          label: 'Health Check',\n          icon: 'M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z',\n          route: '/dependencies/health',\n          badge: '12'\n        }\n      ]\n    },\n    {\n      id: 'tech-stack',\n      label: 'Tech Stack',\n      icon: 'M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z',\n      route: '/tech-stack'\n    },\n    {\n      id: 'stakeholders',\n      label: 'Stakeholders',\n      icon: 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a4 4 0 11-8 0 4 4 0 018 0z',\n      route: '/stakeholders'\n    },\n    {\n      id: 'documentation',\n      label: 'Documentation',\n      icon: 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z',\n      route: '/documentation'\n    },\n    {\n      id: 'security',\n      label: 'Security',\n      icon: 'M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z',\n      children: [\n        {\n          id: 'security-overview',\n          label: 'Overview',\n          icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z',\n          route: '/security'\n        },\n        {\n          id: 'vulnerabilities',\n          label: 'Vulnerabilities',\n          icon: 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z',\n          route: '/security/vulnerabilities',\n          badge: '8'\n        },\n        {\n          id: 'assessments',\n          label: 'Assessments',\n          icon: 'M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4',\n          route: '/security/assessments'\n        }\n      ]\n    },\n    {\n      id: 'reports',\n      label: 'Reports',\n      icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z',\n      route: '/reports'\n    },\n    {\n      id: 'settings',\n      label: 'Settings',\n      icon: 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z M15 12a3 3 0 11-6 0 3 3 0 016 0z',\n      route: '/settings'\n    }\n  ];\n\n  constructor(private router: Router) {}\n\n  toggleNavGroup(item: NavigationItem): void {\n    if (this.isCollapsed) {\n      this.isCollapsed = false;\n    }\n    item.isExpanded = !item.isExpanded;\n  }\n\n  onNavItemClick(item: NavigationItem): void {\n    if (this.isMobile) {\n      this.closeSidebar();\n    }\n  }\n\n  toggleCollapse(): void {\n    this.isCollapsed = !this.isCollapsed;\n    // Close all expanded groups when collapsing\n    if (this.isCollapsed) {\n      this.navigationItems.forEach(item => {\n        if (item.children) {\n          item.isExpanded = false;\n        }\n      });\n    }\n  }\n\n  closeSidebar(): void {\n    this.sidebarClose.emit();\n  }\n\n  openHelp(): void {\n    // Implement help functionality\n    console.log('Opening help...');\n  }\n\n  openFeedback(): void {\n    // Implement feedback functionality\n    console.log('Opening feedback...');\n  }\n}\n", "import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HostL<PERSON><PERSON>, Inject, PLATFORM_ID } from '@angular/core';\nimport { CommonModule, isPlatformBrowser } from '@angular/common';\nimport { RouterOutlet, Router } from '@angular/router';\nimport { Subject } from 'rxjs';\nimport { takeUntil, tap } from 'rxjs/operators';\n\nimport { HeaderComponent } from '../header/header.component';\nimport { SidebarComponent } from '../sidebar/sidebar.component';\nimport { AuthService } from '../../../modules/auth/services/auth.service';\n\n@Component({\n  selector: 'app-main-layout',\n  standalone: true,\n  imports: [CommonModule, RouterOutlet, HeaderComponent, SidebarComponent],\n  template: `\n    <div class=\"layout-container\">\n      @if (isAuthenticated) {\n        <!-- Header -->\n        <app-header\n          [sidebarOpen]=\"sidebarOpen\"\n          (sidebarToggle)=\"toggleSidebar()\"\n          (searchPerformed)=\"onSearch($event)\"\n        ></app-header>\n\n        <!-- Sidebar -->\n        <app-sidebar\n          [isOpen]=\"sidebarOpen\"\n          [isMobile]=\"isMobile\"\n          (sidebarClose)=\"closeSidebar()\"\n        ></app-sidebar>\n\n        <!-- Main Content -->\n        <main class=\"main-content\" [class.sidebar-open]=\"sidebarOpen && !isMobile\">\n          <div class=\"content-wrapper\">\n            <router-outlet></router-outlet>\n          </div>\n        </main>\n      } @else if (isInitialAuthCheck) {\n        <!-- Show loading overlay during initial auth check -->\n        <div class=\"loading-overlay\">\n          <div class=\"loading-spinner\">\n            <div class=\"spinner\"></div>\n            <p class=\"loading-text\">Loading...</p>\n          </div>\n        </div>\n      } @else {\n        <!-- Auth Content -->  \n        <main class=\"auth-content\">\n          <router-outlet></router-outlet>\n        </main>\n      }\n\n      <!-- General loading overlay -->\n      @if (isLoading) {\n        <div class=\"loading-overlay\">\n          <div class=\"loading-spinner\">\n            <div class=\"spinner\"></div>\n            <p class=\"loading-text\">Loading...</p>\n          </div>\n        </div>\n      }\n    </div>\n  `,\n  styleUrls: ['./main-layout.component.scss']\n})\nexport class MainLayoutComponent implements OnInit, OnDestroy {\n  sidebarOpen = true;\n  isMobile = false;\n  isLoading = false;\n  isAuthenticated = false;\n  isInitialAuthCheck = true;\n\n  private destroy$ = new Subject<void>();\n\n  constructor(\n    @Inject(PLATFORM_ID) private platformId: Object,\n    public authService: AuthService,\n    private router: Router\n  ) {\n    // Initialize authentication status\n    this.isAuthenticated = this.authService.isAuthenticated();\n\n    // Subscribe to auth state changes\n    this.authService.currentUser$\n      .pipe(\n        takeUntil(this.destroy$),\n        tap(() => this.isInitialAuthCheck = false)\n      )\n      .subscribe(user => {\n        this.isAuthenticated = !!user;\n        if (!user && !this.isInitialAuthCheck) {\n          this.router.navigate(['/auth/login']);\n        }\n      });\n\n    // Subscribe to loading state\n    this.authService.isLoading$\n      .pipe(takeUntil(this.destroy$))\n      .subscribe(loading => {\n        this.isLoading = loading;\n      });\n  }\n\n  ngOnInit(): void {\n    this.checkScreenSize();\n    this.initializeSidebar();\n  }\n\n  ngOnDestroy(): void {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n\n  @HostListener('window:resize', ['$event'])\n  onResize(event: any): void {\n    this.checkScreenSize();\n  }\n\n  private checkScreenSize(): void {\n    if (!isPlatformBrowser(this.platformId)) {\n      return;\n    }\n\n    const previousIsMobile = this.isMobile;\n    this.isMobile = window.innerWidth < 768;\n\n    // If switching from mobile to desktop, open sidebar only if authenticated\n    if (previousIsMobile && !this.isMobile && this.isAuthenticated) {\n      this.sidebarOpen = true;\n    }\n    // If switching from desktop to mobile, close sidebar\n    else if (!previousIsMobile && this.isMobile) {\n      this.sidebarOpen = false;\n    }\n  }\n\n  private initializeSidebar(): void {\n    if (!isPlatformBrowser(this.platformId)) {\n      return;\n    }\n\n    // On mobile, always start with sidebar closed\n    if (this.isMobile) {\n      this.sidebarOpen = false;\n      return;\n    }\n\n    // Get saved sidebar state from localStorage\n    const savedSidebarState = localStorage.getItem('sidebarOpen');\n    if (savedSidebarState !== null) {\n      this.sidebarOpen = JSON.parse(savedSidebarState);\n    }\n  }\n\n  toggleSidebar(): void {\n    this.sidebarOpen = !this.sidebarOpen;\n    this.saveSidebarState();\n  }\n\n  closeSidebar(): void {\n    this.sidebarOpen = false;\n    this.saveSidebarState();\n  }\n\n  private saveSidebarState(): void {\n    if (!isPlatformBrowser(this.platformId)) {\n      return;\n    }\n\n    // Only save state for desktop\n    if (!this.isMobile) {\n      localStorage.setItem('sidebarOpen', JSON.stringify(this.sidebarOpen));\n    }\n  }\n\n  onSearch(searchTerm: string): void {\n    // Implement global search functionality\n    console.log('Global search:', searchTerm);\n  }\n\n  setLoading(loading: boolean): void {\n    this.isLoading = loading;\n  }\n}\n", "import { Component } from '@angular/core';\r\nimport { MainLayoutComponent } from './core/layout/main-layout/main-layout.component';\r\n\r\n@Component({\r\n  selector: 'app-root',\r\n  standalone: true,\r\n  imports: [MainLayoutComponent],\r\n  template: `<app-main-layout></app-main-layout>`,\r\n  styleUrl: './app.component.scss'\r\n})\r\nexport class AppComponent {\r\n  title = 'Application Catalog System';\r\n}\r\n", "import { bootstrapApplication } from '@angular/platform-browser';\r\nimport { appConfig } from './app/app.config';\r\nimport { AppComponent } from './app/app.component';\r\n\r\nbootstrapApplication(AppComponent, appConfig)\r\n  .catch((err) => console.error(err));\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIO,IAAM,YAA2B,CAAC,OAAO,UAAS;AACvD,QAAM,cAAc,OAAO,WAAW;AACtC,QAAM,SAAS,OAAO,MAAM;AAE5B,MAAI,YAAY,gBAAe,GAAI;AACjC,WAAO;EACT;AAGA,QAAM,aAAa,MAAM;AACzB,SAAO,SAAS,CAAC,aAAa,GAAG,EAAE,aAAa,EAAE,WAAW,WAAU,EAAE,CAAE;AAC3E,SAAO;AACT;;;ACbO,IAAM,SAAiB;EAC5B;IACE,MAAM;IACN,YAAY;IACZ,WAAW;;EAEb;IACE,MAAM;IACN,cAAc,MAAM,OAAO,qBAA4B,EAAE,KAAK,OAAK,EAAE,MAAM;;EAE7E;IACE,MAAM;IACN,aAAa,CAAC,SAAS;IACvB,eAAe,MAAM,OAAO,qBAAyC,EAAE,KAAK,OAAK,EAAE,kBAAkB;;EAEvG;IACE,MAAM;IACN,aAAa,CAAC,SAAS;IACvB,cAAc,MAAM,OAAO,qBAA4C,EAAE,KAAK,OAAK,EAAE,MAAM;;EAE7F;IACE,MAAM;IACN,aAAa,CAAC,SAAS;IACvB,cAAc,MAAM,OAAO,qBAA4C,EAAE,KAAK,OAAK,EAAE,MAAM;;EAE7F;IACE,MAAM;IACN,aAAa,CAAC,SAAS;IACvB,cAAc,MAAM,OAAO,qBAAwC,EAAE,KAAK,OAAK,EAAE,MAAM;;EAEzF;IAAK,MAAM;IACT,aAAa,CAAC,SAAS;IACvB,cAAc,MAAM,OAAO,qBAA4C,EAAE,KAAK,OAAK,EAAE,MAAM;KAAC,QAAA,EAAA,iBAAA,sDAAA,IAAA,CAAA;EAE9F;IACE,MAAM;IACN,aAAa,CAAC,SAAS;IACvB,cAAc,MAAM,OAAO,qBAA8C,EAAE,KAAK,OAAK,EAAE,MAAM;;EAE/F;IACE,MAAM;IACN,aAAa,CAAC,SAAS;IACvB,cAAc,MAAM,OAAO,qBAAoC,EAAE,KAAK,OAAK,EAAE,MAAM;;EAErF;IACE,MAAM;IACN,aAAa,CAAC,SAAS;IACvB,eAAe,MAAM,OAAO,qBAAqC,EAAE,KAAK,OAAK,EAAE,gBAAgB;;EAEjG;IACE,MAAM;IACN,aAAa,CAAC,SAAS;IACvB,eAAe,MAAM,OAAO,qBAAuC,EAAE,KAAK,OAAK,EAAE,iBAAiB;;EACpG;IACE,MAAM;IACN,YAAY;;;;;AC/CT,IAAM,kBAAqC,CAChD,SACA,SACE;AACF,QAAM,cAAc,OAAO,WAAW;AACtC,QAAM,QAAQ,aAAa,QAAQ,YAAY,KAAK,eAAe,QAAQ,YAAY;AAEvF,MAAI,OAAO;AACT,cAAU,QAAQ,MAAM;MACtB,YAAY;QACV,eAAe,UAAU,KAAK;;KAEjC;EACH;AAEA,SAAO,KAAK,OAAO,EAAE,KACnB,WAAW,CAAC,UAA4B;AACtC,QAAI,MAAM,WAAW,KAAK;AAExB,kBAAY,OAAM;IACpB;AACA,WAAO,WAAW,MAAM,KAAK;EAC/B,CAAC,CAAC;AAEN;;;AC3BO,IAAM,YAA+B;EAC1C,WAAW;IACT,2BAA2B,EAAE,iBAAiB,KAAI,CAAE;IACpD,cAAc,MAAM;IACpB,uBAAuB,gBAAe,CAAE;IACxC,kBAAkB,iBAAiB,CAAC,eAAe,CAAC,CAAC;;;;;;;;AC0C7C,IAAA,yBAAA,GAAA,UAAA,EAAA;AAGE,IAAA,qBAAA,SAAA,SAAA,6DAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,YAAA,CAAa;IAAA,CAAA;;AAGtB,IAAA,yBAAA,GAAA,OAAA,CAAA;AACE,IAAA,oBAAA,GAAA,QAAA,EAAA,EAA2C,GAAA,QAAA,EAAA;AAE7C,IAAA,uBAAA,EAAM;;;;;AAmBN,IAAA,yBAAA,GAAA,QAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AACF,IAAA,uBAAA;;;;AADE,IAAA,oBAAA;AAAA,IAAA,6BAAA,KAAA,OAAA,oBAAA,KAAA,QAAA,OAAA,mBAAA,GAAA;;;;;AAuDN,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA0E,GAAA,OAAA,EAAA;;AAEtE,IAAA,yBAAA,GAAA,OAAA,CAAA;AACE,IAAA,oBAAA,GAAA,MAAA;AACF,IAAA,uBAAA,EAAM;;AAER,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAkC,GAAA,KAAA,EAAA;AACF,IAAA,iBAAA,CAAA;AAAwB,IAAA,uBAAA;AACtD,IAAA,yBAAA,GAAA,KAAA,EAAA;AAAgC,IAAA,iBAAA,CAAA;AAA0B,IAAA,uBAAA;AAC1D,IAAA,yBAAA,GAAA,QAAA,EAAA;AAAgC,IAAA,iBAAA,EAAA;;AAA2C,IAAA,uBAAA,EAAO,EAC9E;;;;;AATyB,IAAA,oBAAA;AAAA,IAAA,qBAAA,kBAAA,gBAAA,IAAA;AAErB,IAAA,oBAAA,CAAA;;AAIsB,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,gBAAA,KAAA;AACE,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,gBAAA,OAAA;AACA,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,sBAAA,IAAA,GAAA,gBAAA,WAAA,OAAA,CAAA;;;;;AAGpC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAiE,GAAA,GAAA;AAC5D,IAAA,iBAAA,GAAA,sBAAA;AAAoB,IAAA,uBAAA,EAAI;;;;;;AArBjC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAuE,GAAA,OAAA,EAAA,EACxC,GAAA,IAAA;AACvB,IAAA,iBAAA,GAAA,eAAA;AAAa,IAAA,uBAAA;AACjB,IAAA,yBAAA,GAAA,UAAA,EAAA;AAA8B,IAAA,qBAAA,SAAA,SAAA,0DAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,cAAA,CAAe;IAAA,CAAA;AACpD,IAAA,iBAAA,GAAA,oBAAA;AACF,IAAA,uBAAA,EAAS;AAEX,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,qBAAA,GAAA,uCAAA,IAAA,GAAA,OAAA,EAAA,EAA0E,GAAA,uCAAA,GAAA,GAAA,OAAA,EAAA;AAe5E,IAAA,uBAAA;AACA,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA6B,IAAA,KAAA,EAAA;AAC0B,IAAA,iBAAA,IAAA,wBAAA;AAAsB,IAAA,uBAAA,EAAI,EAC3E;;;;AAlBoD,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,WAAA,OAAA,aAAA;AAYzB,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,OAAA,cAAA,WAAA,CAAA;;;;;;AAUnC,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAyD,GAAA,OAAA,EAAA,EACzB,GAAA,KAAA,EAAA;;AAE1B,IAAA,yBAAA,GAAA,OAAA,CAAA;AACE,IAAA,oBAAA,GAAA,QAAA,EAAA,EAA2D,GAAA,UAAA,EAAA;AAE7D,IAAA,uBAAA;AACA,IAAA,iBAAA,GAAA,oBAAA;AACF,IAAA,uBAAA;;AACA,IAAA,yBAAA,GAAA,KAAA,EAAA;;AACE,IAAA,yBAAA,GAAA,OAAA,CAAA;AACE,IAAA,oBAAA,GAAA,UAAA,EAAA,EAAuC,IAAA,QAAA,EAAA;AAEzC,IAAA,uBAAA;AACA,IAAA,iBAAA,IAAA,eAAA;AACF,IAAA,uBAAA;;AACA,IAAA,oBAAA,IAAA,OAAA,EAAA;AACA,IAAA,yBAAA,IAAA,UAAA,EAAA;AAA0C,IAAA,qBAAA,SAAA,SAAA,2DAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,OAAA,CAAQ;IAAA,CAAA;;AACzD,IAAA,yBAAA,IAAA,OAAA,CAAA;AACE,IAAA,oBAAA,IAAA,QAAA,EAAA,EAAyD,IAAA,YAAA,EAAA,EACV,IAAA,QAAA,EAAA;AAEjD,IAAA,uBAAA;AACA,IAAA,iBAAA,IAAA,YAAA;AACF,IAAA,uBAAA,EAAS,EACL;;;AAMR,IAAO,kBAAP,MAAO,iBAAe;EAkGN;EAjGX,cAAc;EACb,gBAAgB,IAAI,aAAY;EAChC,kBAAkB,IAAI,aAAY;EAE5C,aAAa;EACb,oBAAoB;EACpB,eAAe;EACf,mBAAmB;;EAGnB,WAAW;EACX,WAAW;EACX,aAAa;EACb,oBAAoB;EACpB,gBAAgB;IACd;MACE,IAAI;MACJ,MAAM;MACN,OAAO;MACP,SAAS;MACT,WAAW,oBAAI,KAAI;;IAErB;MACE,IAAI;MACJ,MAAM;MACN,OAAO;MACP,SAAS;MACT,WAAW,oBAAI,KAAI;;IAErB;MACE,IAAI;MACJ,MAAM;MACN,OAAO;MACP,SAAS;MACT,WAAW,oBAAI,KAAI;;;EAIvB,gBAAa;AACX,SAAK,cAAc,KAAI;EACzB;EAEA,SAAS,OAAU;AACjB,SAAK,aAAa,MAAM,OAAO;EACjC;EAEA,gBAAa;AACX,QAAI,KAAK,WAAW,KAAI,GAAI;AAC1B,WAAK,gBAAgB,KAAK,KAAK,WAAW,KAAI,CAAE;IAClD;EACF;EAEA,cAAW;AACT,SAAK,aAAa;AAClB,SAAK,gBAAgB,KAAK,EAAE;EAC9B;EAEA,sBAAmB;AACjB,SAAK,oBAAoB,CAAC,KAAK;AAC/B,SAAK,eAAe;AACpB,SAAK,mBAAmB;EAC1B;EAEA,iBAAc;AACZ,SAAK,eAAe,CAAC,KAAK;AAC1B,SAAK,oBAAoB;AACzB,SAAK,mBAAmB;EAC1B;EAEA,qBAAkB;AAChB,SAAK,mBAAmB,CAAC,KAAK;AAC9B,SAAK,oBAAoB;AACzB,SAAK,eAAe;EACtB;EAEA,gBAAa;AACX,SAAK,oBAAoB;AACzB,SAAK,gBAAgB,CAAA;EACvB;EAEA,cAAc,OAAU;AACtB,UAAM,OAAO,MAAM;EACrB;EAEA,oBAAoB,MAAY;AAC9B,YAAQ,MAAM;MACZ,KAAK;AACH,eAAO;MACT,KAAK;AACH,eAAO;MACT,KAAK;AACH,eAAO;MACT;AACE,eAAO;IACX;EACF;EAEA,YAAoB,aAAwB;AAAxB,SAAA,cAAA;EAA2B;EAE/C,SAAM;AACJ,SAAK,YAAY,OAAM;EACzB;;qCAtGW,kBAAe,4BAAA,WAAA,CAAA;EAAA;yEAAf,kBAAe,WAAA,CAAA,CAAA,YAAA,CAAA,GAAA,QAAA,EAAA,aAAA,cAAA,GAAA,SAAA,EAAA,eAAA,iBAAA,iBAAA,kBAAA,GAAA,OAAA,IAAA,MAAA,IAAA,QAAA,CAAA,CAAA,GAAA,QAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,OAAA,GAAA,CAAA,WAAA,aAAA,QAAA,QAAA,UAAA,gBAAA,GAAA,MAAA,GAAA,CAAA,kBAAA,SAAA,mBAAA,SAAA,gBAAA,KAAA,KAAA,yBAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,cAAA,KAAA,GAAA,YAAA,GAAA,CAAA,WAAA,aAAA,QAAA,QAAA,UAAA,gBAAA,GAAA,YAAA,GAAA,CAAA,kBAAA,SAAA,mBAAA,SAAA,gBAAA,KAAA,KAAA,wJAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,WAAA,aAAA,QAAA,QAAA,UAAA,gBAAA,GAAA,aAAA,GAAA,CAAA,MAAA,MAAA,MAAA,MAAA,KAAA,GAAA,GAAA,CAAA,KAAA,kBAAA,GAAA,CAAA,QAAA,QAAA,eAAA,0DAAA,GAAA,gBAAA,GAAA,iBAAA,SAAA,eAAA,SAAA,GAAA,CAAA,SAAA,gBAAA,cAAA,gBAAA,GAAA,SAAA,GAAA,MAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,OAAA,GAAA,CAAA,KAAA,6CAAA,GAAA,CAAA,KAAA,4BAAA,GAAA,CAAA,SAAA,sBAAA,GAAA,MAAA,GAAA,CAAA,cAAA,iBAAA,GAAA,iBAAA,GAAA,OAAA,GAAA,CAAA,MAAA,MAAA,MAAA,MAAA,KAAA,GAAA,GAAA,CAAA,KAAA,guBAAA,GAAA,CAAA,cAAA,aAAA,GAAA,gBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,SAAA,OAAA,KAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,WAAA,aAAA,QAAA,QAAA,UAAA,gBAAA,GAAA,cAAA,GAAA,CAAA,UAAA,gBAAA,GAAA,CAAA,SAAA,mCAAA,GAAA,MAAA,GAAA,CAAA,SAAA,0BAAA,GAAA,MAAA,GAAA,CAAA,cAAA,gBAAA,GAAA,gBAAA,GAAA,OAAA,GAAA,CAAA,MAAA,MAAA,MAAA,KAAA,MAAA,KAAA,MAAA,IAAA,GAAA,CAAA,MAAA,KAAA,MAAA,KAAA,MAAA,MAAA,MAAA,IAAA,GAAA,CAAA,GAAA,oBAAA,GAAA,CAAA,GAAA,YAAA,wBAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,OAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,SAAA,qBAAA,GAAA,SAAA,SAAA,GAAA,CAAA,SAAA,oBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,cAAA,kBAAA,GAAA,eAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,sBAAA,GAAA,CAAA,GAAA,oBAAA,GAAA,CAAA,GAAA,sBAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,YAAA,eAAA,GAAA,CAAA,cAAA,YAAA,GAAA,eAAA,GAAA,CAAA,KAAA,2CAAA,GAAA,CAAA,MAAA,MAAA,MAAA,KAAA,KAAA,GAAA,GAAA,CAAA,cAAA,gBAAA,GAAA,eAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,iBAAA,eAAA,GAAA,OAAA,GAAA,CAAA,KAAA,yCAAA,GAAA,CAAA,UAAA,kBAAA,GAAA,CAAA,MAAA,MAAA,MAAA,MAAA,MAAA,KAAA,MAAA,IAAA,CAAA,GAAA,UAAA,SAAA,yBAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AArLxB,MAAA,yBAAA,GAAA,UAAA,CAAA,EAAuB,GAAA,OAAA,CAAA,EACO,GAAA,OAAA,CAAA,EAEA,GAAA,UAAA,CAAA;AAGtB,MAAA,qBAAA,SAAA,SAAA,mDAAA;AAAA,eAAS,IAAA,cAAA;MAAe,CAAA;;AAGxB,MAAA,yBAAA,GAAA,OAAA,CAAA;AACE,MAAA,oBAAA,GAAA,QAAA,CAAA;AAEF,MAAA,uBAAA,EAAM;;AAGR,MAAA,yBAAA,GAAA,OAAA,CAAA,EAAwB,GAAA,MAAA,CAAA,EACE,GAAA,KAAA,CAAA;;AAEpB,MAAA,yBAAA,GAAA,OAAA,CAAA;AACE,MAAA,oBAAA,IAAA,QAAA,EAAA;AAEF,MAAA,uBAAA;AACA,MAAA,iBAAA,IAAA,eAAA;AACF,MAAA,uBAAA,EAAI;;AAEN,MAAA,yBAAA,IAAA,QAAA,EAAA;AAA6B,MAAA,iBAAA,IAAA,iCAAA;AAA+B,MAAA,uBAAA,EAAO,EAC/D;AAIR,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA2B,IAAA,OAAA,EAAA;;AAEvB,MAAA,yBAAA,IAAA,OAAA,EAAA;AACE,MAAA,oBAAA,IAAA,UAAA,EAAA,EAAuC,IAAA,QAAA,EAAA;AAEzC,MAAA,uBAAA;;AACA,MAAA,yBAAA,IAAA,SAAA,EAAA;AAIE,MAAA,2BAAA,iBAAA,SAAA,yDAAA,QAAA;AAAA,QAAA,6BAAA,IAAA,YAAA,MAAA,MAAA,IAAA,aAAA;AAAA,eAAA;MAAA,CAAA;AACA,MAAA,qBAAA,SAAA,SAAA,iDAAA,QAAA;AAAA,eAAS,IAAA,SAAA,MAAA;MAAgB,CAAA,EAAC,eAAA,SAAA,yDAAA;AAAA,eACX,IAAA,cAAA;MAAe,CAAA;AANhC,MAAA,uBAAA;AAQA,MAAA,qBAAA,IAAA,oCAAA,GAAA,GAAA,UAAA,EAAA;AAWF,MAAA,uBAAA,EAAM;AAIR,MAAA,yBAAA,IAAA,OAAA,EAAA,EAA4B,IAAA,OAAA,EAAA,EAED,IAAA,UAAA,EAAA;AAIrB,MAAA,qBAAA,SAAA,SAAA,oDAAA;AAAA,eAAS,IAAA,oBAAA;MAAqB,CAAA;;AAG9B,MAAA,yBAAA,IAAA,OAAA,CAAA;AACE,MAAA,oBAAA,IAAA,QAAA,EAAA,EAA6D,IAAA,QAAA,EAAA;AAE/D,MAAA,uBAAA;AACA,MAAA,qBAAA,IAAA,kCAAA,GAAA,GAAA,QAAA,EAAA;AAGF,MAAA,uBAAA,EAAS;;AAIX,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAyB,IAAA,UAAA,EAAA;AAGrB,MAAA,qBAAA,SAAA,SAAA,oDAAA;AAAA,eAAS,IAAA,mBAAA;MAAoB,CAAA;;AAG7B,MAAA,yBAAA,IAAA,OAAA,CAAA;AACE,MAAA,oBAAA,IAAA,UAAA,EAAA,EAAuC,IAAA,QAAA,EAAA;AAEzC,MAAA,uBAAA,EAAM,EACC;;AAIX,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAyB,IAAA,UAAA,EAAA;AAGrB,MAAA,qBAAA,SAAA,SAAA,oDAAA;AAAA,eAAS,IAAA,eAAA;MAAgB,CAAA;AAGzB,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAyB,IAAA,OAAA,EAAA;AAKrB,MAAA,qBAAA,SAAA,SAAA,+CAAA,QAAA;AAAA,eAAS,IAAA,cAAA,MAAA;MAAqB,CAAA;AAJhC,MAAA,uBAAA,EAKC;AAEH,MAAA,yBAAA,IAAA,OAAA,EAAA,EAAuB,IAAA,QAAA,EAAA;AACG,MAAA,iBAAA,EAAA;AAAc,MAAA,uBAAA;AACtC,MAAA,yBAAA,IAAA,QAAA,EAAA;AAAwB,MAAA,iBAAA,EAAA;AAAc,MAAA,uBAAA,EAAO;;AAE/C,MAAA,yBAAA,IAAA,OAAA,EAAA;AACE,MAAA,oBAAA,IAAA,YAAA,EAAA;AACF,MAAA,uBAAA,EAAM,EACC,EACL,EACF;AAIR,MAAA,qBAAA,IAAA,iCAAA,IAAA,GAAA,OAAA,EAAA,EAAuE,IAAA,iCAAA,IAAA,GAAA,OAAA,EAAA;AAyDzE,MAAA,uBAAA;;;AA1KQ,MAAA,oBAAA,CAAA;;AAiCE,MAAA,oBAAA,EAAA;AAAA,MAAA,2BAAA,WAAA,IAAA,UAAA;AAMC,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,UAAA;AAkBD,MAAA,oBAAA,CAAA;AAAA,MAAA,sBAAA,qBAAA,IAAA,oBAAA,CAAA;;AAQkC,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,oBAAA,CAAA;AA6B9B,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,OAAA,IAAA,cAAA,qCAAA,uBAAA,EAAyD,OAAA,IAAA,WAAA,SAAA;AAOnC,MAAA,oBAAA,CAAA;AAAA,MAAA,4BAAA,IAAA,QAAA;AACA,MAAA,oBAAA,CAAA;AAAA,MAAA,4BAAA,IAAA,QAAA;AAWY,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,iBAAA;AA8BT,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,YAAA;;oBAxJ/B,cAAY,SAAA,MAAA,UAAE,cAAY,YAAE,aAAW,sBAAA,iBAAA,OAAA,GAAA,QAAA,CAAA,6+UAAA,EAAA,CAAA;;;sEAuLtC,iBAAe,CAAA;UA1L3B;uBACW,cAAY,YACV,MAAI,SACP,CAAC,cAAc,cAAc,WAAW,GAAC,UACxC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAmLT,QAAA,CAAA,wjSAAA,EAAA,CAAA;uCAIQ,aAAW,CAAA;UAAnB;MACS,eAAa,CAAA;UAAtB;MACS,iBAAe,CAAA;UAAxB;;;;6EAHU,iBAAe,EAAA,WAAA,mBAAA,UAAA,kDAAA,YAAA,IAAA,CAAA;AAAA,GAAA;;;;;;AC1JZ,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA2D,IAAA,iBAAA,CAAA;AAAgB,IAAA,uBAAA;;;;AAAhB,IAAA,oBAAA;AAAA,IAAA,4BAAA,QAAA,KAAA;;;;;;AAZ7D,IAAA,yBAAA,GAAA,KAAA,EAAA;AAME,IAAA,qBAAA,SAAA,SAAA,wDAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,UAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,eAAA,OAAA,CAAoB;IAAA,CAAA;;AAE7B,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,QAAA,EAAA;AACF,IAAA,uBAAA;;AACA,IAAA,yBAAA,GAAA,QAAA,EAAA;AAAsD,IAAA,iBAAA,CAAA;AAAgB,IAAA,uBAAA;AACtE,IAAA,qBAAA,GAAA,2CAAA,GAAA,GAAA,QAAA,EAAA;AACF,IAAA,uBAAA;;;;;AAXE,IAAA,qBAAA,cAAA,QAAA,KAAA,EAAyB,2BAAA,0BAAA,GAAA,KAAA,QAAA,UAAA,GAAA,CAAA;AAO+C,IAAA,oBAAA,CAAA;;AAEhD,IAAA,oBAAA;AAAA,IAAA,sBAAA,WAAA,OAAA,WAAA;AAA8B,IAAA,oBAAA;AAAA,IAAA,4BAAA,QAAA,KAAA;AAC7B,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,QAAA,SAAA,CAAA,OAAA,WAAA;;;;;AAcvB,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA2D,IAAA,iBAAA,CAAA;AAAgB,IAAA,uBAAA;;;;AAAhB,IAAA,oBAAA;AAAA,IAAA,4BAAA,QAAA,KAAA;;;;;;AAC3D,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,YAAA,EAAA;AACF,IAAA,uBAAA;;;;;AAgBI,IAAA,yBAAA,GAAA,QAAA,EAAA;AAAiD,IAAA,iBAAA,CAAA;AAAmB,IAAA,uBAAA;;;;AAAnB,IAAA,oBAAA;AAAA,IAAA,4BAAA,WAAA,KAAA;;;;;;AAXrD,IAAA,yBAAA,GAAA,MAAA,EAAA,EAA8D,GAAA,KAAA,EAAA;AAK1D,IAAA,qBAAA,SAAA,SAAA,oEAAA;AAAA,YAAA,aAAA,wBAAA,GAAA,EAAA;AAAA,YAAA,SAAA,wBAAA,CAAA;AAAA,aAAA,sBAAS,OAAA,eAAA,UAAA,CAAuB;IAAA,CAAA;;AAEhC,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,QAAA,EAAA;AACF,IAAA,uBAAA;;AACA,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA2B,IAAA,iBAAA,CAAA;AAAmB,IAAA,uBAAA;AAC9C,IAAA,qBAAA,GAAA,uDAAA,GAAA,GAAA,QAAA,EAAA;AACF,IAAA,uBAAA,EAAI;;;;AAVF,IAAA,oBAAA;AAAA,IAAA,qBAAA,cAAA,WAAA,KAAA;AAMwE,IAAA,oBAAA,CAAA;;AAE7C,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,WAAA,KAAA;AACC,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,WAAA,KAAA;;;;;AAZlC,IAAA,yBAAA,GAAA,MAAA,EAAA;AACE,IAAA,qBAAA,GAAA,gDAAA,GAAA,GAAA,MAAA,EAAA;AAcF,IAAA,uBAAA;;;;AAd8C,IAAA,oBAAA;AAAA,IAAA,qBAAA,WAAA,QAAA,QAAA;;;;;;AAlBhD,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA6C,GAAA,UAAA,EAAA;AAIzC,IAAA,qBAAA,SAAA,SAAA,+DAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,UAAA,wBAAA,EAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,eAAA,OAAA,CAAoB;IAAA,CAAA;;AAE7B,IAAA,yBAAA,GAAA,OAAA,EAAA;AACE,IAAA,oBAAA,GAAA,QAAA,EAAA;AACF,IAAA,uBAAA;;AACA,IAAA,yBAAA,GAAA,QAAA,EAAA;AAAsD,IAAA,iBAAA,CAAA;AAAgB,IAAA,uBAAA;AACtE,IAAA,qBAAA,GAAA,6CAAA,GAAA,GAAA,QAAA,EAAA,EAA2D,GAAA,iDAAA,GAAA,GAAA,OAAA,EAAA;AAI7D,IAAA,uBAAA;AAGA,IAAA,qBAAA,GAAA,2CAAA,GAAA,GAAA,MAAA,EAAA;AAgBF,IAAA,uBAAA;;;;;AA9BI,IAAA,oBAAA;AAAA,IAAA,sBAAA,sBAAA,QAAA,UAAA;AAIwE,IAAA,oBAAA,CAAA;;AAEhD,IAAA,oBAAA;AAAA,IAAA,sBAAA,WAAA,OAAA,WAAA;AAA8B,IAAA,oBAAA;AAAA,IAAA,4BAAA,QAAA,KAAA;AAC7B,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,QAAA,SAAA,CAAA,OAAA,WAAA;AACC,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,CAAA,OAAA,WAAA;AAMH,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,QAAA,cAAA,CAAA,OAAA,WAAA;;;;;AAnC7B,IAAA,yBAAA,GAAA,MAAA,CAAA;AAEE,IAAA,qBAAA,GAAA,oCAAA,GAAA,GAAA,KAAA,EAAA,EAOC,GAAA,sCAAA,GAAA,GAAA,OAAA,EAAA;AA2CH,IAAA,uBAAA;;;;AAjDK,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,CAAA,QAAA,QAAA;AAeG,IAAA,oBAAA;AAAA,IAAA,qBAAA,QAAA,QAAA,QAAA;;;;;;AAuCZ,IAAA,yBAAA,GAAA,OAAA,EAAA,EAAiD,GAAA,OAAA,EAAA,EACnB,GAAA,OAAA,EAAA,EACC,GAAA,OAAA,EAAA;AAEvB,IAAA,oBAAA,GAAA,OAAA,EAAA;AACA,IAAA,yBAAA,GAAA,QAAA,EAAA;AAA0B,IAAA,iBAAA,CAAA;AAAuB,IAAA,uBAAA,EAAO;AAE1D,IAAA,yBAAA,GAAA,OAAA,EAAA,EAA4B,GAAA,QAAA,EAAA;AACE,IAAA,iBAAA,CAAA;AAA0B,IAAA,uBAAA,EAAO,EACzD;AAGR,IAAA,yBAAA,IAAA,OAAA,EAAA,EAA4B,IAAA,UAAA,EAAA;AACI,IAAA,qBAAA,SAAA,SAAA,2DAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,SAAA,CAAU;IAAA,CAAA;;AAC/C,IAAA,yBAAA,IAAA,OAAA,EAAA;AACE,IAAA,oBAAA,IAAA,UAAA,EAAA,EAAwC,IAAA,QAAA,EAAA,EACc,IAAA,QAAA,EAAA;AAExD,IAAA,uBAAA,EAAM;;AAER,IAAA,yBAAA,IAAA,UAAA,EAAA;AAA8B,IAAA,qBAAA,SAAA,SAAA,2DAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,aAAA,CAAc;IAAA,CAAA;;AACnD,IAAA,yBAAA,IAAA,OAAA,EAAA;AACE,IAAA,oBAAA,IAAA,QAAA,EAAA;AACF,IAAA,uBAAA,EAAM,EACC,EACL,EACF;;;;AAvB4B,IAAA,oBAAA,CAAA;AAAA,IAAA,qBAAA,YAAA,OAAA,aAAA,MAAA;AAEF,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,aAAA,IAAA;AAGE,IAAA,oBAAA,CAAA;AAAA,IAAA,4BAAA,OAAA,aAAA,OAAA;;;;;;AAmCxC,IAAA,yBAAA,GAAA,OAAA,EAAA;AAGE,IAAA,qBAAA,SAAA,SAAA,uDAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAS,OAAA,aAAA,CAAc;IAAA,CAAA;AACxB,IAAA,uBAAA;;;AAIC,IAAO,mBAAP,MAAO,kBAAgB;EAyGP;EAxGX,SAAS;EACT,WAAW;EACV,eAAe,IAAI,aAAY;EAEzC,cAAc;EAEd,eAAe;IACb,QAAQ;IACR,MAAM;IACN,SAAS;;EAGX,kBAAoC;IAClC;MACE,IAAI;MACJ,OAAO;MACP,MAAM;MACN,OAAO;;IAET;MACE,IAAI;MACJ,OAAO;MACP,MAAM;MACN,OAAO;MACP,OAAO;;IAET;MACE,IAAI;MACJ,OAAO;MACP,MAAM;MACN,UAAU;QACR;UACE,IAAI;UACJ,OAAO;UACP,MAAM;UACN,OAAO;;QAET;UACE,IAAI;UACJ,OAAO;UACP,MAAM;UACN,OAAO;UACP,OAAO;;;;IAIb;MACE,IAAI;MACJ,OAAO;MACP,MAAM;MACN,OAAO;;IAET;MACE,IAAI;MACJ,OAAO;MACP,MAAM;MACN,OAAO;;IAET;MACE,IAAI;MACJ,OAAO;MACP,MAAM;MACN,OAAO;;IAET;MACE,IAAI;MACJ,OAAO;MACP,MAAM;MACN,UAAU;QACR;UACE,IAAI;UACJ,OAAO;UACP,MAAM;UACN,OAAO;;QAET;UACE,IAAI;UACJ,OAAO;UACP,MAAM;UACN,OAAO;UACP,OAAO;;QAET;UACE,IAAI;UACJ,OAAO;UACP,MAAM;UACN,OAAO;;;;IAIb;MACE,IAAI;MACJ,OAAO;MACP,MAAM;MACN,OAAO;;IAET;MACE,IAAI;MACJ,OAAO;MACP,MAAM;MACN,OAAO;;;EAIX,YAAoB,QAAc;AAAd,SAAA,SAAA;EAAiB;EAErC,eAAe,MAAoB;AACjC,QAAI,KAAK,aAAa;AACpB,WAAK,cAAc;IACrB;AACA,SAAK,aAAa,CAAC,KAAK;EAC1B;EAEA,eAAe,MAAoB;AACjC,QAAI,KAAK,UAAU;AACjB,WAAK,aAAY;IACnB;EACF;EAEA,iBAAc;AACZ,SAAK,cAAc,CAAC,KAAK;AAEzB,QAAI,KAAK,aAAa;AACpB,WAAK,gBAAgB,QAAQ,UAAO;AAClC,YAAI,KAAK,UAAU;AACjB,eAAK,aAAa;QACpB;MACF,CAAC;IACH;EACF;EAEA,eAAY;AACV,SAAK,aAAa,KAAI;EACxB;EAEA,WAAQ;AAEN,YAAQ,IAAI,iBAAiB;EAC/B;EAEA,eAAY;AAEV,YAAQ,IAAI,qBAAqB;EACnC;;qCAhJW,mBAAgB,4BAAA,MAAA,CAAA;EAAA;yEAAhB,mBAAgB,WAAA,CAAA,CAAA,aAAA,CAAA,GAAA,QAAA,EAAA,QAAA,UAAA,UAAA,WAAA,GAAA,SAAA,EAAA,cAAA,eAAA,GAAA,OAAA,IAAA,MAAA,GAAA,QAAA,CAAA,CAAA,GAAA,SAAA,GAAA,CAAA,GAAA,iBAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,UAAA,GAAA,CAAA,SAAA,YAAA,GAAA,SAAA,SAAA,GAAA,CAAA,SAAA,kBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,SAAA,OAAA,GAAA,CAAA,WAAA,aAAA,QAAA,QAAA,UAAA,gBAAA,GAAA,aAAA,GAAA,CAAA,SAAA,mBAAA,GAAA,SAAA,GAAA,MAAA,GAAA,CAAA,GAAA,UAAA,GAAA,CAAA,SAAA,YAAA,oBAAA,mBAAA,GAAA,cAAA,2BAAA,SAAA,GAAA,MAAA,GAAA,CAAA,SAAA,aAAA,GAAA,MAAA,GAAA,CAAA,oBAAA,mBAAA,GAAA,YAAA,GAAA,SAAA,cAAA,yBAAA,GAAA,CAAA,WAAA,aAAA,QAAA,QAAA,UAAA,gBAAA,GAAA,UAAA,GAAA,CAAA,kBAAA,SAAA,mBAAA,SAAA,gBAAA,GAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,SAAA,aAAA,GAAA,MAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,WAAA,GAAA,CAAA,GAAA,YAAA,oBAAA,GAAA,OAAA,GAAA,CAAA,SAAA,eAAA,WAAA,aAAA,QAAA,QAAA,UAAA,gBAAA,GAAA,MAAA,GAAA,CAAA,SAAA,eAAA,GAAA,MAAA,GAAA,CAAA,WAAA,aAAA,QAAA,QAAA,UAAA,gBAAA,GAAA,aAAA,GAAA,CAAA,UAAA,gBAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,SAAA,eAAA,GAAA,SAAA,SAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,oBAAA,sBAAA,GAAA,eAAA,GAAA,SAAA,YAAA,GAAA,CAAA,WAAA,aAAA,QAAA,QAAA,UAAA,gBAAA,GAAA,aAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,SAAA,gBAAA,GAAA,MAAA,GAAA,CAAA,GAAA,cAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,kBAAA,GAAA,CAAA,GAAA,YAAA,GAAA,CAAA,GAAA,aAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,GAAA,eAAA,GAAA,CAAA,GAAA,gBAAA,GAAA,CAAA,SAAA,wBAAA,GAAA,iBAAA,GAAA,OAAA,GAAA,CAAA,WAAA,aAAA,QAAA,QAAA,UAAA,gBAAA,GAAA,aAAA,GAAA,CAAA,MAAA,MAAA,MAAA,MAAA,KAAA,IAAA,GAAA,CAAA,KAAA,sCAAA,GAAA,CAAA,MAAA,MAAA,MAAA,MAAA,MAAA,SAAA,MAAA,IAAA,GAAA,CAAA,SAAA,iBAAA,GAAA,iBAAA,GAAA,OAAA,GAAA,CAAA,KAAA,+DAAA,GAAA,CAAA,GAAA,mBAAA,GAAA,OAAA,CAAA,GAAA,UAAA,SAAA,0BAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAjHzB,MAAA,yBAAA,GAAA,SAAA,CAAA,EAA6F,GAAA,OAAA,CAAA,EAC9D,GAAA,OAAA,CAAA,EAEF,GAAA,MAAA,CAAA;AAErB,MAAA,qBAAA,GAAA,gCAAA,GAAA,GAAA,MAAA,CAAA;AAqDF,MAAA,uBAAA,EAAK;AAIP,MAAA,qBAAA,GAAA,iCAAA,IAAA,GAAA,OAAA,CAAA;AA8BA,MAAA,yBAAA,GAAA,UAAA,CAAA;AAEE,MAAA,qBAAA,SAAA,SAAA,oDAAA;AAAA,eAAS,IAAA,eAAA;MAAgB,CAAA;;AAGzB,MAAA,yBAAA,GAAA,OAAA,CAAA;AACE,MAAA,oBAAA,GAAA,UAAA;AACF,MAAA,uBAAA,EAAM,EACC,EACL;AAIR,MAAA,qBAAA,GAAA,iCAAA,GAAA,GAAA,OAAA,CAAA;;;AAzGuB,MAAA,sBAAA,gBAAA,IAAA,MAAA,EAA6B,qBAAA,IAAA,WAAA;AAKN,MAAA,oBAAA,CAAA;AAAA,MAAA,qBAAA,WAAA,IAAA,eAAA;AAyDb,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,CAAA,IAAA,WAAA;AAiC3B,MAAA,oBAAA;AAAA,MAAA,qBAAA,SAAA,IAAA,cAAA,mBAAA,kBAAA;AAGY,MAAA,oBAAA,CAAA;;AASf,MAAA,oBAAA;AAAA,MAAA,qBAAA,QAAA,IAAA,UAAA,IAAA,QAAA;;oBA7GK,cAAY,SAAA,MAAE,cAAY,YAAA,gBAAA,GAAA,QAAA,CAAA,yySAAA,EAAA,CAAA;;;sEAmHzB,kBAAgB,CAAA;UAtH5B;uBACW,eAAa,YACX,MAAI,SACP,CAAC,cAAc,YAAY,GAAC,UAC3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA+GT,QAAA,CAAA,okPAAA,EAAA,CAAA;kCAIQ,QAAM,CAAA;UAAd;MACQ,UAAQ,CAAA;UAAhB;MACS,cAAY,CAAA;UAArB;;;;6EAHU,kBAAgB,EAAA,WAAA,oBAAA,UAAA,oDAAA,YAAA,IAAA,CAAA;AAAA,GAAA;;;;;;AClHrB,IAAA,yBAAA,GAAA,cAAA,CAAA;AAEE,IAAA,qBAAA,iBAAA,SAAA,iFAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAiB,OAAA,cAAA,CAAe;IAAA,CAAA,EAAC,mBAAA,SAAA,iFAAA,QAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBACd,OAAA,SAAA,MAAA,CAAgB;IAAA,CAAA;AACpC,IAAA,uBAAA;AAGD,IAAA,yBAAA,GAAA,eAAA,CAAA;AAGE,IAAA,qBAAA,gBAAA,SAAA,iFAAA;AAAA,MAAA,wBAAA,GAAA;AAAA,YAAA,SAAA,wBAAA;AAAA,aAAA,sBAAgB,OAAA,aAAA,CAAc;IAAA,CAAA;AAC/B,IAAA,uBAAA;AAGD,IAAA,yBAAA,GAAA,QAAA,CAAA,EAA2E,GAAA,OAAA,CAAA;AAEvE,IAAA,oBAAA,GAAA,eAAA;AACF,IAAA,uBAAA,EAAM;;;;AAhBN,IAAA,qBAAA,eAAA,OAAA,WAAA;AAOA,IAAA,oBAAA;AAAA,IAAA,qBAAA,UAAA,OAAA,WAAA,EAAsB,YAAA,OAAA,QAAA;AAMG,IAAA,oBAAA;AAAA,IAAA,sBAAA,gBAAA,OAAA,eAAA,CAAA,OAAA,QAAA;;;;;AAO3B,IAAA,yBAAA,GAAA,OAAA,CAAA,EAA6B,GAAA,OAAA,CAAA;AAEzB,IAAA,oBAAA,GAAA,OAAA,CAAA;AACA,IAAA,yBAAA,GAAA,KAAA,CAAA;AAAwB,IAAA,iBAAA,GAAA,YAAA;AAAU,IAAA,uBAAA,EAAI,EAClC;;;;;AAIR,IAAA,yBAAA,GAAA,QAAA,CAAA;AACE,IAAA,oBAAA,GAAA,eAAA;AACF,IAAA,uBAAA;;;;;AAKA,IAAA,yBAAA,GAAA,OAAA,CAAA,EAA6B,GAAA,OAAA,CAAA;AAEzB,IAAA,oBAAA,GAAA,OAAA,CAAA;AACA,IAAA,yBAAA,GAAA,KAAA,CAAA;AAAwB,IAAA,iBAAA,GAAA,YAAA;AAAU,IAAA,uBAAA,EAAI,EAClC;;;AAOV,IAAO,sBAAP,MAAO,qBAAmB;EAUC;EACtB;EACC;EAXV,cAAc;EACd,WAAW;EACX,YAAY;EACZ,kBAAkB;EAClB,qBAAqB;EAEb,WAAW,IAAI,QAAO;EAE9B,YAC+B,YACtB,aACC,QAAc;AAFO,SAAA,aAAA;AACtB,SAAA,cAAA;AACC,SAAA,SAAA;AAGR,SAAK,kBAAkB,KAAK,YAAY,gBAAe;AAGvD,SAAK,YAAY,aACd,KACC,UAAU,KAAK,QAAQ,GACvB,IAAI,MAAM,KAAK,qBAAqB,KAAK,CAAC,EAE3C,UAAU,UAAO;AAChB,WAAK,kBAAkB,CAAC,CAAC;AACzB,UAAI,CAAC,QAAQ,CAAC,KAAK,oBAAoB;AACrC,aAAK,OAAO,SAAS,CAAC,aAAa,CAAC;MACtC;IACF,CAAC;AAGH,SAAK,YAAY,WACd,KAAK,UAAU,KAAK,QAAQ,CAAC,EAC7B,UAAU,aAAU;AACnB,WAAK,YAAY;IACnB,CAAC;EACL;EAEA,WAAQ;AACN,SAAK,gBAAe;AACpB,SAAK,kBAAiB;EACxB;EAEA,cAAW;AACT,SAAK,SAAS,KAAI;AAClB,SAAK,SAAS,SAAQ;EACxB;EAGA,SAAS,OAAU;AACjB,SAAK,gBAAe;EACtB;EAEQ,kBAAe;AACrB,QAAI,CAAC,kBAAkB,KAAK,UAAU,GAAG;AACvC;IACF;AAEA,UAAM,mBAAmB,KAAK;AAC9B,SAAK,WAAW,OAAO,aAAa;AAGpC,QAAI,oBAAoB,CAAC,KAAK,YAAY,KAAK,iBAAiB;AAC9D,WAAK,cAAc;IACrB,WAES,CAAC,oBAAoB,KAAK,UAAU;AAC3C,WAAK,cAAc;IACrB;EACF;EAEQ,oBAAiB;AACvB,QAAI,CAAC,kBAAkB,KAAK,UAAU,GAAG;AACvC;IACF;AAGA,QAAI,KAAK,UAAU;AACjB,WAAK,cAAc;AACnB;IACF;AAGA,UAAM,oBAAoB,aAAa,QAAQ,aAAa;AAC5D,QAAI,sBAAsB,MAAM;AAC9B,WAAK,cAAc,KAAK,MAAM,iBAAiB;IACjD;EACF;EAEA,gBAAa;AACX,SAAK,cAAc,CAAC,KAAK;AACzB,SAAK,iBAAgB;EACvB;EAEA,eAAY;AACV,SAAK,cAAc;AACnB,SAAK,iBAAgB;EACvB;EAEQ,mBAAgB;AACtB,QAAI,CAAC,kBAAkB,KAAK,UAAU,GAAG;AACvC;IACF;AAGA,QAAI,CAAC,KAAK,UAAU;AAClB,mBAAa,QAAQ,eAAe,KAAK,UAAU,KAAK,WAAW,CAAC;IACtE;EACF;EAEA,SAAS,YAAkB;AAEzB,YAAQ,IAAI,kBAAkB,UAAU;EAC1C;EAEA,WAAW,SAAgB;AACzB,SAAK,YAAY;EACnB;;qCArHW,sBAAmB,4BAUpB,WAAW,GAAA,4BAAA,WAAA,GAAA,4BAAA,MAAA,CAAA;EAAA;yEAVV,sBAAmB,WAAA,CAAA,CAAA,iBAAA,CAAA,GAAA,cAAA,SAAA,iCAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAAnB,MAAA,qBAAA,UAAA,SAAA,8CAAA,QAAA;AAAA,eAAA,IAAA,SAAA,MAAA;MAAgB,GAAA,OAAA,yBAAA;;;;AAlDzB,MAAA,yBAAA,GAAA,OAAA,CAAA;AACE,MAAA,qBAAA,GAAA,4CAAA,GAAA,CAAA,EAAuB,GAAA,4CAAA,GAAA,GAAA,OAAA,CAAA,EAqBU,GAAA,4CAAA,GAAA,GAAA,QAAA,CAAA,EAQxB,GAAA,4CAAA,GAAA,GAAA,OAAA,CAAA;AAgBX,MAAA,uBAAA;;;AA7CE,MAAA,oBAAA;AAAA,MAAA,wBAAA,IAAA,kBAAA,IAAA,IAAA,qBAAA,IAAA,CAAA;AAqCA,MAAA,oBAAA,CAAA;AAAA,MAAA,wBAAA,IAAA,YAAA,IAAA,EAAA;;oBAxCM,cAAc,cAAc,iBAAiB,gBAAgB,GAAA,QAAA,CAAA,q9EAAA,EAAA,CAAA;;;sEAoD5D,qBAAmB,CAAA;UAvD/B;uBACW,mBAAiB,YACf,MAAI,SACP,CAAC,cAAc,cAAc,iBAAiB,gBAAgB,GAAC,UAC9D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAgDT,QAAA,CAAA,isEAAA,EAAA,CAAA;;UAaE;WAAO,WAAW;oDAuCrB,UAAQ,CAAA;UADP;WAAa,iBAAiB,CAAC,QAAQ,CAAC;;;;6EAhD9B,qBAAmB,EAAA,WAAA,uBAAA,UAAA,4DAAA,YAAA,GAAA,CAAA;AAAA,GAAA;;;ACvD1B,IAAO,eAAP,MAAO,cAAY;EACvB,QAAQ;;qCADG,eAAY;EAAA;yEAAZ,eAAY,WAAA,CAAA,CAAA,UAAA,CAAA,GAAA,OAAA,GAAA,MAAA,GAAA,UAAA,SAAA,sBAAA,IAAA,KAAA;AAAA,QAAA,KAAA,GAAA;AAHZ,MAAA,oBAAA,GAAA,iBAAA;;oBADD,mBAAmB,GAAA,eAAA,EAAA,CAAA;;;sEAIlB,cAAY,CAAA;UAPxB;uBACW,YAAU,YACR,MAAI,SACP,CAAC,mBAAmB,GAAC,UACpB,sCAAqC,CAAA;;;;6EAGpC,cAAY,EAAA,WAAA,gBAAA,UAAA,4BAAA,YAAA,GAAA,CAAA;AAAA,GAAA;;;ACNzB,qBAAqB,cAAc,SAAS,EACzC,MAAM,CAAC,QAAQ,QAAQ,MAAM,GAAG,CAAC;", "names": []}