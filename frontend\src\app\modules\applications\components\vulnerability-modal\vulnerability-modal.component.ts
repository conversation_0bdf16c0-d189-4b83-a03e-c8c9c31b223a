import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { SlideModalComponent } from '../../../../shared/components/slide-modal/slide-modal.component';
import { FormInputComponent } from '../../../../shared/components/form-input/form-input.component';

export interface VulnerabilityFormData {
  title: string;
  severity: string;
  cveId: string;
  cvssScore: number;
  status: string;
  description: string;
}

@Component({
  selector: 'app-vulnerability-modal',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, SlideModalComponent, FormInputComponent],
  template: `
    <app-slide-modal
      [isOpen]="isOpen"
      [title]="editMode ? 'Edit Vulnerability' : 'Add Vulnerability'"
      subtitle="Configure security vulnerability details"
      [loading]="loading"
      [canConfirm]="vulnerabilityForm.valid"
      confirmText="Save Vulnerability"
      (closed)="onClose()"
      (confirmed)="onSave()"
      (cancelled)="onClose()"
    >
      <form [formGroup]="vulnerabilityForm" class="vulnerability-form">
        <div class="form-grid">
          <app-form-input
            label="Vulnerability Title"
            placeholder="e.g., SQL Injection in User Search"
            formControlName="title"
            [required]="true"
            [errorMessage]="getFieldError('title')"
          ></app-form-input>

          <div class="form-group">
            <label class="form-label">Severity</label>
            <select formControlName="severity" class="form-select">
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="critical">Critical</option>
            </select>
          </div>

          <app-form-input
            label="CVE ID (Optional)"
            placeholder="e.g., CVE-2024-0001"
            formControlName="cveId"
            [errorMessage]="getFieldError('cveId')"
          ></app-form-input>

          <app-form-input
            label="CVSS Score"
            placeholder="e.g., 7.5"
            formControlName="cvssScore"
            type="number"
            [errorMessage]="getFieldError('cvssScore')"
          ></app-form-input>

          <div class="form-group">
            <label class="form-label">Status</label>
            <select formControlName="status" class="form-select">
              <option value="open">Open</option>
              <option value="in_progress">In Progress</option>
              <option value="resolved">Resolved</option>
              <option value="false_positive">False Positive</option>
              <option value="accepted_risk">Accepted Risk</option>
            </select>
          </div>

          <div class="form-group full-width">
            <label class="form-label">Description</label>
            <textarea 
              formControlName="description"
              class="form-textarea"
              placeholder="Describe the vulnerability, its impact, and potential remediation steps..."
              rows="4"
            ></textarea>
          </div>
        </div>
      </form>
    </app-slide-modal>
  `,
  styles: [`
    .vulnerability-form {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-lg);
    }

    .form-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-lg);
    }

    .form-group {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-sm);
    }

    .form-group.full-width {
      grid-column: 1 / -1;
    }

    .form-label {
      font-size: var(--text-sm);
      font-weight: 500;
      color: var(--secondary-700);
    }

    .form-select,
    .form-textarea {
      padding: var(--spacing-md);
      border: 1px solid var(--secondary-300);
      border-radius: var(--radius-md);
      background: white;
      font-size: var(--text-sm);
      color: var(--secondary-800);
      transition: all 0.2s ease;

      &:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px var(--primary-100);
      }
    }

    .form-textarea {
      resize: vertical;
      min-height: 100px;
      font-family: inherit;
    }

    @media (max-width: 768px) {
      .form-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
      }
    }
  `]
})
export class VulnerabilityModalComponent implements OnInit {
  @Input() isOpen = false;
  @Input() editMode = false;
  @Input() initialData: VulnerabilityFormData | null = null;
  @Input() loading = false;

  @Output() closed = new EventEmitter<void>();
  @Output() saved = new EventEmitter<VulnerabilityFormData>();

  vulnerabilityForm!: FormGroup;

  constructor(private fb: FormBuilder) {}

  ngOnInit(): void {
    this.initializeForm();
  }

  ngOnChanges(): void {
    if (this.vulnerabilityForm && this.initialData) {
      this.vulnerabilityForm.patchValue(this.initialData);
    }
  }

  private initializeForm(): void {
    this.vulnerabilityForm = this.fb.group({
      title: ['', [Validators.required, Validators.minLength(5)]],
      severity: ['medium', [Validators.required]],
      cveId: [''],
      cvssScore: [0, [Validators.min(0), Validators.max(10)]],
      status: ['open', [Validators.required]],
      description: ['', [Validators.required, Validators.minLength(10)]]
    });

    if (this.initialData) {
      this.vulnerabilityForm.patchValue(this.initialData);
    }
  }

  getFieldError(fieldName: string): string {
    const field = this.vulnerabilityForm.get(fieldName);
    if (field && field.invalid && field.touched) {
      if (field.errors?.['required']) {
        return `${fieldName} is required`;
      }
      if (field.errors?.['minlength']) {
        return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;
      }
      if (field.errors?.['min']) {
        return `${fieldName} must be at least ${field.errors['min'].min}`;
      }
      if (field.errors?.['max']) {
        return `${fieldName} must be at most ${field.errors['max'].max}`;
      }
    }
    return '';
  }

  onClose(): void {
    this.vulnerabilityForm.reset();
    this.closed.emit();
  }

  onSave(): void {
    if (this.vulnerabilityForm.valid) {
      this.saved.emit(this.vulnerabilityForm.value);
    }
  }
}
