import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { Observable, of } from 'rxjs';
import { Dependency } from '../models/dependency.model';
import { DependencyService } from '../services/dependency.service';
import { DependencyFormComponent } from '../dependency-form/dependency-form.component';

@Component({
  selector: 'app-dependency-edit',
  templateUrl: './dependency-edit.component.html',
  styleUrls: ['./dependency-edit.component.scss'],
  imports: [CommonModule, DependencyFormComponent],
  standalone: true
})
export class DependencyEditComponent implements OnInit {
  dependency$: Observable<Dependency | undefined>;
  
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private dependencyService: DependencyService
  ) {
    this.dependency$ = of(undefined);
  }
  
  ngOnInit(): void {
    this.route.paramMap.subscribe(params => {
      const id = params.get('id');
      if (id) {
        this.dependency$ = this.dependencyService.getDependencyById(id);
      }
    });
  }
  
  onFormSubmit(dependencyData: Omit<Dependency, 'id'>): void {
    this.route.paramMap.subscribe(params => {
      const id = params.get('id');
      if (id) {
        const updatedDependency: Dependency = {
          ...dependencyData,
          id: id
        };
        
        this.dependencyService.updateDependency(updatedDependency).subscribe({
          next: (updatedDep) => {
            this.router.navigate(['/dependencies', updatedDep.id]);
          },
          error: (err) => {
            console.error('Failed to update dependency:', err);
          }
        });
      }
    });
  }
  
  onCancel(): void {
    this.route.paramMap.subscribe(params => {
      const id = params.get('id');
      if (id) {
        this.router.navigate(['/dependencies', id]);
      } else {
        this.router.navigate(['/dependencies']);
      }
    });
  }
}