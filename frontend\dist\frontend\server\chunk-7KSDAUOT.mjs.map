{"version": 3, "sources": ["src/app/modules/auth/services/auth.service.ts"], "sourcesContent": ["import { Injectable, Inject, PLATFORM_ID } from '@angular/core';\r\nimport { Router } from '@angular/router';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { BehaviorSubject, Observable, of } from 'rxjs';\r\nimport { map, tap, catchError, delay } from 'rxjs/operators';\r\nimport { environment } from '../../../../environments/environment';\r\nimport { BaseApiService } from '../../../shared/services/base-api.service';\r\nimport { isPlatformBrowser } from '@angular/common';\r\n\r\nexport interface User {\r\n  id: string;\r\n  email: string;\r\n  name: string;\r\n  role: string;\r\n}\r\n\r\nexport interface LoginCredentials {\r\n  email: string;\r\n  password: string;\r\n  rememberMe?: boolean;\r\n}\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class AuthService {\r\n  private currentUserSubject = new BehaviorSubject<User | null>(null);\r\n  public currentUser$ = this.currentUserSubject.asObservable();\r\n  private isLoadingSubject = new BehaviorSubject<boolean>(false);\r\n  public isLoading$ = this.isLoadingSubject.asObservable();\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private baseApi: BaseApiService,\r\n    @Inject(PLATFORM_ID) private platformId: Object\r\n  ) {\r\n    this.loadUserFromStorage();\r\n  }\r\n\r\n  private loadUserFromStorage() {\r\n    if (!isPlatformBrowser(this.platformId)) return;\r\n    const userStr = localStorage.getItem('user') || sessionStorage.getItem('user');\r\n    if (userStr) {\r\n      try {\r\n        const user = JSON.parse(userStr);\r\n        this.currentUserSubject.next(user);\r\n      } catch (e) {\r\n        this.logout();\r\n      }\r\n    }\r\n  }\r\n  login(credentials: LoginCredentials): Observable<User> {\r\n    this.isLoadingSubject.next(true);\r\n    \r\n    // TODO: Replace with real API call\r\n    // return this.baseApi.post<{ user: User; token: string }>('auth/login', credentials)\r\n    const mockResponse: { user: User; token: string } = {\r\n      user: {\r\n        id: '1',\r\n        email: credentials.email,\r\n        name: 'John Doe',\r\n        role: 'admin'\r\n      },\r\n      token: 'mock-jwt-token'\r\n    };\r\n    \r\n    return of(mockResponse).pipe(\r\n      delay(1000),\r\n      map(response => {\r\n        const storage = credentials.rememberMe ? localStorage : sessionStorage;\r\n        if (isPlatformBrowser(this.platformId)) {\r\n          storage.setItem('auth_token', response.token);\r\n          storage.setItem('user', JSON.stringify(response.user));\r\n        }\r\n        this.currentUserSubject.next(response.user);\r\n        return response.user;\r\n      }),\r\n      tap(() => {\r\n        this.isLoadingSubject.next(false);\r\n        this.router.navigate(['/dashboard']);\r\n      }),\r\n      catchError(error => {\r\n        this.isLoadingSubject.next(false);\r\n        throw error;\r\n      })\r\n    );\r\n  }\r\n\r\n  logout(): void {\r\n    if (isPlatformBrowser(this.platformId)) {\r\n      localStorage.removeItem('auth_token');\r\n      localStorage.removeItem('user');\r\n      sessionStorage.removeItem('auth_token');\r\n      sessionStorage.removeItem('user');\r\n    }\r\n    this.currentUserSubject.next(null);\r\n    this.router.navigate(['/auth/login']);\r\n  }\r\n\r\n  isAuthenticated(): boolean {\r\n    return !!this.currentUserSubject.value;\r\n  }\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAyBM,IAAO,cAAP,MAAO,aAAW;EAOZ;EACA;EACqB;EARvB,qBAAqB,IAAI,gBAA6B,IAAI;EAC3D,eAAe,KAAK,mBAAmB,aAAY;EAClD,mBAAmB,IAAI,gBAAyB,KAAK;EACtD,aAAa,KAAK,iBAAiB,aAAY;EAEtD,YACU,QACA,SACqB,YAAkB;AAFvC,SAAA,SAAA;AACA,SAAA,UAAA;AACqB,SAAA,aAAA;AAE7B,SAAK,oBAAmB;EAC1B;EAEQ,sBAAmB;AACzB,QAAI,CAAC,kBAAkB,KAAK,UAAU;AAAG;AACzC,UAAM,UAAU,aAAa,QAAQ,MAAM,KAAK,eAAe,QAAQ,MAAM;AAC7E,QAAI,SAAS;AACX,UAAI;AACF,cAAM,OAAO,KAAK,MAAM,OAAO;AAC/B,aAAK,mBAAmB,KAAK,IAAI;MACnC,SAAS,GAAG;AACV,aAAK,OAAM;MACb;IACF;EACF;EACA,MAAM,aAA6B;AACjC,SAAK,iBAAiB,KAAK,IAAI;AAI/B,UAAM,eAA8C;MAClD,MAAM;QACJ,IAAI;QACJ,OAAO,YAAY;QACnB,MAAM;QACN,MAAM;;MAER,OAAO;;AAGT,WAAO,GAAG,YAAY,EAAE,KACtB,MAAM,GAAI,GACV,IAAI,cAAW;AACb,YAAM,UAAU,YAAY,aAAa,eAAe;AACxD,UAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,gBAAQ,QAAQ,cAAc,SAAS,KAAK;AAC5C,gBAAQ,QAAQ,QAAQ,KAAK,UAAU,SAAS,IAAI,CAAC;MACvD;AACA,WAAK,mBAAmB,KAAK,SAAS,IAAI;AAC1C,aAAO,SAAS;IAClB,CAAC,GACD,IAAI,MAAK;AACP,WAAK,iBAAiB,KAAK,KAAK;AAChC,WAAK,OAAO,SAAS,CAAC,YAAY,CAAC;IACrC,CAAC,GACD,WAAW,WAAQ;AACjB,WAAK,iBAAiB,KAAK,KAAK;AAChC,YAAM;IACR,CAAC,CAAC;EAEN;EAEA,SAAM;AACJ,QAAI,kBAAkB,KAAK,UAAU,GAAG;AACtC,mBAAa,WAAW,YAAY;AACpC,mBAAa,WAAW,MAAM;AAC9B,qBAAe,WAAW,YAAY;AACtC,qBAAe,WAAW,MAAM;IAClC;AACA,SAAK,mBAAmB,KAAK,IAAI;AACjC,SAAK,OAAO,SAAS,CAAC,aAAa,CAAC;EACtC;EAEA,kBAAe;AACb,WAAO,CAAC,CAAC,KAAK,mBAAmB;EACnC;;qCA5EW,cAAW,mBAAA,MAAA,GAAA,mBAAA,cAAA,GAAA,mBASZ,WAAW,CAAA;EAAA;4EATV,cAAW,SAAX,aAAW,WAAA,YAFV,OAAM,CAAA;;;sEAEP,aAAW,CAAA;UAHvB;WAAW;MACV,YAAY;KACb;;UAUI;WAAO,WAAW;;;", "names": []}