import { Injectable } from '@angular/core';
import { HttpClient, HttpParams, HttpHeaders, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError, BehaviorSubject } from 'rxjs';
import { catchError, map, retry, timeout } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
  errors?: string[];
  pagination?: PaginationInfo;
}

export interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalItems: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

export interface QueryParams {
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  search?: string;
  filters?: { [key: string]: any };
}

@Injectable({
  providedIn: 'root'
})
export class BaseApiService {
  private readonly baseUrl = environment.apiUrl || 'http://localhost:5000/api';
  private readonly defaultTimeout = 30000; // 30 seconds
  private readonly maxRetries = 3;

  private loadingSubject = new BehaviorSubject<boolean>(false);
  public loading$ = this.loadingSubject.asObservable();

  constructor(private http: HttpClient) {}

  /**
   * GET request with error handling and loading state
   */
  get<T>(endpoint: string, params?: QueryParams): Observable<ApiResponse<T>> {
    this.setLoading(true);

    const httpParams = this.buildHttpParams(params);
    const options = {
      params: httpParams,
      headers: this.getHeaders()
    };

    return this.http.get<ApiResponse<T>>(`${this.baseUrl}/${endpoint}`, options)
      .pipe(
        timeout(this.defaultTimeout),
        retry(this.maxRetries),
        map(response => this.handleSuccess(response)),
        catchError(error => this.handleError(error))
      );
  }

  /**
   * POST request with error handling and loading state
   */
  post<T>(endpoint: string, data: any): Observable<ApiResponse<T>> {
    this.setLoading(true);

    const options = {
      headers: this.getHeaders()
    };

    return this.http.post<ApiResponse<T>>(`${this.baseUrl}/${endpoint}`, data, options)
      .pipe(
        timeout(this.defaultTimeout),
        map(response => this.handleSuccess(response)),
        catchError(error => this.handleError(error))
      );
  }

  /**
   * PUT request with error handling and loading state
   */
  put<T>(endpoint: string, data: any): Observable<ApiResponse<T>> {
    this.setLoading(true);

    const options = {
      headers: this.getHeaders()
    };

    return this.http.put<ApiResponse<T>>(`${this.baseUrl}/${endpoint}`, data, options)
      .pipe(
        timeout(this.defaultTimeout),
        map(response => this.handleSuccess(response)),
        catchError(error => this.handleError(error))
      );
  }

  /**
   * PATCH request with error handling and loading state
   */
  patch<T>(endpoint: string, data: any): Observable<ApiResponse<T>> {
    this.setLoading(true);

    const options = {
      headers: this.getHeaders()
    };

    return this.http.patch<ApiResponse<T>>(`${this.baseUrl}/${endpoint}`, data, options)
      .pipe(
        timeout(this.defaultTimeout),
        map(response => this.handleSuccess(response)),
        catchError(error => this.handleError(error))
      );
  }

  /**
   * DELETE request with error handling and loading state
   */
  delete<T>(endpoint: string): Observable<ApiResponse<T>> {
    this.setLoading(true);

    const options = {
      headers: this.getHeaders()
    };

    return this.http.delete<ApiResponse<T>>(`${this.baseUrl}/${endpoint}`, options)
      .pipe(
        timeout(this.defaultTimeout),
        map(response => this.handleSuccess(response)),
        catchError(error => this.handleError(error))
      );
  }

  /**
   * Upload file with progress tracking
   */
  uploadFile<T>(endpoint: string, file: File, additionalData?: any): Observable<ApiResponse<T>> {
    this.setLoading(true);

    const formData = new FormData();
    formData.append('file', file);

    if (additionalData) {
      Object.keys(additionalData).forEach(key => {
        formData.append(key, additionalData[key]);
      });
    }

    const headers = new HttpHeaders();
    // Don't set Content-Type header for FormData, let browser set it with boundary

    return this.http.post<ApiResponse<T>>(`${this.baseUrl}/${endpoint}`, formData, { headers })
      .pipe(
        timeout(60000), // Longer timeout for file uploads
        map(response => this.handleSuccess(response)),
        catchError(error => this.handleError(error))
      );
  }

  /**
   * Download file
   */
  downloadFile(endpoint: string, filename?: string): Observable<Blob> {
    this.setLoading(true);

    const options = {
      headers: this.getHeaders(),
      responseType: 'blob' as 'json'
    };

    return this.http.get(`${this.baseUrl}/${endpoint}`, options)
      .pipe(
        timeout(60000), // Longer timeout for downloads
        map((blob: any) => {
          this.setLoading(false);
          return blob as Blob;
        }),
        catchError(error => this.handleError(error))
      );
  }

  /**
   * Build HTTP parameters from query params
   */
  private buildHttpParams(params?: QueryParams): HttpParams {
    let httpParams = new HttpParams();

    if (params) {
      if (params.page !== undefined) {
        httpParams = httpParams.set('page', params.page.toString());
      }
      if (params.pageSize !== undefined) {
        httpParams = httpParams.set('pageSize', params.pageSize.toString());
      }
      if (params.sortBy) {
        httpParams = httpParams.set('sortBy', params.sortBy);
      }
      if (params.sortOrder) {
        httpParams = httpParams.set('sortOrder', params.sortOrder);
      }
      if (params.search) {
        httpParams = httpParams.set('search', params.search);
      }
      if (params.filters) {
        Object.keys(params.filters).forEach(key => {
          const value = params.filters![key];
          if (value !== null && value !== undefined && value !== '') {
            if (Array.isArray(value)) {
              value.forEach(v => {
                httpParams = httpParams.append(`filters.${key}`, v.toString());
              });
            } else {
              httpParams = httpParams.set(`filters.${key}`, value.toString());
            }
          }
        });
      }
    }

    return httpParams;
  }

  /**
   * Get default headers
   */
  private getHeaders(): HttpHeaders {
    let headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    });

    // Add authorization header if token exists
    const token = this.getAuthToken();
    if (token) {
      headers = headers.set('Authorization', `Bearer ${token}`);
    }

    return headers;
  }

  /**
   * Get authentication token from storage
   */
  private getAuthToken(): string | null {
    return localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token');
  }

  /**
   * Handle successful response
   */
  private handleSuccess<T>(response: ApiResponse<T>): ApiResponse<T> {
    this.setLoading(false);
    return response;
  }

  /**
   * Handle error response
   */
  private handleError(error: HttpErrorResponse): Observable<never> {
    this.setLoading(false);

    let errorMessage = 'An unexpected error occurred';

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Client Error: ${error.error.message}`;
    } else {
      // Server-side error
      switch (error.status) {
        case 400:
          errorMessage = 'Bad Request: Please check your input';
          break;
        case 401:
          errorMessage = 'Unauthorized: Please log in again';
          // Optionally redirect to login
          break;
        case 403:
          errorMessage = 'Forbidden: You do not have permission to perform this action';
          break;
        case 404:
          errorMessage = 'Not Found: The requested resource was not found';
          break;
        case 409:
          errorMessage = 'Conflict: The resource already exists or there is a conflict';
          break;
        case 422:
          errorMessage = 'Validation Error: Please check your input';
          break;
        case 500:
          errorMessage = 'Internal Server Error: Please try again later';
          break;
        case 503:
          errorMessage = 'Service Unavailable: The server is temporarily unavailable';
          break;
        default:
          errorMessage = `Server Error: ${error.status} - ${error.message}`;
      }

      // If the server provides a custom error message, use it
      if (error.error && error.error.message) {
        errorMessage = error.error.message;
      }
    }

    console.error('API Error:', error);
    return throwError(() => new Error(errorMessage));
  }

  /**
   * Set loading state
   */
  private setLoading(loading: boolean): void {
    this.loadingSubject.next(loading);
  }

  /**
   * Get current loading state
   */
  get isLoading(): boolean {
    return this.loadingSubject.value;
  }
}
 