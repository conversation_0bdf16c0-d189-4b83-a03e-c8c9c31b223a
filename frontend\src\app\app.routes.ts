import { Routes } from '@angular/router';
import { authGuard } from './modules/auth/guards/auth.guard';

export const routes: Routes = [
  {
    path: '',
    redirectTo: '/dashboard',
    pathMatch: 'full'
  },
  {
    path: 'auth',
    loadChildren: () => import('./modules/auth/auth.routes').then(m => m.routes)
  },
  {
    path: 'dashboard',
    canActivate: [authGuard],
    loadComponent: () => import('./modules/dashboard/dashboard.component').then(m => m.DashboardComponent)
  },
  {
    path: 'applications',
    canActivate: [authGuard],
    loadChildren: () => import('./modules/applications/applications.routes').then(m => m.routes)
  },
  {
    path: 'dependencies',
    canActivate: [authGuard],
    loadChildren: () => import('./modules/dependencies/dependencies.routes').then(m => m.routes)
  },
  {
    path: 'tech-stack',
    canActivate: [authGuard],
    loadChildren: () => import('./modules/tech-stack/tech-stack.routes').then(m => m.routes)
  },
  {    path: 'stakeholders',
    canActivate: [authGuard],
    loadChildren: () => import('./modules/stakeholders/stakeholders.routes').then(m => m.routes)
  },
  {
    path: 'documentation',
    canActivate: [authGuard],
    loadChildren: () => import('./modules/documentation/documentation.routes').then(m => m.routes)
  },
  {
    path: 'security',
    canActivate: [authGuard],
    loadChildren: () => import('./modules/security/security.routes').then(m => m.routes)
  },
  {
    path: 'reports',
    canActivate: [authGuard],
    loadComponent: () => import('./modules/reports/reports.component').then(m => m.ReportsComponent)
  },
  {
    path: 'settings',
    canActivate: [authGuard],
    loadComponent: () => import('./modules/settings/settings.component').then(m => m.SettingsComponent)  },
  {
    path: '**',
    redirectTo: '/dashboard'
  }
];
