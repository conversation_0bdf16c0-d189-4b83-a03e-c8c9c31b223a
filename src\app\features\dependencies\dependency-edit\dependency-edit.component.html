<div class="container-fluid" *ngIf="(dependency$ | async) as dependency; else loading">
  <div class="row mb-4">
    <div class="col">
      <button class="btn btn-outline-secondary mb-3" (click)="onCancel()">
        <i class="bi bi-arrow-left me-2"></i>Back to Dependency
      </button>
      <h1>Edit Dependency: {{ dependency.name }}</h1>
    </div>
  </div>
  
  <div class="row">
    <div class="col">
      <app-dependency-form 
        [dependency]="dependency" 
        (formSubmit)="onFormSubmit($event)">
      </app-dependency-form>
    </div>
  </div>
</div>

<ng-template #loading>
  <div class="container-fluid">
    <div class="row">
      <div class="col text-center py-5">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-3">Loading dependency details...</p>
      </div>
    </div>
  </div>
</ng-template>