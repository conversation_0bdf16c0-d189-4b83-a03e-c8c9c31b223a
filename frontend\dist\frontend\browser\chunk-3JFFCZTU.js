import {
  ApplicationSelectionService
} from "./chunk-7XI6LZQT.js";
import {
  TableComponent
} from "./chunk-QQYNLSVO.js";
import {
  ChartComponent
} from "./chunk-HCVU2C6O.js";
import {
  FormInputComponent
} from "./chunk-WQFVHIJL.js";
import {
  SlideModalComponent
} from "./chunk-6DLBFET6.js";
import {
  Default<PERSON><PERSON>ueAccessor,
  FormBuilder,
  FormControlName,
  FormGroupDirective,
  NgControlStatus,
  NgControlStatusGroup,
  NgSelectOption,
  ReactiveFormsModule,
  RequiredValidator,
  SelectControlValueAccessor,
  Validators,
  ɵNgNoValidate,
  ɵNgSelectMultipleOption
} from "./chunk-SM75SJZE.js";
import {
  CardComponent
} from "./chunk-SLWZQAXJ.js";
import {
  ButtonComponent
} from "./chunk-QMVBAXS7.js";
import {
  RouterModule
} from "./chunk-IKU57TF7.js";
import {
  AsyncPipe,
  CommonModule,
  Component,
  EventEmitter,
  Input,
  NgForOf,
  NgIf,
  Output,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵNgOnChangesFeature,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵnextContext,
  ɵɵpipe,
  ɵɵpipeBind1,
  ɵɵproperty,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate,
  ɵɵtextInterpolate1,
  ɵɵtextInterpolate2
} from "./chunk-3JQLQ36P.js";
import "./chunk-WDMUDEB6.js";

// src/app/shared/components/modals/shared-dependency-modal/shared-dependency-modal.component.ts
function SharedDependencyModalComponent_div_2_option_6_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "option", 44);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const app_r1 = ctx.$implicit;
    \u0275\u0275property("value", app_r1.id);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate2(" ", app_r1.name, " - ", app_r1.department, " ");
  }
}
function SharedDependencyModalComponent_div_2_div_8_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 45);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate1(" ", ctx_r1.getFieldError("applicationId"), " ");
  }
}
function SharedDependencyModalComponent_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 5)(1, "label", 6);
    \u0275\u0275text(2, "Application *");
    \u0275\u0275elementEnd();
    \u0275\u0275elementStart(3, "select", 40)(4, "option", 41);
    \u0275\u0275text(5, "Select an application");
    \u0275\u0275elementEnd();
    \u0275\u0275template(6, SharedDependencyModalComponent_div_2_option_6_Template, 2, 3, "option", 42);
    \u0275\u0275pipe(7, "async");
    \u0275\u0275elementEnd();
    \u0275\u0275template(8, SharedDependencyModalComponent_div_2_div_8_Template, 2, 1, "div", 43);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r1 = \u0275\u0275nextContext();
    \u0275\u0275advance(6);
    \u0275\u0275property("ngForOf", \u0275\u0275pipeBind1(7, 2, ctx_r1.applicationOptions$));
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", ctx_r1.getFieldError("applicationId"));
  }
}
var SharedDependencyModalComponent = class _SharedDependencyModalComponent {
  fb;
  applicationSelectionService;
  isOpen = false;
  editMode = false;
  initialData = null;
  loading = false;
  showApplicationSelection = true;
  // Hide when used within applications module
  closed = new EventEmitter();
  saved = new EventEmitter();
  dependencyForm;
  applicationOptions$;
  constructor(fb, applicationSelectionService) {
    this.fb = fb;
    this.applicationSelectionService = applicationSelectionService;
    this.applicationOptions$ = this.applicationSelectionService.getApplicationOptions();
  }
  ngOnInit() {
    this.initializeForm();
  }
  ngOnChanges() {
    if (this.dependencyForm && this.initialData) {
      this.dependencyForm.patchValue(this.initialData);
    }
  }
  initializeForm() {
    const formConfig = {
      name: ["", [Validators.required, Validators.minLength(2)]],
      type: ["library", [Validators.required]],
      version: ["", [Validators.required]],
      description: [""],
      criticality: ["medium", [Validators.required]],
      status: ["active", [Validators.required]],
      maintainer: ["", [Validators.required]],
      licenseType: ["MIT", [Validators.required]],
      repository: [""],
      documentation: [""]
    };
    if (this.showApplicationSelection) {
      formConfig.applicationId = ["", [Validators.required]];
    }
    this.dependencyForm = this.fb.group(formConfig);
    if (this.initialData) {
      this.dependencyForm.patchValue(this.initialData);
    }
  }
  getFieldError(fieldName) {
    const field = this.dependencyForm.get(fieldName);
    if (field && field.invalid && field.touched) {
      if (field.errors?.["required"]) {
        return `${fieldName} is required`;
      }
      if (field.errors?.["minlength"]) {
        return `${fieldName} must be at least ${field.errors["minlength"].requiredLength} characters`;
      }
      if (field.errors?.["url"]) {
        return `${fieldName} must be a valid URL`;
      }
    }
    return "";
  }
  onSave() {
    if (this.dependencyForm.valid) {
      const formData = this.dependencyForm.value;
      this.saved.emit(formData);
    }
  }
  onClose() {
    this.closed.emit();
  }
  static \u0275fac = function SharedDependencyModalComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _SharedDependencyModalComponent)(\u0275\u0275directiveInject(FormBuilder), \u0275\u0275directiveInject(ApplicationSelectionService));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _SharedDependencyModalComponent, selectors: [["app-shared-dependency-modal"]], inputs: { isOpen: "isOpen", editMode: "editMode", initialData: "initialData", loading: "loading", showApplicationSelection: "showApplicationSelection" }, outputs: { closed: "closed", saved: "saved" }, features: [\u0275\u0275NgOnChangesFeature], decls: 75, vars: 15, consts: [["confirmText", "Save Dependency", 3, "closed", "confirmed", "cancelled", "isOpen", "title", "subtitle", "loading", "canConfirm"], [1, "dependency-form", 3, "formGroup"], ["class", "form-group", 4, "ngIf"], [1, "form-grid"], ["label", "Dependency Name", "placeholder", "e.g., Angular, Express.js", "formControlName", "name", 3, "required", "errorMessage"], [1, "form-group"], [1, "form-label"], ["formControlName", "type", 1, "form-select"], ["value", "library"], ["value", "framework"], ["value", "service"], ["value", "database"], ["value", "api"], ["value", "tool"], ["value", "infrastructure"], ["label", "Version", "placeholder", "e.g., 16.2.0, ^4.18.2", "formControlName", "version", 3, "required", "errorMessage"], ["formControlName", "criticality", 1, "form-select"], ["value", "low"], ["value", "medium"], ["value", "high"], ["value", "critical"], ["formControlName", "status", 1, "form-select"], ["value", "active"], ["value", "deprecated"], ["value", "outdated"], ["value", "vulnerable"], ["label", "Maintainer", "placeholder", "e.g., Google, Microsoft", "formControlName", "maintainer", 3, "required", "errorMessage"], ["formControlName", "licenseType", 1, "form-select"], ["value", "MIT"], ["value", "Apache-2.0"], ["value", "GPL-3.0"], ["value", "BSD-3-Clause"], ["value", "ISC"], ["value", "MPL-2.0"], ["value", "proprietary"], ["value", "other"], ["label", "Repository URL", "placeholder", "https://github.com/...", "formControlName", "repository", 3, "errorMessage"], ["label", "Documentation URL", "placeholder", "https://docs.example.com/...", "formControlName", "documentation", 3, "errorMessage"], [1, "form-group", "full-width"], ["formControlName", "description", "placeholder", "Describe the purpose and usage of this dependency...", "rows", "3", 1, "form-textarea"], ["formControlName", "applicationId", 1, "form-select"], ["value", ""], [3, "value", 4, "ngFor", "ngForOf"], ["class", "field-error", 4, "ngIf"], [3, "value"], [1, "field-error"]], template: function SharedDependencyModalComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "app-slide-modal", 0);
      \u0275\u0275listener("closed", function SharedDependencyModalComponent_Template_app_slide_modal_closed_0_listener() {
        return ctx.onClose();
      })("confirmed", function SharedDependencyModalComponent_Template_app_slide_modal_confirmed_0_listener() {
        return ctx.onSave();
      })("cancelled", function SharedDependencyModalComponent_Template_app_slide_modal_cancelled_0_listener() {
        return ctx.onClose();
      });
      \u0275\u0275elementStart(1, "form", 1);
      \u0275\u0275template(2, SharedDependencyModalComponent_div_2_Template, 9, 4, "div", 2);
      \u0275\u0275elementStart(3, "div", 3);
      \u0275\u0275element(4, "app-form-input", 4);
      \u0275\u0275elementStart(5, "div", 5)(6, "label", 6);
      \u0275\u0275text(7, "Type");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(8, "select", 7)(9, "option", 8);
      \u0275\u0275text(10, "Library");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(11, "option", 9);
      \u0275\u0275text(12, "Framework");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(13, "option", 10);
      \u0275\u0275text(14, "Service");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(15, "option", 11);
      \u0275\u0275text(16, "Database");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(17, "option", 12);
      \u0275\u0275text(18, "API");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(19, "option", 13);
      \u0275\u0275text(20, "Tool");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(21, "option", 14);
      \u0275\u0275text(22, "Infrastructure");
      \u0275\u0275elementEnd()()();
      \u0275\u0275element(23, "app-form-input", 15);
      \u0275\u0275elementStart(24, "div", 5)(25, "label", 6);
      \u0275\u0275text(26, "Criticality");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(27, "select", 16)(28, "option", 17);
      \u0275\u0275text(29, "Low");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(30, "option", 18);
      \u0275\u0275text(31, "Medium");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(32, "option", 19);
      \u0275\u0275text(33, "High");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(34, "option", 20);
      \u0275\u0275text(35, "Critical");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(36, "div", 5)(37, "label", 6);
      \u0275\u0275text(38, "Status");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(39, "select", 21)(40, "option", 22);
      \u0275\u0275text(41, "Active");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(42, "option", 23);
      \u0275\u0275text(43, "Deprecated");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(44, "option", 24);
      \u0275\u0275text(45, "Outdated");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(46, "option", 25);
      \u0275\u0275text(47, "Vulnerable");
      \u0275\u0275elementEnd()()();
      \u0275\u0275element(48, "app-form-input", 26);
      \u0275\u0275elementStart(49, "div", 5)(50, "label", 6);
      \u0275\u0275text(51, "License Type");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(52, "select", 27)(53, "option", 28);
      \u0275\u0275text(54, "MIT");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(55, "option", 29);
      \u0275\u0275text(56, "Apache 2.0");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(57, "option", 30);
      \u0275\u0275text(58, "GPL 3.0");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(59, "option", 31);
      \u0275\u0275text(60, "BSD 3-Clause");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(61, "option", 32);
      \u0275\u0275text(62, "ISC");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(63, "option", 33);
      \u0275\u0275text(64, "Mozilla Public License 2.0");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(65, "option", 34);
      \u0275\u0275text(66, "Proprietary");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(67, "option", 35);
      \u0275\u0275text(68, "Other");
      \u0275\u0275elementEnd()()();
      \u0275\u0275element(69, "app-form-input", 36)(70, "app-form-input", 37);
      \u0275\u0275elementStart(71, "div", 38)(72, "label", 6);
      \u0275\u0275text(73, "Description");
      \u0275\u0275elementEnd();
      \u0275\u0275element(74, "textarea", 39);
      \u0275\u0275elementEnd()()()();
    }
    if (rf & 2) {
      \u0275\u0275property("isOpen", ctx.isOpen)("title", ctx.editMode ? "Edit Dependency" : "Add Dependency")("subtitle", ctx.showApplicationSelection ? "Configure dependency details and select application" : "Configure dependency details")("loading", ctx.loading)("canConfirm", ctx.dependencyForm.valid);
      \u0275\u0275advance();
      \u0275\u0275property("formGroup", ctx.dependencyForm);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.showApplicationSelection);
      \u0275\u0275advance(2);
      \u0275\u0275property("required", true)("errorMessage", ctx.getFieldError("name"));
      \u0275\u0275advance(19);
      \u0275\u0275property("required", true)("errorMessage", ctx.getFieldError("version"));
      \u0275\u0275advance(25);
      \u0275\u0275property("required", true)("errorMessage", ctx.getFieldError("maintainer"));
      \u0275\u0275advance(21);
      \u0275\u0275property("errorMessage", ctx.getFieldError("repository"));
      \u0275\u0275advance();
      \u0275\u0275property("errorMessage", ctx.getFieldError("documentation"));
    }
  }, dependencies: [CommonModule, NgForOf, NgIf, AsyncPipe, ReactiveFormsModule, \u0275NgNoValidate, NgSelectOption, \u0275NgSelectMultipleOption, DefaultValueAccessor, SelectControlValueAccessor, NgControlStatus, NgControlStatusGroup, RequiredValidator, FormGroupDirective, FormControlName, SlideModalComponent, FormInputComponent], styles: ["\n\n.dependency-form[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-lg);\n}\n.form-grid[_ngcontent-%COMP%] {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: var(--spacing-lg);\n}\n.form-group[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-sm);\n}\n.form-group.full-width[_ngcontent-%COMP%] {\n  grid-column: 1/-1;\n}\n.form-label[_ngcontent-%COMP%] {\n  font-size: var(--text-sm);\n  font-weight: 500;\n  color: var(--secondary-700);\n}\n.form-select[_ngcontent-%COMP%], \n.form-textarea[_ngcontent-%COMP%] {\n  height: 40px;\n  padding: 0 var(--spacing-md);\n  border: 1px solid var(--secondary-300);\n  border-radius: var(--radius-md);\n  background: white;\n  font-size: var(--text-sm);\n  color: var(--secondary-800);\n  transition: all 0.2s ease;\n}\n.form-select[_ngcontent-%COMP%]:focus, \n.form-textarea[_ngcontent-%COMP%]:focus {\n  outline: none;\n  border-color: var(--primary-500);\n  box-shadow: 0 0 0 3px var(--primary-100);\n}\n.form-textarea[_ngcontent-%COMP%] {\n  height: auto;\n  padding: var(--spacing-sm) var(--spacing-md);\n  resize: vertical;\n  min-height: 80px;\n}\n.field-error[_ngcontent-%COMP%] {\n  font-size: var(--text-sm);\n  color: var(--error-600);\n}\n@media (max-width: 768px) {\n  .form-grid[_ngcontent-%COMP%] {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-md);\n  }\n}\n/*# sourceMappingURL=shared-dependency-modal.component.css.map */"] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(SharedDependencyModalComponent, [{
    type: Component,
    args: [{ selector: "app-shared-dependency-modal", standalone: true, imports: [CommonModule, ReactiveFormsModule, SlideModalComponent, FormInputComponent], template: `
    <app-slide-modal
      [isOpen]="isOpen"
      [title]="editMode ? 'Edit Dependency' : 'Add Dependency'"
      [subtitle]="showApplicationSelection ? 'Configure dependency details and select application' : 'Configure dependency details'"
      [loading]="loading"
      [canConfirm]="dependencyForm.valid"
      confirmText="Save Dependency"
      (closed)="onClose()"
      (confirmed)="onSave()"
      (cancelled)="onClose()"
    >
      <form [formGroup]="dependencyForm" class="dependency-form">
        <!-- Application Selection (only when not in applications module) -->
        <div class="form-group" *ngIf="showApplicationSelection">
          <label class="form-label">Application *</label>
          <select formControlName="applicationId" class="form-select">
            <option value="">Select an application</option>
            <option *ngFor="let app of applicationOptions$ | async" [value]="app.id">
              {{ app.name }} - {{ app.department }}
            </option>
          </select>
          <div class="field-error" *ngIf="getFieldError('applicationId')">
            {{ getFieldError('applicationId') }}
          </div>
        </div>

        <div class="form-grid">
          <app-form-input
            label="Dependency Name"
            placeholder="e.g., Angular, Express.js"
            formControlName="name"
            [required]="true"
            [errorMessage]="getFieldError('name')"
          ></app-form-input>

          <div class="form-group">
            <label class="form-label">Type</label>
            <select formControlName="type" class="form-select">
              <option value="library">Library</option>
              <option value="framework">Framework</option>
              <option value="service">Service</option>
              <option value="database">Database</option>
              <option value="api">API</option>
              <option value="tool">Tool</option>
              <option value="infrastructure">Infrastructure</option>
            </select>
          </div>

          <app-form-input
            label="Version"
            placeholder="e.g., 16.2.0, ^4.18.2"
            formControlName="version"
            [required]="true"
            [errorMessage]="getFieldError('version')"
          ></app-form-input>

          <div class="form-group">
            <label class="form-label">Criticality</label>
            <select formControlName="criticality" class="form-select">
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="critical">Critical</option>
            </select>
          </div>

          <div class="form-group">
            <label class="form-label">Status</label>
            <select formControlName="status" class="form-select">
              <option value="active">Active</option>
              <option value="deprecated">Deprecated</option>
              <option value="outdated">Outdated</option>
              <option value="vulnerable">Vulnerable</option>
            </select>
          </div>

          <app-form-input
            label="Maintainer"
            placeholder="e.g., Google, Microsoft"
            formControlName="maintainer"
            [required]="true"
            [errorMessage]="getFieldError('maintainer')"
          ></app-form-input>

          <div class="form-group">
            <label class="form-label">License Type</label>
            <select formControlName="licenseType" class="form-select">
              <option value="MIT">MIT</option>
              <option value="Apache-2.0">Apache 2.0</option>
              <option value="GPL-3.0">GPL 3.0</option>
              <option value="BSD-3-Clause">BSD 3-Clause</option>
              <option value="ISC">ISC</option>
              <option value="MPL-2.0">Mozilla Public License 2.0</option>
              <option value="proprietary">Proprietary</option>
              <option value="other">Other</option>
            </select>
          </div>

          <app-form-input
            label="Repository URL"
            placeholder="https://github.com/..."
            formControlName="repository"
            [errorMessage]="getFieldError('repository')"
          ></app-form-input>

          <app-form-input
            label="Documentation URL"
            placeholder="https://docs.example.com/..."
            formControlName="documentation"
            [errorMessage]="getFieldError('documentation')"
          ></app-form-input>

          <div class="form-group full-width">
            <label class="form-label">Description</label>
            <textarea 
              formControlName="description"
              class="form-textarea"
              placeholder="Describe the purpose and usage of this dependency..."
              rows="3"
            ></textarea>
          </div>
        </div>
      </form>
    </app-slide-modal>
  `, styles: ["/* angular:styles/component:scss;a361df0318dfa4ec329d40d258fd843e1fa90bd9fb1919c3f979b75b4b15fd18;C:/Users/<USER>/Documents/VibeCoding/AppCatalog/app-catalog/frontend/src/app/shared/components/modals/shared-dependency-modal/shared-dependency-modal.component.ts */\n.dependency-form {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-lg);\n}\n.form-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: var(--spacing-lg);\n}\n.form-group {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-sm);\n}\n.form-group.full-width {\n  grid-column: 1/-1;\n}\n.form-label {\n  font-size: var(--text-sm);\n  font-weight: 500;\n  color: var(--secondary-700);\n}\n.form-select,\n.form-textarea {\n  height: 40px;\n  padding: 0 var(--spacing-md);\n  border: 1px solid var(--secondary-300);\n  border-radius: var(--radius-md);\n  background: white;\n  font-size: var(--text-sm);\n  color: var(--secondary-800);\n  transition: all 0.2s ease;\n}\n.form-select:focus,\n.form-textarea:focus {\n  outline: none;\n  border-color: var(--primary-500);\n  box-shadow: 0 0 0 3px var(--primary-100);\n}\n.form-textarea {\n  height: auto;\n  padding: var(--spacing-sm) var(--spacing-md);\n  resize: vertical;\n  min-height: 80px;\n}\n.field-error {\n  font-size: var(--text-sm);\n  color: var(--error-600);\n}\n@media (max-width: 768px) {\n  .form-grid {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-md);\n  }\n}\n/*# sourceMappingURL=shared-dependency-modal.component.css.map */\n"] }]
  }], () => [{ type: FormBuilder }, { type: ApplicationSelectionService }], { isOpen: [{
    type: Input
  }], editMode: [{
    type: Input
  }], initialData: [{
    type: Input
  }], loading: [{
    type: Input
  }], showApplicationSelection: [{
    type: Input
  }], closed: [{
    type: Output
  }], saved: [{
    type: Output
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(SharedDependencyModalComponent, { className: "SharedDependencyModalComponent", filePath: "src/app/shared/components/modals/shared-dependency-modal/shared-dependency-modal.component.ts", lineNumber: 220 });
})();

// src/app/modules/dependencies/dependencies.component.ts
var DependenciesComponent = class _DependenciesComponent {
  fb;
  dependencies = [];
  filteredDependencies = [];
  loading = false;
  // Modal state
  showModal = false;
  editMode = false;
  editData = null;
  modalLoading = false;
  // Statistics
  stats = {
    total: 0,
    critical: 0,
    outdated: 0,
    vulnerabilities: 0,
    newThisMonth: 0,
    criticalPercentage: 0
  };
  // Chart data
  typeChartData = null;
  criticalityChartData = null;
  chartOptions = {
    responsive: true,
    maintainAspectRatio: false
  };
  // Table configuration
  tableColumns = [
    { key: "name", label: "Name", sortable: true },
    { key: "type", label: "Type", type: "badge", sortable: true },
    { key: "version", label: "Version", sortable: true },
    { key: "criticality", label: "Criticality", type: "badge", sortable: true },
    { key: "status", label: "Status", type: "badge", sortable: true },
    { key: "applications", label: "Apps", type: "number", sortable: true },
    { key: "vulnerabilities", label: "Vulnerabilities", type: "number", sortable: true },
    { key: "lastUpdated", label: "Last Updated", type: "date", sortable: true }
  ];
  tableActions = [
    {
      label: "Edit",
      icon: "M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",
      variant: "ghost",
      action: (item) => this.editDependency(item)
    },
    {
      label: "Delete",
      icon: "M3 6h18m-2 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",
      variant: "error",
      action: (item) => this.deleteDependency(item)
    }
  ];
  constructor(fb) {
    this.fb = fb;
  }
  ngOnInit() {
    this.loadDependencies();
  }
  loadDependencies() {
    this.loading = true;
    setTimeout(() => {
      this.dependencies = this.generateMockDependencies();
      this.filteredDependencies = [...this.dependencies];
      this.updateStats();
      this.updateChartData();
      this.loading = false;
    }, 1e3);
  }
  generateMockDependencies() {
    const types = ["library", "framework", "service", "database", "api", "tool", "infrastructure"];
    const criticalities = ["low", "medium", "high", "critical"];
    const statuses = ["active", "deprecated", "end_of_life", "security_risk", "update_required"];
    return Array.from({ length: 50 }, (_, i) => ({
      id: i + 1,
      name: `Dependency ${i + 1}`,
      type: types[Math.floor(Math.random() * types.length)],
      version: `${Math.floor(Math.random() * 5) + 1}.${Math.floor(Math.random() * 10)}.${Math.floor(Math.random() * 10)}`,
      criticality: criticalities[Math.floor(Math.random() * criticalities.length)],
      status: statuses[Math.floor(Math.random() * statuses.length)],
      isInternal: Math.random() > 0.7,
      lastUpdated: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1e3),
      applications: Math.floor(Math.random() * 20) + 1,
      vulnerabilities: Math.floor(Math.random() * 5),
      description: `Description for dependency ${i + 1}`
    }));
  }
  updateStats() {
    this.stats.total = this.dependencies.length;
    this.stats.critical = this.dependencies.filter((d) => d.criticality === "critical").length;
    this.stats.outdated = this.dependencies.filter((d) => d.status === "update_required").length;
    this.stats.vulnerabilities = this.dependencies.reduce((sum, d) => sum + d.vulnerabilities, 0);
    this.stats.newThisMonth = this.dependencies.filter((d) => new Date(d.lastUpdated).getMonth() === (/* @__PURE__ */ new Date()).getMonth()).length;
    this.stats.criticalPercentage = Math.round(this.stats.critical / this.stats.total * 100);
  }
  updateChartData() {
    const typeCount = this.dependencies.reduce((acc, dep) => {
      acc[dep.type] = (acc[dep.type] || 0) + 1;
      return acc;
    }, {});
    this.typeChartData = {
      labels: Object.keys(typeCount),
      datasets: [{
        data: Object.values(typeCount),
        backgroundColor: [
          "#0ea5e9",
          "#22c55e",
          "#f59e0b",
          "#ef4444",
          "#8b5cf6",
          "#06b6d4",
          "#84cc16"
        ]
      }]
    };
    const criticalityCount = this.dependencies.reduce((acc, dep) => {
      acc[dep.criticality] = (acc[dep.criticality] || 0) + 1;
      return acc;
    }, {});
    this.criticalityChartData = {
      labels: Object.keys(criticalityCount),
      datasets: [{
        label: "Dependencies",
        data: Object.values(criticalityCount),
        backgroundColor: ["#22c55e", "#f59e0b", "#f97316", "#ef4444"]
      }]
    };
  }
  openAddModal() {
    this.editMode = false;
    this.editData = null;
    this.showModal = true;
  }
  editDependency(dependency) {
    this.editMode = true;
    this.editData = {
      name: dependency.name,
      type: dependency.type,
      version: dependency.version,
      description: dependency.description || "",
      criticality: dependency.criticality,
      status: dependency.status,
      maintainer: "Unknown",
      // Default value since not in original interface
      licenseType: "MIT"
      // Default value since not in original interface
    };
    this.showModal = true;
  }
  deleteDependency(dependency) {
    if (confirm(`Are you sure you want to delete ${dependency.name}?`)) {
      this.dependencies = this.dependencies.filter((d) => d.id !== dependency.id);
      this.filteredDependencies = this.filteredDependencies.filter((d) => d.id !== dependency.id);
      this.updateStats();
      this.updateChartData();
    }
  }
  closeModal() {
    this.showModal = false;
    this.editMode = false;
    this.editData = null;
  }
  onDependencySaved(dependencyData) {
    this.modalLoading = true;
    setTimeout(() => {
      console.log("Dependency saved:", dependencyData);
      this.modalLoading = false;
      this.closeModal();
      this.loadDependencies();
    }, 1e3);
  }
  onSearchInput(event) {
    const target = event.target;
    this.onSearchChanged(target.value);
  }
  onSearchChanged(searchTerm) {
    this.applyFilters();
  }
  onFilterChanged(filterType, event) {
    const target = event.target;
    this.applyFilters();
  }
  onFiltersChanged(filters) {
    this.applyFilters();
  }
  onSortChanged(sort) {
    console.log("Sort changed:", sort);
  }
  applyFilters() {
    this.filteredDependencies = [...this.dependencies];
  }
  static \u0275fac = function DependenciesComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _DependenciesComponent)(\u0275\u0275directiveInject(FormBuilder));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _DependenciesComponent, selectors: [["app-dependencies"]], decls: 75, vars: 20, consts: [[1, "dependencies-page"], [1, "page-content"], [1, "page-header"], [1, "header-content"], [1, "page-subtitle"], [1, "header-actions"], ["variant", "primary", "leftIcon", "M12 4v16m8-8H4", 3, "clicked"], [1, "stats-grid"], ["title", "Total Dependencies"], [1, "stat-content"], [1, "stat-number"], [1, "stat-change", "positive"], ["title", "Critical Dependencies"], [1, "stat-number", "critical"], [1, "stat-change"], ["title", "Outdated"], [1, "stat-number", "warning"], ["title", "Vulnerabilities"], [1, "stat-number", "error"], [1, "charts-section"], ["title", "Dependencies by Type", "subtitle", "Distribution of dependency types"], ["type", "doughnut", "height", "300px", 3, "data", "options"], ["title", "Criticality Distribution", "subtitle", "Dependencies by criticality level"], ["type", "bar", "height", "300px", 3, "data", "options"], ["title", "Dependencies", "subtitle", "All application dependencies"], [1, "table-controls"], [1, "search-controls"], ["type", "text", "placeholder", "Search dependencies...", 1, "search-input", 3, "input"], [1, "filter-select", 3, "change"], ["value", ""], ["value", "library"], ["value", "framework"], ["value", "service"], ["value", "database"], ["value", "api"], ["value", "tool"], ["value", "infrastructure"], ["value", "low"], ["value", "medium"], ["value", "high"], ["value", "critical"], [3, "sortChanged", "columns", "data", "actions", "loading", "sortable"], [3, "closed", "saved", "isOpen", "editMode", "initialData", "loading", "showApplicationSelection"]], template: function DependenciesComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "div", 0)(1, "div", 1)(2, "div", 2)(3, "div", 3)(4, "h1");
      \u0275\u0275text(5, "Dependencies");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(6, "p", 4);
      \u0275\u0275text(7, "Manage and monitor application dependencies");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(8, "div", 5)(9, "app-button", 6);
      \u0275\u0275listener("clicked", function DependenciesComponent_Template_app_button_clicked_9_listener() {
        return ctx.openAddModal();
      });
      \u0275\u0275text(10, " Add Dependency ");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(11, "div", 7)(12, "app-card", 8)(13, "div", 9)(14, "div", 10);
      \u0275\u0275text(15);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(16, "div", 11);
      \u0275\u0275text(17);
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(18, "app-card", 12)(19, "div", 9)(20, "div", 13);
      \u0275\u0275text(21);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(22, "div", 14);
      \u0275\u0275text(23);
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(24, "app-card", 15)(25, "div", 9)(26, "div", 16);
      \u0275\u0275text(27);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(28, "div", 14);
      \u0275\u0275text(29, "Need updates");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(30, "app-card", 17)(31, "div", 9)(32, "div", 18);
      \u0275\u0275text(33);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(34, "div", 14);
      \u0275\u0275text(35, "Security issues");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(36, "div", 19)(37, "app-card", 20);
      \u0275\u0275element(38, "app-chart", 21);
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(39, "app-card", 22);
      \u0275\u0275element(40, "app-chart", 23);
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(41, "app-card", 24)(42, "div", 25)(43, "div", 26)(44, "input", 27);
      \u0275\u0275listener("input", function DependenciesComponent_Template_input_input_44_listener($event) {
        return ctx.onSearchInput($event);
      });
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(45, "select", 28);
      \u0275\u0275listener("change", function DependenciesComponent_Template_select_change_45_listener($event) {
        return ctx.onFilterChanged("type", $event);
      });
      \u0275\u0275elementStart(46, "option", 29);
      \u0275\u0275text(47, "All Types");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(48, "option", 30);
      \u0275\u0275text(49, "Library");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(50, "option", 31);
      \u0275\u0275text(51, "Framework");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(52, "option", 32);
      \u0275\u0275text(53, "Service");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(54, "option", 33);
      \u0275\u0275text(55, "Database");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(56, "option", 34);
      \u0275\u0275text(57, "API");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(58, "option", 35);
      \u0275\u0275text(59, "Tool");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(60, "option", 36);
      \u0275\u0275text(61, "Infrastructure");
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(62, "select", 28);
      \u0275\u0275listener("change", function DependenciesComponent_Template_select_change_62_listener($event) {
        return ctx.onFilterChanged("criticality", $event);
      });
      \u0275\u0275elementStart(63, "option", 29);
      \u0275\u0275text(64, "All Criticality");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(65, "option", 37);
      \u0275\u0275text(66, "Low");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(67, "option", 38);
      \u0275\u0275text(68, "Medium");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(69, "option", 39);
      \u0275\u0275text(70, "High");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(71, "option", 40);
      \u0275\u0275text(72, "Critical");
      \u0275\u0275elementEnd()()()();
      \u0275\u0275elementStart(73, "app-table", 41);
      \u0275\u0275listener("sortChanged", function DependenciesComponent_Template_app_table_sortChanged_73_listener($event) {
        return ctx.onSortChanged($event);
      });
      \u0275\u0275elementEnd()();
      \u0275\u0275elementStart(74, "app-shared-dependency-modal", 42);
      \u0275\u0275listener("closed", function DependenciesComponent_Template_app_shared_dependency_modal_closed_74_listener() {
        return ctx.closeModal();
      })("saved", function DependenciesComponent_Template_app_shared_dependency_modal_saved_74_listener($event) {
        return ctx.onDependencySaved($event);
      });
      \u0275\u0275elementEnd()()();
    }
    if (rf & 2) {
      \u0275\u0275advance(15);
      \u0275\u0275textInterpolate(ctx.stats.total);
      \u0275\u0275advance(2);
      \u0275\u0275textInterpolate1("+", ctx.stats.newThisMonth, " this month");
      \u0275\u0275advance(4);
      \u0275\u0275textInterpolate(ctx.stats.critical);
      \u0275\u0275advance(2);
      \u0275\u0275textInterpolate1("", ctx.stats.criticalPercentage, "% of total");
      \u0275\u0275advance(4);
      \u0275\u0275textInterpolate(ctx.stats.outdated);
      \u0275\u0275advance(6);
      \u0275\u0275textInterpolate(ctx.stats.vulnerabilities);
      \u0275\u0275advance(5);
      \u0275\u0275property("data", ctx.typeChartData)("options", ctx.chartOptions);
      \u0275\u0275advance(2);
      \u0275\u0275property("data", ctx.criticalityChartData)("options", ctx.chartOptions);
      \u0275\u0275advance(33);
      \u0275\u0275property("columns", ctx.tableColumns)("data", ctx.filteredDependencies)("actions", ctx.tableActions)("loading", ctx.loading)("sortable", true);
      \u0275\u0275advance();
      \u0275\u0275property("isOpen", ctx.showModal)("editMode", ctx.editMode)("initialData", ctx.editData)("loading", ctx.modalLoading)("showApplicationSelection", true);
    }
  }, dependencies: [
    CommonModule,
    RouterModule,
    ReactiveFormsModule,
    NgSelectOption,
    \u0275NgSelectMultipleOption,
    CardComponent,
    ButtonComponent,
    TableComponent,
    ChartComponent,
    SharedDependencyModalComponent
  ], styles: [`

.dependencies-page[_ngcontent-%COMP%] {
  min-height: 100%;
}
.page-content[_ngcontent-%COMP%] {
  padding: var(--spacing-xl);
}
.page-header[_ngcontent-%COMP%] {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-xl);
}
.header-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--secondary-900);
}
.page-subtitle[_ngcontent-%COMP%] {
  margin: 0;
  font-size: var(--text-lg);
  color: var(--secondary-600);
}
.stats-grid[_ngcontent-%COMP%] {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}
.stat-content[_ngcontent-%COMP%] {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
}
.stat-number[_ngcontent-%COMP%] {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--secondary-900);
}
.stat-number.critical[_ngcontent-%COMP%] {
  color: var(--error-600);
}
.stat-number.warning[_ngcontent-%COMP%] {
  color: var(--warning-600);
}
.stat-number.error[_ngcontent-%COMP%] {
  color: var(--error-600);
}
.stat-change[_ngcontent-%COMP%] {
  font-size: var(--text-sm);
  color: var(--secondary-600);
}
.stat-change.positive[_ngcontent-%COMP%] {
  color: var(--success-600);
}
.charts-section[_ngcontent-%COMP%] {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}
.table-controls[_ngcontent-%COMP%] {
  margin-bottom: var(--spacing-lg);
}
.search-controls[_ngcontent-%COMP%] {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}
.search-input[_ngcontent-%COMP%], 
.filter-select[_ngcontent-%COMP%] {
  height: 40px;
  padding: 0 var(--spacing-md);
  border: 1px solid var(--secondary-300);
  border-radius: var(--radius-md);
  background: white;
  font-size: var(--text-sm);
  color: var(--secondary-800);
  transition: all 0.2s ease;
}
.search-input[_ngcontent-%COMP%]:focus, 
.filter-select[_ngcontent-%COMP%]:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}
.search-input[_ngcontent-%COMP%] {
  flex: 1;
  min-width: 200px;
}
.filter-select[_ngcontent-%COMP%] {
  min-width: 150px;
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-sm) center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
  appearance: none;
}
@media (max-width: 768px) {
  .page-header[_ngcontent-%COMP%] {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }
  .stats-grid[_ngcontent-%COMP%] {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  .charts-section[_ngcontent-%COMP%] {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  .search-controls[_ngcontent-%COMP%] {
    flex-direction: column;
    align-items: stretch;
  }
  .search-input[_ngcontent-%COMP%], 
   .filter-select[_ngcontent-%COMP%] {
    min-width: auto;
  }
}
/*# sourceMappingURL=dependencies.component.css.map */`] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(DependenciesComponent, [{
    type: Component,
    args: [{ selector: "app-dependencies", standalone: true, imports: [
      CommonModule,
      RouterModule,
      ReactiveFormsModule,
      CardComponent,
      ButtonComponent,
      TableComponent,
      ChartComponent,
      SharedDependencyModalComponent
    ], template: `
    <div class="dependencies-page">
      <div class="page-content">
        <div class="page-header">
          <div class="header-content">
            <h1>Dependencies</h1>
            <p class="page-subtitle">Manage and monitor application dependencies</p>
          </div>
          <div class="header-actions">
            <app-button
              variant="primary"
              leftIcon="M12 4v16m8-8H4"
              (clicked)="openAddModal()"
            >
              Add Dependency
            </app-button>
          </div>
        </div>

      <!-- Statistics Cards -->
      <div class="stats-grid">
        <app-card title="Total Dependencies">
          <div class="stat-content">
            <div class="stat-number">{{ stats.total }}</div>
            <div class="stat-change positive">+{{ stats.newThisMonth }} this month</div>
          </div>
        </app-card>

        <app-card title="Critical Dependencies">
          <div class="stat-content">
            <div class="stat-number critical">{{ stats.critical }}</div>
            <div class="stat-change">{{ stats.criticalPercentage }}% of total</div>
          </div>
        </app-card>

        <app-card title="Outdated">
          <div class="stat-content">
            <div class="stat-number warning">{{ stats.outdated }}</div>
            <div class="stat-change">Need updates</div>
          </div>
        </app-card>

        <app-card title="Vulnerabilities">
          <div class="stat-content">
            <div class="stat-number error">{{ stats.vulnerabilities }}</div>
            <div class="stat-change">Security issues</div>
          </div>
        </app-card>
      </div>

      <!-- Charts Section -->
      <div class="charts-section">
        <app-card title="Dependencies by Type" subtitle="Distribution of dependency types">
          <app-chart
            type="doughnut"
            [data]="typeChartData"
            [options]="chartOptions"
            height="300px"
          ></app-chart>
        </app-card>

        <app-card title="Criticality Distribution" subtitle="Dependencies by criticality level">
          <app-chart
            type="bar"
            [data]="criticalityChartData"
            [options]="chartOptions"
            height="300px"
          ></app-chart>
        </app-card>
      </div>

      <!-- Dependencies Table -->
      <app-card title="Dependencies" subtitle="All application dependencies">
        <div class="table-controls">
          <div class="search-controls">
            <input
              type="text"
              placeholder="Search dependencies..."
              class="search-input"
              (input)="onSearchInput($event)"
            >
            <select class="filter-select" (change)="onFilterChanged('type', $event)">
              <option value="">All Types</option>
              <option value="library">Library</option>
              <option value="framework">Framework</option>
              <option value="service">Service</option>
              <option value="database">Database</option>
              <option value="api">API</option>
              <option value="tool">Tool</option>
              <option value="infrastructure">Infrastructure</option>
            </select>
            <select class="filter-select" (change)="onFilterChanged('criticality', $event)">
              <option value="">All Criticality</option>
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="critical">Critical</option>
            </select>
          </div>
        </div>

        <app-table
          [columns]="tableColumns"
          [data]="filteredDependencies"
          [actions]="tableActions"
          [loading]="loading"
          [sortable]="true"
          (sortChanged)="onSortChanged($event)"
        ></app-table>
      </app-card>

      <!-- Add/Edit Modal -->
      <app-shared-dependency-modal
        [isOpen]="showModal"
        [editMode]="editMode"
        [initialData]="editData"
        [loading]="modalLoading"
        [showApplicationSelection]="true"
        (closed)="closeModal()"
        (saved)="onDependencySaved($event)"
      ></app-shared-dependency-modal>
      </div>
    </div>
  `, styles: [`/* angular:styles/component:scss;9c441774b0b7d2d27697105d77f356f5bc4f966179cbfed3a6c39aa8fa59bcb6;C:/Users/<USER>/Documents/VibeCoding/AppCatalog/app-catalog/frontend/src/app/modules/dependencies/dependencies.component.ts */
.dependencies-page {
  min-height: 100%;
}
.page-content {
  padding: var(--spacing-xl);
}
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-xl);
}
.header-content h1 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--secondary-900);
}
.page-subtitle {
  margin: 0;
  font-size: var(--text-lg);
  color: var(--secondary-600);
}
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}
.stat-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
}
.stat-number {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--secondary-900);
}
.stat-number.critical {
  color: var(--error-600);
}
.stat-number.warning {
  color: var(--warning-600);
}
.stat-number.error {
  color: var(--error-600);
}
.stat-change {
  font-size: var(--text-sm);
  color: var(--secondary-600);
}
.stat-change.positive {
  color: var(--success-600);
}
.charts-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}
.table-controls {
  margin-bottom: var(--spacing-lg);
}
.search-controls {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}
.search-input,
.filter-select {
  height: 40px;
  padding: 0 var(--spacing-md);
  border: 1px solid var(--secondary-300);
  border-radius: var(--radius-md);
  background: white;
  font-size: var(--text-sm);
  color: var(--secondary-800);
  transition: all 0.2s ease;
}
.search-input:focus,
.filter-select:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}
.search-input {
  flex: 1;
  min-width: 200px;
}
.filter-select {
  min-width: 150px;
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-sm) center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
  appearance: none;
}
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;
  }
  .stats-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  .charts-section {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  .search-controls {
    flex-direction: column;
    align-items: stretch;
  }
  .search-input,
  .filter-select {
    min-width: auto;
  }
}
/*# sourceMappingURL=dependencies.component.css.map */
`] }]
  }], () => [{ type: FormBuilder }], null);
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(DependenciesComponent, { className: "DependenciesComponent", filePath: "src/app/modules/dependencies/dependencies.component.ts", lineNumber: 313 });
})();
export {
  DependenciesComponent
};
//# sourceMappingURL=chunk-3JFFCZTU.js.map
