import {
  CommonModule,
  Component,
  Input,
  NgIf,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵadvance,
  ɵɵclassMap,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵnextContext,
  ɵɵprojection,
  ɵɵprojectionDef,
  ɵɵproperty,
  ɵɵtemplate,
  ɵɵtext,
  ɵɵtextInterpolate
} from "./chunk-3JQLQ36P.js";

// src/app/shared/components/card/card.component.ts
var _c0 = ["*", [["", "slot", "header"]], [["", "slot", "header-actions"]], [["", "slot", "footer"]]];
var _c1 = ["*", "[slot=header]", "[slot=header-actions]", "[slot=footer]"];
function CardComponent_div_1_div_2_h3_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "h3", 11);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.title);
  }
}
function CardComponent_div_1_div_2_p_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "p", 12);
    \u0275\u0275text(1);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(3);
    \u0275\u0275advance();
    \u0275\u0275textInterpolate(ctx_r0.subtitle);
  }
}
function CardComponent_div_1_div_2_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 8);
    \u0275\u0275template(1, CardComponent_div_1_div_2_h3_1_Template, 2, 1, "h3", 9)(2, CardComponent_div_1_div_2_p_2_Template, 2, 1, "p", 10);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext(2);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.title);
    \u0275\u0275advance();
    \u0275\u0275property("ngIf", ctx_r0.subtitle);
  }
}
function CardComponent_div_1_div_5_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 13);
    \u0275\u0275projection(1, 2);
    \u0275\u0275elementEnd();
  }
}
function CardComponent_div_1_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 3)(1, "div", 4);
    \u0275\u0275template(2, CardComponent_div_1_div_2_Template, 3, 2, "div", 5);
    \u0275\u0275elementStart(3, "div", 6);
    \u0275\u0275projection(4, 1);
    \u0275\u0275elementEnd()();
    \u0275\u0275template(5, CardComponent_div_1_div_5_Template, 2, 0, "div", 7);
    \u0275\u0275elementEnd();
  }
  if (rf & 2) {
    const ctx_r0 = \u0275\u0275nextContext();
    \u0275\u0275advance(2);
    \u0275\u0275property("ngIf", ctx_r0.title || ctx_r0.subtitle);
    \u0275\u0275advance(3);
    \u0275\u0275property("ngIf", ctx_r0.hasHeaderActions);
  }
}
function CardComponent_div_4_Template(rf, ctx) {
  if (rf & 1) {
    \u0275\u0275elementStart(0, "div", 14);
    \u0275\u0275projection(1, 3);
    \u0275\u0275elementEnd();
  }
}
var CardComponent = class _CardComponent {
  variant = "default";
  size = "md";
  title = "";
  subtitle = "";
  padded = true;
  hoverable = false;
  clickable = false;
  get cardClasses() {
    const classes = [
      "card",
      `card-${this.variant}`,
      `card-${this.size}`
    ];
    if (this.hoverable)
      classes.push("card-hoverable");
    if (this.clickable)
      classes.push("card-clickable");
    return classes.join(" ");
  }
  get hasHeader() {
    return !!(this.title || this.subtitle || this.hasHeaderSlot || this.hasHeaderActions);
  }
  get hasHeaderSlot() {
    return !this.title && !this.subtitle;
  }
  get hasHeaderActions() {
    return true;
  }
  get hasFooter() {
    return true;
  }
  static \u0275fac = function CardComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _CardComponent)();
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _CardComponent, selectors: [["app-card"]], inputs: { variant: "variant", size: "size", title: "title", subtitle: "subtitle", padded: "padded", hoverable: "hoverable", clickable: "clickable" }, ngContentSelectors: _c1, decls: 5, vars: 6, consts: [["class", "card-header", 4, "ngIf"], [1, "card-content"], ["class", "card-footer", 4, "ngIf"], [1, "card-header"], [1, "card-header-content"], ["class", "card-title-section", 4, "ngIf"], [1, "card-header-slot"], ["class", "card-header-actions", 4, "ngIf"], [1, "card-title-section"], ["class", "card-title", 4, "ngIf"], ["class", "card-subtitle", 4, "ngIf"], [1, "card-title"], [1, "card-subtitle"], [1, "card-header-actions"], [1, "card-footer"]], template: function CardComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275projectionDef(_c0);
      \u0275\u0275elementStart(0, "div");
      \u0275\u0275template(1, CardComponent_div_1_Template, 6, 2, "div", 0);
      \u0275\u0275elementStart(2, "div", 1);
      \u0275\u0275projection(3);
      \u0275\u0275elementEnd();
      \u0275\u0275template(4, CardComponent_div_4_Template, 2, 0, "div", 2);
      \u0275\u0275elementEnd();
    }
    if (rf & 2) {
      \u0275\u0275classMap(ctx.cardClasses);
      \u0275\u0275advance();
      \u0275\u0275property("ngIf", ctx.hasHeader);
      \u0275\u0275advance();
      \u0275\u0275classProp("card-content-padded", ctx.padded);
      \u0275\u0275advance(2);
      \u0275\u0275property("ngIf", ctx.hasFooter);
    }
  }, dependencies: [CommonModule, NgIf], styles: ['\n\n.card[_ngcontent-%COMP%] {\n  background: white;\n  border-radius: var(--radius-lg);\n  overflow: hidden;\n  transition: all 0.2s ease;\n  position: relative;\n}\n.card-default[_ngcontent-%COMP%] {\n  border: 1px solid var(--secondary-200);\n  box-shadow: var(--shadow-sm);\n}\n.card-outlined[_ngcontent-%COMP%] {\n  border: 1px solid var(--secondary-300);\n  box-shadow: none;\n}\n.card-elevated[_ngcontent-%COMP%] {\n  border: none;\n  box-shadow: var(--shadow-lg);\n}\n.card-filled[_ngcontent-%COMP%] {\n  border: none;\n  background: var(--secondary-50);\n  box-shadow: none;\n}\n.card-sm[_ngcontent-%COMP%] {\n  border-radius: var(--radius-md);\n}\n.card-md[_ngcontent-%COMP%] {\n  border-radius: var(--radius-lg);\n}\n.card-lg[_ngcontent-%COMP%] {\n  border-radius: var(--radius-xl);\n}\n.card-hoverable[_ngcontent-%COMP%] {\n  cursor: pointer;\n}\n.card-hoverable[_ngcontent-%COMP%]:hover {\n  transform: translateY(-2px);\n  box-shadow: var(--shadow-xl);\n  border-color: var(--secondary-300);\n}\n.card-clickable[_ngcontent-%COMP%] {\n  cursor: pointer;\n  -webkit-user-select: none;\n  user-select: none;\n}\n.card-clickable[_ngcontent-%COMP%]:hover {\n  background-color: var(--neutral-50);\n}\n.card-clickable[_ngcontent-%COMP%]:active {\n  transform: scale(0.98);\n}\n.card-header[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: flex-start;\n  justify-content: space-between;\n  gap: var(--spacing-md);\n  border-bottom: 1px solid var(--secondary-200);\n}\n.card-sm[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%] {\n  padding: var(--spacing-md);\n}\n.card-md[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%] {\n  padding: var(--spacing-lg);\n}\n.card-lg[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%] {\n  padding: var(--spacing-xl);\n}\n.card-header-content[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: flex-start;\n  gap: var(--spacing-md);\n  flex: 1;\n  min-width: 0;\n}\n.card-title-section[_ngcontent-%COMP%] {\n  flex: 1;\n  min-width: 0;\n}\n.card-title[_ngcontent-%COMP%] {\n  margin: 0;\n  font-size: var(--text-lg);\n  font-weight: 600;\n  color: var(--secondary-900);\n  line-height: var(--leading-tight);\n}\n.card-sm[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\n  font-size: var(--text-base);\n}\n.card-lg[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\n  font-size: var(--text-xl);\n}\n.card-subtitle[_ngcontent-%COMP%] {\n  margin: var(--spacing-xs) 0 0 0;\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n  line-height: var(--leading-normal);\n}\n.card-sm[_ngcontent-%COMP%]   .card-subtitle[_ngcontent-%COMP%] {\n  font-size: var(--text-xs);\n}\n.card-lg[_ngcontent-%COMP%]   .card-subtitle[_ngcontent-%COMP%] {\n  font-size: var(--text-base);\n}\n.card-header-slot[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n}\n.card-header-actions[_ngcontent-%COMP%] {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n  flex-shrink: 0;\n}\n.card-content[_ngcontent-%COMP%] {\n  flex: 1;\n}\n.card-content-padded.card-sm[_ngcontent-%COMP%] {\n  padding: var(--spacing-md);\n}\n.card-content-padded.card-md[_ngcontent-%COMP%] {\n  padding: var(--spacing-lg);\n}\n.card-content-padded.card-lg[_ngcontent-%COMP%] {\n  padding: var(--spacing-xl);\n}\n.card-footer[_ngcontent-%COMP%] {\n  border-top: 1px solid var(--secondary-200);\n  background: var(--neutral-50);\n}\n.card-sm[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%] {\n  padding: var(--spacing-md);\n}\n.card-md[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%] {\n  padding: var(--spacing-lg);\n}\n.card-lg[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%] {\n  padding: var(--spacing-xl);\n}\n.card-stats[_ngcontent-%COMP%] {\n  text-align: center;\n}\n.card-stats[_ngcontent-%COMP%]   .card-content[_ngcontent-%COMP%] {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: var(--spacing-sm);\n}\n.card-stats[_ngcontent-%COMP%]   .stats-value[_ngcontent-%COMP%] {\n  font-size: var(--text-3xl);\n  font-weight: 700;\n  color: var(--primary-600);\n  line-height: 1;\n}\n.card-stats[_ngcontent-%COMP%]   .stats-label[_ngcontent-%COMP%] {\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n  font-weight: 500;\n}\n.card-stats[_ngcontent-%COMP%]   .stats-change[_ngcontent-%COMP%] {\n  font-size: var(--text-xs);\n  font-weight: 500;\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border-radius: var(--radius-sm);\n}\n.card-stats[_ngcontent-%COMP%]   .stats-change.stats-change-positive[_ngcontent-%COMP%] {\n  background: var(--success-100);\n  color: var(--success-700);\n}\n.card-stats[_ngcontent-%COMP%]   .stats-change.stats-change-negative[_ngcontent-%COMP%] {\n  background: var(--error-100);\n  color: var(--error-700);\n}\n.card-stats[_ngcontent-%COMP%]   .stats-change.stats-change-neutral[_ngcontent-%COMP%] {\n  background: var(--secondary-100);\n  color: var(--secondary-700);\n}\n.card-metric[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%] {\n  border-bottom: none;\n  padding-bottom: 0;\n}\n.card-metric[_ngcontent-%COMP%]   .metric-value[_ngcontent-%COMP%] {\n  font-size: var(--text-2xl);\n  font-weight: 700;\n  color: var(--secondary-900);\n  margin-bottom: var(--spacing-xs);\n}\n.card-metric[_ngcontent-%COMP%]   .metric-label[_ngcontent-%COMP%] {\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n  margin-bottom: var(--spacing-md);\n}\n.card-metric[_ngcontent-%COMP%]   .metric-chart[_ngcontent-%COMP%] {\n  height: 60px;\n  margin-top: var(--spacing-md);\n}\n.card-loading[_ngcontent-%COMP%] {\n  position: relative;\n  overflow: hidden;\n}\n.card-loading[_ngcontent-%COMP%]::after {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background:\n    linear-gradient(\n      90deg,\n      transparent,\n      rgba(255, 255, 255, 0.4),\n      transparent);\n  animation: _ngcontent-%COMP%_shimmer 1.5s infinite;\n}\n@keyframes _ngcontent-%COMP%_shimmer {\n  0% {\n    left: -100%;\n  }\n  100% {\n    left: 100%;\n  }\n}\n.card-error[_ngcontent-%COMP%] {\n  border-color: var(--error-300);\n  background: var(--error-50);\n}\n.card-error[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%] {\n  border-bottom-color: var(--error-200);\n}\n.card-error[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\n  color: var(--error-800);\n}\n.card-error[_ngcontent-%COMP%]   .card-subtitle[_ngcontent-%COMP%] {\n  color: var(--error-600);\n}\n.card-success[_ngcontent-%COMP%] {\n  border-color: var(--success-300);\n  background: var(--success-50);\n}\n.card-success[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%] {\n  border-bottom-color: var(--success-200);\n}\n.card-success[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\n  color: var(--success-800);\n}\n.card-success[_ngcontent-%COMP%]   .card-subtitle[_ngcontent-%COMP%] {\n  color: var(--success-600);\n}\n.card-warning[_ngcontent-%COMP%] {\n  border-color: var(--warning-300);\n  background: var(--warning-50);\n}\n.card-warning[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%] {\n  border-bottom-color: var(--warning-200);\n}\n.card-warning[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\n  color: var(--warning-800);\n}\n.card-warning[_ngcontent-%COMP%]   .card-subtitle[_ngcontent-%COMP%] {\n  color: var(--warning-600);\n}\n@media (max-width: 768px) {\n  .card-header[_ngcontent-%COMP%] {\n    flex-direction: column;\n    align-items: stretch;\n    gap: var(--spacing-sm);\n  }\n  .card-header-content[_ngcontent-%COMP%] {\n    flex-direction: column;\n    gap: var(--spacing-sm);\n  }\n  .card-header-actions[_ngcontent-%COMP%] {\n    justify-content: flex-end;\n  }\n  .card-lg[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%], \n   .card-lg[_ngcontent-%COMP%]   .card-content-padded[_ngcontent-%COMP%], \n   .card-lg[_ngcontent-%COMP%]   .card-footer[_ngcontent-%COMP%] {\n    padding: var(--spacing-lg);\n  }\n}\n@media print {\n  .card[_ngcontent-%COMP%] {\n    break-inside: avoid;\n    box-shadow: none;\n    border: 1px solid var(--secondary-300);\n  }\n  .card-hoverable[_ngcontent-%COMP%]:hover, \n   .card-clickable[_ngcontent-%COMP%]:hover {\n    transform: none;\n    box-shadow: none;\n  }\n}\n/*# sourceMappingURL=card.component.css.map */'] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(CardComponent, [{
    type: Component,
    args: [{ selector: "app-card", standalone: true, imports: [CommonModule], template: `
    <div [class]="cardClasses">
      <!-- Card Header -->
      <div class="card-header" *ngIf="hasHeader">
        <div class="card-header-content">
          <!-- Title and Subtitle -->
          <div class="card-title-section" *ngIf="title || subtitle">
            <h3 class="card-title" *ngIf="title">{{ title }}</h3>
            <p class="card-subtitle" *ngIf="subtitle">{{ subtitle }}</p>
          </div>

          <!-- Header Slot -->
          <div class="card-header-slot">
            <ng-content select="[slot=header]"></ng-content>
          </div>
        </div>

        <!-- Header Actions -->
        <div class="card-header-actions" *ngIf="hasHeaderActions">
          <ng-content select="[slot=header-actions]"></ng-content>
        </div>
      </div>

      <!-- Card Content -->
      <div class="card-content" [class.card-content-padded]="padded">
        <ng-content></ng-content>
      </div>

      <!-- Card Footer -->
      <div class="card-footer" *ngIf="hasFooter">
        <ng-content select="[slot=footer]"></ng-content>
      </div>
    </div>
  `, styles: ['/* src/app/shared/components/card/card.component.scss */\n.card {\n  background: white;\n  border-radius: var(--radius-lg);\n  overflow: hidden;\n  transition: all 0.2s ease;\n  position: relative;\n}\n.card-default {\n  border: 1px solid var(--secondary-200);\n  box-shadow: var(--shadow-sm);\n}\n.card-outlined {\n  border: 1px solid var(--secondary-300);\n  box-shadow: none;\n}\n.card-elevated {\n  border: none;\n  box-shadow: var(--shadow-lg);\n}\n.card-filled {\n  border: none;\n  background: var(--secondary-50);\n  box-shadow: none;\n}\n.card-sm {\n  border-radius: var(--radius-md);\n}\n.card-md {\n  border-radius: var(--radius-lg);\n}\n.card-lg {\n  border-radius: var(--radius-xl);\n}\n.card-hoverable {\n  cursor: pointer;\n}\n.card-hoverable:hover {\n  transform: translateY(-2px);\n  box-shadow: var(--shadow-xl);\n  border-color: var(--secondary-300);\n}\n.card-clickable {\n  cursor: pointer;\n  -webkit-user-select: none;\n  user-select: none;\n}\n.card-clickable:hover {\n  background-color: var(--neutral-50);\n}\n.card-clickable:active {\n  transform: scale(0.98);\n}\n.card-header {\n  display: flex;\n  align-items: flex-start;\n  justify-content: space-between;\n  gap: var(--spacing-md);\n  border-bottom: 1px solid var(--secondary-200);\n}\n.card-sm .card-header {\n  padding: var(--spacing-md);\n}\n.card-md .card-header {\n  padding: var(--spacing-lg);\n}\n.card-lg .card-header {\n  padding: var(--spacing-xl);\n}\n.card-header-content {\n  display: flex;\n  align-items: flex-start;\n  gap: var(--spacing-md);\n  flex: 1;\n  min-width: 0;\n}\n.card-title-section {\n  flex: 1;\n  min-width: 0;\n}\n.card-title {\n  margin: 0;\n  font-size: var(--text-lg);\n  font-weight: 600;\n  color: var(--secondary-900);\n  line-height: var(--leading-tight);\n}\n.card-sm .card-title {\n  font-size: var(--text-base);\n}\n.card-lg .card-title {\n  font-size: var(--text-xl);\n}\n.card-subtitle {\n  margin: var(--spacing-xs) 0 0 0;\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n  line-height: var(--leading-normal);\n}\n.card-sm .card-subtitle {\n  font-size: var(--text-xs);\n}\n.card-lg .card-subtitle {\n  font-size: var(--text-base);\n}\n.card-header-slot {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n}\n.card-header-actions {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n  flex-shrink: 0;\n}\n.card-content {\n  flex: 1;\n}\n.card-content-padded.card-sm {\n  padding: var(--spacing-md);\n}\n.card-content-padded.card-md {\n  padding: var(--spacing-lg);\n}\n.card-content-padded.card-lg {\n  padding: var(--spacing-xl);\n}\n.card-footer {\n  border-top: 1px solid var(--secondary-200);\n  background: var(--neutral-50);\n}\n.card-sm .card-footer {\n  padding: var(--spacing-md);\n}\n.card-md .card-footer {\n  padding: var(--spacing-lg);\n}\n.card-lg .card-footer {\n  padding: var(--spacing-xl);\n}\n.card-stats {\n  text-align: center;\n}\n.card-stats .card-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  gap: var(--spacing-sm);\n}\n.card-stats .stats-value {\n  font-size: var(--text-3xl);\n  font-weight: 700;\n  color: var(--primary-600);\n  line-height: 1;\n}\n.card-stats .stats-label {\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n  font-weight: 500;\n}\n.card-stats .stats-change {\n  font-size: var(--text-xs);\n  font-weight: 500;\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border-radius: var(--radius-sm);\n}\n.card-stats .stats-change.stats-change-positive {\n  background: var(--success-100);\n  color: var(--success-700);\n}\n.card-stats .stats-change.stats-change-negative {\n  background: var(--error-100);\n  color: var(--error-700);\n}\n.card-stats .stats-change.stats-change-neutral {\n  background: var(--secondary-100);\n  color: var(--secondary-700);\n}\n.card-metric .card-header {\n  border-bottom: none;\n  padding-bottom: 0;\n}\n.card-metric .metric-value {\n  font-size: var(--text-2xl);\n  font-weight: 700;\n  color: var(--secondary-900);\n  margin-bottom: var(--spacing-xs);\n}\n.card-metric .metric-label {\n  font-size: var(--text-sm);\n  color: var(--secondary-600);\n  margin-bottom: var(--spacing-md);\n}\n.card-metric .metric-chart {\n  height: 60px;\n  margin-top: var(--spacing-md);\n}\n.card-loading {\n  position: relative;\n  overflow: hidden;\n}\n.card-loading::after {\n  content: "";\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background:\n    linear-gradient(\n      90deg,\n      transparent,\n      rgba(255, 255, 255, 0.4),\n      transparent);\n  animation: shimmer 1.5s infinite;\n}\n@keyframes shimmer {\n  0% {\n    left: -100%;\n  }\n  100% {\n    left: 100%;\n  }\n}\n.card-error {\n  border-color: var(--error-300);\n  background: var(--error-50);\n}\n.card-error .card-header {\n  border-bottom-color: var(--error-200);\n}\n.card-error .card-title {\n  color: var(--error-800);\n}\n.card-error .card-subtitle {\n  color: var(--error-600);\n}\n.card-success {\n  border-color: var(--success-300);\n  background: var(--success-50);\n}\n.card-success .card-header {\n  border-bottom-color: var(--success-200);\n}\n.card-success .card-title {\n  color: var(--success-800);\n}\n.card-success .card-subtitle {\n  color: var(--success-600);\n}\n.card-warning {\n  border-color: var(--warning-300);\n  background: var(--warning-50);\n}\n.card-warning .card-header {\n  border-bottom-color: var(--warning-200);\n}\n.card-warning .card-title {\n  color: var(--warning-800);\n}\n.card-warning .card-subtitle {\n  color: var(--warning-600);\n}\n@media (max-width: 768px) {\n  .card-header {\n    flex-direction: column;\n    align-items: stretch;\n    gap: var(--spacing-sm);\n  }\n  .card-header-content {\n    flex-direction: column;\n    gap: var(--spacing-sm);\n  }\n  .card-header-actions {\n    justify-content: flex-end;\n  }\n  .card-lg .card-header,\n  .card-lg .card-content-padded,\n  .card-lg .card-footer {\n    padding: var(--spacing-lg);\n  }\n}\n@media print {\n  .card {\n    break-inside: avoid;\n    box-shadow: none;\n    border: 1px solid var(--secondary-300);\n  }\n  .card-hoverable:hover,\n  .card-clickable:hover {\n    transform: none;\n    box-shadow: none;\n  }\n}\n/*# sourceMappingURL=card.component.css.map */\n'] }]
  }], null, { variant: [{
    type: Input
  }], size: [{
    type: Input
  }], title: [{
    type: Input
  }], subtitle: [{
    type: Input
  }], padded: [{
    type: Input
  }], hoverable: [{
    type: Input
  }], clickable: [{
    type: Input
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(CardComponent, { className: "CardComponent", filePath: "src/app/shared/components/card/card.component.ts", lineNumber: 47 });
})();

export {
  CardComponent
};
//# sourceMappingURL=chunk-SLWZQAXJ.js.map
