import './polyfills.server.mjs';
import {
  FormInputComponent
} from "./chunk-ZMVY5VP4.mjs";
import {
  SlideModalComponent
} from "./chunk-V4LU676I.mjs";
import {
  CheckboxControlValueAccessor,
  DefaultValueAccessor,
  FormBuilder,
  FormControlName,
  FormGroupDirective,
  NgControlStatus,
  NgControlStatusGroup,
  NgSelectOption,
  ReactiveFormsModule,
  RequiredValidator,
  SelectControlValueAccessor,
  Validators,
  ɵNgNoValidate,
  ɵNgSelectMultipleOption
} from "./chunk-22WSPHJT.mjs";
import {
  CommonModule,
  Component,
  EventEmitter,
  Input,
  Output,
  setClassMetadata,
  ɵsetClassDebugInfo,
  ɵɵNgOnChangesFeature,
  ɵɵadvance,
  ɵɵdefineComponent,
  ɵɵdirectiveInject,
  ɵɵelement,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵlistener,
  ɵɵproperty,
  ɵɵtext
} from "./chunk-IPMSWJNG.mjs";

// src/app/modules/applications/components/stakeholder-modal/stakeholder-modal.component.ts
var StakeholderModalComponent = class _StakeholderModalComponent {
  fb;
  isOpen = false;
  editMode = false;
  initialData = null;
  loading = false;
  closed = new EventEmitter();
  saved = new EventEmitter();
  stakeholderForm;
  constructor(fb) {
    this.fb = fb;
  }
  ngOnInit() {
    this.initializeForm();
  }
  ngOnChanges() {
    if (this.stakeholderForm && this.initialData) {
      this.stakeholderForm.patchValue(this.initialData);
    }
  }
  initializeForm() {
    this.stakeholderForm = this.fb.group({
      name: ["", [Validators.required, Validators.minLength(2)]],
      email: ["", [Validators.required, Validators.email]],
      role: ["developer", [Validators.required]],
      department: ["", [Validators.required]],
      responsibility: [""],
      isPrimary: [false]
    });
    if (this.initialData) {
      this.stakeholderForm.patchValue(this.initialData);
    }
  }
  getFieldError(fieldName) {
    const field = this.stakeholderForm.get(fieldName);
    if (field && field.invalid && field.touched) {
      if (field.errors?.["required"]) {
        return `${fieldName} is required`;
      }
      if (field.errors?.["minlength"]) {
        return `${fieldName} must be at least ${field.errors["minlength"].requiredLength} characters`;
      }
      if (field.errors?.["email"]) {
        return "Please enter a valid email address";
      }
    }
    return "";
  }
  onClose() {
    this.stakeholderForm.reset();
    this.closed.emit();
  }
  onSave() {
    if (this.stakeholderForm.valid) {
      this.saved.emit(this.stakeholderForm.value);
    }
  }
  static \u0275fac = function StakeholderModalComponent_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _StakeholderModalComponent)(\u0275\u0275directiveInject(FormBuilder));
  };
  static \u0275cmp = /* @__PURE__ */ \u0275\u0275defineComponent({ type: _StakeholderModalComponent, selectors: [["app-stakeholder-modal"]], inputs: { isOpen: "isOpen", editMode: "editMode", initialData: "initialData", loading: "loading" }, outputs: { closed: "closed", saved: "saved" }, features: [\u0275\u0275NgOnChangesFeature], decls: 41, vars: 11, consts: [["subtitle", "Configure team member details", "confirmText", "Save Stakeholder", 3, "closed", "confirmed", "cancelled", "isOpen", "title", "loading", "canConfirm"], [1, "stakeholder-form", 3, "formGroup"], [1, "form-grid"], ["label", "Full Name", "placeholder", "e.g., John Doe", "formControlName", "name", 3, "required", "errorMessage"], ["label", "Email Address", "placeholder", "e.g., <EMAIL>", "formControlName", "email", "type", "email", 3, "required", "errorMessage"], [1, "form-group"], [1, "form-label"], ["formControlName", "role", 1, "form-select"], ["value", "product_owner"], ["value", "developer"], ["value", "tech_lead"], ["value", "architect"], ["value", "business_analyst"], ["value", "project_manager"], ["value", "qa_engineer"], ["value", "devops_engineer"], ["value", "security_engineer"], ["value", "stakeholder"], ["label", "Department", "placeholder", "e.g., Engineering, Product", "formControlName", "department", 3, "required", "errorMessage"], [1, "form-group", "full-width"], [1, "checkbox-label"], ["type", "checkbox", "formControlName", "isPrimary", 1, "checkbox-input"], [1, "checkbox-text"], [1, "checkbox-description"], ["formControlName", "responsibility", "placeholder", "Describe this person's role and responsibilities for the application...", "rows", "3", 1, "form-textarea"]], template: function StakeholderModalComponent_Template(rf, ctx) {
    if (rf & 1) {
      \u0275\u0275elementStart(0, "app-slide-modal", 0);
      \u0275\u0275listener("closed", function StakeholderModalComponent_Template_app_slide_modal_closed_0_listener() {
        return ctx.onClose();
      })("confirmed", function StakeholderModalComponent_Template_app_slide_modal_confirmed_0_listener() {
        return ctx.onSave();
      })("cancelled", function StakeholderModalComponent_Template_app_slide_modal_cancelled_0_listener() {
        return ctx.onClose();
      });
      \u0275\u0275elementStart(1, "form", 1)(2, "div", 2);
      \u0275\u0275element(3, "app-form-input", 3)(4, "app-form-input", 4);
      \u0275\u0275elementStart(5, "div", 5)(6, "label", 6);
      \u0275\u0275text(7, "Role");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(8, "select", 7)(9, "option", 8);
      \u0275\u0275text(10, "Product Owner");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(11, "option", 9);
      \u0275\u0275text(12, "Developer");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(13, "option", 10);
      \u0275\u0275text(14, "Tech Lead");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(15, "option", 11);
      \u0275\u0275text(16, "Architect");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(17, "option", 12);
      \u0275\u0275text(18, "Business Analyst");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(19, "option", 13);
      \u0275\u0275text(20, "Project Manager");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(21, "option", 14);
      \u0275\u0275text(22, "QA Engineer");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(23, "option", 15);
      \u0275\u0275text(24, "DevOps Engineer");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(25, "option", 16);
      \u0275\u0275text(26, "Security Engineer");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(27, "option", 17);
      \u0275\u0275text(28, "Stakeholder");
      \u0275\u0275elementEnd()()();
      \u0275\u0275element(29, "app-form-input", 18);
      \u0275\u0275elementStart(30, "div", 19)(31, "label", 20);
      \u0275\u0275element(32, "input", 21);
      \u0275\u0275elementStart(33, "span", 22);
      \u0275\u0275text(34, "Primary Contact");
      \u0275\u0275elementEnd();
      \u0275\u0275elementStart(35, "span", 23);
      \u0275\u0275text(36, "This person is the primary contact for this application");
      \u0275\u0275elementEnd()()();
      \u0275\u0275elementStart(37, "div", 19)(38, "label", 6);
      \u0275\u0275text(39, "Responsibility");
      \u0275\u0275elementEnd();
      \u0275\u0275element(40, "textarea", 24);
      \u0275\u0275elementEnd()()()();
    }
    if (rf & 2) {
      \u0275\u0275property("isOpen", ctx.isOpen)("title", ctx.editMode ? "Edit Stakeholder" : "Add Stakeholder")("loading", ctx.loading)("canConfirm", ctx.stakeholderForm.valid);
      \u0275\u0275advance();
      \u0275\u0275property("formGroup", ctx.stakeholderForm);
      \u0275\u0275advance(2);
      \u0275\u0275property("required", true)("errorMessage", ctx.getFieldError("name"));
      \u0275\u0275advance();
      \u0275\u0275property("required", true)("errorMessage", ctx.getFieldError("email"));
      \u0275\u0275advance(25);
      \u0275\u0275property("required", true)("errorMessage", ctx.getFieldError("department"));
    }
  }, dependencies: [CommonModule, ReactiveFormsModule, \u0275NgNoValidate, NgSelectOption, \u0275NgSelectMultipleOption, DefaultValueAccessor, CheckboxControlValueAccessor, SelectControlValueAccessor, NgControlStatus, NgControlStatusGroup, RequiredValidator, FormGroupDirective, FormControlName, SlideModalComponent, FormInputComponent], styles: [`

.stakeholder-form[_ngcontent-%COMP%] {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}
.form-grid[_ngcontent-%COMP%] {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
}
.form-group[_ngcontent-%COMP%] {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}
.form-group.full-width[_ngcontent-%COMP%] {
  grid-column: 1/-1;
}
.form-label[_ngcontent-%COMP%] {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--secondary-700);
}
.form-select[_ngcontent-%COMP%], 
.form-textarea[_ngcontent-%COMP%] {
  border: 1px solid var(--secondary-300);
  border-radius: var(--radius-md);
  background: white;
  font-size: var(--text-sm);
  color: var(--secondary-800);
  transition: all 0.2s ease;
}
.form-select[_ngcontent-%COMP%]:focus, 
.form-textarea[_ngcontent-%COMP%]:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}
.form-select[_ngcontent-%COMP%] {
  height: 40px;
  padding: 0 var(--spacing-md);
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-sm) center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
  appearance: none;
}
.form-textarea[_ngcontent-%COMP%] {
  padding: var(--spacing-md);
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}
.checkbox-label[_ngcontent-%COMP%] {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  cursor: pointer;
  padding: var(--spacing-md);
  border: 1px solid var(--secondary-200);
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
}
.checkbox-label[_ngcontent-%COMP%]:hover {
  border-color: var(--primary-300);
  background: var(--primary-25);
}
.checkbox-input[_ngcontent-%COMP%] {
  margin: 0;
  margin-top: 2px;
}
.checkbox-text[_ngcontent-%COMP%] {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--secondary-900);
  margin-bottom: var(--spacing-xs);
}
.checkbox-description[_ngcontent-%COMP%] {
  font-size: var(--text-xs);
  color: var(--secondary-600);
  display: block;
}
@media (max-width: 768px) {
  .form-grid[_ngcontent-%COMP%] {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
}
/*# sourceMappingURL=stakeholder-modal.component.css.map */`] });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(StakeholderModalComponent, [{
    type: Component,
    args: [{ selector: "app-stakeholder-modal", standalone: true, imports: [CommonModule, ReactiveFormsModule, SlideModalComponent, FormInputComponent], template: `
    <app-slide-modal
      [isOpen]="isOpen"
      [title]="editMode ? 'Edit Stakeholder' : 'Add Stakeholder'"
      subtitle="Configure team member details"
      [loading]="loading"
      [canConfirm]="stakeholderForm.valid"
      confirmText="Save Stakeholder"
      (closed)="onClose()"
      (confirmed)="onSave()"
      (cancelled)="onClose()"
    >
      <form [formGroup]="stakeholderForm" class="stakeholder-form">
        <div class="form-grid">
          <app-form-input
            label="Full Name"
            placeholder="e.g., John Doe"
            formControlName="name"
            [required]="true"
            [errorMessage]="getFieldError('name')"
          ></app-form-input>

          <app-form-input
            label="Email Address"
            placeholder="e.g., <EMAIL>"
            formControlName="email"
            type="email"
            [required]="true"
            [errorMessage]="getFieldError('email')"
          ></app-form-input>

          <div class="form-group">
            <label class="form-label">Role</label>
            <select formControlName="role" class="form-select">
              <option value="product_owner">Product Owner</option>
              <option value="developer">Developer</option>
              <option value="tech_lead">Tech Lead</option>
              <option value="architect">Architect</option>
              <option value="business_analyst">Business Analyst</option>
              <option value="project_manager">Project Manager</option>
              <option value="qa_engineer">QA Engineer</option>
              <option value="devops_engineer">DevOps Engineer</option>
              <option value="security_engineer">Security Engineer</option>
              <option value="stakeholder">Stakeholder</option>
            </select>
          </div>

          <app-form-input
            label="Department"
            placeholder="e.g., Engineering, Product"
            formControlName="department"
            [required]="true"
            [errorMessage]="getFieldError('department')"
          ></app-form-input>

          <div class="form-group full-width">
            <label class="checkbox-label">
              <input
                type="checkbox"
                formControlName="isPrimary"
                class="checkbox-input"
              >
              <span class="checkbox-text">Primary Contact</span>
              <span class="checkbox-description">This person is the primary contact for this application</span>
            </label>
          </div>

          <div class="form-group full-width">
            <label class="form-label">Responsibility</label>
            <textarea
              formControlName="responsibility"
              class="form-textarea"
              placeholder="Describe this person's role and responsibilities for the application..."
              rows="3"
            ></textarea>
          </div>
        </div>
      </form>
    </app-slide-modal>
  `, styles: [`/* angular:styles/component:scss;15b9ef55eb6720ce45112de94ca8cf02250f25fb82d03d2c10565fdecb059dbf;C:/Users/<USER>/Documents/VibeCoding/AppCatalog/app-catalog/frontend/src/app/modules/applications/components/stakeholder-modal/stakeholder-modal.component.ts */
.stakeholder-form {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}
.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
}
.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}
.form-group.full-width {
  grid-column: 1/-1;
}
.form-label {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--secondary-700);
}
.form-select,
.form-textarea {
  border: 1px solid var(--secondary-300);
  border-radius: var(--radius-md);
  background: white;
  font-size: var(--text-sm);
  color: var(--secondary-800);
  transition: all 0.2s ease;
}
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-500);
  box-shadow: 0 0 0 3px var(--primary-100);
}
.form-select {
  height: 40px;
  padding: 0 var(--spacing-md);
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-sm) center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
  appearance: none;
}
.form-textarea {
  padding: var(--spacing-md);
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}
.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-sm);
  cursor: pointer;
  padding: var(--spacing-md);
  border: 1px solid var(--secondary-200);
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
}
.checkbox-label:hover {
  border-color: var(--primary-300);
  background: var(--primary-25);
}
.checkbox-input {
  margin: 0;
  margin-top: 2px;
}
.checkbox-text {
  font-size: var(--text-sm);
  font-weight: 500;
  color: var(--secondary-900);
  margin-bottom: var(--spacing-xs);
}
.checkbox-description {
  font-size: var(--text-xs);
  color: var(--secondary-600);
  display: block;
}
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
}
/*# sourceMappingURL=stakeholder-modal.component.css.map */
`] }]
  }], () => [{ type: FormBuilder }], { isOpen: [{
    type: Input
  }], editMode: [{
    type: Input
  }], initialData: [{
    type: Input
  }], loading: [{
    type: Input
  }], closed: [{
    type: Output
  }], saved: [{
    type: Output
  }] });
})();
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && \u0275setClassDebugInfo(StakeholderModalComponent, { className: "StakeholderModalComponent", filePath: "src/app/modules/applications/components/stakeholder-modal/stakeholder-modal.component.ts", lineNumber: 206 });
})();

export {
  StakeholderModalComponent
};
//# sourceMappingURL=chunk-JCRQO2VH.mjs.map
